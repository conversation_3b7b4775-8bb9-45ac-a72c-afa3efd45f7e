import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { forkJoin, filter } from 'rxjs';
import { Organization, OrganizationsService } from '../../core/services/organization.service';
import { AuthService } from '../../core/services/auth.service';
import { ScrollService } from '../../core/services/Scroll.service';

interface ExtendedOrganization extends Organization {
  name: string;
  hasDeviceGroupScope: boolean;
  hasLocationGroupScope: boolean;
  hasOrgManageDevices: boolean;
  hasSynapseOrgType: boolean;
  hasSynapseViewAllOrganizations: boolean;
}

@Component({
  selector: 'app-menu',
  standalone: false,
  templateUrl: './menu.component.html',
  styleUrl: './menu.component.css'
})
export class MenuComponent implements OnInit {
  @ViewChild('sider') sider!: ElementRef;
  selectedKey: string | null = null;
  isCollapsed = false;
  openMap: { [name: string]: boolean } = {
    sub1: false,
    sub2: false,
    sub3: false,
    sub4: false
  };
  isFavorite = false;
  hasSynapseViewAllUsers = false;
  hasSynapseViewAllOrganizations = false;
  hasSynapseManagerAllOrganizations = false;
  hasSynapseDeleteOrganizations = false;
  synapseMenu: ExtendedOrganization[] = [];
  organizationsMenu: ExtendedOrganization[] = [];

  constructor(
    private router: Router,
    private organizationsService: OrganizationsService,
    private authService: AuthService,
    private message: NzMessageService,
    private scrollService: ScrollService
  ) {
    this.loadPermissions();
  }

  ngOnInit() {
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: NavigationEnd) => {
      this.checkActiveMenu();
      this.authService.permissions$.subscribe((permissionsResponse: any) => {
        const orgPermissions = permissionsResponse?.data?.permissions
          ?.filter((perm: any) => perm.scope === 'org')
          .flatMap((perm: any) => perm.permissions) || [];

        // Save permissions for Organizations when navigating to /organizations
        if (event.urlAfterRedirects.includes('/organizations')) {
          const orgRelevantPermissions = orgPermissions.filter((perm: string) =>
            ['synapse_view_organizations', 'synapse_manage_organizations', 'synapse_delete_organizations'].includes(perm)
          );
          localStorage.setItem('organizationPermissionsSynapse', JSON.stringify(orgRelevantPermissions));
        }

        // Save permissions for Users when navigating to /all-users
        if (event.urlAfterRedirects.includes('/users')) {
          const userRelevantPermissions = orgPermissions.filter((perm: string) =>
            ['synapse_manage_synapse_users', 'synapse_delete_synapse_users', 'synapse_view_synapse_users'].includes(perm)
          );
          localStorage.setItem('userPermissionsSynapse', JSON.stringify(userRelevantPermissions));
        }
      });
    });
  }

  private loadPermissions() {
    this.authService.permissions$.subscribe({
      next: (response: any) => {
        if (!response || !Array.isArray(response.data.permissions)) {
          this.hasSynapseViewAllUsers = false;
          this.hasSynapseViewAllOrganizations = false;
          this.hasSynapseManagerAllOrganizations = false;
          this.hasSynapseDeleteOrganizations = false;
          this.synapseMenu = [];
          this.organizationsMenu = [];
          return;
        }
        const permissions = response.data.permissions;

        // Get organizationId from scope: "org"
        const orgIds = [...new Set(
          permissions
            .filter((perm: any) => perm.scope === 'org')
            .map((perm: any) => perm.organizationId)
        )];

        const orgPermissions = permissions.reduce((acc: { [key: string]: any[] }, perm: any) => {
          if (!acc[perm.organizationId]) {
            acc[perm.organizationId] = [];
          }
          acc[perm.organizationId].push(perm);
          return acc;
        }, {});

        // Check permissions in scope 'org'
        this.hasSynapseViewAllOrganizations = orgIds.some((orgId: any) =>
          (orgPermissions[orgId] || []).some(
            (perm: any) => perm.permissions.includes('synapse_view_organizations')
          )
        );
        this.hasSynapseManagerAllOrganizations = orgIds.some((orgId: any) =>
          (orgPermissions[orgId] || []).some(
            (perm: any) => perm.permissions.includes('synapse_manage_organizations')
          )
        );
        this.hasSynapseDeleteOrganizations = orgIds.some((orgId: any) =>
          (orgPermissions[orgId] || []).some(
            (perm: any) => perm.permissions.includes('synapse_delete_organizations')
          )
        );
        this.hasSynapseViewAllUsers = orgIds.some((orgId: any) =>
          (orgPermissions[orgId] || []).some(
            (perm: any) => perm.permissions.includes('synapse_view_synapse_users')
          )
        );

        // Fetch organization details for all orgIds
        const orgRequests = orgIds.map((orgId: any) =>
          this.organizationsService.getOrganizationsId(orgId)
        );

        forkJoin(orgRequests).subscribe({
          next: (orgs: any) => {
            this.synapseMenu = [];
            this.organizationsMenu = [];

            orgs
              .filter((org: any): org is Organization => org != null && !!org.id && !!org.name)
              .forEach((org: any) => {
                const extendedOrg: ExtendedOrganization = {
                  ...org,
                  hasDeviceGroupScope: (orgPermissions[org.id] || []).some(
                    (perm: any) => perm.scope === 'device_group'
                  ),
                  hasLocationGroupScope: (orgPermissions[org.id] || []).some(
                    (perm: any) => perm.scope === 'location_group'
                  ),
                  hasOrgManageDevices: (orgPermissions[org.id] || []).some(
                    (perm: any) => perm.permissions.includes('org_manage_devices')
                  ),
                  hasSynapseOrgType: org.orgtypeidentifier === 'synapse',
                  hasSynapseViewAllOrganizations: (orgPermissions[org.id] || []).some(
                    (perm: any) => perm.permissions.includes('synapse_view_organizations')
                  )
                };

                // Classify organizations into synapseMenu or organizationsMenu
                if (extendedOrg.hasSynapseOrgType) {
                  this.synapseMenu.push(extendedOrg);
                } else if (org.orgtypeidentifier === 'municipality') {
                  this.organizationsMenu.push(extendedOrg);
                }
              });

            this.organizationsMenu.sort((a: any, b: any) => a.name.localeCompare(b.name));
          },
          error: (err) => {
            this.message.create('error', 'Failed to load organizationsMenu. Please try again.', { nzDuration: 5000 });
            this.synapseMenu = [];
            this.organizationsMenu = [];
          }
        });
      },
      error: (err) => {
        this.hasSynapseViewAllUsers = false;
        this.hasSynapseViewAllOrganizations = false;
        this.hasSynapseManagerAllOrganizations = false;
        this.hasSynapseDeleteOrganizations = false;
        this.synapseMenu = [];
        this.organizationsMenu = [];
      }
    });
  }

  openHandler(key: string, open?: boolean): void {
    if (open !== undefined) {
      this.openMap[key] = open;
    } else {
      this.openMap[key] = true;
    }
  }

  private checkActiveMenu(): void {
    const activeRoutes = ['/permissions', '/users'];
    if (activeRoutes.some((route) => this.isActive(route))) {
      this.openMap['sub1'] = true;
      this.openHandler('sub1');
    } else {
      this.openMap['sub1'] = false;
    }
  }

  onSiderScroll(event: Event): void {
    const element = this.sider.nativeElement;
    if (element.scrollTop + element.clientHeight >= element.scrollHeight - 10) {
      this.scrollService.scrollToTop();
    }
  }

  toggleCollapsed(): void {
    this.isCollapsed = !this.isCollapsed;
  }

  isActive(route: string): boolean {
    return this.router.url.includes(route);
  }

  handleFavorite() {
    this.isFavorite = !this.isFavorite;
  }
}