package devicegroups

import "errors"

var (
	ErrDatabaseOperation       = errors.New("database operation failed")
	ErrUnexpectedFields        = errors.New("request contains unexpected fields")
	ErrInvalidRequestBody      = errors.New("invalid request body")
	ErrInvalidDeviceGroupsName = errors.New("role name cannot be empty")
	ErrOrganizationNotFound    = errors.New("organization not found")
	ErrDeviceGroupNotFound     = errors.New("device group not found")
)
