import { NgModule, APP_INITIALIZER } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { CredentialsInterceptor } from './interceptors/credentials.interceptor';
import { AuthService } from './core/services/auth.service';
import { AuthGuard } from './core/guards/auth.guard';
import { HttpClientModule, HTTP_INTERCEPTORS, provideHttpClient } from '@angular/common/http';

import { AppComponent } from './app.component';
import { FooListComponent } from './foo-list/foo-list.component';
import { provideNzI18n } from 'ng-zorro-antd/i18n';
import { en_US } from 'ng-zorro-antd/i18n';
import { registerLocaleData } from '@angular/common';
import en from '@angular/common/locales/en';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { OrganizationsComponent } from './pages/organizations/organizations.component';
import { SoftwareGatewayComponent } from './pages/software-gateway/software-gateway.component';
import { GatewayEditAddComponent } from './pages/software-gateway/gateway-edit-add/gateway-edit-add.component';
import { SoftwareGatewayConfigurationComponent } from './pages/software-gateway-configuration/software-gateway-configuration.component';
import { ForgotPasswordComponent } from './auth/forgot-password/forgot-password.component';
import { RolesPermissionsComponent } from './pages/roles-permissions/roles-permissions.component';
import { RolesComponent } from './pages/roles-permissions/roles/roles.component';
import { PermissionsComponent } from './pages/roles-permissions/permissions/permissions.component';
import { AddEditRoleComponent } from './pages/roles-permissions/add-edit-role/add-edit-role.component';

import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { EditAddComponent } from './pages/organizations/edit-add/edit-add.component';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { IconDefinition, IconDirective } from '@ant-design/icons-angular';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { ClipboardModule } from '@angular/cdk/clipboard';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzAlertModule } from 'ng-zorro-antd/alert';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import * as AllIcons from '@ant-design/icons-angular/icons';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';

registerLocaleData(en);

import { environment } from '../environments/environments';
import { ProtectedComponent } from './protected/protected.component';
import { RouterModule } from '@angular/router';
import { AppRoutingModule } from './app-routing.module';
import { HomeComponent } from './home/<USER>';
import { HeaderComponent } from './shared/header/header.component';
import { MenuComponent } from './shared/menu/menu.component';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { ConfigurationEditAddComponent } from './pages/software-gateway-configuration/configuration-edit-add/configuration-edit-add.component';
import { DevicesComponent } from './pages/devices/devices.component';
import { DevicesEditAddComponent } from './pages/devices/devices-edit-add/devices-edit-add.component';
import { ImportDeviceComponent } from './pages/devices/import-device/import-device.component';
import { LoginComponent } from './auth/login/login.component';
import { RegisterComponent } from './auth/register/register.component';
import { UsersComponent } from './pages/users/users.component';
import { AddEditUserComponent } from './pages/users/add-edit-user/add-edit-user.component';
import { InvitationsTableComponent } from './pages/users/invitations-table/invitations-table.component';
import { AuthenticationMethodsComponent } from './pages/authentication-methods/authentication-methods.component';
import { ChangePasswordComponent } from './pages/authentication-methods/change-password/change-password.component';
import { InvitationsPendingComponent } from './pages/invitations-pending/invitations-pending.component';

// export function initAuth(oauth: OAuthService) {
//   return () => {
//     const cfg: AuthConfig = {
//       issuer: environment.oidc.issuer,
//       clientId: environment.oidc.clientId,
//       redirectUri: environment.oidc.redirectUri,
//       scope: environment.oidc.scope,
//       responseType: environment.oidc.responseType,
//       strictDiscoveryDocumentValidation: false,
//       requireHttps: false,
//     };
//     oauth.configure(cfg);
//     return oauth.loadDiscoveryDocumentAndTryLogin();
//   };
// }

const icons = Object.values(AllIcons) as IconDefinition[];
@NgModule({
  declarations: [
    AppComponent,
    HomeComponent,
    ProtectedComponent,
    OrganizationsComponent,
    EditAddComponent,
    HeaderComponent,
    MenuComponent,
    SoftwareGatewayComponent,
    GatewayEditAddComponent,
    SoftwareGatewayConfigurationComponent,
    ConfigurationEditAddComponent,
    DevicesComponent,
    DevicesEditAddComponent,
    ImportDeviceComponent,
    LoginComponent,
    RegisterComponent,
    ForgotPasswordComponent,
    RolesPermissionsComponent,
    RolesComponent,
    PermissionsComponent,
    AddEditRoleComponent,
    UsersComponent,
    AddEditUserComponent,
    InvitationsTableComponent,
    AuthenticationMethodsComponent,
    ChangePasswordComponent,
    InvitationsPendingComponent,
  ],
  imports: [
    BrowserModule,
    HttpClientModule,
    FooListComponent,
    RouterModule,
    AppRoutingModule,
    FormsModule,
    NzTableModule,
    NzButtonModule,
    NzModalModule,
    NzFormModule,
    ReactiveFormsModule,
    NzDividerModule,
    NzInputModule,
    NzIconModule,
    NzMenuModule,
    // IconDirective,
    NzLayoutModule,
    NzSelectModule,
    NzAvatarModule,
    NzToolTipModule,
    NzTagModule,
    NzPopconfirmModule,
    NzSwitchModule,
    ClipboardModule,
    NzCardModule,
    NzAlertModule,
    NzBreadCrumbModule,
    NzUploadModule,
    NzIconModule.forRoot(icons),
    NzDatePickerModule,
  ],
  providers: [
    AuthService,
    AuthGuard,
    { provide: HTTP_INTERCEPTORS, useClass: CredentialsInterceptor, multi: true },
    // {
    //   provide: APP_INITIALIZER,
    //   // useFactory: initAuth,
    //   multi: true
    // },
    provideNzI18n(en_US),
    provideAnimationsAsync(),
    provideHttpClient()
  ],
  bootstrap: [AppComponent],
})
export class AppModule { }
