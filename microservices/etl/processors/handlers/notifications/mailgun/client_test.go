package mailgun

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"testing"

	"github.com/google/uuid"
	mg "github.com/mailgun/mailgun-go/v5"
	"github.com/mailgun/mailgun-go/v5/mtypes"
)

// MockMailgunClient implements *mailgun.Client for testing
type MockMailgunClient struct {
	sendFunc func(ctx context.Context, message mg.Message) (string, error)
}

func (m *MockMailgunClient) Send(ctx context.Context, message mg.Message) (string, error) {
	if m.sendFunc != nil {
		return m.sendFunc(ctx, message)
	}
	return "test-id", nil
}

// Implement other required methods with empty implementations for testing
func (m *MockMailgunClient) SetAPIBase(base string) error { return nil }
func (m *MockMailgunClient) APIBase() string              { return "" }
func (m *MockMailgunClient) APIKey() string               { return "" }
func (m *MockMailgunClient) SetHTTPClient(c *http.Client) {}
func (m *MockMailgunClient) HTTPClient() *http.Client     { return nil }

// TestMailgunClient implements only the methods needed for testing RealMailgunClient.Send
type TestMailgunClient struct {
	sendFunc func(ctx context.Context, message mg.Message) (mtypes.SendMessageResponse, error)
}

func (m *TestMailgunClient) Send(ctx context.Context, message mg.Message) (mtypes.SendMessageResponse, error) {
	if m.sendFunc != nil {
		return m.sendFunc(ctx, message)
	}
	return mtypes.SendMessageResponse{ID: "test-id"}, nil
}

// Implement the minimal interface methods needed
func (m *TestMailgunClient) APIBase() string               { return "" }
func (m *TestMailgunClient) APIKey() string                { return "" }
func (m *TestMailgunClient) SetAPIBase(url string) error   { return nil }
func (m *TestMailgunClient) SetHTTPClient(c *http.Client)  {}
func (m *TestMailgunClient) HTTPClient() *http.Client      { return nil }
func (m *TestMailgunClient) AddOverrideHeader(k, v string) {}

// Implement all other required methods with empty implementations
func (m *TestMailgunClient) ReSend(ctx context.Context, url string, recipients ...string) (mtypes.SendMessageResponse, error) {
	return mtypes.SendMessageResponse{}, nil
}

func (m *TestMailgunClient) ListBounces(domain string, opts *mg.ListOptions) *mg.BouncesIterator {
	return nil
}

func (m *TestMailgunClient) GetBounce(ctx context.Context, domain, address string) (mtypes.Bounce, error) {
	return mtypes.Bounce{}, nil
}

func (m *TestMailgunClient) AddBounce(ctx context.Context, domain, address, code, err string) error {
	return nil
}

func (m *TestMailgunClient) DeleteBounce(ctx context.Context, domain, address string) error {
	return nil
}
func (m *TestMailgunClient) DeleteBounceList(ctx context.Context, domain string) error { return nil }
func (m *TestMailgunClient) ListMetrics(opts mg.MetricsOptions) (*mg.MetricsIterator, error) {
	return nil, nil
}

func (m *TestMailgunClient) GetTag(ctx context.Context, domain, tag string) (mtypes.Tag, error) {
	return mtypes.Tag{}, nil
}
func (m *TestMailgunClient) DeleteTag(ctx context.Context, domain, tag string) error { return nil }
func (m *TestMailgunClient) ListTags(domain string, opts *mg.ListTagOptions) *mg.TagIterator {
	return nil
}
func (m *TestMailgunClient) ListDomains(opts *mg.ListDomainsOptions) *mg.DomainsIterator { return nil }
func (m *TestMailgunClient) GetDomain(ctx context.Context, domain string, opts *mg.GetDomainOptions) (mtypes.GetDomainResponse, error) {
	return mtypes.GetDomainResponse{}, nil
}

func (m *TestMailgunClient) CreateDomain(ctx context.Context, domain string, opts *mg.CreateDomainOptions) (mtypes.GetDomainResponse, error) {
	return mtypes.GetDomainResponse{}, nil
}

func (m *TestMailgunClient) VerifyDomain(ctx context.Context, domain string) (mtypes.GetDomainResponse, error) {
	return mtypes.GetDomainResponse{}, nil
}

func (m *TestMailgunClient) UpdateDomain(ctx context.Context, domain string, opts *mg.UpdateDomainOptions) error {
	return nil
}
func (m *TestMailgunClient) DeleteDomain(ctx context.Context, domain string) error { return nil }
func (m *TestMailgunClient) VerifyAndReturnDomain(ctx context.Context, domain string) (mtypes.GetDomainResponse, error) {
	return mtypes.GetDomainResponse{}, nil
}

func (m *TestMailgunClient) ListIPDomains(ip string, opts *mg.ListIPDomainOptions) *mg.IPDomainsIterator {
	return nil
}

func (m *TestMailgunClient) UpdateDomainConnection(ctx context.Context, domain string, dc mtypes.DomainConnection) error {
	return nil
}

func (m *TestMailgunClient) GetDomainConnection(ctx context.Context, domain string) (mtypes.DomainConnection, error) {
	return mtypes.DomainConnection{}, nil
}

func (m *TestMailgunClient) GetDomainTracking(ctx context.Context, domain string) (mtypes.DomainTracking, error) {
	return mtypes.DomainTracking{}, nil
}

func (m *TestMailgunClient) UpdateClickTracking(ctx context.Context, domain, active string) error {
	return nil
}

func (m *TestMailgunClient) UpdateUnsubscribeTracking(ctx context.Context, domain, active, htmlFooter, textFooter string) error {
	return nil
}

func (m *TestMailgunClient) UpdateOpenTracking(ctx context.Context, domain, active string) error {
	return nil
}

func (m *TestMailgunClient) UpdateDomainDkimSelector(ctx context.Context, domain, dkimSelector string) error {
	return nil
}

func (m *TestMailgunClient) GetStoredMessage(ctx context.Context, url string) (mtypes.StoredMessage, error) {
	return mtypes.StoredMessage{}, nil
}

func (m *TestMailgunClient) GetStoredMessageRaw(ctx context.Context, id string) (mtypes.StoredMessageRaw, error) {
	return mtypes.StoredMessageRaw{}, nil
}

func (m *TestMailgunClient) GetStoredAttachment(ctx context.Context, url string) ([]byte, error) {
	return nil, nil
}

func (m *TestMailgunClient) ListCredentials(domain string, opts *mg.ListOptions) *mg.CredentialsIterator {
	return nil
}

func (m *TestMailgunClient) CreateCredential(ctx context.Context, domain, login, password string) error {
	return nil
}

func (m *TestMailgunClient) ChangeCredentialPassword(ctx context.Context, domain, login, password string) error {
	return nil
}

func (m *TestMailgunClient) DeleteCredential(ctx context.Context, domain, login string) error {
	return nil
}

func (m *TestMailgunClient) ListUnsubscribes(domain string, opts *mg.ListOptions) *mg.UnsubscribesIterator {
	return nil
}

func (m *TestMailgunClient) GetUnsubscribe(ctx context.Context, domain, address string) (mtypes.Unsubscribe, error) {
	return mtypes.Unsubscribe{}, nil
}

func (m *TestMailgunClient) CreateUnsubscribe(ctx context.Context, domain, address, tag string) error {
	return nil
}

func (m *TestMailgunClient) CreateUnsubscribes(ctx context.Context, domain string, unsubscribes []mtypes.Unsubscribe) error {
	return nil
}

func (m *TestMailgunClient) DeleteUnsubscribe(ctx context.Context, domain, address string) error {
	return nil
}

func (m *TestMailgunClient) DeleteUnsubscribeWithTag(ctx context.Context, domain, a, t string) error {
	return nil
}

func (m *TestMailgunClient) ListComplaints(domain string, opts *mg.ListOptions) *mg.ComplaintsIterator {
	return nil
}

func (m *TestMailgunClient) GetComplaint(ctx context.Context, domain, address string) (mtypes.Complaint, error) {
	return mtypes.Complaint{}, nil
}

func (m *TestMailgunClient) CreateComplaint(ctx context.Context, domain, address string) error {
	return nil
}

func (m *TestMailgunClient) CreateComplaints(ctx context.Context, domain string, addresses []string) error {
	return nil
}

func (m *TestMailgunClient) DeleteComplaint(ctx context.Context, domain, address string) error {
	return nil
}
func (m *TestMailgunClient) ListRoutes(opts *mg.ListOptions) *mg.RoutesIterator { return nil }
func (m *TestMailgunClient) GetRoute(ctx context.Context, address string) (mtypes.Route, error) {
	return mtypes.Route{}, nil
}

func (m *TestMailgunClient) CreateRoute(ctx context.Context, address mtypes.Route) (mtypes.Route, error) {
	return mtypes.Route{}, nil
}
func (m *TestMailgunClient) DeleteRoute(ctx context.Context, address string) error { return nil }
func (m *TestMailgunClient) UpdateRoute(ctx context.Context, address string, r mtypes.Route) (mtypes.Route, error) {
	return mtypes.Route{}, nil
}

func (m *TestMailgunClient) ListWebhooks(ctx context.Context, domain string) (map[string][]string, error) {
	return nil, nil
}

func (m *TestMailgunClient) CreateWebhook(ctx context.Context, domain, kind string, url []string) error {
	return nil
}
func (m *TestMailgunClient) DeleteWebhook(ctx context.Context, domain, kind string) error { return nil }
func (m *TestMailgunClient) GetWebhook(ctx context.Context, domain, kind string) ([]string, error) {
	return nil, nil
}

func (m *TestMailgunClient) UpdateWebhook(ctx context.Context, domain, kind string, url []string) error {
	return nil
}

func (m *TestMailgunClient) VerifyWebhookSignature(sig mtypes.Signature) (verified bool, err error) {
	return false, nil
}
func (m *TestMailgunClient) ListMailingLists(opts *mg.ListOptions) *mg.ListsIterator { return nil }
func (m *TestMailgunClient) CreateMailingList(ctx context.Context, address mtypes.MailingList) (mtypes.MailingList, error) {
	return mtypes.MailingList{}, nil
}
func (m *TestMailgunClient) DeleteMailingList(ctx context.Context, address string) error { return nil }
func (m *TestMailgunClient) GetMailingList(ctx context.Context, address string) (mtypes.MailingList, error) {
	return mtypes.MailingList{}, nil
}

func (m *TestMailgunClient) UpdateMailingList(ctx context.Context, address string, ml mtypes.MailingList) (mtypes.MailingList, error) {
	return mtypes.MailingList{}, nil
}

func (m *TestMailgunClient) ListMembers(address string, opts *mg.ListOptions) *mg.MemberListIterator {
	return nil
}

func (m *TestMailgunClient) GetMember(ctx context.Context, MemberAddr, listAddr string) (mtypes.Member, error) {
	return mtypes.Member{}, nil
}

func (m *TestMailgunClient) CreateMember(ctx context.Context, merge bool, addr string, prototype mtypes.Member) error {
	return nil
}

func (m *TestMailgunClient) CreateMemberList(ctx context.Context, subscribed *bool, addr string, newMembers []any) error {
	return nil
}

func (m *TestMailgunClient) UpdateMember(ctx context.Context, Member, list string, prototype mtypes.Member) (mtypes.Member, error) {
	return mtypes.Member{}, nil
}
func (m *TestMailgunClient) DeleteMember(ctx context.Context, Member, list string) error { return nil }
func (m *TestMailgunClient) ListEvents(domain string, opts *mg.ListEventOptions) *mg.EventIterator {
	return nil
}

func (m *TestMailgunClient) PollEvents(domain string, opts *mg.ListEventOptions) *mg.EventPoller {
	return nil
}

func (m *TestMailgunClient) ListIPs(ctx context.Context, dedicated, enabled bool) ([]mtypes.IPAddress, error) {
	return nil, nil
}

func (m *TestMailgunClient) GetIP(ctx context.Context, ip string) (mtypes.IPAddress, error) {
	return mtypes.IPAddress{}, nil
}

func (m *TestMailgunClient) ListDomainIPs(ctx context.Context, domain string) ([]mtypes.IPAddress, error) {
	return nil, nil
}
func (m *TestMailgunClient) AddDomainIP(ctx context.Context, domain, ip string) error    { return nil }
func (m *TestMailgunClient) DeleteDomainIP(ctx context.Context, domain, ip string) error { return nil }
func (m *TestMailgunClient) ListExports(ctx context.Context, url string) ([]mtypes.Export, error) {
	return nil, nil
}

func (m *TestMailgunClient) GetExport(ctx context.Context, id string) (mtypes.Export, error) {
	return mtypes.Export{}, nil
}

func (m *TestMailgunClient) GetExportLink(ctx context.Context, id string) (string, error) {
	return "", nil
}
func (m *TestMailgunClient) CreateExport(ctx context.Context, url string) error { return nil }
func (m *TestMailgunClient) GetTagLimits(ctx context.Context, domain string) (mtypes.TagLimits, error) {
	return mtypes.TagLimits{}, nil
}

func (m *TestMailgunClient) CreateTemplate(ctx context.Context, domain string, template *mtypes.Template) error {
	return nil
}

func (m *TestMailgunClient) GetTemplate(ctx context.Context, domain, name string) (mtypes.Template, error) {
	return mtypes.Template{}, nil
}

func (m *TestMailgunClient) UpdateTemplate(ctx context.Context, domain string, template *mtypes.Template) error {
	return nil
}

func (m *TestMailgunClient) DeleteTemplate(ctx context.Context, domain, name string) error {
	return nil
}

func (m *TestMailgunClient) ListTemplates(domain string, opts *mg.ListTemplateOptions) *mg.TemplatesIterator {
	return nil
}

func (m *TestMailgunClient) AddTemplateVersion(ctx context.Context, domain, templateName string, version *mtypes.TemplateVersion) error {
	return nil
}

func (m *TestMailgunClient) GetTemplateVersion(ctx context.Context, domain, templateName, tag string) (mtypes.TemplateVersion, error) {
	return mtypes.TemplateVersion{}, nil
}

func (m *TestMailgunClient) UpdateTemplateVersion(ctx context.Context, domain, templateName string, version *mtypes.TemplateVersion) error {
	return nil
}

func (m *TestMailgunClient) DeleteTemplateVersion(ctx context.Context, domain, templateName, tag string) error {
	return nil
}

func (m *TestMailgunClient) ListTemplateVersions(domain, templateName string, opts *mg.ListOptions) *mg.TemplateVersionsIterator {
	return nil
}

func (m *TestMailgunClient) ValidateEmail(ctx context.Context, email string, mailBoxVerify bool) (mtypes.ValidateEmailResponse, error) {
	return mtypes.ValidateEmailResponse{}, nil
}

func (m *TestMailgunClient) ListAlertsEvents(context.Context, *mg.ListAlertsEventsOptions) (*mtypes.AlertsEventsResponse, error) {
	return nil, nil
}

func (m *TestMailgunClient) ListAlerts(context.Context, *mg.ListAlertsOptions) (*mtypes.AlertsSettingsResponse, error) {
	return nil, nil
}

func (m *TestMailgunClient) AddAlert(context.Context, mtypes.AlertsEventSettingRequest) (*mtypes.AlertsEventSettingResponse, error) {
	return nil, nil
}
func (m *TestMailgunClient) DeleteAlert(ctx context.Context, id uuid.UUID) error { return nil }
func (m *TestMailgunClient) ListMonitoredDomains(opts mg.ListMonitoredDomainsOptions) (*mg.MonitoredDomainsIterator, error) {
	return nil, nil
}

func (m *TestMailgunClient) ListSubaccounts(opts *mg.ListSubaccountsOptions) *mg.SubaccountsIterator {
	return nil
}

func (m *TestMailgunClient) CreateSubaccount(ctx context.Context, subaccountName string) (mtypes.SubaccountResponse, error) {
	return mtypes.SubaccountResponse{}, nil
}

func (m *TestMailgunClient) GetSubaccount(ctx context.Context, subaccountID string) (mtypes.SubaccountResponse, error) {
	return mtypes.SubaccountResponse{}, nil
}

func (m *TestMailgunClient) EnableSubaccount(ctx context.Context, subaccountID string) (mtypes.SubaccountResponse, error) {
	return mtypes.SubaccountResponse{}, nil
}

func (m *TestMailgunClient) DisableSubaccount(ctx context.Context, subaccountID string) (mtypes.SubaccountResponse, error) {
	return mtypes.SubaccountResponse{}, nil
}
func (m *TestMailgunClient) SetOnBehalfOfSubaccount(subaccountID string) {}
func (m *TestMailgunClient) RemoveOnBehalfOfSubaccount()                 {}
func (m *TestMailgunClient) ListAPIKeys(ctx context.Context, opts *mg.ListAPIKeysOptions) ([]mtypes.APIKey, error) {
	return nil, nil
}

func (m *TestMailgunClient) CreateAPIKey(ctx context.Context, role string, opts *mg.CreateAPIKeyOptions) (mtypes.APIKey, error) {
	return mtypes.APIKey{}, nil
}
func (m *TestMailgunClient) DeleteAPIKey(ctx context.Context, id string) error { return nil }
func (m *TestMailgunClient) RegeneratePublicAPIKey(ctx context.Context) (mtypes.RegeneratePublicAPIKeyResponse, error) {
	return mtypes.RegeneratePublicAPIKeyResponse{}, nil
}

func TestNewClient_Success(t *testing.T) {
	// Set up environment variables
	originalDomain := os.Getenv("MG_DOMAIN")
	originalAPIKey := os.Getenv("MG_API_KEY")
	originalSender := os.Getenv("MG_SENDER")
	defer func() {
		os.Setenv("MG_DOMAIN", originalDomain)
		os.Setenv("MG_API_KEY", originalAPIKey)
		os.Setenv("MG_SENDER", originalSender)
	}()
	os.Setenv("MG_DOMAIN", "test.example.com")
	os.Setenv("MG_API_KEY", "test-api-key")
	os.Setenv("MG_SENDER", "<EMAIL>")

	// Create client
	client, err := NewClient(context.Background())
	if err != nil {
		t.Errorf("NewClient() returned error: %v", err)
	}
	if client == nil {
		t.Fatal("NewClient() returned nil")
	}
	if client.domain != "test.example.com" {
		t.Errorf("Expected domain 'test.example.com', got '%s'", client.domain)
	}
	if client.senderEmail != "<EMAIL>" {
		t.Errorf("Expected senderEmail '<EMAIL>', got '%s'", client.senderEmail)
	}
}

func TestNewClient_MissingDomain(t *testing.T) {
	// Clear MG_DOMAIN environment variable
	originalDomain := os.Getenv("MG_DOMAIN")
	defer os.Setenv("MG_DOMAIN", originalDomain)
	os.Unsetenv("MG_DOMAIN")

	// Set other required variables
	os.Setenv("MG_API_KEY", "test-api-key")

	// Create client
	client, err := NewClient(context.Background())

	if err != ErrMissingDomain {
		t.Errorf("Expected ErrMissingDomain, got: %v", err)
	}
	if client != nil {
		t.Error("NewClient() should return nil when domain is missing")
	}
}

func TestNewClient_MissingAPIKey(t *testing.T) {
	// Clear MG_API_KEY environment variable
	originalAPIKey := os.Getenv("MG_API_KEY")
	defer os.Setenv("MG_API_KEY", originalAPIKey)
	os.Unsetenv("MG_API_KEY")

	// Set other required variables
	os.Setenv("MG_DOMAIN", "test.example.com")

	// Create client
	client, err := NewClient(context.Background())

	if err != ErrMissingAPIKey {
		t.Errorf("Expected ErrMissingAPIKey, got: %v", err)
	}
	if client != nil {
		t.Error("NewClient() should return nil when API key is missing")
	}
}

func TestNewClient_MissingSender(t *testing.T) {
	// Clear MG_SENDER environment variable
	originalSender := os.Getenv("MG_SENDER")
	defer os.Setenv("MG_SENDER", originalSender)
	os.Unsetenv("MG_SENDER")

	// Set other required variables
	os.Setenv("MG_DOMAIN", "test.example.com")
	os.Setenv("MG_API_KEY", "test-api-key")

	// Create client
	client, err := NewClient(context.Background())

	if err != ErrMissingSender {
		t.Errorf("Expected ErrMissingSender, got: %v", err)
	}
	if client != nil {
		t.Error("NewClient() should return nil when sender is missing")
	}
}

func TestNewClient_WithStub(t *testing.T) {
	// Set up environment variables
	originalDomain := os.Getenv("MG_DOMAIN")
	originalAPIKey := os.Getenv("MG_API_KEY")
	originalSender := os.Getenv("MG_SENDER")
	originalStub := os.Getenv("MG_STUB")
	defer func() {
		os.Setenv("MG_DOMAIN", originalDomain)
		os.Setenv("MG_API_KEY", originalAPIKey)
		os.Setenv("MG_SENDER", originalSender)
		os.Setenv("MG_STUB", originalStub)
	}()
	os.Setenv("MG_DOMAIN", "test.example.com")
	os.Setenv("MG_API_KEY", "test-api-key")
	os.Setenv("MG_SENDER", "<EMAIL>")
	os.Setenv("MG_STUB", "true")

	// Create client
	client, err := NewClient(context.Background())
	if err != nil {
		t.Errorf("NewClient() returned error: %v", err)
	}
	if client == nil {
		t.Fatal("NewClient() returned nil")
	}
	if client.domain != "test.example.com" {
		t.Errorf("Expected domain 'test.example.com', got '%s'", client.domain)
	}
	if client.senderEmail != "<EMAIL>" {
		t.Errorf("Expected senderEmail '<EMAIL>', got '%s'", client.senderEmail)
	}

	// Verify it's using stub client
	if _, ok := client.sender.(*StubMailgunClient); !ok {
		t.Error("Expected StubMailgunClient when MG_STUB is set")
	}
}

func TestNewClient_WithCustomHost(t *testing.T) {
	// Set up environment variables
	originalDomain := os.Getenv("MG_DOMAIN")
	originalAPIKey := os.Getenv("MG_API_KEY")
	originalSender := os.Getenv("MG_SENDER")
	originalHost := os.Getenv("MG_HOST")
	originalStub := os.Getenv("MG_STUB")
	defer func() {
		os.Setenv("MG_DOMAIN", originalDomain)
		os.Setenv("MG_API_KEY", originalAPIKey)
		os.Setenv("MG_SENDER", originalSender)
		os.Setenv("MG_HOST", originalHost)
		os.Setenv("MG_STUB", originalStub)
	}()
	os.Setenv("MG_DOMAIN", "test.example.com")
	os.Setenv("MG_API_KEY", "test-api-key")
	os.Setenv("MG_SENDER", "<EMAIL>")
	os.Setenv("MG_HOST", "localhost:3000")
	os.Unsetenv("MG_STUB") // Ensure MG_STUB is not set

	// Create client
	client, err := NewClient(context.Background())
	if err != nil {
		t.Errorf("NewClient() returned error: %v", err)
	}
	if client == nil {
		t.Fatal("NewClient() returned nil")
	}
	if client.domain != "test.example.com" {
		t.Errorf("Expected domain 'test.example.com', got '%s'", client.domain)
	}
	if client.senderEmail != "<EMAIL>" {
		t.Errorf("Expected senderEmail '<EMAIL>', got '%s'", client.senderEmail)
	}

	// Verify it's using RealMailgunClient
	if _, ok := client.sender.(*RealMailgunClient); !ok {
		t.Error("Expected RealMailgunClient when MG_HOST is set")
	}
}

func TestNewClient_ProductionMode(t *testing.T) {
	// Set up environment variables
	originalDomain := os.Getenv("MG_DOMAIN")
	originalAPIKey := os.Getenv("MG_API_KEY")
	originalSender := os.Getenv("MG_SENDER")
	originalHost := os.Getenv("MG_HOST")
	originalStub := os.Getenv("MG_STUB")
	defer func() {
		os.Setenv("MG_DOMAIN", originalDomain)
		os.Setenv("MG_API_KEY", originalAPIKey)
		os.Setenv("MG_SENDER", originalSender)
		os.Setenv("MG_HOST", originalHost)
		os.Setenv("MG_STUB", originalStub)
	}()
	os.Setenv("MG_DOMAIN", "test.example.com")
	os.Setenv("MG_API_KEY", "test-api-key")
	os.Setenv("MG_SENDER", "<EMAIL>")
	os.Unsetenv("MG_HOST") // Ensure MG_HOST is not set
	os.Unsetenv("MG_STUB") // Ensure MG_STUB is not set

	// Create client
	client, err := NewClient(context.Background())
	if err != nil {
		t.Errorf("NewClient() returned error: %v", err)
	}
	if client == nil {
		t.Fatal("NewClient() returned nil")
	}
	if client.domain != "test.example.com" {
		t.Errorf("Expected domain 'test.example.com', got '%s'", client.domain)
	}
	if client.senderEmail != "<EMAIL>" {
		t.Errorf("Expected senderEmail '<EMAIL>', got '%s'", client.senderEmail)
	}

	// Verify it's using RealMailgunClient
	if _, ok := client.sender.(*RealMailgunClient); !ok {
		t.Error("Expected RealMailgunClient in production mode")
	}
}

func TestWithClient_FromContext(t *testing.T) {
	// Create a client
	client := &Client{domain: "test.example.com", senderEmail: "<EMAIL>"}

	// Store in context
	ctx := WithClient(context.Background(), client)

	// Retrieve from context
	retrievedClient := FromContext(ctx)

	if retrievedClient != client {
		t.Error("FromContext() should return the same client that was stored")
	}
}

func TestFromContext_NoClient(t *testing.T) {
	// Create context without client
	ctx := context.Background()

	// Retrieve from context
	client := FromContext(ctx)

	if client != nil {
		t.Error("FromContext() should return nil when no client is stored")
	}
}

func TestClient_SendEmail_Success(t *testing.T) {
	// Set up environment variables
	originalSender := os.Getenv("MG_SENDER")
	defer os.Setenv("MG_SENDER", originalSender)
	os.Setenv("MG_SENDER", "<EMAIL>")

	// Create a mock Mailgun client
	mockMG := &MockMailgunClient{}
	client := &Client{
		sender:      mockMG,
		domain:      "test.example.com",
		senderEmail: "<EMAIL>",
	}

	// Send email
	ctx := context.Background()
	err := client.SendEmail(ctx, "<EMAIL>", "Test Subject", "Test Body")
	if err != nil {
		t.Errorf("SendEmail() returned error: %v", err)
	}
}

func TestClient_SendEmail_APIError(t *testing.T) {
	// Set up environment variables
	originalSender := os.Getenv("MG_SENDER")
	defer os.Setenv("MG_SENDER", originalSender)
	os.Setenv("MG_SENDER", "<EMAIL>")

	// Create a mock Mailgun client that returns error
	mockMG := &MockMailgunClient{
		sendFunc: func(ctx context.Context, message mg.Message) (string, error) {
			return "", fmt.Errorf("mailgun API error")
		},
	}
	client := &Client{
		sender:      mockMG,
		domain:      "test.example.com",
		senderEmail: "<EMAIL>",
	}

	// Send email
	ctx := context.Background()
	err := client.SendEmail(ctx, "<EMAIL>", "Test Subject", "Test Body")

	if err == nil {
		t.Error("SendEmail() should return error when API returns error status")
	}
}

func TestRealMailgunClient_Send(t *testing.T) {
	// Test RealMailgunClient.Send by testing the actual implementation
	// We'll test it indirectly through the Client.SendEmail method

	// Set up environment variables
	originalDomain := os.Getenv("MG_DOMAIN")
	originalAPIKey := os.Getenv("MG_API_KEY")
	originalSender := os.Getenv("MG_SENDER")
	defer func() {
		os.Setenv("MG_DOMAIN", originalDomain)
		os.Setenv("MG_API_KEY", originalAPIKey)
		os.Setenv("MG_SENDER", originalSender)
	}()
	os.Setenv("MG_DOMAIN", "test.example.com")
	os.Setenv("MG_API_KEY", "test-api-key")
	os.Setenv("MG_SENDER", "<EMAIL>")

	// Create client (this will use RealMailgunClient in production mode)
	client, err := NewClient(context.Background())
	if err != nil {
		t.Fatalf("NewClient() returned error: %v", err)
	}

	// Verify it's using RealMailgunClient
	if _, ok := client.sender.(*RealMailgunClient); !ok {
		t.Skip("Skipping test - not using RealMailgunClient (likely MG_STUB or MG_HOST is set)")
	}

	// Test that the client can be created successfully
	if client.domain != "test.example.com" {
		t.Errorf("Expected domain 'test.example.com', got '%s'", client.domain)
	}
	if client.senderEmail != "<EMAIL>" {
		t.Errorf("Expected senderEmail '<EMAIL>', got '%s'", client.senderEmail)
	}

	// Test that SendEmail will fail with test API key (expected behavior)
	ctx := context.Background()
	err = client.SendEmail(ctx, "<EMAIL>", "Test Subject", "Test Body")
	if err == nil {
		t.Error("Expected SendEmail to fail with test API key")
	}
}

func TestRealMailgunClient_Send_Indirect_Error(t *testing.T) {
	// Test RealMailgunClient error handling by testing the actual implementation
	// We'll test it indirectly through the Client.SendEmail method

	// Set up environment variables
	originalDomain := os.Getenv("MG_DOMAIN")
	originalAPIKey := os.Getenv("MG_API_KEY")
	originalSender := os.Getenv("MG_SENDER")
	defer func() {
		os.Setenv("MG_DOMAIN", originalDomain)
		os.Setenv("MG_API_KEY", originalAPIKey)
		os.Setenv("MG_SENDER", originalSender)
	}()
	os.Setenv("MG_DOMAIN", "test.example.com")
	os.Setenv("MG_API_KEY", "invalid-api-key") // Invalid API key to trigger error
	os.Setenv("MG_SENDER", "<EMAIL>")

	// Create client
	client, err := NewClient(context.Background())
	if err != nil {
		t.Fatalf("NewClient() returned error: %v", err)
	}

	// Verify it's using RealMailgunClient
	if _, ok := client.sender.(*RealMailgunClient); !ok {
		t.Skip("Skipping test - not using RealMailgunClient (likely MG_STUB or MG_HOST is set)")
	}

	// Test that the client can be created successfully even with invalid API key
	if client.domain != "test.example.com" {
		t.Errorf("Expected domain 'test.example.com', got '%s'", client.domain)
	}

	// Test that SendEmail will fail with invalid API key
	ctx := context.Background()
	err = client.SendEmail(ctx, "<EMAIL>", "Test Subject", "Test Body")
	if err == nil {
		t.Error("Expected SendEmail to fail with invalid API key")
	}
}

func TestStubMailgunClient_Send(t *testing.T) {
	// Create StubMailgunClient
	stubClient := &StubMailgunClient{}

	// Create a test message
	message := mg.NewMessage("<EMAIL>", "Test Subject", "Test Body", "<EMAIL>")

	// Test Send method
	ctx := context.Background()
	id, err := stubClient.Send(ctx, message)
	if err != nil {
		t.Errorf("StubMailgunClient.Send() returned error: %v", err)
	}
	if id != "stub-id" {
		t.Errorf("Expected message ID 'stub-id', got '%s'", id)
	}
}

func TestRealMailgunClient_Send_Success(t *testing.T) {
	// Create a test client that implements the minimal interface
	mockMG := &TestMailgunClient{
		sendFunc: func(ctx context.Context, message mg.Message) (mtypes.SendMessageResponse, error) {
			return mtypes.SendMessageResponse{ID: "test-message-id"}, nil
		},
	}

	// Create RealMailgunClient with our test client
	realClient := &RealMailgunClient{client: mockMG}

	// Create a test message
	message := mg.NewMessage("<EMAIL>", "Test Subject", "Test Body", "<EMAIL>")

	// Test Send method
	ctx := context.Background()
	id, err := realClient.Send(ctx, message)
	if err != nil {
		t.Errorf("RealMailgunClient.Send() returned error: %v", err)
	}
	if id != "test-message-id" {
		t.Errorf("Expected message ID 'test-message-id', got '%s'", id)
	}
}

func TestRealMailgunClient_Send_Error(t *testing.T) {
	// Create a test client that returns error
	mockMG := &TestMailgunClient{
		sendFunc: func(ctx context.Context, message mg.Message) (mtypes.SendMessageResponse, error) {
			return mtypes.SendMessageResponse{}, fmt.Errorf("mailgun API error")
		},
	}

	// Create RealMailgunClient with our test client
	realClient := &RealMailgunClient{client: mockMG}

	// Create a test message
	message := mg.NewMessage("<EMAIL>", "Test Subject", "Test Body", "<EMAIL>")

	// Test Send method
	ctx := context.Background()
	id, err := realClient.Send(ctx, message)

	if err == nil {
		t.Error("RealMailgunClient.Send() should return error when API returns error")
	}
	if id != "" {
		t.Errorf("Expected empty message ID on error, got '%s'", id)
	}
}
