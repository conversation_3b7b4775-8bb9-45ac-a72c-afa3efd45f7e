package postgres

import (
	"database/sql"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"synapse-its.com/shared/mocks"
	apsReset "synapse-its.com/shared/rest/domain/aps_reset"
)

// Mock sql.Result for testing
type mockSQLResult struct {
	mock.Mock
}

func (m *mockSQLResult) LastInsertId() (int64, error) {
	args := m.Called()
	return args.Get(0).(int64), args.Error(1)
}

func (m *mockSQLResult) RowsAffected() (int64, error) {
	args := m.Called()
	return args.Get(0).(int64), args.Error(1)
}

func TestNewApsResetRepo(t *testing.T) {
	t.Parallel()

	// Arrange
	mockDB := &mocks.FakeDBExecutor{}

	// Act
	repo := NewApsResetRepo(mockDB)

	// Assert
	assert.NotNil(t, repo)
	assert.Equal(t, mockDB, repo.db)
}

func TestApsResetRepo_FindConfigByUserIDAndOrganizationID(t *testing.T) {
	tests := []struct {
		name          string
		userID        uuid.UUID
		orgID         uuid.UUID
		setupMock     func(*mocks.FakeDBExecutor)
		expectedError string
		expectConfig  bool
	}{
		{
			name:   "Success - valid config found",
			userID: uuid.MustParse("12345678-1234-1234-1234-123456789abc"),
			orgID:  uuid.MustParse("*************-4321-4321-cba987654321"),
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Verify the query contains expected elements
					assert.Contains(t, query, "SELECT")
					assert.Contains(t, query, "FROM {{APSResetConfig}}")
					assert.Contains(t, query, "WHERE UserId = $1 AND OrganizationId = $2")

					// Verify the arguments
					assert.Len(t, args, 2)
					assert.Equal(t, uuid.MustParse("12345678-1234-1234-1234-123456789abc"), args[0])
					assert.Equal(t, uuid.MustParse("*************-4321-4321-cba987654321"), args[1])

					// Set the destination struct with test data
					dbConfig := dest.(*dbAPSResetConfig)
					dbConfig.ID = uuid.MustParse("11111111-1111-1111-1111-111111111111")
					dbConfig.UserID = uuid.MustParse("12345678-1234-1234-1234-123456789abc")
					dbConfig.OrganizationID = uuid.MustParse("*************-4321-4321-cba987654321")
					dbConfig.ConfigAPSResetSuccessLimit = 5
					dbConfig.ConfigAPSResetSuccessListLength = 3
					dbConfig.ConfigAPSResetSuccessListWindowMinutes = 60
					dbConfig.IsDeleted = false
					dbConfig.CreatedAt = "2025-01-01T00:00:00Z"
					dbConfig.UpdatedAt = "2025-01-01T00:00:00Z"

					return nil
				}
			},
			expectConfig: true,
		},
		{
			name:   "Success - config with zero values",
			userID: uuid.MustParse("*************-2222-2222-************"),
			orgID:  uuid.MustParse("*************-3333-3333-************"),
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Set the destination struct with test data
					dbConfig := dest.(*dbAPSResetConfig)
					dbConfig.ID = uuid.MustParse("*************-4444-4444-************")
					dbConfig.UserID = uuid.MustParse("*************-2222-2222-************")
					dbConfig.OrganizationID = uuid.MustParse("*************-3333-3333-************")
					dbConfig.ConfigAPSResetSuccessLimit = 0
					dbConfig.ConfigAPSResetSuccessListLength = 0
					dbConfig.ConfigAPSResetSuccessListWindowMinutes = 0
					dbConfig.IsDeleted = false
					dbConfig.CreatedAt = "2025-01-01T00:00:00Z"
					dbConfig.UpdatedAt = "2025-01-01T00:00:00Z"

					return nil
				}
			},
			expectConfig: true,
		},
		{
			name:   "Success - config with large values",
			userID: uuid.MustParse("*************-5555-5555-************"),
			orgID:  uuid.MustParse("*************-6666-6666-************"),
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Set the destination struct with test data
					dbConfig := dest.(*dbAPSResetConfig)
					dbConfig.ID = uuid.MustParse("*************-7777-7777-************")
					dbConfig.UserID = uuid.MustParse("*************-5555-5555-************")
					dbConfig.OrganizationID = uuid.MustParse("*************-6666-6666-************")
					dbConfig.ConfigAPSResetSuccessLimit = 1000
					dbConfig.ConfigAPSResetSuccessListLength = 100
					dbConfig.ConfigAPSResetSuccessListWindowMinutes = 1440
					dbConfig.IsDeleted = false
					dbConfig.CreatedAt = "2025-01-01T00:00:00Z"
					dbConfig.UpdatedAt = "2025-01-01T00:00:00Z"

					return nil
				}
			},
			expectConfig: true,
		},
		{
			name:   "Error - database query failed",
			userID: uuid.MustParse("*************-8888-8888-************"),
			orgID:  uuid.MustParse("*************-9999-9999-************"),
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedError: "database connection failed",
		},
		{
			name:   "Error - no rows found",
			userID: uuid.MustParse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"),
			orgID:  uuid.MustParse("bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb"),
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
			},
			expectedError: "sql: no rows in result set",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			mockDB := &mocks.FakeDBExecutor{}
			tt.setupMock(mockDB)

			repo := &apsResetRepo{
				db: mockDB,
			}

			// Act
			config, err := repo.FindConfigByUserIDAndOrganizationID(tt.userID, tt.orgID)

			// Assert
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err.Error())
				assert.Nil(t, config)
			} else {
				assert.NoError(t, err)
				if tt.expectConfig {
					assert.NotNil(t, config)
					assert.NotEqual(t, uuid.Nil, config.ID)
					assert.NotEqual(t, uuid.Nil, config.UserID)
					assert.NotEqual(t, uuid.Nil, config.OrganizationID)
				} else {
					assert.Nil(t, config)
				}
			}

			// Verify that QueryRowStruct was called exactly once
			assert.Equal(t, 1, mockDB.QueryRowStructCallCount)
		})
	}
}

func TestApsResetRepo_FindStatisticByUserIDAndOrganizationID(t *testing.T) {
	tests := []struct {
		name            string
		userID          uuid.UUID
		orgID           uuid.UUID
		setupMock       func(*mocks.FakeDBExecutor)
		expectedError   string
		expectStatistic bool
	}{
		{
			name:   "Success - valid statistic found",
			userID: uuid.MustParse("12345678-1234-1234-1234-123456789abc"),
			orgID:  uuid.MustParse("*************-4321-4321-cba987654321"),
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Verify the query contains expected elements
					assert.Contains(t, query, "SELECT")
					assert.Contains(t, query, "FROM {{APSResetStatistic}}")
					assert.Contains(t, query, "WHERE UserId = $1 AND OrganizationId = $2")

					// Verify the arguments
					assert.Len(t, args, 2)
					assert.Equal(t, uuid.MustParse("12345678-1234-1234-1234-123456789abc"), args[0])
					assert.Equal(t, uuid.MustParse("*************-4321-4321-cba987654321"), args[1])

					// Set the destination struct with test data
					dbStat := dest.(*dbAPSResetStatistic)
					dbStat.ID = uuid.MustParse("11111111-1111-1111-1111-111111111111")
					dbStat.UserID = uuid.MustParse("12345678-1234-1234-1234-123456789abc")
					dbStat.OrganizationID = uuid.MustParse("*************-4321-4321-cba987654321")
					dbStat.StatAPSResetCount = 5
					dbStat.StatAPSResetSuccessCount = 3
					dbStat.StatAPSResetSuccessList = []string{
						"2025-01-01T10:00:00Z",
						"2025-01-01T11:00:00Z",
					}
					dbStat.IsDeleted = false
					dbStat.CreatedAt = "2025-01-01T00:00:00Z"
					dbStat.UpdatedAt = "2025-01-01T00:00:00Z"

					return nil
				}
			},
			expectStatistic: true,
		},
		{
			name:   "Success - statistic with empty success list",
			userID: uuid.MustParse("*************-2222-2222-************"),
			orgID:  uuid.MustParse("*************-3333-3333-************"),
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Set the destination struct with test data
					dbStat := dest.(*dbAPSResetStatistic)
					dbStat.ID = uuid.MustParse("*************-4444-4444-************")
					dbStat.UserID = uuid.MustParse("*************-2222-2222-************")
					dbStat.OrganizationID = uuid.MustParse("*************-3333-3333-************")
					dbStat.StatAPSResetCount = 0
					dbStat.StatAPSResetSuccessCount = 0
					dbStat.StatAPSResetSuccessList = []string{}
					dbStat.IsDeleted = false
					dbStat.CreatedAt = "2025-01-01T00:00:00Z"
					dbStat.UpdatedAt = "2025-01-01T00:00:00Z"

					return nil
				}
			},
			expectStatistic: true,
		},
		{
			name:   "Success - statistic with nil UUIDs",
			userID: uuid.Nil,
			orgID:  uuid.Nil,
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Set the destination struct with test data
					dbStat := dest.(*dbAPSResetStatistic)
					dbStat.ID = uuid.Nil
					dbStat.UserID = uuid.Nil
					dbStat.OrganizationID = uuid.Nil
					dbStat.StatAPSResetCount = 2
					dbStat.StatAPSResetSuccessCount = 1
					dbStat.StatAPSResetSuccessList = []string{"2025-01-01T12:00:00Z"}
					dbStat.IsDeleted = false
					dbStat.CreatedAt = "2025-01-01T00:00:00Z"
					dbStat.UpdatedAt = "2025-01-01T00:00:00Z"

					return nil
				}
			},
			expectStatistic: true,
		},
		{
			name:   "Error - database query failed",
			userID: uuid.MustParse("*************-5555-5555-************"),
			orgID:  uuid.MustParse("*************-6666-6666-************"),
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedError: "database connection failed",
		},
		{
			name:   "Error - no rows found",
			userID: uuid.MustParse("*************-7777-7777-************"),
			orgID:  uuid.MustParse("*************-8888-8888-************"),
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
			},
			expectedError: "sql: no rows in result set",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			mockDB := &mocks.FakeDBExecutor{}
			tt.setupMock(mockDB)

			repo := &apsResetRepo{
				db: mockDB,
			}

			// Act
			statistic, err := repo.FindStatisticByUserIDAndOrganizationID(tt.userID, tt.orgID)

			// Assert
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err.Error())
				assert.Nil(t, statistic)
			} else {
				assert.NoError(t, err)
				if tt.expectStatistic {
					assert.NotNil(t, statistic)
					// For nil UUIDs test case, we expect Nil UUIDs
					if tt.userID == uuid.Nil {
						assert.Equal(t, uuid.Nil, statistic.ID)
						assert.Equal(t, uuid.Nil, statistic.UserID)
						assert.Equal(t, uuid.Nil, statistic.OrganizationID)
					} else {
						assert.NotEqual(t, uuid.Nil, statistic.ID)
						assert.NotEqual(t, uuid.Nil, statistic.UserID)
						assert.NotEqual(t, uuid.Nil, statistic.OrganizationID)
					}
				} else {
					assert.Nil(t, statistic)
				}
			}

			// Verify that QueryRowStruct was called exactly once
			assert.Equal(t, 1, mockDB.QueryRowStructCallCount)
		})
	}
}

func TestApsResetRepo_UpdateStatistic(t *testing.T) {
	tests := []struct {
		name          string
		statistic     *apsReset.APSResetStatistic
		setupMock     func(*mocks.FakeDBExecutor)
		expectedError string
	}{
		{
			name: "Success - update statistic with data",
			statistic: &apsReset.APSResetStatistic{
				ID:                       uuid.MustParse("12345678-1234-1234-1234-123456789abc"),
				UserID:                   uuid.MustParse("*************-4321-4321-cba987654321"),
				OrganizationID:           uuid.MustParse("11111111-1111-1111-1111-111111111111"),
				StatAPSResetCount:        5,
				StatAPSResetSuccessCount: 3,
				StatAPSResetSuccessList: []time.Time{
					time.Date(2025, 1, 1, 10, 0, 0, 0, time.UTC),
					time.Date(2025, 1, 1, 11, 0, 0, 0, time.UTC),
				},
			},
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockResult := &mockSQLResult{}
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)

				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					// Verify the query contains expected elements
					assert.Contains(t, query, "UPDATE {{APSResetStatistic}}")
					assert.Contains(t, query, "SET StatAPSResetCount = $1")
					assert.Contains(t, query, "StatAPSResetSuccessCount = $2")
					assert.Contains(t, query, "StatAPSResetSuccessList = $3")
					assert.Contains(t, query, "UpdatedAt = NOW()")
					assert.Contains(t, query, "WHERE UserId = $4 AND OrganizationId = $5")

					// Verify the arguments
					assert.Len(t, args, 5)
					assert.Equal(t, 5, args[0])                                                      // StatAPSResetCount
					assert.Equal(t, 3, args[1])                                                      // StatAPSResetSuccessCount
					assert.Equal(t, uuid.MustParse("*************-4321-4321-cba987654321"), args[3]) // UserID
					assert.Equal(t, uuid.MustParse("11111111-1111-1111-1111-111111111111"), args[4]) // OrganizationID

					return mockResult, nil
				}
			},
		},
		{
			name: "Success - update statistic with empty success list",
			statistic: &apsReset.APSResetStatistic{
				ID:                       uuid.MustParse("*************-2222-2222-************"),
				UserID:                   uuid.MustParse("*************-3333-3333-************"),
				OrganizationID:           uuid.MustParse("*************-4444-4444-************"),
				StatAPSResetCount:        0,
				StatAPSResetSuccessCount: 0,
				StatAPSResetSuccessList:  []time.Time{},
			},
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockResult := &mockSQLResult{}
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)

				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					// Verify the arguments
					assert.Len(t, args, 5)
					assert.Equal(t, 0, args[0])                                                      // StatAPSResetCount
					assert.Equal(t, 0, args[1])                                                      // StatAPSResetSuccessCount
					assert.Equal(t, uuid.MustParse("*************-3333-3333-************"), args[3]) // UserID
					assert.Equal(t, uuid.MustParse("*************-4444-4444-************"), args[4]) // OrganizationID

					return mockResult, nil
				}
			},
		},
		{
			name: "Error - database update failed",
			statistic: &apsReset.APSResetStatistic{
				ID:                       uuid.MustParse("*************-5555-5555-************"),
				UserID:                   uuid.MustParse("*************-6666-6666-************"),
				OrganizationID:           uuid.MustParse("*************-7777-7777-************"),
				StatAPSResetCount:        5,
				StatAPSResetSuccessCount: 3,
				StatAPSResetSuccessList:  []time.Time{time.Date(2025, 1, 1, 12, 0, 0, 0, time.UTC)},
			},
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("update failed")
				}
			},
			expectedError: "update failed",
		},
		{
			name: "Error - database connection failed",
			statistic: &apsReset.APSResetStatistic{
				ID:                       uuid.MustParse("*************-8888-8888-************"),
				UserID:                   uuid.MustParse("*************-9999-9999-************"),
				OrganizationID:           uuid.MustParse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"),
				StatAPSResetCount:        2,
				StatAPSResetSuccessCount: 1,
				StatAPSResetSuccessList:  []time.Time{},
			},
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("connection lost")
				}
			},
			expectedError: "connection lost",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			mockDB := &mocks.FakeDBExecutor{}
			tt.setupMock(mockDB)

			repo := &apsResetRepo{
				db: mockDB,
			}

			// Act
			err := repo.UpdateStatistic(tt.statistic)

			// Assert
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err.Error())
			} else {
				assert.NoError(t, err)
			}

			// Verify that Exec was called exactly once
			assert.Equal(t, 1, mockDB.ExecCallCount)
		})
	}
}

func TestApsResetRepo_CreateConfig(t *testing.T) {
	tests := []struct {
		name          string
		userID        uuid.UUID
		orgID         uuid.UUID
		setupMock     func(*mocks.FakeDBExecutor)
		expectedError string
		expectConfig  bool
	}{
		{
			name:   "Success - create config with valid UUIDs",
			userID: uuid.MustParse("12345678-1234-1234-1234-123456789abc"),
			orgID:  uuid.MustParse("*************-4321-4321-cba987654321"),
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Verify the query contains expected elements
					assert.Contains(t, query, "INSERT INTO {{APSResetConfig}}")
					assert.Contains(t, query, "UserId")
					assert.Contains(t, query, "OrganizationId")
					assert.Contains(t, query, "VALUES")
					assert.Contains(t, query, "RETURNING")

					// Verify the arguments
					assert.Len(t, args, 2)
					assert.Equal(t, uuid.MustParse("12345678-1234-1234-1234-123456789abc"), args[0])
					assert.Equal(t, uuid.MustParse("*************-4321-4321-cba987654321"), args[1])

					// Set the destination struct with test data
					dbConfig := dest.(*dbAPSResetConfig)
					dbConfig.ID = uuid.MustParse("11111111-1111-1111-1111-111111111111")
					dbConfig.UserID = uuid.MustParse("12345678-1234-1234-1234-123456789abc")
					dbConfig.OrganizationID = uuid.MustParse("*************-4321-4321-cba987654321")
					dbConfig.ConfigAPSResetSuccessLimit = 1000
					dbConfig.ConfigAPSResetSuccessListLength = 10
					dbConfig.ConfigAPSResetSuccessListWindowMinutes = 60

					return nil
				}
			},
			expectConfig: true,
		},
		{
			name:   "Success - create config with nil UUIDs",
			userID: uuid.Nil,
			orgID:  uuid.Nil,
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Set the destination struct with test data
					dbConfig := dest.(*dbAPSResetConfig)
					dbConfig.ID = uuid.Nil
					dbConfig.UserID = uuid.Nil
					dbConfig.OrganizationID = uuid.Nil
					dbConfig.ConfigAPSResetSuccessLimit = 0
					dbConfig.ConfigAPSResetSuccessListLength = 0
					dbConfig.ConfigAPSResetSuccessListWindowMinutes = 0

					return nil
				}
			},
			expectConfig: true,
		},
		{
			name:   "Error - database insert failed",
			userID: uuid.MustParse("*************-2222-2222-************"),
			orgID:  uuid.MustParse("*************-3333-3333-************"),
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("insert failed")
				}
			},
			expectedError: "insert failed",
		},
		{
			name:   "Error - database connection failed",
			userID: uuid.MustParse("*************-4444-4444-************"),
			orgID:  uuid.MustParse("*************-5555-5555-************"),
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("connection lost")
				}
			},
			expectedError: "connection lost",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			mockDB := &mocks.FakeDBExecutor{}
			tt.setupMock(mockDB)

			repo := &apsResetRepo{
				db: mockDB,
			}

			// Act
			config, err := repo.CreateConfig(tt.userID, tt.orgID)

			// Assert
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err.Error())
				assert.Nil(t, config)
			} else {
				assert.NoError(t, err)
				if tt.expectConfig {
					assert.NotNil(t, config)
					if tt.userID == uuid.Nil {
						assert.Equal(t, uuid.Nil, config.ID)
						assert.Equal(t, uuid.Nil, config.UserID)
						assert.Equal(t, uuid.Nil, config.OrganizationID)
					} else {
						assert.NotEqual(t, uuid.Nil, config.ID)
						assert.NotEqual(t, uuid.Nil, config.UserID)
						assert.NotEqual(t, uuid.Nil, config.OrganizationID)
					}
				} else {
					assert.Nil(t, config)
				}
			}

			// Verify that QueryRowStruct was called exactly once
			assert.Equal(t, 1, mockDB.QueryRowStructCallCount)
		})
	}
}

func TestApsResetRepo_CreateStatistic(t *testing.T) {
	tests := []struct {
		name            string
		userID          uuid.UUID
		orgID           uuid.UUID
		setupMock       func(*mocks.FakeDBExecutor)
		expectedError   string
		expectStatistic bool
	}{
		{
			name:   "Success - create statistic with valid UUIDs",
			userID: uuid.MustParse("12345678-1234-1234-1234-123456789abc"),
			orgID:  uuid.MustParse("*************-4321-4321-cba987654321"),
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Verify the query contains expected elements
					assert.Contains(t, query, "INSERT INTO {{APSResetStatistic}}")
					assert.Contains(t, query, "UserId")
					assert.Contains(t, query, "OrganizationId")
					assert.Contains(t, query, "VALUES")
					assert.Contains(t, query, "RETURNING")

					// Verify the arguments
					assert.Len(t, args, 2)
					assert.Equal(t, uuid.MustParse("12345678-1234-1234-1234-123456789abc"), args[0])
					assert.Equal(t, uuid.MustParse("*************-4321-4321-cba987654321"), args[1])

					// Set the destination struct with test data
					dbStat := dest.(*dbAPSResetStatistic)
					dbStat.ID = uuid.MustParse("11111111-1111-1111-1111-111111111111")
					dbStat.UserID = uuid.MustParse("12345678-1234-1234-1234-123456789abc")
					dbStat.OrganizationID = uuid.MustParse("*************-4321-4321-cba987654321")
					dbStat.StatAPSResetCount = 0
					dbStat.StatAPSResetSuccessCount = 0
					dbStat.StatAPSResetSuccessList = []string{}

					return nil
				}
			},
			expectStatistic: true,
		},
		{
			name:   "Success - create statistic with nil UUIDs",
			userID: uuid.Nil,
			orgID:  uuid.Nil,
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Set the destination struct with test data
					dbStat := dest.(*dbAPSResetStatistic)
					dbStat.ID = uuid.Nil
					dbStat.UserID = uuid.Nil
					dbStat.OrganizationID = uuid.Nil
					dbStat.StatAPSResetCount = 0
					dbStat.StatAPSResetSuccessCount = 0
					dbStat.StatAPSResetSuccessList = []string{}

					return nil
				}
			},
			expectStatistic: true,
		},
		{
			name:   "Error - database insert failed",
			userID: uuid.MustParse("*************-2222-2222-************"),
			orgID:  uuid.MustParse("*************-3333-3333-************"),
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("insert failed")
				}
			},
			expectedError: "insert failed",
		},
		{
			name:   "Error - database connection failed",
			userID: uuid.MustParse("*************-4444-4444-************"),
			orgID:  uuid.MustParse("*************-5555-5555-************"),
			setupMock: func(mockDB *mocks.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("connection lost")
				}
			},
			expectedError: "connection lost",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			mockDB := &mocks.FakeDBExecutor{}
			tt.setupMock(mockDB)

			repo := &apsResetRepo{
				db: mockDB,
			}

			// Act
			statistic, err := repo.CreateStatistic(tt.userID, tt.orgID)

			// Assert
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err.Error())
				assert.Nil(t, statistic)
			} else {
				assert.NoError(t, err)
				if tt.expectStatistic {
					assert.NotNil(t, statistic)
					if tt.userID == uuid.Nil {
						assert.Equal(t, uuid.Nil, statistic.ID)
						assert.Equal(t, uuid.Nil, statistic.UserID)
						assert.Equal(t, uuid.Nil, statistic.OrganizationID)
					} else {
						assert.NotEqual(t, uuid.Nil, statistic.ID)
						assert.NotEqual(t, uuid.Nil, statistic.UserID)
						assert.NotEqual(t, uuid.Nil, statistic.OrganizationID)
					}
				} else {
					assert.Nil(t, statistic)
				}
			}

			// Verify that QueryRowStruct was called exactly once
			assert.Equal(t, 1, mockDB.QueryRowStructCallCount)
		})
	}
}

func TestApsResetRepo_InterfaceCompliance(t *testing.T) {
	t.Parallel()

	// This test ensures that apsResetRepo implements the expected interface
	// The interface is defined in the domain layer as:
	// type Repository interface {
	//     FindConfigByUserIDAndOrganizationID(userID, orgID uuid.UUID) (*APSResetConfig, error)
	//     FindStatisticByUserIDAndOrganizationID(userID, orgID uuid.UUID) (*APSResetStatistic, error)
	//     UpdateStatistic(statistic *APSResetStatistic) error
	// }

	// Create a mock that satisfies the interface
	var repo interface {
		FindConfigByUserIDAndOrganizationID(userID, orgID uuid.UUID) (*apsReset.APSResetConfig, error)
		FindStatisticByUserIDAndOrganizationID(userID, orgID uuid.UUID) (*apsReset.APSResetStatistic, error)
		UpdateStatistic(statistic *apsReset.APSResetStatistic) error
	} = &apsResetRepo{}

	// Test that the interface methods can be called
	userID := uuid.MustParse("12345678-1234-1234-1234-123456789abc")
	orgID := uuid.MustParse("*************-4321-4321-cba987654321")

	// This should compile and run without errors
	assert.NotNil(t, repo)

	// Test that the method signatures are correct
	// This is a compile-time check, but we can also verify it works at runtime
	mockDB := &mocks.FakeDBExecutor{}

	// Cast to concrete type to set the mock
	concreteRepo := repo.(*apsResetRepo)
	concreteRepo.db = mockDB

	// Test the method calls (they will return structs with zero values, but we're testing interface compliance)
	config, err := repo.FindConfigByUserIDAndOrganizationID(userID, orgID)
	assert.NotNil(t, config)             // FakeDBExecutor creates struct with zero values
	assert.NoError(t, err)               // FakeDBExecutor returns nil error by default
	assert.Equal(t, uuid.Nil, config.ID) // Zero value UUID

	statistic, err := repo.FindStatisticByUserIDAndOrganizationID(userID, orgID)
	assert.NotNil(t, statistic)             // FakeDBExecutor creates struct with zero values
	assert.NoError(t, err)                  // FakeDBExecutor returns nil error by default
	assert.Equal(t, uuid.Nil, statistic.ID) // Zero value UUID

	updateStatistic := &apsReset.APSResetStatistic{
		UserID:         userID,
		OrganizationID: orgID,
	}
	err = repo.UpdateStatistic(updateStatistic)
	assert.NoError(t, err) // FakeDBExecutor returns nil error by default
}
