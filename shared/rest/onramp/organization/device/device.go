package device

import (
	"context"
	"encoding/json"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/rest/onramp/helper"
)

// HandlerDeps is a struct that contains the dependencies for the device handler
type HandlerDeps struct {
	GetDeviceByOrganizationID                    func(connect.DatabaseExecutor, uuid.UUID) (*DevicesResponse, error)
	GetConnections                               func(context.Context, ...bool) (*connect.Connections, error)
	CreateDevice                                 func(connect.DatabaseExecutor, uuid.UUID, *UpdateRequest) (*DeviceResponse, error)
	UpdateDevice                                 func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, *UpdateRequest) (*DeviceResponse, error)
	DeleteDevice                                 func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
	ValidateSoftwareGateWayBelongsToOrganization func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
	ValidateLocationBelongsToOrganization        func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
}

// Handles the GET /device request
func GetDeviceByOrganizationIDWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate organization ID from path
		orgId, err := helper.ValidateOrganizationID(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get devices for organization
		devices, err := deps.GetDeviceByOrganizationID(pg, orgId)
		if err != nil {
			logger.Errorf("failed to get devices for organization %s: %v", orgId, err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return success response
		response.CreateSuccessResponse(devices, w)
	}
}

// Create a new device
func CreateDeviceWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate organization ID from path
		orgId, err := helper.ValidateOrganizationID(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get and validate the device request body
		device, err := parseDeviceRequest(r, false)
		if err != nil {
			logger.Errorf("invalid request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate SoftwareGatewayId belongs to the organization
		if device.(*UpdateRequest).SoftwareGatewayId != nil {
			err := deps.ValidateSoftwareGateWayBelongsToOrganization(pg, orgId, *device.(*UpdateRequest).SoftwareGatewayId)
			if err != nil {
				logger.Errorf("failed to get software gateway: %v", err)
				response.CreateBadRequestResponse(w)
				return
			}
		}

		// Validate LocationId belongs to the organization
		if device.(*UpdateRequest).LocationId != nil {
			err := deps.ValidateLocationBelongsToOrganization(pg, orgId, *device.(*UpdateRequest).LocationId)
			if err != nil {
				logger.Errorf("failed to get location: %v", err)
				response.CreateBadRequestResponse(w)
				return
			}
		}

		// Create the device
		createdDevice, err := deps.CreateDevice(pg, orgId, device.(*UpdateRequest))
		if err != nil {
			logger.Errorf("failed to create device: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return success response
		response.CreateSuccessResponse(createdDevice, w)
	}
}

// Update a device
func UpdateDeviceWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate organization ID from path
		orgId, err := helper.ValidateOrganizationID(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate device ID from path
		deviceId, err := helper.ValidateDeviceID(r)
		if err != nil {
			logger.Errorf("invalid device ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get and validate the device request body
		device, err := parseDeviceRequest(r, true)
		if err != nil {
			logger.Errorf("invalid request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate SoftwareGatewayId belongs to the organization
		if device.(*UpdateRequest).SoftwareGatewayId != nil {
			err := deps.ValidateSoftwareGateWayBelongsToOrganization(pg, orgId, *device.(*UpdateRequest).SoftwareGatewayId)
			if err != nil {
				logger.Errorf("failed to get software gateway: %v", err)
				response.CreateBadRequestResponse(w)
				return
			}
		}

		// Validate LocationId belongs to the organization
		if device.(*UpdateRequest).LocationId != nil {
			err := deps.ValidateLocationBelongsToOrganization(pg, orgId, *device.(*UpdateRequest).LocationId)
			if err != nil {
				logger.Errorf("failed to get location: %v", err)
				response.CreateBadRequestResponse(w)
				return
			}
		}

		// Update the device
		updatedDevice, err := deps.UpdateDevice(pg, orgId, deviceId, device.(*UpdateRequest))
		if err != nil {
			logger.Errorf("failed to update device: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return success response
		response.CreateSuccessResponse(updatedDevice, w)
	}
}

// Delete a device
func DeleteDeviceWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate organization ID from path
		orgId, err := helper.ValidateOrganizationID(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate device ID from path
		deviceId, err := helper.ValidateDeviceID(r)
		if err != nil {
			logger.Errorf("invalid device ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Delete the device
		err = deps.DeleteDevice(pg, orgId, deviceId)
		if err != nil {
			logger.Errorf("failed to delete device: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return success response
		response.CreateSuccessResponse(nil, w)
	}
}

// parse the create and update request body
func parseDeviceRequest(r *http.Request, isUpdate bool) (interface{}, error) {
	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields() // Reject unexpected fields

	// Helper function to trim fields and validate if not empty
	trimField := func(value *string) {
		if value != nil {
			trimmed := strings.TrimSpace(*value)
			*value = trimmed
		}
	}

	// Helper function to validate type value
	validateTypeValue := func(typeValue string) error {
		if typeValue != "EDI_LEGACY" && typeValue != "EDI_NEXT_GEN" {
			return ErrInvalidDeviceTypeValue
		}
		return nil
	}

	var req UpdateRequest
	if err := decoder.Decode(&req); err != nil {
		logger.Infof("failed to parse request: %v", err)
		if strings.Contains(err.Error(), "unknown field") {
			return nil, ErrUnexpectedFields
		}
		return nil, ErrInvalidRequestBody
	}

	// Trim all fields first
	trimField(req.Name)
	trimField(req.Type)
	trimField(req.Description)
	trimField(req.IpAddress)

	// Validate Name and Type based on operation
	if !isUpdate {
		// Create: Name and Type cannot be nil or empty
		if req.Name == nil || *req.Name == "" {
			return nil, ErrInvalidDeviceName
		}
		if req.Type == nil || *req.Type == "" {
			return nil, ErrInvalidDeviceType
		}
	} else {
		// Update: Name and Type can be nil but if provided cannot be empty
		if req.Name != nil && *req.Name == "" {
			return nil, ErrInvalidDeviceName
		}
		if req.Type != nil && *req.Type == "" {
			return nil, ErrInvalidDeviceType
		}
	}

	// Validate Type value if provided
	if req.Type != nil {
		if err := validateTypeValue(*req.Type); err != nil {
			return nil, err
		}
	}

	return &req, nil
}

// Delete a device
func deleteDevice(pg connect.DatabaseExecutor, orgId uuid.UUID, deviceId uuid.UUID) error {
	// Get the current time in UTC
	now := time.Now().UTC()

	query := `
		UPDATE {{Device}}
		SET
			IsDeleted = true,
			UpdatedAt = $1
		WHERE 
			OrganizationId = $2 
			AND Id = $3
			AND IsDeleted = false
	`

	result, err := pg.Exec(query, now, orgId, deviceId)
	if err != nil {
		logger.Errorf("failed to delete device: %v", err)
		return ErrDatabaseOperation
	}

	if result != nil {
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			logger.Errorf("failed to get rows affected: %v", err)
			return ErrDatabaseOperation
		}
		if rowsAffected == 0 {
			logger.Errorf("device not found: %v", deviceId)
			return ErrDeviceNotFound
		}
	}
	return nil
}

// Update a device
func updateDevice(pg connect.DatabaseExecutor, orgId uuid.UUID, deviceId uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
	// Get the current time in UTC
	now := time.Now().UTC()

	// First, get the current device to preserve existing values
	currentDevice, err := getDeviceByID(pg, orgId, deviceId)
	if err != nil {
		logger.Errorf("failed to get current device: %v", err)
		return nil, ErrDeviceNotFound
	}

	// Build update values using current values as defaults
	updateValues := UpdateDeviceSchema{
		Name:              currentDevice.Name,
		Description:       currentDevice.Description,
		IpAddress:         currentDevice.IpAddress,
		Port:              currentDevice.Port,
		Type:              currentDevice.Type,
		FlushConnectionMs: currentDevice.FlushConnectionMs,
		EnableRealtime:    currentDevice.EnableRealtime,
		IsEnabled:         currentDevice.IsEnabled,
		LocationId:        currentDevice.LocationId,
		SoftwareGatewayId: currentDevice.SoftwareGatewayId,
	}

	// Override with provided values
	if req.Name != nil {
		updateValues.Name = *req.Name
	}
	if req.Description != nil {
		updateValues.Description = *req.Description
	}
	if req.IpAddress != nil {
		updateValues.IpAddress = *req.IpAddress
	}
	if req.Port != nil {
		updateValues.Port = *req.Port
	}
	if req.Type != nil {
		updateValues.Type = *req.Type
	}
	if req.FlushConnectionMs != nil {
		updateValues.FlushConnectionMs = *req.FlushConnectionMs
	}
	if req.EnableRealtime != nil {
		updateValues.EnableRealtime = *req.EnableRealtime
	}
	if req.IsEnabled != nil {
		updateValues.IsEnabled = *req.IsEnabled
	}
	if req.LocationId != nil {
		updateValues.LocationId = req.LocationId
	}
	if req.SoftwareGatewayId != nil {
		updateValues.SoftwareGatewayId = req.SoftwareGatewayId
	}

	query := `
		UPDATE {{Device}}
		SET
			Name = $1,
			Description = $2,
			IpAddress = $3,
			Port = $4,
			Type = $5,
			FlushConnectionMs = $6,
			EnableRealtime = $7,
			IsEnabled = $8,
			LocationId = $9,
			SoftwareGatewayId = $10,
			UpdatedAt = $11
		WHERE OrganizationId = $12 AND Id = $13
		RETURNING id, origid, softwaregatewayid, locationid, organizationid, name, description, ipaddress, port, type, flushconnectionms, enablerealtime, isenabled, updatedat
	`

	var device DeviceResponse
	err = pg.QueryRowStruct(&device, query,
		updateValues.Name,
		updateValues.Description,
		updateValues.IpAddress,
		updateValues.Port,
		updateValues.Type,
		updateValues.FlushConnectionMs,
		updateValues.EnableRealtime,
		updateValues.IsEnabled,
		updateValues.LocationId,
		updateValues.SoftwareGatewayId,
		now,
		orgId,
		deviceId,
	)
	if err != nil {
		logger.Errorf("failed to update device: %v", err)
		return nil, ErrDatabaseOperation
	}

	return &device, nil
}

// Create a new device
func createDevice(pg connect.DatabaseExecutor, orgId uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
	// Get the current time in UTC
	now := time.Now().UTC()

	// Set default values if not provided
	flushConnectionMs := 500
	if req.FlushConnectionMs != nil {
		flushConnectionMs = *req.FlushConnectionMs
	}

	enableRealtime := true
	if req.EnableRealtime != nil {
		enableRealtime = *req.EnableRealtime
	}

	isEnabled := true
	if req.IsEnabled != nil {
		isEnabled = *req.IsEnabled
	}

	query := `
		INSERT INTO {{Device}} (
			SoftwareGatewayId,
			LocationId,
			OrganizationId,
			Name,
			Description,
			IpAddress,
			Port,
			Type,
			FlushConnectionMs,
			EnableRealtime,
			IsEnabled,
			CreatedAt,
			UpdatedAt
		)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
		RETURNING id, origid, softwaregatewayid, locationid, organizationid, name, description, ipaddress, port, type, flushconnectionms, enablerealtime, isenabled, createdat, updatedat
	`

	// Dereference required fields (Name and Type are guaranteed to be non-nil)
	name := *req.Name
	deviceType := *req.Type

	// Handle optional fields
	var description, ipAddress interface{}
	var port interface{}

	if req.Description != nil {
		description = *req.Description
	}
	if req.IpAddress != nil {
		ipAddress = *req.IpAddress
	}
	if req.Port != nil {
		port = *req.Port
	}

	var device DeviceResponse
	err := pg.QueryRowStruct(&device, query, req.SoftwareGatewayId, req.LocationId, orgId, name, description, ipAddress, port, deviceType, flushConnectionMs, enableRealtime, isEnabled, now, now)
	if err != nil {
		logger.Errorf("failed to create device: %v", err)
		return nil, ErrDatabaseOperation
	}

	return &device, nil
}

// Get all devices by organization id
func getDeviceByOrganizationID(pg connect.DatabaseExecutor, orgId uuid.UUID) (*DevicesResponse, error) {
	query := `
		SELECT
			id,
			origid,
			softwaregatewayid, 
			locationid,
			organizationid, 
			name,
			description,
			ipaddress,
			port,
			type,
			flushconnectionms,
			enablerealtime,
			isenabled,
			createdat,
			updatedat
		FROM {{Device}}
		WHERE organizationid = $1
		AND isdeleted = false
		ORDER BY name ASC
	`

	var devices DevicesResponse
	err := pg.QueryGenericSlice(&devices.Devices, query, orgId)
	if err != nil {
		logger.Errorf("failed to query devices: %v", err)
		return nil, ErrDatabaseOperation
	}

	return &devices, nil
}

// Get a device by ID
func getDeviceByID(pg connect.DatabaseExecutor, orgId uuid.UUID, deviceId uuid.UUID) (*DeviceResponse, error) {
	query := `
		SELECT
			id,
			origid,
			softwaregatewayid, 
			locationid,
			organizationid, 
			name,
			description,
			ipaddress,
			port,
			type,
			flushconnectionms,
			enablerealtime,
			isenabled,
			createdat,
			updatedat
		FROM {{Device}}
		WHERE organizationid = $1 AND id = $2 AND isdeleted = false
	`

	var device DeviceResponse
	err := pg.QueryRowStruct(&device, query, orgId, deviceId)
	if err != nil {
		logger.Errorf("failed to query device: %v", err)
		return nil, ErrDatabaseOperation
	}

	return &device, nil
}

var (
	GetDeviceByOrganizationIDHandler = GetDeviceByOrganizationIDWithDeps(HandlerDeps{
		GetConnections:            connect.GetConnections,
		GetDeviceByOrganizationID: getDeviceByOrganizationID,
	})

	CreateDeviceHandler = CreateDeviceWithDeps(HandlerDeps{
		GetConnections: connect.GetConnections,
		CreateDevice:   createDevice,
		ValidateSoftwareGateWayBelongsToOrganization: helper.ValidateSoftwareGateWayBelongsToOrganization,
		ValidateLocationBelongsToOrganization:        helper.ValidateLocationBelongsToOrganization,
	})

	UpdateDeviceHandler = UpdateDeviceWithDeps(HandlerDeps{
		GetConnections: connect.GetConnections,
		UpdateDevice:   updateDevice,
		ValidateSoftwareGateWayBelongsToOrganization: helper.ValidateSoftwareGateWayBelongsToOrganization,
		ValidateLocationBelongsToOrganization:        helper.ValidateLocationBelongsToOrganization,
	})

	DeleteDeviceHandler = DeleteDeviceWithDeps(HandlerDeps{
		GetConnections: connect.GetConnections,
		DeleteDevice:   deleteDevice,
	})
)
