package permissions

import (
	"context"
	"net/http"

	"synapse-its.com/onramp/domain"
	"synapse-its.com/onramp/middlewares"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/logger"
)

// HandlerDeps bundles dependencies for injection and testing.
type HandlerDeps struct {
	UserInfoFromSessionContext     func(ctx context.Context) (*domain.UserPermissions, bool)
	TransformPermissionsToResponse func(userPermissions *domain.UserPermissions) (*UserPermissionsResponse, error)
}

// HandlerWithDeps returns an http.HandlerFunc with injected deps.
func HandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get user info from session context (set by session middleware)
		userPermissions, ok := deps.UserInfoFromSessionContext(ctx)
		if !ok {
			logger.Error(ErrUserInfoRetrieve.Error())
			response.CreateUnauthorizedResponse(w)
			return
		}

		// Transform permissions from session to API response format
		permissions, err := deps.TransformPermissionsToResponse(userPermissions)
		if err != nil {
			logger.Errorf("Error transforming user permissions: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(permissions, w)
	}
}

// UserInfoFromSessionContext extracts user information from session context
func UserInfoFromSessionContext(ctx context.Context) (*domain.UserPermissions, bool) {
	// Get session data from context
	sessionDataInterface := ctx.Value(middlewares.SessionContextKey)
	if sessionDataInterface == nil {
		return nil, false
	}

	// Type assert to session.SessionData
	sessionData, ok := sessionDataInterface.(*domain.Session)
	if !ok {
		return nil, false
	}

	// If we have UserPermissions in the session, use them
	if sessionData.UserPermissions != nil {
		return sessionData.UserPermissions, true
	}

	// Otherwise, create a basic UserPermissions with just the UserID
	return &domain.UserPermissions{
		UserID:      sessionData.UserID,
		Permissions: domain.DefaultPermissions,
	}, true
}

// transformPermissionsToResponse transforms user permissions from session to API response format
func transformPermissionsToResponse(userPermissions *domain.UserPermissions) (*UserPermissionsResponse, error) {
	// Transform to API response format
	response := &UserPermissionsResponse{
		UserID:      userPermissions.UserID,
		Permissions: make([]PermissionScope, len(userPermissions.Permissions)),
	}

	for i, perm := range userPermissions.Permissions {
		var scopeID *string
		var orgID *string

		// Handle scope ID (null for global scope)
		if perm.ScopeID != "" {
			scopeID = &perm.ScopeID
		}

		// Handle organization ID (null for global scope)
		if perm.OrganizationID != "" {
			orgID = &perm.OrganizationID
		}

		response.Permissions[i] = PermissionScope{
			Scope:          perm.Scope,
			ScopeID:        scopeID,
			OrganizationID: orgID,
			Permissions:    perm.Permissions,
		}
	}

	return response, nil
}

// Handler is the production-ready HTTP handler using default dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	UserInfoFromSessionContext:     UserInfoFromSessionContext,
	TransformPermissionsToResponse: transformPermissionsToResponse,
})
