package locations

import "errors"

var (
	ErrDatabaseOperation    = errors.New("database operation failed")
	ErrUnexpectedFields     = errors.New("request contains unexpected fields")
	ErrInvalidRequestBody   = errors.New("invalid request body")
	ErrInvalidLocationName  = errors.New("location name cannot be empty")
	ErrInvalidLocationDesc  = errors.New("location description cannot be empty")
	ErrInvalidLatitude      = errors.New("invalid latitude value")
	ErrInvalidLongitude     = errors.New("invalid longitude value")
	ErrOrganizationNotFound = errors.New("organization not found")
	ErrLocationNotFound     = errors.New("location not found")
)
