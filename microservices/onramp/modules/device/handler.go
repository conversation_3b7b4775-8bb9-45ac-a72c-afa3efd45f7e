package device

import (
	"net/http"

	"github.com/gorilla/mux"
	RestDevice "synapse-its.com/shared/rest/onramp/organization/device"
)

type Handler struct{}

func NewHandler() *Handler {
	return &Handler{}
}

func (h *Handler) RegisterRoutes(router *mux.Router) {
	// Device Management
	router.HandleFunc("/organizations/{organizationId}/device", RestDevice.CreateDeviceHandler).Methods(http.MethodPost)
	router.HandleFunc("/organizations/{organizationId}/device", RestDevice.GetDeviceByOrganizationIDHandler).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{organizationId}/device/{deviceId}", RestDevice.UpdateDeviceHandler).Methods(http.MethodPatch)
	router.HandleFunc("/organizations/{organizationId}/device/{deviceId}", RestDevice.DeleteDeviceHandler).Methods(http.MethodDelete)
}
