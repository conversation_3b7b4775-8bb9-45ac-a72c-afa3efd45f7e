package socketio

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"

	"bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/wrappers"
	"synapse-its.com/rushhour/domain"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/mocks"
)

func TestNewBinaryMessageHandler(t *testing.T) {
	handler := NewBinaryMessageHandler()

	assert.NotNil(t, handler)
	assert.NotNil(t, handler.protoRegistry)
	assert.Greater(t, len(handler.protoRegistry), 0, "Should have registered proto types")
}

func TestBinaryMessageHandler_ValidateEnvelope(t *testing.T) {
	tests := []struct {
		name          string
		envelope      *domain.SocketEnvelope
		expectError   bool
		errorContains string
	}{
		{
			name:          "nil envelope",
			envelope:      nil,
			expectError:   true,
			errorContains: "envelope cannot be nil",
		},
		{
			name: "valid wrapper command envelope",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				RequestId:      12345,
				DeviceId:       "device123",
				OrganizationId: "org456",
				UserId:         "user789",
				Origin:         domain.OriginTypeFSA,
				Payload:        []byte("test payload"),
			},
			expectError: false,
		},
		{
			name: "missing device ID",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				RequestId:      12345,
				DeviceId:       "", // Missing
				OrganizationId: "org456",
				UserId:         "user789",
				Origin:         domain.OriginTypeFSA,
				Payload:        []byte("test payload"),
			},
			expectError:   true,
			errorContains: "device_id is required",
		},
		{
			name: "missing organization ID",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				RequestId:      12345,
				DeviceId:       "device123",
				OrganizationId: "", // Missing
				UserId:         "user789",
				Origin:         domain.OriginTypeFSA,
				Payload:        []byte("test payload"),
			},
			expectError:   true,
			errorContains: "organization_id is required",
		},
		{
			name: "missing user ID for command",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				RequestId:      12345,
				DeviceId:       "device123",
				OrganizationId: "org456",
				UserId:         "", // Missing for command - ValidateEnvelope doesn't check this
				Origin:         domain.OriginTypeFSA,
				Payload:        []byte("test payload"),
			},
			expectError:   false, // ValidateEnvelope doesn't validate user_id
			errorContains: "",
		},
		{
			name: "valid wrapper response envelope",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperResponse,
				RequestId:      12345,
				DeviceId:       "device123",
				OrganizationId: "org456",
				Origin:         domain.OriginTypeGateway,
				Payload:        []byte("test payload"),
			},
			expectError: false,
		},
		{
			name: "empty payload",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				RequestId:      12345,
				DeviceId:       "device123",
				OrganizationId: "org456",
				UserId:         "user789",
				Origin:         domain.OriginTypeFSA,
				Payload:        nil, // Empty payload - ValidateEnvelope doesn't check this
			},
			expectError:   false, // ValidateEnvelope doesn't validate payload
			errorContains: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := NewBinaryMessageHandler()
			err := handler.ValidateEnvelope(tt.envelope)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestBinaryMessageHandler_ExtractRequestID(t *testing.T) {
	tests := []struct {
		name       string
		envelope   *domain.SocketEnvelope
		expectedID uint32
	}{
		{
			name: "valid request ID",
			envelope: &domain.SocketEnvelope{
				RequestId: 12345,
			},
			expectedID: 12345,
		},
		{
			name: "zero request ID",
			envelope: &domain.SocketEnvelope{
				RequestId: 0,
			},
			expectedID: 0,
		},
		{
			name:       "nil envelope",
			envelope:   nil,
			expectedID: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := NewBinaryMessageHandler()
			result := handler.ExtractRequestID(tt.envelope)
			assert.Equal(t, tt.expectedID, result)
		})
	}
}

func TestBinaryMessageHandler_ExtractDeviceID(t *testing.T) {
	tests := []struct {
		name       string
		envelope   *domain.SocketEnvelope
		expectedID string
	}{
		{
			name: "valid device ID",
			envelope: &domain.SocketEnvelope{
				DeviceId: "device123",
			},
			expectedID: "device123",
		},
		{
			name: "empty device ID",
			envelope: &domain.SocketEnvelope{
				DeviceId: "",
			},
			expectedID: "",
		},
		{
			name:       "nil envelope",
			envelope:   nil,
			expectedID: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := NewBinaryMessageHandler()
			result := handler.ExtractDeviceID(tt.envelope)
			assert.Equal(t, tt.expectedID, result)
		})
	}
}

func TestBinaryMessageHandler_ExtractOrganizationID(t *testing.T) {
	tests := []struct {
		name       string
		envelope   *domain.SocketEnvelope
		expectedID string
	}{
		{
			name: "valid organization ID",
			envelope: &domain.SocketEnvelope{
				OrganizationId: "org456",
			},
			expectedID: "org456",
		},
		{
			name: "empty organization ID",
			envelope: &domain.SocketEnvelope{
				OrganizationId: "",
			},
			expectedID: "",
		},
		{
			name:       "nil envelope",
			envelope:   nil,
			expectedID: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := NewBinaryMessageHandler()
			result := handler.ExtractOrganizationID(tt.envelope)
			assert.Equal(t, tt.expectedID, result)
		})
	}
}

func TestBinaryMessageHandler_ExtractSessionID(t *testing.T) {
	tests := []struct {
		name       string
		envelope   *domain.SocketEnvelope
		expectedID string
	}{
		{
			name: "valid session ID",
			envelope: &domain.SocketEnvelope{
				SessionId: "session789",
			},
			expectedID: "session789",
		},
		{
			name: "empty session ID",
			envelope: &domain.SocketEnvelope{
				SessionId: "",
			},
			expectedID: "",
		},
		{
			name:       "nil envelope",
			envelope:   nil,
			expectedID: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := NewBinaryMessageHandler()
			result := handler.ExtractSessionID(tt.envelope)
			assert.Equal(t, tt.expectedID, result)
		})
	}
}

func TestBinaryMessageHandler_CreateBinaryMessage(t *testing.T) {
	tests := []struct {
		name          string
		envelope      *domain.SocketEnvelope
		expectError   bool
		errorContains string
	}{
		{
			name:          "nil envelope",
			envelope:      nil,
			expectError:   true,
			errorContains: "envelope cannot be nil",
		},
		{
			name: "valid envelope",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				RequestId:      12345,
				DeviceId:       "device123",
				OrganizationId: "org456",
				UserId:         "user789",
				Origin:         domain.OriginTypeFSA,
				Payload:        []byte("test payload"),
			},
			expectError: false,
		},
		{
			name: "envelope with missing device ID - still creates binary",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				RequestId:      12345,
				DeviceId:       "", // Missing - but CreateBinaryMessage doesn't validate this
				OrganizationId: "org456",
				UserId:         "user789",
				Origin:         domain.OriginTypeFSA,
				Payload:        []byte("test payload"),
			},
			expectError:   false, // CreateBinaryMessage doesn't validate envelope content
			errorContains: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := NewBinaryMessageHandler()
			result, err := handler.CreateBinaryMessage(tt.envelope)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Greater(t, len(result), 0)

				// Verify we can unmarshal the result back
				var envelope domain.SocketEnvelope
				err = proto.Unmarshal(result, &envelope)
				assert.NoError(t, err)
				assert.Equal(t, tt.envelope.Type, envelope.Type)
				assert.Equal(t, tt.envelope.RequestId, envelope.RequestId)
				assert.Equal(t, tt.envelope.DeviceId, envelope.DeviceId)
			}
		})
	}
}

func TestBinaryMessageHandler_ProcessBinaryMessage(t *testing.T) {
	tests := []struct {
		name            string
		data            []byte
		userPermissions *authorizer.UserPermissions
		orgID           string
		expectError     bool
		errorContains   string
	}{
		{
			name:          "empty data",
			data:          nil,
			expectError:   true,
			errorContains: "command payload cannot be empty", // ProcessBinaryMessage checks data length first
		},
		{
			name:          "invalid protobuf data",
			data:          []byte("invalid protobuf"),
			expectError:   true,
			errorContains: "failed to unmarshal",
		},
		{
			name: "organization mismatch returns device access denied",
			data: func() []byte {
				envelope := &domain.SocketEnvelope{
					Type:           domain.EnvelopeTypeWrapperCommand,
					RequestId:      12345,
					DeviceId:       "device123",
					OrganizationId: "org999", // Does not match provided orgID below
					UserId:         "user789",
					Origin:         domain.OriginTypeFSA,
					Payload:        []byte("any payload"),
				}
				data, _ := proto.Marshal(envelope)
				return data
			}(),
			userPermissions: nil,
			orgID:           "org456",
			expectError:     true,
			errorContains:   "device access denied",
		},
		{
			name: "valid protobuf data with nil permissions",
			data: func() []byte {
				envelope := &domain.SocketEnvelope{
					Type:           domain.EnvelopeTypeWrapperCommand,
					RequestId:      12345,
					DeviceId:       "device123",
					OrganizationId: "org456",
					UserId:         "user789",
					Origin:         domain.OriginTypeFSA,
					Payload:        []byte("test payload"),
				}
				data, _ := proto.Marshal(envelope)
				return data
			}(),
			userPermissions: nil,
			orgID:           "org456",
			expectError:     true, // validateDeviceCommand will fail with invalid inner payload
			errorContains:   "command not permitted",
		},
		{
			name: "wrapper response from gateway with validation error",
			data: func() []byte {
				envelope := &domain.SocketEnvelope{
					Type:           domain.EnvelopeTypeWrapperResponse,
					RequestId:      12345,
					DeviceId:       "device123",
					OrganizationId: "org456",
					Origin:         domain.OriginTypeGateway,
					Payload:        []byte{}, // Empty payload will cause validateDeviceResponse to fail
				}
				data, _ := proto.Marshal(envelope)
				return data
			}(),
			userPermissions: &authorizer.UserPermissions{
				UserID: "user123",
				Permissions: []authorizer.Permission{
					{
						OrganizationID: "org456",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			orgID:         "org456",
			expectError:   true,
			errorContains: "command not permitted", // This is the error returned when validateDeviceResponse fails
		},
		{
			name: "wrapper response from gateway with successful validation",
			data: func() []byte {
				envelope := &domain.SocketEnvelope{
					Type:           domain.EnvelopeTypeWrapperResponse,
					RequestId:      12345,
					DeviceId:       "device123",
					OrganizationId: "org456",
					Origin:         domain.OriginTypeGateway,
					Payload:        []byte("valid response payload"), // Valid payload will pass validateDeviceResponse
				}
				data, _ := proto.Marshal(envelope)
				return data
			}(),
			userPermissions: &authorizer.UserPermissions{
				UserID: "user123",
				Permissions: []authorizer.Permission{
					{
						OrganizationID: "org456",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			orgID:       "org456",
			expectError: false, // validateDeviceResponse will succeed
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := NewBinaryMessageHandler()
			// Create a mock database executor for testing
			mockDB := &mocks.FakeDBExecutor{}
			result, err := handler.ProcessBinaryMessage(tt.data, tt.userPermissions, mockDB, tt.orgID)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

func TestBinaryMessageHandler_ValidateBinaryMessage(t *testing.T) {
	tests := []struct {
		name              string
		data              []byte
		userPermissions   *authorizer.UserPermissions
		orgID             string
		setupFn           func() (*domain.SocketEnvelope, *mocks.FakeDBExecutor)
		expectedError     error
		expectedResultNil bool
		description       string
	}{
		{
			name:            "nil data",
			data:            nil,
			userPermissions: nil,
			orgID:           "org456",
			setupFn: func() (*domain.SocketEnvelope, *mocks.FakeDBExecutor) {
				return nil, &mocks.FakeDBExecutor{}
			},
			expectedError:     ErrCommandPayloadEmpty,
			expectedResultNil: true,
			description:       "should return error for empty data",
		},
		{
			name:            "empty data slice",
			data:            []byte{},
			userPermissions: nil,
			orgID:           "org456",
			setupFn: func() (*domain.SocketEnvelope, *mocks.FakeDBExecutor) {
				return nil, &mocks.FakeDBExecutor{}
			},
			expectedError:     ErrCommandPayloadEmpty,
			expectedResultNil: true,
			description:       "should return error for empty data slice",
		},
		{
			name:            "empty command type",
			data:            []byte{1, 2, 3}, // dummy data
			userPermissions: &authorizer.UserPermissions{UserID: "user123"},
			orgID:           "org456",
			setupFn: func() (*domain.SocketEnvelope, *mocks.FakeDBExecutor) {
				// Build a WrapperCommand with required fields but nil Command (oneof not set)
				wc := &wrappers.WrapperCommand{
					Version:   1,
					RequestId: 42,
					// Command: nil
				}
				wcBytes, _ := proto.Marshal(wc)

				// Embed it in the rushhour envelope as a wrapper command from FSA
				envelope := &domain.SocketEnvelope{
					Type:           domain.EnvelopeTypeWrapperCommand,
					DeviceId:       "device123",
					OrganizationId: "org456",
					Origin:         domain.OriginTypeFSA,
					Payload:        wcBytes,
				}

				return envelope, &mocks.FakeDBExecutor{}
			},
			expectedError:     ErrUnableToDetermineCommandType,
			expectedResultNil: false,
			description:       "should return ErrUnableToDetermineCommandType when command type cannot be determined",
		},
		{
			name: "permission check fails",
			data: []byte{1, 2, 3}, // dummy data
			userPermissions: &authorizer.UserPermissions{
				UserID: "user123",
				Permissions: []authorizer.Permission{
					{
						OrganizationID: "org456",
						Permissions:    []string{"some_other_permission"}, // Missing required permission
					},
				},
			},
			orgID: "org456",
			setupFn: func() (*domain.SocketEnvelope, *mocks.FakeDBExecutor) {
				// Build a WrapperCommand with a valid command type that will pass ExtractCommandType
				wc := &wrappers.WrapperCommand{
					Version:   1,
					RequestId: 42,
					Command:   &wrappers.WrapperCommand_RequestLogCounts{}, // This will return "request_log_counts"
				}
				wcBytes, _ := proto.Marshal(wc)

				// Embed it in the rushhour envelope as a wrapper command from FSA
				envelope := &domain.SocketEnvelope{
					Type:           domain.EnvelopeTypeWrapperCommand,
					DeviceId:       "device123",
					OrganizationId: "org456",
					Origin:         domain.OriginTypeFSA,
					Payload:        wcBytes,
				}

				return envelope, &mocks.FakeDBExecutor{}
			},
			expectedError:     ErrCommandNotPermitted,
			expectedResultNil: false,
			description:       "should return ErrCommandNotPermitted when permission check fails",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			handler := NewBinaryMessageHandler()
			envelope, mockDB := tt.setupFn()

			if envelope != nil {
				// Test validateDeviceCommand path
				err := handler.validateDeviceCommand(envelope, tt.userPermissions, mockDB)
				assert.ErrorIs(t, err, tt.expectedError, tt.description)
			} else {
				// Test ValidateBinaryMessage path for empty data cases
				result, err := handler.ValidateBinaryMessage(tt.data, tt.userPermissions, mockDB, tt.orgID)
				assert.Error(t, err, tt.description)
				assert.ErrorIs(t, err, tt.expectedError, tt.description)
				if tt.expectedResultNil {
					assert.Nil(t, result, "resulting envelope should be nil on error")
				}
			}
		})
	}
}

func TestBinaryMessageHandler_RelayCommand(t *testing.T) {
	tests := []struct {
		name                 string
		deviceID             string
		orgID                string
		userID               string
		requestID            uint32
		deviceCommandPayload []byte
		expectError          bool
		errorContains        string
	}{
		{
			name:                 "valid relay command",
			deviceID:             "device123",
			orgID:                "org456",
			userID:               "user789",
			requestID:            12345,
			deviceCommandPayload: []byte("command payload"),
			expectError:          false,
		},
		{
			name:                 "empty device ID - still creates envelope",
			deviceID:             "", // RelayCommand doesn't validate inputs
			orgID:                "org456",
			userID:               "user789",
			requestID:            12345,
			deviceCommandPayload: []byte("command payload"),
			expectError:          false, // RelayCommand doesn't validate, just creates envelope
		},
		{
			name:                 "empty org ID - still creates envelope",
			deviceID:             "device123",
			orgID:                "", // RelayCommand doesn't validate inputs
			userID:               "user789",
			requestID:            12345,
			deviceCommandPayload: []byte("command payload"),
			expectError:          false, // RelayCommand doesn't validate, just creates envelope
		},
		{
			name:                 "empty user ID - still creates envelope",
			deviceID:             "device123",
			orgID:                "org456",
			userID:               "", // RelayCommand doesn't validate inputs
			requestID:            12345,
			deviceCommandPayload: []byte("command payload"),
			expectError:          false, // RelayCommand doesn't validate, just creates envelope
		},
		{
			name:                 "empty payload - still creates envelope",
			deviceID:             "device123",
			orgID:                "org456",
			userID:               "user789",
			requestID:            12345,
			deviceCommandPayload: nil,   // RelayCommand doesn't validate inputs
			expectError:          false, // RelayCommand doesn't validate, just creates envelope
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := NewBinaryMessageHandler()
			result, err := handler.RelayCommand(tt.deviceID, tt.orgID, tt.userID, tt.requestID, tt.deviceCommandPayload)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Greater(t, len(result), 0)
			}
		})
	}
}

func TestBinaryMessageHandler_RelayResponse(t *testing.T) {
	tests := []struct {
		name                  string
		deviceID              string
		orgID                 string
		requestID             uint32
		deviceResponsePayload []byte
		expectError           bool
		errorContains         string
	}{
		{
			name:                  "valid relay response",
			deviceID:              "device123",
			orgID:                 "org456",
			requestID:             12345,
			deviceResponsePayload: []byte("response payload"),
			expectError:           false,
		},
		{
			name:                  "empty device ID - still creates envelope",
			deviceID:              "", // RelayResponse doesn't validate inputs
			orgID:                 "org456",
			requestID:             12345,
			deviceResponsePayload: []byte("response payload"),
			expectError:           false, // RelayResponse doesn't validate, just creates envelope
		},
		{
			name:                  "empty org ID - still creates envelope",
			deviceID:              "device123",
			orgID:                 "", // RelayResponse doesn't validate inputs
			requestID:             12345,
			deviceResponsePayload: []byte("response payload"),
			expectError:           false, // RelayResponse doesn't validate, just creates envelope
		},
		{
			name:                  "empty payload - still creates envelope",
			deviceID:              "device123",
			orgID:                 "org456",
			requestID:             12345,
			deviceResponsePayload: nil,   // RelayResponse doesn't validate inputs
			expectError:           false, // RelayResponse doesn't validate, just creates envelope
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := NewBinaryMessageHandler()
			result, err := handler.RelayResponse(tt.deviceID, tt.orgID, tt.requestID, tt.deviceResponsePayload)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Greater(t, len(result), 0)
			}
		})
	}
}

func TestBinaryMessageHandler_validateEnvelopePermissions(t *testing.T) {
	tests := []struct {
		name            string
		envelope        *domain.SocketEnvelope
		userPermissions *authorizer.UserPermissions
		orgID           string
		expectError     bool
		errorContains   string
	}{
		{
			name:          "nil envelope",
			envelope:      nil,
			expectError:   true,
			errorContains: "envelope cannot be nil",
		},
		{
			name: "nil user permissions with org mismatch",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				DeviceId:       "device123",
				OrganizationId: "org456",
			},
			userPermissions: nil,
			orgID:           "", // Empty orgID causes organization mismatch before nil check
			expectError:     true,
			errorContains:   "organization mismatch",
		},
		{
			name: "organization mismatch",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				DeviceId:       "device123",
				OrganizationId: "org456",
			},
			userPermissions: &authorizer.UserPermissions{
				UserID: "user123",
				Permissions: []authorizer.Permission{
					{
						OrganizationID: "org999",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			orgID:         "org999", // Different from envelope.OrganizationId (org456)
			expectError:   true,
			errorContains: "organization mismatch",
		},
		{
			name: "insufficient device permissions - TODO not implemented",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				DeviceId:       "device123",
				OrganizationId: "org456",
			},
			userPermissions: &authorizer.UserPermissions{
				UserID: "user123",
				Permissions: []authorizer.Permission{
					{
						OrganizationID: "org456",
						Permissions:    []string{"some_other_permission"}, // No device access
					},
				},
			},
			orgID:         "org456",
			expectError:   false, // Device permission validation is TODO - not implemented yet
			errorContains: "",
		},
		{
			name: "valid wrapper command permissions",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				DeviceId:       "device123",
				OrganizationId: "org456",
			},
			userPermissions: &authorizer.UserPermissions{
				UserID: "user123",
				Permissions: []authorizer.Permission{
					{
						OrganizationID: "org456",
						Permissions:    []string{"org_manage_devices"}, // Can send commands
					},
				},
			},
			orgID:       "org456",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := NewBinaryMessageHandler()
			err := handler.validateEnvelopePermissions(tt.envelope, tt.userPermissions, tt.orgID)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestBinaryMessageHandler_EdgeCases(t *testing.T) {
	handler := NewBinaryMessageHandler()

	t.Run("registry contains expected types", func(t *testing.T) {
		// Verify some expected types are registered
		assert.Contains(t, handler.protoRegistry, domain.EnvelopeTypeWrapperCommand)
		assert.Contains(t, handler.protoRegistry, domain.EnvelopeTypeWrapperResponse)
	})

	t.Run("extract methods handle nil gracefully", func(t *testing.T) {
		assert.Equal(t, uint32(0), handler.ExtractRequestID(nil))
		assert.Equal(t, "", handler.ExtractDeviceID(nil))
		assert.Equal(t, "", handler.ExtractSessionID(nil))
		assert.Equal(t, "", handler.ExtractOrganizationID(nil))
	})
}

func TestBinaryMessageHandler_validateDeviceResponse(t *testing.T) {
	tests := []struct {
		name            string
		envelope        *domain.SocketEnvelope
		userPermissions *authorizer.UserPermissions
		expectError     bool
		errorContains   string
	}{
		{
			name: "empty payload",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperResponse,
				DeviceId:       "device123",
				OrganizationId: "org456",
				Payload:        []byte{}, // Empty payload
			},
			userPermissions: nil,
			expectError:     true,
			errorContains:   "response payload cannot be empty",
		},
		{
			name: "nil payload",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperResponse,
				DeviceId:       "device123",
				OrganizationId: "org456",
				Payload:        nil, // Nil payload
			},
			userPermissions: nil,
			expectError:     true,
			errorContains:   "response payload cannot be empty",
		},
		{
			name: "valid payload - currently no validation implemented",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperResponse,
				DeviceId:       "device123",
				OrganizationId: "org456",
				Payload:        []byte("valid response payload"),
			},
			userPermissions: nil,
			expectError:     false, // Currently no validation beyond empty check
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := NewBinaryMessageHandler()
			err := handler.validateDeviceResponse(tt.envelope, tt.userPermissions)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestBinaryMessageHandler_ExtractInnerRequestID(t *testing.T) {
	tests := []struct {
		name          string
		envelope      *domain.SocketEnvelope
		expectError   bool
		errorContains string
		expectedID    uint32
	}{
		{
			name:          "nil envelope",
			envelope:      nil,
			expectError:   true,
			errorContains: "envelope or payload is empty",
		},
		{
			name: "empty payload",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				DeviceId:       "device123",
				OrganizationId: "org456",
				Payload:        []byte{}, // Empty payload
			},
			expectError:   true,
			errorContains: "envelope or payload is empty",
		},
		{
			name: "nil payload",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				DeviceId:       "device123",
				OrganizationId: "org456",
				Payload:        nil, // Nil payload
			},
			expectError:   true,
			errorContains: "envelope or payload is empty",
		},
		{
			name: "valid payload - currently not implemented",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				DeviceId:       "device123",
				OrganizationId: "org456",
				Payload:        []byte("valid command payload"),
			},
			expectError:   true,
			errorContains: "inner request ID extraction not implemented",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := NewBinaryMessageHandler()
			requestID, err := handler.ExtractInnerRequestID(tt.envelope)

			if tt.expectError {
				assert.Error(t, err)
				assert.Equal(t, uint32(0), requestID)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedID, requestID)
			}
		})
	}
}

func TestBinaryMessageHandler_registerProtoTypes(t *testing.T) {
	handler := NewBinaryMessageHandler()

	// Registry should already be populated by constructor
	assert.Len(t, handler.protoRegistry, 2)

	// Calling registerProtoTypes again should still work (idempotent)
	handler.registerProtoTypes()

	// Verify the registry is still populated correctly
	assert.Len(t, handler.protoRegistry, 2)

	// Test that the registered types can be created
	commandFactory, exists := handler.protoRegistry[domain.EnvelopeTypeWrapperCommand]
	assert.True(t, exists)
	assert.NotNil(t, commandFactory)

	responseFactory, exists := handler.protoRegistry[domain.EnvelopeTypeWrapperResponse]
	assert.True(t, exists)
	assert.NotNil(t, responseFactory)

	// Test that the factories create the correct type
	commandMsg := commandFactory()
	assert.NotNil(t, commandMsg)

	responseMsg := responseFactory()
	assert.NotNil(t, responseMsg)
}

func TestBinaryMessageHandler_ValidateEnvelope_AdditionalCases(t *testing.T) {
	handler := NewBinaryMessageHandler()

	tests := []struct {
		name        string
		envelope    *domain.SocketEnvelope
		expectError bool
		description string
	}{
		{
			name: "valid envelope with wrapper command type",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				DeviceId:       "device123",
				OrganizationId: "org456",
				UserId:         "user789",
				Payload:        []byte("valid payload"),
			},
			expectError: false,
			description: "Should pass validation for wrapper command",
		},
		{
			name: "valid envelope with wrapper response type",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperResponse,
				DeviceId:       "device123",
				OrganizationId: "org456",
				Payload:        []byte("valid response payload"),
			},
			expectError: false,
			description: "Should pass validation for wrapper response",
		},
		{
			name: "envelope with different organization ID format",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				DeviceId:       "device-with-hyphens",
				OrganizationId: "org_with_underscores",
				Payload:        []byte("payload"),
			},
			expectError: false,
			description: "Should handle different ID formats",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := handler.ValidateEnvelope(tt.envelope)

			if tt.expectError {
				assert.Error(t, err, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
			}
		})
	}
}

func TestBinaryMessageHandler_CreateBinaryMessage_AdditionalCases(t *testing.T) {
	handler := NewBinaryMessageHandler()

	tests := []struct {
		name         string
		envelope     *domain.SocketEnvelope
		expectError  bool
		expectResult bool
		description  string
	}{
		{
			name: "envelope with all fields populated",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				RequestId:      12345,
				SessionId:      "session123",
				UserId:         "user456",
				DeviceId:       "device789",
				OrganizationId: "org012",
				Origin:         domain.OriginTypeFSA,
				Payload:        []byte("complete payload data"),
			},
			expectError:  false,
			expectResult: true,
			description:  "Should create binary message with all fields",
		},
		{
			name: "envelope with minimal required fields",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperResponse,
				OrganizationId: "org999",
				Payload:        []byte("minimal payload"),
			},
			expectError:  false,
			expectResult: true,
			description:  "Should create binary message with minimal fields",
		},
		{
			name: "envelope with very large payload",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				DeviceId:       "device_large_payload",
				OrganizationId: "org_large",
				Payload:        make([]byte, 10*1024), // 10KB payload
			},
			expectError:  false,
			expectResult: true,
			description:  "Should handle large payloads",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := handler.CreateBinaryMessage(tt.envelope)

			if tt.expectError {
				assert.Error(t, err, tt.description)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err, tt.description)
				if tt.expectResult {
					assert.NotNil(t, result, tt.description)
					assert.Greater(t, len(result), 0, "Result should not be empty")
				}
			}
		})
	}
}

func TestBinaryMessageHandler_validateDeviceCommand_AdditionalCases(t *testing.T) {
	handler := NewBinaryMessageHandler()

	tests := []struct {
		name            string
		envelope        *domain.SocketEnvelope
		userPermissions *authorizer.UserPermissions
		expectError     bool
		description     string
	}{
		{
			name: "command envelope with nil permissions",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				DeviceId:       "device123",
				OrganizationId: "org456",
				Payload:        []byte("command payload"),
			},
			userPermissions: nil,
			expectError:     true, // validateDeviceCommand will fail with invalid inner payload
			description:     "Should fail with invalid protobuf payload",
		},
		{
			name: "command envelope with empty payload",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				DeviceId:       "device123",
				OrganizationId: "org456",
				Payload:        []byte{},
			},
			userPermissions: nil,
			expectError:     true,
			description:     "Should reject empty command payload",
		},
		{
			name: "command envelope with valid payload",
			envelope: &domain.SocketEnvelope{
				Type:           domain.EnvelopeTypeWrapperCommand,
				DeviceId:       "device123",
				OrganizationId: "org456",
				Payload:        []byte("valid command data"),
			},
			userPermissions: nil,
			expectError:     true, // validateDeviceCommand will fail with invalid inner payload
			description:     "Should fail with invalid protobuf payload",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a mock database executor for testing
			mockDB := &mocks.FakeDBExecutor{}
			err := handler.validateDeviceCommand(tt.envelope, tt.userPermissions, mockDB)

			if tt.expectError {
				assert.Error(t, err, tt.description)
			} else {
				assert.NoError(t, err, tt.description)
			}
		})
	}
}
