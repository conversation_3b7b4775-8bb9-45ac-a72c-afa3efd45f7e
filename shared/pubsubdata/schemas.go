package pubsubdata

import (
	"time"
)

// Topic name constants - single source of truth for all topic names
const (
	TopicDLQMessages                    = "dlq-messages"
	TopicDLQBQBatch                     = "dlq-bqbatch"
	TopicBrokerGatewayRMSData           = "broker-gateway-rmsData"
	TopicBrokerGatewayRMSEngine         = "broker-gateway-rmsEngine"
	TopicBrokerGatewayMonitorName       = "broker-gateway-monitorName"
	TopicBrokerGatewayMacAddress        = "broker-gateway-macAddress"
	TopicBrokerGatewayFaultLogs         = "broker-gateway-faultLogs"
	TopicBrokerGatewayPerfStats         = "broker-gateway-perfStats"
	TopicBrokerGatewayGatewayLog        = "broker-gateway-gatewayLog"
	TopicBrokerGatewayFaultNotification = "broker-gateway-faultNotification"
	TopicBrokerGatewayWrapperResponse   = "broker-gateway-wrapperResponse"
	TopicETLNotifications               = "etl-notifications"
	TopicBrokerSynapsePurgeExpired      = "broker-synapse-purgeExpired"
)

// Subscription name constants - single source of truth for all subscription names
const (
	SubscriptionETLProcessingDLQMessages              = "etl-processing-dlq-messages"
	SubscriptionETLProcessingDLQBQBatch               = "etl-processing-dlq-bqbatch"
	SubscriptionETLProcessingGatewayRMSData           = "etl-processing-gateway-rmsData"
	SubscriptionETLRawGatewayRMSData                  = "etl-raw-gateway-rmsData"
	SubscriptionETLProcessingGatewayRMSEngine         = "etl-processing-gateway-rmsEngine"
	SubscriptionETLRawGatewayRMSEngine                = "etl-raw-gateway-rmsEngine"
	SubscriptionETLProcessingGatewayMonitorName       = "etl-processing-gateway-monitorName"
	SubscriptionETLRawGatewayMonitorName              = "etl-raw-gateway-monitorName"
	SubscriptionETLProcessingGatewayMacAddress        = "etl-processing-gateway-macAddress"
	SubscriptionETLRawGatewayMacAddress               = "etl-raw-gateway-macAddress"
	SubscriptionETLProcessingGatewayFaultLogs         = "etl-processing-gateway-faultLogs"
	SubscriptionETLRawGatewayFaultLogs                = "etl-raw-gateway-faultLogs"
	SubscriptionETLProcessingGatewayPerfStats         = "etl-processing-gateway-perfStats"
	SubscriptionETLRawGatewayPerfStats                = "etl-raw-gateway-perfStats"
	SubscriptionETLProcessingGatewayGatewayLog        = "etl-processing-gateway-gatewayLog"
	SubscriptionETLRawGatewayGatewayLog               = "etl-raw-gateway-gatewayLog"
	SubscriptionETLProcessingGatewayFaultNotification = "etl-processing-gateway-faultNotification"
	SubscriptionETLRawGatewayFaultNotification        = "etl-raw-gateway-faultNotification"
	SubscriptionETLProcessingGatewayWrapperResponse   = "etl-processing-gateway-wrapperResponse"
	SubscriptionETLRawGatewayWrapperResponse          = "etl-raw-gateway-wrapperResponse"
	SubscriptionETLProcessingNotificationAlerts       = "etl-processing-notification-alerts"
	SubscriptionETLRawNotificationAlerts              = "etl-raw-notification-alerts"
	SubscriptionETLProcessingSynapsePurgeExpired      = "etl-processing-synapse-purgeExpired"
	SubscriptionETLRawSynapsePurgeExpired             = "etl-raw-synapse-purgeExpired"
)

// All of the subscription topic pairs for Pubsub
var PubsubSubscriptions = map[string]string{
	SubscriptionETLProcessingDLQMessages:              TopicDLQMessages,
	SubscriptionETLProcessingDLQBQBatch:               TopicDLQBQBatch,
	SubscriptionETLProcessingGatewayRMSData:           TopicBrokerGatewayRMSData,
	SubscriptionETLRawGatewayRMSData:                  TopicBrokerGatewayRMSData,
	SubscriptionETLProcessingGatewayRMSEngine:         TopicBrokerGatewayRMSEngine,
	SubscriptionETLRawGatewayRMSEngine:                TopicBrokerGatewayRMSEngine,
	SubscriptionETLProcessingGatewayMonitorName:       TopicBrokerGatewayMonitorName,
	SubscriptionETLRawGatewayMonitorName:              TopicBrokerGatewayMonitorName,
	SubscriptionETLProcessingGatewayMacAddress:        TopicBrokerGatewayMacAddress,
	SubscriptionETLRawGatewayMacAddress:               TopicBrokerGatewayMacAddress,
	SubscriptionETLProcessingGatewayFaultLogs:         TopicBrokerGatewayFaultLogs,
	SubscriptionETLRawGatewayFaultLogs:                TopicBrokerGatewayFaultLogs,
	SubscriptionETLProcessingGatewayPerfStats:         TopicBrokerGatewayPerfStats,
	SubscriptionETLRawGatewayPerfStats:                TopicBrokerGatewayPerfStats,
	SubscriptionETLProcessingGatewayGatewayLog:        TopicBrokerGatewayGatewayLog,
	SubscriptionETLRawGatewayGatewayLog:               TopicBrokerGatewayGatewayLog,
	SubscriptionETLProcessingGatewayFaultNotification: TopicBrokerGatewayFaultNotification,
	SubscriptionETLRawGatewayFaultNotification:        TopicBrokerGatewayFaultNotification,
	SubscriptionETLProcessingGatewayWrapperResponse:   TopicBrokerGatewayWrapperResponse,
	SubscriptionETLRawGatewayWrapperResponse:          TopicBrokerGatewayWrapperResponse,
	SubscriptionETLProcessingNotificationAlerts:       TopicETLNotifications,
	SubscriptionETLRawNotificationAlerts:              TopicETLNotifications,
	SubscriptionETLProcessingSynapsePurgeExpired:      TopicBrokerSynapsePurgeExpired,
	SubscriptionETLRawSynapsePurgeExpired:             TopicBrokerSynapsePurgeExpired,
}

// All of the topic names for Pubsub
var Topics = []string{
	TopicDLQMessages,
	TopicDLQBQBatch, // This is a DLQ for the DLQ incase BigQuery beings to fail.
	TopicBrokerGatewayRMSData,
	TopicBrokerGatewayRMSEngine,
	TopicBrokerGatewayMonitorName,
	TopicBrokerGatewayMacAddress,
	TopicBrokerGatewayFaultLogs,
	TopicBrokerGatewayPerfStats,
	TopicBrokerGatewayGatewayLog,
	TopicBrokerGatewayFaultNotification,
	TopicBrokerGatewayWrapperResponse,
	TopicETLNotifications,
	TopicBrokerSynapsePurgeExpired,
}

type CommonAttributes struct {
	Topic                  string
	OrganizationIdentifier string
	DeviceType             string
	DLQReason              string
	ReceiveTimestamp       time.Time
}

// HeaderDetails is a custom type that list all of the headers we will send to pubsub
type HeaderDetails struct {
	Host            string `json:"Host"`
	UserAgent       string `json:"User-Agent"`
	ContentLength   string `json:"Content-Length"`
	ContentType     string `json:"Content-Type"`
	GatewayDeviceID string `json:"gateway-device-id"`
	MessageVersion  string `json:"message-version"`
	MessageType     string `json:"message-type"`
	APIKey          string `json:"x-api-key,omitempty"` // Gets removed before sending
	GatewayTimezone string `json:"tz"`
}

// PubsubMessageWrapper is a helper struct that contains the exported fields from pubsub.Message.
type PubsubMessageWrapper struct {
	ID              string            `json:"id"`
	Data            []byte            `json:"data"`
	Attributes      map[string]string `json:"attributes,omitempty"`
	OrderingKey     string            `json:"ordering_key,omitempty"`
	PublishTime     time.Time         `json:"publish_time,omitempty"`
	DeliveryAttempt int64             `json:"delivery_attempt,omitempty"`
}

// NotificationRequest is the request body for the notification service
type NotificationRequest struct {
	Type     string                 `json:"type"`
	Payload  map[string]interface{} `json:"payload"`
	Metadata map[string]interface{} `json:"metadata"`
}
