package invites

import (
	"bytes"
	htmltemplate "html/template"
	texttemplate "text/template"

	"synapse-its.com/shared/logger"
)

// Default email template HTML
const defaultEmailTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>You're Invited to {{.AppName}}!</title>
</head>
<body style="margin:0;padding:0;background-color:#f4f4f4;">
  <table width="100%" cellpadding="0" cellspacing="0" role="presentation">
    <tr>
      <td align="center" style="padding:20px 0;">
        <table width="600" cellpadding="0" cellspacing="0" style="background-color:#ffffff;border-radius:8px;overflow:hidden;">
          <tr>
            <td style="padding:40px;text-align:center;font-family:Arial,sans-serif;">
              <h1 style="margin:0;color:#333333;font-size:24px;">You're Invited!</h1>
            </td>
          </tr>
          <tr>
            <td style="padding:0 40px 20px;font-family:Arial,sans-serif;color:#555555;font-size:16px;line-height:1.5;">
              {{if .Message}}<p>{{.Message}}</p>{{end}}
              <p style="text-align:center;padding:20px 0;">
                <a href="{{.InviteLink}}" style="background-color:#007BFF;color:#ffffff;padding:14px 28px;text-decoration:none;border-radius:4px;display:inline-block;font-size:16px;">
                  Accept Invitation
                </a>
              </p>
              <p>If the button above doesn't work, copy and paste this link into your browser:</p>
              <p style="word-break:break-all;"><a href="{{.InviteLink}}" style="color:#007BFF;">{{.InviteLink}}</a></p>
            </td>
          </tr>
          <tr>
            <td style="padding:20px 40px;background-color:#f9f9f9;font-family:Arial,sans-serif;color:#999999;font-size:12px;">
              <p style="margin:0;">Sent by {{.AppName}} on behalf of {{.OrganizationName}}</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>`

const defaultEmailSubject = "You're Invited to {{.AppName}}!"

// renderEmailTemplate
// Variable to allow template injection for testing
var emailTemplate = defaultEmailTemplate

// Variable to allow subject template injection for testing
var emailSubjectTemplate = defaultEmailSubject

// renderEmailSubject renders the email subject template
func renderEmailSubject(data EmailTemplateData) (string, error) {
	tmpl, err := texttemplate.New("subject").Parse(emailSubjectTemplate)
	if err != nil {
		logger.Errorf("failed to parse email subject template: %v", err)
		return "", ErrTemplateParsing
	}

	var buf bytes.Buffer
	err = tmpl.Execute(&buf, data)
	if err != nil {
		logger.Errorf("failed to execute email subject template: %v", err)
		return "", ErrTemplateExecution
	}

	return buf.String(), nil
}

func renderEmailTemplate(data EmailTemplateData) (string, error) {
	tmpl, err := htmltemplate.New("invite").Parse(emailTemplate)
	if err != nil {
		logger.Errorf("failed to parse email template: %v", err)
		return "", ErrTemplateParsing
	}

	var buf bytes.Buffer
	err = tmpl.Execute(&buf, data)
	if err != nil {
		logger.Errorf("failed to execute email template: %v", err)
		return "", ErrTemplateExecution
	}

	return buf.String(), nil
}
