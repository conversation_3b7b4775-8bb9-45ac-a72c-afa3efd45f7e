package invites

import (
	"database/sql"
	"errors"
	"strings"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// Using the existing mockResult from invites_test.go

// Test_verifyEmailInAuthMethod tests the verifyEmailInAuthMethod function
func Test_verifyEmailInAuthMethod(t *testing.T) {
	t.Parallel()

	userID := uuid.New()
	email := "<EMAIL>"

	tests := []struct {
		name        string
		requireSSO  bool
		mockDB      *dbexecutor.FakeDBExecutor
		expectedErr error
	}{
		{
			name:       "success_without_sso",
			requireSSO: false,
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"count": int64(1),
					}, nil
				},
			},
			expectedErr: nil,
		},
		{
			name:       "success_with_sso_oidc",
			requireSSO: true,
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					// Query with OIDC filter finds a record
					return map[string]interface{}{
						"count": int64(1),
					}, nil
				},
			},
			expectedErr: nil,
		},
		{
			name:       "email_not_found_without_sso",
			requireSSO: false,
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"count": int64(0),
					}, nil
				},
			},
			expectedErr: ErrEmailNotInAuthMethod,
		},
		{
			name:       "sso_required_but_no_oidc_auth_method",
			requireSSO: true,
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					// Query with OIDC filter finds no records (user only has USERNAME_PASSWORD)
					return map[string]interface{}{
						"count": int64(0),
					}, nil
				},
			},
			expectedErr: ErrSSORequiredButNotOIDC,
		},
		{
			name: "database_error",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return nil, errors.New("database error")
				},
			},
			expectedErr: ErrDatabaseOperation,
		},
		{
			name: "invalid_row_data_missing_count",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{}, nil
				},
			},
			expectedErr: ErrInvalidRowData,
		},
		{
			name: "invalid_row_data_wrong_type",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"count": "not_a_number",
					}, nil
				},
			},
			expectedErr: ErrInvalidRowData,
		},
		{
			name:       "sso_query_validation",
			requireSSO: true,
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					// Verify that when requireSSO is true, the query contains the OIDC filter
					if !strings.Contains(query, "type = 'OIDC'") {
						return nil, errors.New("query does not contain OIDC filter")
					}
					return map[string]interface{}{
						"count": int64(1),
					}, nil
				},
			},
			expectedErr: nil,
		},
		{
			name:       "non_sso_query_validation",
			requireSSO: false,
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					// Verify that when requireSSO is false, the query does NOT contain the OIDC filter
					if strings.Contains(query, "type = 'OIDC'") {
						return nil, errors.New("query should not contain OIDC filter")
					}
					return map[string]interface{}{
						"count": int64(1),
					}, nil
				},
			},
			expectedErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := verifyEmailInAuthMethod(tt.mockDB, userID, email, tt.requireSSO)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test_verifyCustomRoleForOrganization tests the verifyCustomRoleForOrganization function
func Test_verifyCustomRoleForOrganization(t *testing.T) {
	t.Parallel()

	orgID := uuid.New()
	roleID := uuid.New()

	tests := []struct {
		name        string
		mockDB      *dbexecutor.FakeDBExecutor
		expectedErr error
	}{
		{
			name: "success",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"count": int64(1),
					}, nil
				},
			},
			expectedErr: nil,
		},
		{
			name: "role_not_found",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"count": int64(0),
					}, nil
				},
			},
			expectedErr: ErrInvalidCustomRole,
		},
		{
			name: "database_error",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return nil, errors.New("database error")
				},
			},
			expectedErr: ErrDatabaseOperation,
		},
		{
			name: "invalid_row_data_missing_count",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{}, nil
				},
			},
			expectedErr: ErrInvalidRowData,
		},
		{
			name: "invalid_row_data_wrong_type",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"count": "not_a_number",
					}, nil
				},
			},
			expectedErr: ErrInvalidRowData,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := verifyCustomRoleForOrganization(tt.mockDB, orgID, roleID)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test_verifyUserNotMemberOfOrganization tests the verifyUserNotMemberOfOrganization function
func Test_verifyUserNotMemberOfOrganization(t *testing.T) {
	t.Parallel()

	userID := uuid.New()
	orgID := uuid.New()

	tests := []struct {
		name        string
		mockDB      *dbexecutor.FakeDBExecutor
		expectedErr error
	}{
		{
			name: "success_not_member",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"count": int64(0),
					}, nil
				},
			},
			expectedErr: nil,
		},
		{
			name: "user_already_member",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"count": int64(1),
					}, nil
				},
			},
			expectedErr: ErrUserAlreadyMember,
		},
		{
			name: "database_error",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return nil, errors.New("database error")
				},
			},
			expectedErr: ErrDatabaseOperation,
		},
		{
			name: "invalid_row_data_missing_count",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{}, nil
				},
			},
			expectedErr: ErrInvalidRowData,
		},
		{
			name: "invalid_row_data_wrong_type",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"count": "not_a_number",
					}, nil
				},
			},
			expectedErr: ErrInvalidRowData,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := verifyUserNotMemberOfOrganization(tt.mockDB, userID, orgID)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test_createMembershipForUser tests the createMembershipForUser function
func Test_createMembershipForUser(t *testing.T) {
	t.Parallel()

	userID := uuid.New()
	orgID := uuid.New()
	roleID := uuid.New()
	email := "<EMAIL>"
	authMethodID := uuid.New()

	tests := []struct {
		name        string
		mockDB      *dbexecutor.FakeDBExecutor
		expectedErr error
	}{
		{
			name: "success",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"id": authMethodID.String(),
					}, nil
				},
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					return &mockResult{rowsAffected: 1}, nil
				},
			},
			expectedErr: nil,
		},
		{
			name: "auth_method_not_found",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return nil, sql.ErrNoRows
				},
			},
			expectedErr: ErrEmailNotInAuthMethod,
		},
		{
			name: "auth_method_database_error",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return nil, errors.New("database error")
				},
			},
			expectedErr: ErrDatabaseOperation,
		},
		{
			name: "invalid_auth_method_data_missing_id",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{}, nil
				},
			},
			expectedErr: ErrInvalidRowData,
		},
		{
			name: "invalid_auth_method_data_wrong_type",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"id": 123,
					}, nil
				},
			},
			expectedErr: ErrInvalidRowData,
		},
		{
			name: "invalid_uuid_format",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"id": "invalid-uuid",
					}, nil
				},
			},
			expectedErr: ErrInvalidRowData,
		},
		{
			name: "membership_creation_error",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					if strings.Contains(query, "AuthMethod") {
						return map[string]interface{}{
							"id": authMethodID.String(),
						}, nil
					}
					// Membership query fails
					return nil, errors.New("membership creation failed")
				},
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					return &mockResult{rowsAffected: 1}, nil
				},
			},
			expectedErr: ErrDatabaseOperation,
		},
		{
			name: "role_assignment_creation_error",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"id": authMethodID.String(),
					}, nil
				},
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					if strings.Contains(query, "OrgRoleAssignments") {
						return nil, errors.New("role assignment creation failed")
					}
					return &mockResult{rowsAffected: 1}, nil
				},
			},
			expectedErr: ErrDatabaseOperation,
		},
		{
			name: "membership_query_missing_id_field",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					if strings.Contains(query, "AuthMethod") {
						return map[string]interface{}{
							"id": authMethodID.String(),
						}, nil
					}
					// Membership query returns data but missing "id" field
					return map[string]interface{}{
						"other_field": "some_value",
					}, nil
				},
			},
			expectedErr: ErrInvalidRowData,
		},
		{
			name: "membership_query_id_wrong_type",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					if strings.Contains(query, "AuthMethod") {
						return map[string]interface{}{
							"id": authMethodID.String(),
						}, nil
					}
					// Membership query returns "id" but as wrong type (int instead of string)
					return map[string]interface{}{
						"id": 12345,
					}, nil
				},
			},
			expectedErr: ErrInvalidRowData,
		},
		{
			name: "membership_query_id_nil",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					if strings.Contains(query, "AuthMethod") {
						return map[string]interface{}{
							"id": authMethodID.String(),
						}, nil
					}
					// Membership query returns "id" but as nil
					return map[string]interface{}{
						"id": nil,
					}, nil
				},
			},
			expectedErr: ErrInvalidRowData,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := createMembershipForUser(tt.mockDB, userID, orgID, roleID, email)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
