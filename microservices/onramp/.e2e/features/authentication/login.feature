Feature: User authentication

Background:
  Given I am on the home page
  Then I should see a modal with the title "Login required"
  When I click the "OK" button with class "custom-login-modal-required"
  Then I should see the login page with the title "Login" in an h2 tag
  When I click the "Synapse SSO" button with id "btn-keycloak"
  And I complete the Keycloak login form with username "<EMAIL>" and password "puppies1234"
  Then I should be redirected back to the application
