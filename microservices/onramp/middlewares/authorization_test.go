package middlewares

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/onramp/domain"
)

func TestUserAuthorizationMiddleware(t *testing.T) {
	t.<PERSON>()

	// Create a test handler that will be called if authorization succeeds
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]string{"status": "success"})
	})

	tests := []struct {
		name           string
		sessionData    *domain.Session
		urlPath        string
		expectedStatus int
		expectedBody   map[string]interface{}
	}{
		{
			name: "successful_authorization_same_user",
			sessionData: &domain.Session{
				UserID: "123e4567-e89b-12d3-a456-************",
			},
			urlPath:        "/user/123e4567-e89b-12d3-a456-************/auth-methods",
			expectedStatus: http.StatusOK,
			expectedBody:   map[string]interface{}{"status": "success"},
		},
		{
			name: "forbidden_different_user",
			sessionData: &domain.Session{
				UserID: "123e4567-e89b-12d3-a456-************",
			},
			urlPath:        "/user/987fcdeb-51a2-43d1-b654-321987654321/auth-methods",
			expectedStatus: http.StatusForbidden,
			expectedBody:   nil,
		},
		{
			name:           "unauthorized_no_session",
			sessionData:    nil,
			urlPath:        "/user/123e4567-e89b-12d3-a456-************/auth-methods",
			expectedStatus: http.StatusUnauthorized,
			expectedBody:   nil,
		},
		{
			name: "unauthorized_empty_user_id",
			sessionData: &domain.Session{
				UserID: "",
			},
			urlPath:        "/user/123e4567-e89b-12d3-a456-************/auth-methods",
			expectedStatus: http.StatusUnauthorized,
			expectedBody:   nil,
		},
		{
			name: "unauthorized_invalid_user_id_in_session",
			sessionData: &domain.Session{
				UserID: "invalid-uuid",
			},
			urlPath:        "/user/123e4567-e89b-12d3-a456-************/auth-methods",
			expectedStatus: http.StatusUnauthorized,
			expectedBody:   nil,
		},
		{
			name: "bad_request_invalid_user_id_in_url",
			sessionData: &domain.Session{
				UserID: "123e4567-e89b-12d3-a456-************",
			},
			urlPath:        "/user/invalid-uuid/auth-methods",
			expectedStatus: http.StatusBadRequest,
			expectedBody:   nil,
		},
		{
			name: "bad_request_empty_user_id_in_url",
			sessionData: &domain.Session{
				UserID: "123e4567-e89b-12d3-a456-************",
			},
			urlPath:        "/user//auth-methods",
			expectedStatus: http.StatusBadRequest,
			expectedBody:   nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create the middleware
			middleware := UserAuthorizationMiddleware()

			// Create the handler chain
			handler := middleware(testHandler)

			// Create the request
			req := httptest.NewRequest("GET", tt.urlPath, nil)

			// Set up the context with session data if provided
			if tt.sessionData != nil {
				req = req.WithContext(context.WithValue(req.Context(), SessionContextKey, tt.sessionData))
			}

			// Set up URL variables for the path
			if tt.urlPath != "/user//auth-methods" {
				// Extract user ID from the path for URL variables
				userID := extractUserIDFromPath(tt.urlPath)
				if userID != "" {
					req = mux.SetURLVars(req, map[string]string{
						"userId": userID,
					})
				}
			}

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the request
			handler.ServeHTTP(rr, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, rr.Code)

			// Assert response body for success cases
			if tt.expectedBody != nil {
				var responseBody map[string]interface{}
				err := json.Unmarshal(rr.Body.Bytes(), &responseBody)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedBody, responseBody)
			}
		})
	}
}

// extractUserIDFromPath is a helper function to extract user ID from URL path
// This simulates what the router would do when setting URL variables
func extractUserIDFromPath(path string) string {
	// Simple extraction for testing - in real scenarios, the router handles this
	// Expected pattern: /user/{userId}/...
	if len(path) > 6 && path[:6] == "/user/" {
		remaining := path[6:]
		for i, char := range remaining {
			if char == '/' {
				return remaining[:i]
			}
		}
		return remaining
	}
	return ""
}

func TestUserAuthorizationMiddleware_Integration(t *testing.T) {
	t.Parallel()

	// Test with real UUIDs
	testUserID := uuid.New()
	otherUserID := uuid.New()

	// Create a test handler
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]string{"status": "success"})
	})

	// Create the middleware
	middleware := UserAuthorizationMiddleware()
	handler := middleware(testHandler)

	tests := []struct {
		name           string
		sessionUserID  string
		requestUserID  string
		expectedStatus int
	}{
		{
			name:           "real_uuid_success",
			sessionUserID:  testUserID.String(),
			requestUserID:  testUserID.String(),
			expectedStatus: http.StatusOK,
		},
		{
			name:           "real_uuid_forbidden",
			sessionUserID:  testUserID.String(),
			requestUserID:  otherUserID.String(),
			expectedStatus: http.StatusForbidden,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create session with real UUID
			sessionData := &domain.Session{
				UserID: tt.sessionUserID,
			}

			// Create request
			req := httptest.NewRequest("GET", "/user/"+tt.requestUserID+"/auth-methods", nil)
			req = req.WithContext(context.WithValue(req.Context(), SessionContextKey, sessionData))
			req = mux.SetURLVars(req, map[string]string{
				"userId": tt.requestUserID,
			})

			rr := httptest.NewRecorder()
			handler.ServeHTTP(rr, req)

			assert.Equal(t, tt.expectedStatus, rr.Code)
		})
	}
}

func TestUserAuthorizationMiddleware_AuthorizationContext(t *testing.T) {
	t.Parallel()

	// Create a test handler that checks for authorization context
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Extract authorization context
		authContext, ok := GetAuthorizationContext(r.Context())
		if !ok {
			http.Error(w, "no authorization context", http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]interface{}{
			"status": "success",
			"userID": authContext.UserID.String(),
		})
	})

	tests := []struct {
		name              string
		sessionUserID     string
		requestUserID     string
		expectedStatus    int
		expectedUserID    string
		expectAuthContext bool
	}{
		{
			name:              "authorization_context_present",
			sessionUserID:     "123e4567-e89b-12d3-a456-************",
			requestUserID:     "123e4567-e89b-12d3-a456-************",
			expectedStatus:    http.StatusOK,
			expectedUserID:    "123e4567-e89b-12d3-a456-************",
			expectAuthContext: true,
		},
		{
			name:              "authorization_context_not_present_on_unauthorized",
			sessionUserID:     "123e4567-e89b-12d3-a456-************",
			requestUserID:     "987fcdeb-51a2-43d1-b654-321987654321",
			expectedStatus:    http.StatusForbidden,
			expectedUserID:    "",
			expectAuthContext: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create session data
			sessionData := &domain.Session{
				UserID: tt.sessionUserID,
			}

			// Create the middleware
			middleware := UserAuthorizationMiddleware()
			handler := middleware(testHandler)

			// Create request
			req := httptest.NewRequest("GET", "/user/"+tt.requestUserID+"/auth-methods", nil)
			req = req.WithContext(context.WithValue(req.Context(), SessionContextKey, sessionData))
			req = mux.SetURLVars(req, map[string]string{
				"userId": tt.requestUserID,
			})

			rr := httptest.NewRecorder()
			handler.ServeHTTP(rr, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, rr.Code)

			// Assert response body for success cases
			if tt.expectAuthContext {
				var responseBody map[string]interface{}
				err := json.Unmarshal(rr.Body.Bytes(), &responseBody)
				assert.NoError(t, err)
				assert.Equal(t, "success", responseBody["status"])
				assert.Equal(t, tt.expectedUserID, responseBody["userID"])
			}
		})
	}
}

func Test_GetAuthorizationContext(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		setupContext   func() context.Context
		expectedOK     bool
		expectedUserID uuid.UUID
	}{
		{
			name: "authorization_context_present",
			setupContext: func() context.Context {
				testUserID := uuid.New()
				authContext := &AuthorizationContext{
					UserID: testUserID,
				}
				ctx := context.WithValue(context.Background(), AuthorizationContextKey, authContext)
				return ctx
			},
			expectedOK:     true,
			expectedUserID: uuid.Nil, // Will be set in the test
		},
		{
			name: "authorization_context_not_present",
			setupContext: func() context.Context {
				return context.Background()
			},
			expectedOK:     false,
			expectedUserID: uuid.Nil,
		},
		{
			name: "authorization_context_wrong_type",
			setupContext: func() context.Context {
				return context.WithValue(context.Background(), AuthorizationContextKey, "wrong-type")
			},
			expectedOK:     false,
			expectedUserID: uuid.Nil,
		},
		{
			name: "authorization_context_nil_value",
			setupContext: func() context.Context {
				return context.WithValue(context.Background(), AuthorizationContextKey, nil)
			},
			expectedOK:     false,
			expectedUserID: uuid.Nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup context
			ctx := tt.setupContext()

			// Extract authorization context
			result, ok := GetAuthorizationContext(ctx)

			// Assert results
			assert.Equal(t, tt.expectedOK, ok)
			if tt.expectedOK {
				assert.NotNil(t, result)
				// For the first test case, we need to check that the UserID is not nil
				if tt.name == "authorization_context_present" {
					assert.NotEqual(t, uuid.Nil, result.UserID)
				} else {
					assert.Equal(t, tt.expectedUserID, result.UserID)
				}
			} else {
				assert.Nil(t, result)
			}
		})
	}
}

func TestAuthorizationContext_Structure(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		setupContext   func() *AuthorizationContext
		expectedUserID uuid.UUID
	}{
		{
			name: "authorization_context_creation",
			setupContext: func() *AuthorizationContext {
				testUserID := uuid.New()
				return &AuthorizationContext{
					UserID: testUserID,
				}
			},
			expectedUserID: uuid.Nil, // Will be set in the test
		},
		{
			name: "authorization_context_zero_value",
			setupContext: func() *AuthorizationContext {
				var authContext AuthorizationContext
				return &authContext
			},
			expectedUserID: uuid.Nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create authorization context
			authContext := tt.setupContext()

			// Assert structure
			assert.NotNil(t, authContext)
			if tt.name == "authorization_context_creation" {
				assert.NotEqual(t, uuid.Nil, authContext.UserID)
			} else {
				assert.Equal(t, tt.expectedUserID, authContext.UserID)
			}
		})
	}
}
