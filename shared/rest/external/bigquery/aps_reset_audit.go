package bigquery

import (
	"time"

	"github.com/google/uuid"
	"synapse-its.com/shared/connect"
)

type apsResetAuditor struct {
	bq connect.DatabaseExecutor
}

func NewApsResetAuditor(bq connect.DatabaseExecutor) *apsResetAuditor {
	return &apsResetAuditor{
		bq: bq,
	}
}

// Audit implements the apsResetAuditor interface to log APS factory reset requests
func (a *apsResetAuditor) Audit(userID, orgID uuid.UUID, challengeCode string, err error) error {
	// Determine success status and error message
	success := err == nil
	var errorMessage string
	if !success {
		errorMessage = err.Error()
	}

	// Get current timestamp
	requestTimestamp := time.Now()

	// Insert audit record into APSFactoryResetLogs table
	query := `
		INSERT INTO {{APSFactoryResetLogs}} (
			user_id,
			organization_id,
			challenge_code,
			success,
			error_message,
			request_timestamp
		) VALUES ($1, $2, $3, $4, $5, $6)
	`

	// Note: IP address is not available in the current context, so we'll set it to NULL
	// In a future enhancement, this could be passed from the handler or middleware
	_, dbErr := a.bq.Exec(query,
		userID.String(),
		orgID.String(),
		challengeCode,
		success,
		errorMessage,
		requestTimestamp,
	)

	return dbErr
}
