package config

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// HandlerDeps bundles dependencies for injection and testing.
type HandlerDeps struct {
	GetConnections           func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	GetSoftwareGatewayConfig func(pg connect.DatabaseExecutor, softwareGatewayId uuid.UUID) (*SoftwareGateway, []ResolvedGatewayConfigSetting, error)

	// Template CRUD operations
	CreateGatewayConfigTemplate func(pg connect.DatabaseExecutor, req *CreateGatewayConfigTemplateRequest) (*GatewayConfigTemplate, error)
	GetGatewayConfigTemplate    func(pg connect.DatabaseExecutor, organizationId uuid.UUID, templateId uuid.UUID) (*GatewayConfigTemplate, error)
	UpdateGatewayConfigTemplate func(pg connect.DatabaseExecutor, organizationId uuid.UUID, templateId uuid.UUID, req *UpdateGatewayConfigTemplateRequest) (*GatewayConfigTemplate, error)
	DeleteGatewayConfigTemplate func(pg connect.DatabaseExecutor, organizationId uuid.UUID, templateId uuid.UUID) error
	ListGatewayConfigTemplates  func(pg connect.DatabaseExecutor, organizationId uuid.UUID) ([]GatewayConfigTemplate, error)

	// Template Settings CRUD operations
	CreateOrUpdateGatewayConfigTemplateSetting func(pg connect.DatabaseExecutor, organizationId uuid.UUID, req *CreateOrUpdateGatewayConfigTemplateSettingRequest) (*GatewayConfigTemplateSetting, error)
	GetGatewayConfigTemplateSettings           func(pg connect.DatabaseExecutor, organizationId uuid.UUID, templateId uuid.UUID) ([]GatewayConfigTemplateSetting, error)
	DeleteGatewayConfigTemplateSetting         func(pg connect.DatabaseExecutor, organizationId uuid.UUID, templateId uuid.UUID, setting string) error
	BulkReplaceGatewayConfigTemplateSettings   func(pg connect.DatabaseExecutor, organizationId uuid.UUID, templateId uuid.UUID, settings []GatewayConfigTemplateSetting) error

	// Setting Override CRUD operations
	CreateOrUpdateGatewayConfigTemplateSettingOverride func(pg connect.DatabaseExecutor, organizationId uuid.UUID, req *CreateOrUpdateGatewayConfigTemplateSettingOverrideRequest) (*GatewayConfigTemplateSettingOverride, error)
	GetGatewayConfigTemplateSettingOverrides           func(pg connect.DatabaseExecutor, organizationId uuid.UUID, softwareGatewayId uuid.UUID) ([]GatewayConfigTemplateSettingOverride, error)
	DeleteGatewayConfigTemplateSettingOverride         func(pg connect.DatabaseExecutor, organizationId uuid.UUID, softwareGatewayId uuid.UUID, setting string) error
	BulkReplaceGatewayConfigTemplateSettingOverrides   func(pg connect.DatabaseExecutor, organizationId uuid.UUID, softwareGatewayId uuid.UUID, overrides []GatewayConfigTemplateSettingOverride) error
	BulkUpsertGatewayConfigTemplateSettingOverrides    func(pg connect.DatabaseExecutor, organizationId uuid.UUID, softwareGatewayId uuid.UUID, settingValues map[string]string) ([]GatewayConfigTemplateSettingOverride, error)

	// Legacy support: TODO remove when GUI is updated
	GetOrganizationIdBySoftwareGatewayId func(pg connect.DatabaseExecutor, softwareGatewayId uuid.UUID) (uuid.UUID, error)

	// Base Settings CRUD operations
	CreateGatewayConfigTemplateBaseSetting func(pg connect.DatabaseExecutor, baseSetting GatewayConfigTemplateBaseSetting) error
	GetGatewayConfigTemplateBaseSetting    func(pg connect.DatabaseExecutor, setting string) (*GatewayConfigTemplateBaseSetting, error)
	UpdateGatewayConfigTemplateBaseSetting func(pg connect.DatabaseExecutor, setting string, baseSetting GatewayConfigTemplateBaseSetting) error
	DeleteGatewayConfigTemplateBaseSetting func(pg connect.DatabaseExecutor, setting string) error
	ListGatewayConfigTemplateBaseSettings  func(pg connect.DatabaseExecutor) ([]GatewayConfigTemplateBaseSetting, error)
}

// This handler will get a software gateway by its identifier
func GetByIdentifierHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Validate softwareGatewayId first before making any database connections
		vars := mux.Vars(r)
		softwareGatewayIdStr := vars["identifier"]
		softwareGatewayId, err := uuid.Parse(softwareGatewayIdStr)
		if err != nil {
			logger.Errorf("invalid software gateway id: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Get software gateway with template-based configuration
		softwareGateway, settings, err := deps.GetSoftwareGatewayConfig(pg, softwareGatewayId)
		if err != nil {
			if errors.Is(err, ErrSoftwareGatewayConfigNotFound) {
				logger.Errorf("Software gateway not found: %s", softwareGatewayId)
				response.CreateNotFoundResponse(w)
				return
			}

			logger.Errorf("Error getting software gateway config with templates: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return the software gateway with resolved template settings
		response.CreateSuccessResponse(softwareGateway.ToResponseWithSettings(settings), w)
	}
}

// Template CRUD HTTP Handlers

// CreateTemplateHandlerWithDeps creates a new gateway config template
func CreateTemplateHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Extract organization ID from URL path
		vars := mux.Vars(r)
		organizationIdStr := vars["organizationId"]
		organizationId, err := uuid.Parse(organizationIdStr)
		if err != nil {
			logger.Errorf("Invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Parse request body
		var req CreateGatewayConfigTemplateRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			logger.Errorf("Failed to parse request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Override organizationId from URL path (security measure)
		req.OrganizationId = organizationId

		// Basic validation
		if req.Name == "" || req.Description == "" {
			logger.Error("Missing required fields in request")
			response.CreateBadRequestResponse(w)
			return
		}

		// Create template
		template, err := deps.CreateGatewayConfigTemplate(connections.Postgres, &req)
		if err != nil {
			logger.Errorf("Failed to create gateway config template: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(template, w)
	}
}

// GetTemplateHandlerWithDeps retrieves a specific gateway config template
func GetTemplateHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Extract organization ID and template ID from URL
		vars := mux.Vars(r)
		organizationIdStr := vars["organizationId"]
		organizationId, err := uuid.Parse(organizationIdStr)
		if err != nil {
			logger.Errorf("Invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		templateIdStr := vars["templateId"]
		templateId, err := uuid.Parse(templateIdStr)
		if err != nil {
			logger.Errorf("Invalid template ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Get template for the specific organization
		template, err := deps.GetGatewayConfigTemplate(connections.Postgres, organizationId, templateId)
		if err != nil {
			if errors.Is(err, ErrGatewayConfigTemplateNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Failed to get gateway config template: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(template, w)
	}
}

// UpdateTemplateHandlerWithDeps updates a gateway config template
func UpdateTemplateHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Extract organization ID and template ID from URL
		vars := mux.Vars(r)
		organizationIdStr := vars["organizationId"]
		organizationId, err := uuid.Parse(organizationIdStr)
		if err != nil {
			logger.Errorf("Invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		templateIdStr := vars["templateId"]
		templateId, err := uuid.Parse(templateIdStr)
		if err != nil {
			logger.Errorf("Invalid template ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse request body
		var req UpdateGatewayConfigTemplateRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			logger.Errorf("Failed to parse request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Update template for the specific organization
		template, err := deps.UpdateGatewayConfigTemplate(connections.Postgres, organizationId, templateId, &req)
		if err != nil {
			if errors.Is(err, ErrGatewayConfigTemplateNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Failed to update gateway config template: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(template, w)
	}
}

// DeleteTemplateHandlerWithDeps deletes a gateway config template
func DeleteTemplateHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Extract organization ID and template ID from URL
		vars := mux.Vars(r)
		organizationIdStr := vars["organizationId"]
		organizationId, err := uuid.Parse(organizationIdStr)
		if err != nil {
			logger.Errorf("Invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		templateIdStr := vars["templateId"]
		templateId, err := uuid.Parse(templateIdStr)
		if err != nil {
			logger.Errorf("Invalid template ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Delete template for the specific organization
		err = deps.DeleteGatewayConfigTemplate(connections.Postgres, organizationId, templateId)
		if err != nil {
			if errors.Is(err, ErrGatewayConfigTemplateNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Failed to delete gateway config template: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(nil, w)
	}
}

// ListTemplatesHandlerWithDeps lists gateway config templates for an organization
func ListTemplatesHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Extract organization ID from URL path
		vars := mux.Vars(r)
		organizationIdStr := vars["organizationId"]
		organizationId, err := uuid.Parse(organizationIdStr)
		if err != nil {
			logger.Errorf("Invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// List templates
		templates, err := deps.ListGatewayConfigTemplates(connections.Postgres, organizationId)
		if err != nil {
			logger.Errorf("Failed to list gateway config templates: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(templates, w)
	}
}

// Template Settings HTTP Handlers

// CreateOrUpdateTemplateSettingHandlerWithDeps creates or updates a template setting
func CreateOrUpdateTemplateSettingHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Extract organization ID and template ID from URL
		vars := mux.Vars(r)
		organizationIdStr := vars["organizationId"]
		organizationId, err := uuid.Parse(organizationIdStr)
		if err != nil {
			logger.Errorf("Invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		templateIdStr := vars["templateId"]
		templateId, err := uuid.Parse(templateIdStr)
		if err != nil {
			logger.Errorf("Invalid template ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse request body
		var req CreateOrUpdateGatewayConfigTemplateSettingRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			logger.Errorf("Failed to parse request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Set template ID from URL
		req.GatewayConfigTemplateId = templateId

		// Basic validation
		if req.Setting == "" || req.Value == "" {
			logger.Error("Missing required fields in request")
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Create or update setting with organization validation
		setting, err := deps.CreateOrUpdateGatewayConfigTemplateSetting(connections.Postgres, organizationId, &req)
		if err != nil {
			if errors.Is(err, ErrGatewayConfigTemplateNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Failed to create or update template setting: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(setting, w)
	}
}

// GetTemplateSettingsHandlerWithDeps retrieves all settings for a template
func GetTemplateSettingsHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Extract organization ID and template ID from URL
		vars := mux.Vars(r)
		organizationIdStr := vars["organizationId"]
		organizationId, err := uuid.Parse(organizationIdStr)
		if err != nil {
			logger.Errorf("Invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		templateIdStr := vars["templateId"]
		templateId, err := uuid.Parse(templateIdStr)
		if err != nil {
			logger.Errorf("Invalid template ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Get settings with organization validation
		settings, err := deps.GetGatewayConfigTemplateSettings(connections.Postgres, organizationId, templateId)
		if err != nil {
			if errors.Is(err, ErrGatewayConfigTemplateNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Failed to get template settings: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(settings, w)
	}
}

// DeleteTemplateSettingHandlerWithDeps deletes a specific template setting
func DeleteTemplateSettingHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Extract organization ID, template ID and setting from URL
		vars := mux.Vars(r)
		organizationIdStr := vars["organizationId"]
		organizationId, err := uuid.Parse(organizationIdStr)
		if err != nil {
			logger.Errorf("Invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		templateIdStr := vars["templateId"]
		setting := vars["setting"]

		templateId, err := uuid.Parse(templateIdStr)
		if err != nil {
			logger.Errorf("Invalid template ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Delete setting with organization validation
		err = deps.DeleteGatewayConfigTemplateSetting(connections.Postgres, organizationId, templateId, setting)
		if err != nil {
			if errors.Is(err, ErrGatewayConfigTemplateNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			if errors.Is(err, ErrGatewayConfigTemplateSettingNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Failed to delete template setting: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(nil, w)
	}
}

// BulkReplaceTemplateSettingsHandlerWithDeps replaces all settings for a template
func BulkReplaceTemplateSettingsHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Extract organization ID and template ID from URL
		vars := mux.Vars(r)
		organizationIdStr := vars["organizationId"]
		organizationId, err := uuid.Parse(organizationIdStr)
		if err != nil {
			logger.Errorf("Invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		templateIdStr := vars["templateId"]
		templateId, err := uuid.Parse(templateIdStr)
		if err != nil {
			logger.Errorf("Invalid template ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse request body
		var settings []GatewayConfigTemplateSetting
		if err := json.NewDecoder(r.Body).Decode(&settings); err != nil {
			logger.Errorf("Failed to parse request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Bulk replace settings with organization validation
		err = deps.BulkReplaceGatewayConfigTemplateSettings(connections.Postgres, organizationId, templateId, settings)
		if err != nil {
			if errors.Is(err, ErrGatewayConfigTemplateNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Failed to bulk replace template settings: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(nil, w)
	}
}

// Override Settings HTTP Handlers

// CreateOrUpdateOverrideHandlerWithDeps creates or updates a setting override
func CreateOrUpdateOverrideHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Extract organization ID and software gateway ID from URL
		vars := mux.Vars(r)
		organizationIdStr := vars["organizationId"]
		organizationId, err := uuid.Parse(organizationIdStr)
		if err != nil {
			logger.Errorf("Invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		identifierStr := vars["identifier"]
		softwareGatewayId, err := uuid.Parse(identifierStr)
		if err != nil {
			logger.Errorf("Invalid software gateway ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse request body
		var req CreateOrUpdateGatewayConfigTemplateSettingOverrideRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			logger.Errorf("Failed to parse request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Set software gateway ID from URL
		req.SoftwareGatewayId = softwareGatewayId

		// Basic validation
		if req.Setting == "" || req.Value == "" {
			logger.Error("Missing required fields in request")
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Create or update override with organization validation
		override, err := deps.CreateOrUpdateGatewayConfigTemplateSettingOverride(connections.Postgres, organizationId, &req)
		if err != nil {
			if errors.Is(err, ErrSoftwareGatewayConfigNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Failed to create or update setting override: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(override, w)
	}
}

// GetOverridesHandlerWithDeps retrieves all overrides for a software gateway
func GetOverridesHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Extract organization ID and software gateway ID from URL
		vars := mux.Vars(r)
		organizationIdStr := vars["organizationId"]
		organizationId, err := uuid.Parse(organizationIdStr)
		if err != nil {
			logger.Errorf("Invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		identifierStr := vars["identifier"]
		softwareGatewayId, err := uuid.Parse(identifierStr)
		if err != nil {
			logger.Errorf("Invalid software gateway ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Get overrides with organization validation
		overrides, err := deps.GetGatewayConfigTemplateSettingOverrides(connections.Postgres, organizationId, softwareGatewayId)
		if err != nil {
			if errors.Is(err, ErrSoftwareGatewayConfigNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Failed to get setting overrides: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(overrides, w)
	}
}

// DeleteOverrideHandlerWithDeps deletes a specific setting override
func DeleteOverrideHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Extract organization ID, software gateway ID and setting from URL
		vars := mux.Vars(r)
		organizationIdStr := vars["organizationId"]
		organizationId, err := uuid.Parse(organizationIdStr)
		if err != nil {
			logger.Errorf("Invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		identifierStr := vars["identifier"]
		setting := vars["setting"]

		softwareGatewayId, err := uuid.Parse(identifierStr)
		if err != nil {
			logger.Errorf("Invalid software gateway ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Delete override with organization validation
		err = deps.DeleteGatewayConfigTemplateSettingOverride(connections.Postgres, organizationId, softwareGatewayId, setting)
		if err != nil {
			if errors.Is(err, ErrSoftwareGatewayConfigNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			if errors.Is(err, ErrGatewayConfigTemplateSettingOverrideNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Failed to delete setting override: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(nil, w)
	}
}

// BulkReplaceOverridesHandlerWithDeps replaces all overrides for a software gateway
func BulkReplaceOverridesHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Extract organization ID and software gateway ID from URL
		vars := mux.Vars(r)
		organizationIdStr := vars["organizationId"]
		organizationId, err := uuid.Parse(organizationIdStr)
		if err != nil {
			logger.Errorf("Invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		identifierStr := vars["identifier"]
		softwareGatewayId, err := uuid.Parse(identifierStr)
		if err != nil {
			logger.Errorf("Invalid software gateway ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse request body
		var overrides []GatewayConfigTemplateSettingOverride
		if err := json.NewDecoder(r.Body).Decode(&overrides); err != nil {
			logger.Errorf("Failed to parse request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Bulk replace overrides with organization validation
		err = deps.BulkReplaceGatewayConfigTemplateSettingOverrides(connections.Postgres, organizationId, softwareGatewayId, overrides)
		if err != nil {
			if errors.Is(err, ErrSoftwareGatewayConfigNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Failed to bulk replace setting overrides: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(nil, w)
	}
}

// BulkUpsertOverridesHandlerWithDeps upserts setting overrides for a software gateway from key-value pairs
func BulkUpsertOverridesHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Extract organization ID and software gateway ID from URL
		vars := mux.Vars(r)
		organizationIdStr := vars["organizationId"]
		organizationId, err := uuid.Parse(organizationIdStr)
		if err != nil {
			logger.Errorf("Invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		identifierStr := vars["identifier"]
		softwareGatewayId, err := uuid.Parse(identifierStr)
		if err != nil {
			logger.Errorf("Invalid software gateway ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse request body as key-value pairs
		var settingValues BulkUpsertGatewayConfigTemplateSettingOverridesRequest
		if err := json.NewDecoder(r.Body).Decode(&settingValues); err != nil {
			logger.Errorf("Failed to parse request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate that we have at least one setting
		if len(settingValues) == 0 {
			logger.Error("No setting-value pairs provided in request")
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Bulk upsert overrides with organization validation
		overrides, err := deps.BulkUpsertGatewayConfigTemplateSettingOverrides(connections.Postgres, organizationId, softwareGatewayId, settingValues)
		if err != nil {
			if errors.Is(err, ErrSoftwareGatewayConfigNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Failed to bulk upsert setting overrides: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(overrides, w)
	}
}

// TODO: Remove this legacy endpoint when GUI is updated to include organizationId in URL
// Legacy support: BulkUpsertOverridesLegacyHandlerWithDeps for sites that don't pass organizationId in URL
func BulkUpsertOverridesLegacyHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Extract software gateway ID from URL
		vars := mux.Vars(r)
		identifierStr := vars["identifier"]
		softwareGatewayId, err := uuid.Parse(identifierStr)
		if err != nil {
			logger.Errorf("Invalid software gateway ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Look up organizationId from softwareGatewayId
		organizationId, err := deps.GetOrganizationIdBySoftwareGatewayId(connections.Postgres, softwareGatewayId)
		if err != nil {
			if errors.Is(err, ErrSoftwareGatewayConfigNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Failed to lookup organization for software gateway: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Parse request body as key-value pairs
		var settingValues BulkUpsertGatewayConfigTemplateSettingOverridesRequest
		if err := json.NewDecoder(r.Body).Decode(&settingValues); err != nil {
			logger.Errorf("Failed to parse request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate that we have at least one setting
		if len(settingValues) == 0 {
			logger.Error("No setting-value pairs provided in request")
			response.CreateBadRequestResponse(w)
			return
		}

		// Bulk upsert overrides using the organizationId we looked up
		overrides, err := deps.BulkUpsertGatewayConfigTemplateSettingOverrides(connections.Postgres, organizationId, softwareGatewayId, settingValues)
		if err != nil {
			if errors.Is(err, ErrSoftwareGatewayConfigNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Failed to bulk upsert setting overrides: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(overrides, w)
	}
}

// GetSoftwareGateway retrieves a software gateway by identifier with template-based configuration
var getSoftwareGatewayByIdentifier = func(pg connect.DatabaseExecutor, softwareGatewayId uuid.UUID) (*SoftwareGateway, []ResolvedGatewayConfigSetting, error) {
	// First get the basic gateway info including templateId
	gatewayQuery := `
		SELECT 
			Id,
			Name,
			Description,
			Config,
			TemplateId
		FROM {{SoftwareGateway}}
		WHERE Id = $1 AND IsDeleted = false`

	var gateway SoftwareGateway
	err := pg.QueryRowStruct(&gateway, gatewayQuery, softwareGatewayId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil, ErrSoftwareGatewayConfigNotFound
		}
		logger.Errorf("Failed to get software gateway by identifier: %v", err)
		return nil, nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	// Get all settings with COALESCE logic
	settingsQuery := `
		SELECT 
			base.Setting,
			COALESCE(override.Value, template.Value, base.DefaultValue) as Value,
			CASE 
				WHEN override.Value IS NOT NULL THEN 'override'
				WHEN template.Value IS NOT NULL THEN 'template'
				ELSE 'default'
			END as Source,
			base.Name,
			base.Description,
			base.Format
		FROM {{GatewayConfigTemplateBaseSettings}} base
		LEFT JOIN {{GatewayConfigTemplateSettings}} template 
			ON base.Setting = template.Setting 
			AND template.GatewayConfigTemplateId = $1
		LEFT JOIN {{GatewayConfigTemplateSettingOverrides}} override 
			ON base.Setting = override.Setting 
			AND override.SoftwareGatewayId = $2
		WHERE base.IsDeleted = false
		ORDER BY base.Setting`

	var settings []ResolvedGatewayConfigSetting
	err = pg.QueryGenericSlice(&settings, settingsQuery, gateway.TemplateId, softwareGatewayId)
	if err != nil {
		logger.Errorf("Failed to get template-based settings: %v", err)
		return nil, nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return &gateway, settings, nil
}

// GetSoftwareGatewayByIdentifier is the exported version of getSoftwareGatewayByIdentifier
// It retrieves a software gateway by identifier with template-based configuration
func GetSoftwareGatewayByIdentifier(pg connect.DatabaseExecutor, softwareGatewayId uuid.UUID) (*SoftwareGateway, []ResolvedGatewayConfigSetting, error) {
	return getSoftwareGatewayByIdentifier(pg, softwareGatewayId)
}

// CRUD Operations for GatewayConfigTemplate

// CreateGatewayConfigTemplate creates a new gateway config template
var createGatewayConfigTemplate = func(pg connect.DatabaseExecutor, req *CreateGatewayConfigTemplateRequest) (*GatewayConfigTemplate, error) {
	now := time.Now().UTC()
	query := `
		INSERT INTO {{GatewayConfigTemplate}} (Name, OrganizationId, Description, CreatedAt, UpdatedAt)
		VALUES ($1, $2, $3, $4, $5)
		RETURNING Id, Name, OrganizationId, Description, IsDeleted`

	var template GatewayConfigTemplate
	err := pg.QueryRowStruct(&template, query, req.Name, req.OrganizationId, req.Description, now, now)
	if err != nil {
		logger.Errorf("Failed to create gateway config template: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}
	return &template, nil
}

// GetGatewayConfigTemplate retrieves a gateway config template by ID for a specific organization
var getGatewayConfigTemplate = func(pg connect.DatabaseExecutor, organizationId uuid.UUID, templateId uuid.UUID) (*GatewayConfigTemplate, error) {
	query := `
		SELECT Id, Name, OrganizationId, Description, IsDeleted
		FROM {{GatewayConfigTemplate}}
		WHERE Id = $1 AND OrganizationId = $2 AND IsDeleted = false`

	var template GatewayConfigTemplate
	err := pg.QueryRowStruct(&template, query, templateId, organizationId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrGatewayConfigTemplateNotFound
		}
		logger.Errorf("Failed to get gateway config template: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}
	return &template, nil
}

// UpdateGatewayConfigTemplate updates an existing gateway config template for a specific organization
var updateGatewayConfigTemplate = func(pg connect.DatabaseExecutor, organizationId uuid.UUID, templateId uuid.UUID, req *UpdateGatewayConfigTemplateRequest) (*GatewayConfigTemplate, error) {
	now := time.Now().UTC()
	query := `
		UPDATE {{GatewayConfigTemplate}}
		SET Name = $1, Description = $2, UpdatedAt = $3
		WHERE Id = $4 AND OrganizationId = $5 AND IsDeleted = false
		RETURNING Id, Name, OrganizationId, Description, IsDeleted`

	var template GatewayConfigTemplate
	err := pg.QueryRowStruct(&template, query, req.Name, req.Description, now, templateId, organizationId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrGatewayConfigTemplateNotFound
		}
		logger.Errorf("Failed to update gateway config template: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}
	return &template, nil
}

// DeleteGatewayConfigTemplate soft deletes a gateway config template for a specific organization
var deleteGatewayConfigTemplate = func(pg connect.DatabaseExecutor, organizationId uuid.UUID, templateId uuid.UUID) error {
	now := time.Now().UTC()
	query := `
		UPDATE {{GatewayConfigTemplate}}
		SET IsDeleted = true, UpdatedAt = $1
		WHERE Id = $2 AND OrganizationId = $3 AND IsDeleted = false`

	result, err := pg.Exec(query, now, templateId, organizationId)
	if err != nil {
		logger.Errorf("Failed to delete gateway config template: %v", err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Errorf("Failed to get rows affected: %v", err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	if rowsAffected == 0 {
		return ErrGatewayConfigTemplateNotFound
	}

	return nil
}

// ListGatewayConfigTemplates retrieves gateway config templates for an organization
var listGatewayConfigTemplates = func(pg connect.DatabaseExecutor, organizationId uuid.UUID) ([]GatewayConfigTemplate, error) {
	query := `
		SELECT Id, Name, OrganizationId, Description, IsDeleted
		FROM {{GatewayConfigTemplate}}
		WHERE OrganizationId = $1 AND IsDeleted = false
		ORDER BY Name`

	var templates []GatewayConfigTemplate
	err := pg.QueryGenericSlice(&templates, query, organizationId)
	if err != nil {
		logger.Errorf("Failed to list gateway config templates: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}
	return templates, nil
}

// CRUD Operations for GatewayConfigTemplateSettings (Template Setting Values)

// CreateOrUpdateGatewayConfigTemplateSetting creates or updates a setting value for a template
var createOrUpdateGatewayConfigTemplateSetting = func(pg connect.DatabaseExecutor, organizationId uuid.UUID, req *CreateOrUpdateGatewayConfigTemplateSettingRequest) (*GatewayConfigTemplateSetting, error) {
	// First, verify that the template belongs to the organization
	templateQuery := `
		SELECT 1 FROM {{GatewayConfigTemplate}}
		WHERE Id = $1 AND OrganizationId = $2 AND IsDeleted = false`

	var templateExists struct {
		Exists int `db:"?column?"`
	}
	err := pg.QueryRowStruct(&templateExists, templateQuery, req.GatewayConfigTemplateId, organizationId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrGatewayConfigTemplateNotFound
		}
		logger.Errorf("Failed to verify template ownership: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	// Ensure base setting exists (create if missing)
	ensureBaseSettingQuery := `
		INSERT INTO {{GatewayConfigTemplateBaseSettings}} (setting, name, description, format, defaultValue, isDeleted)
		VALUES ($1, $2, $3, '{"type": "string"}', '', false)
		ON CONFLICT (setting) DO NOTHING`

	settingName := fmt.Sprintf("Dynamic Setting: %s", req.Setting)
	settingDesc := fmt.Sprintf("Dynamically created setting for %s", req.Setting)
	_, err = pg.Exec(ensureBaseSettingQuery, req.Setting, settingName, settingDesc)
	if err != nil {
		logger.Errorf("Failed to ensure base setting exists for %s: %v", req.Setting, err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	// Try to update first
	updateQuery := `
		UPDATE {{GatewayConfigTemplateSettings}}
		SET Value = $3
		WHERE GatewayConfigTemplateId = $1 AND Setting = $2
		RETURNING GatewayConfigTemplateId, Setting, Value`

	var setting GatewayConfigTemplateSetting
	err = pg.QueryRowStruct(&setting, updateQuery, req.GatewayConfigTemplateId, req.Setting, req.Value)
	if err != nil {
		if err == sql.ErrNoRows {
			// Record doesn't exist, insert it
			insertQuery := `
				INSERT INTO {{GatewayConfigTemplateSettings}} (GatewayConfigTemplateId, Setting, Value)
				VALUES ($1, $2, $3)
				RETURNING GatewayConfigTemplateId, Setting, Value`

			err = pg.QueryRowStruct(&setting, insertQuery, req.GatewayConfigTemplateId, req.Setting, req.Value)
			if err != nil {
				logger.Errorf("Failed to insert gateway config template setting: %v", err)
				return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
			}
		} else {
			logger.Errorf("Failed to update gateway config template setting: %v", err)
			return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
		}
	}
	return &setting, nil
}

// GetGatewayConfigTemplateSettings retrieves all setting values for a template
var getGatewayConfigTemplateSettings = func(pg connect.DatabaseExecutor, organizationId uuid.UUID, templateId uuid.UUID) ([]GatewayConfigTemplateSetting, error) {
	// First, verify that the template belongs to the organization
	templateQuery := `
		SELECT 1 FROM {{GatewayConfigTemplate}}
		WHERE Id = $1 AND OrganizationId = $2 AND IsDeleted = false`

	var existsResult struct {
		Exists int `db:"?column?"`
	}
	err := pg.QueryRowStruct(&existsResult, templateQuery, templateId, organizationId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrGatewayConfigTemplateNotFound
		}
		logger.Errorf("Failed to verify template ownership: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	// Now get the settings
	query := `
		SELECT GatewayConfigTemplateId, Setting, Value
		FROM {{GatewayConfigTemplateSettings}}
		WHERE GatewayConfigTemplateId = $1
		ORDER BY Setting`

	var settings []GatewayConfigTemplateSetting
	err = pg.QueryGenericSlice(&settings, query, templateId)
	if err != nil {
		logger.Errorf("Failed to get gateway config template settings: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}
	return settings, nil
}

// DeleteGatewayConfigTemplateSetting deletes a specific setting from a template
var deleteGatewayConfigTemplateSetting = func(pg connect.DatabaseExecutor, organizationId uuid.UUID, templateId uuid.UUID, setting string) error {
	// First, verify that the template belongs to the organization
	templateQuery := `
		SELECT 1 FROM {{GatewayConfigTemplate}}
		WHERE Id = $1 AND OrganizationId = $2 AND IsDeleted = false`

	var existsResult struct {
		Exists int `db:"?column?"`
	}
	err := pg.QueryRowStruct(&existsResult, templateQuery, templateId, organizationId)
	if err != nil {
		if err == sql.ErrNoRows {
			return ErrGatewayConfigTemplateNotFound
		}
		logger.Errorf("Failed to verify template ownership: %v", err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	// Now delete the setting
	query := `
		DELETE FROM {{GatewayConfigTemplateSettings}}
		WHERE GatewayConfigTemplateId = $1 AND Setting = $2`

	result, err := pg.Exec(query, templateId, setting)
	if err != nil {
		logger.Errorf("Failed to delete gateway config template setting: %v", err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Errorf("Failed to get rows affected: %v", err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	if rowsAffected == 0 {
		return ErrGatewayConfigTemplateSettingNotFound
	}

	return nil
}

// BulkReplaceGatewayConfigTemplateSettings replaces all settings for a template
var bulkReplaceGatewayConfigTemplateSettings = func(pg connect.DatabaseExecutor, organizationId uuid.UUID, templateId uuid.UUID, settings []GatewayConfigTemplateSetting) error {
	// Use deadlock retry to handle race conditions and deadlocks
	return connect.WithDeadlockRetry(func() error {
		// First, verify that the template belongs to the organization
		templateQuery := `
			SELECT 1 FROM {{GatewayConfigTemplate}}
			WHERE Id = $1 AND OrganizationId = $2 AND IsDeleted = false`

		var existsResult struct {
			Exists int `db:"?column?"`
		}
		err := pg.QueryRowStruct(&existsResult, templateQuery, templateId, organizationId)
		if err != nil {
			if err == sql.ErrNoRows {
				return ErrGatewayConfigTemplateNotFound
			}
			logger.Errorf("Failed to verify template ownership: %v", err)
			return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
		}

		// Delete all existing settings for this template
		deleteQuery := `DELETE FROM {{GatewayConfigTemplateSettings}} WHERE GatewayConfigTemplateId = $1`
		_, err = pg.Exec(deleteQuery, templateId)
		if err != nil {
			logger.Errorf("Failed to delete existing template settings: %v", err)
			return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
		}

		// Insert new settings
		if len(settings) > 0 {
			// First ensure all base settings exist
			ensureBaseSettingQuery := `
				INSERT INTO {{GatewayConfigTemplateBaseSettings}} (setting, name, description, format, defaultValue, isDeleted)
				VALUES ($1, $2, $3, '{"type": "string"}', '', false)
				ON CONFLICT (setting) DO NOTHING`

			for _, setting := range settings {
				settingName := fmt.Sprintf("Dynamic Setting: %s", setting.Setting)
				settingDesc := fmt.Sprintf("Dynamically created setting for %s", setting.Setting)
				_, err = pg.Exec(ensureBaseSettingQuery, setting.Setting, settingName, settingDesc)
				if err != nil {
					logger.Errorf("Failed to ensure base setting exists for %s: %v", setting.Setting, err)
					return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
				}
			}

			insertQuery := `
				INSERT INTO {{GatewayConfigTemplateSettings}} (GatewayConfigTemplateId, Setting, Value)
				VALUES ($1, $2, $3)`

			for _, setting := range settings {
				_, err = pg.Exec(insertQuery, templateId, setting.Setting, setting.Value)
				if err != nil {
					logger.Errorf("Failed to insert template setting: %v", err)
					return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
				}
			}
		}

		return nil
	})
}

// CRUD Operations for GatewayConfigTemplateSettingOverrides

// CreateOrUpdateGatewayConfigTemplateSettingOverride creates or updates a setting override
var createOrUpdateGatewayConfigTemplateSettingOverride = func(pg connect.DatabaseExecutor, organizationId uuid.UUID, req *CreateOrUpdateGatewayConfigTemplateSettingOverrideRequest) (*GatewayConfigTemplateSettingOverride, error) {
	// First, verify that the software gateway belongs to the organization
	gatewayQuery := `
		SELECT 1 FROM {{SoftwareGateway}}
		WHERE Id = $1 AND OrganizationId = $2`

	var gatewayExists struct {
		Exists int `db:"?column?"`
	}
	err := pg.QueryRowStruct(&gatewayExists, gatewayQuery, req.SoftwareGatewayId, organizationId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrSoftwareGatewayConfigNotFound
		}
		logger.Errorf("Failed to verify software gateway ownership: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	// Ensure base setting exists (create if missing)
	ensureBaseSettingQuery := `
		INSERT INTO {{GatewayConfigTemplateBaseSettings}} (setting, name, description, format, defaultValue, isDeleted)
		VALUES ($1, $2, $3, '{"type": "string"}', '', false)
		ON CONFLICT (setting) DO NOTHING`

	settingName := fmt.Sprintf("Dynamic Setting: %s", req.Setting)
	settingDesc := fmt.Sprintf("Dynamically created setting for %s", req.Setting)
	_, err = pg.Exec(ensureBaseSettingQuery, req.Setting, settingName, settingDesc)
	if err != nil {
		logger.Errorf("Failed to ensure base setting exists for %s: %v", req.Setting, err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	// Try to update first
	updateQuery := `
		UPDATE {{GatewayConfigTemplateSettingOverrides}}
		SET Value = $3
		WHERE SoftwareGatewayId = $1 AND Setting = $2
		RETURNING SoftwareGatewayId, Setting, Value`

	var override GatewayConfigTemplateSettingOverride
	err = pg.QueryRowStruct(&override, updateQuery, req.SoftwareGatewayId, req.Setting, req.Value)
	if err != nil {
		if err == sql.ErrNoRows {
			// Record doesn't exist, insert it
			insertQuery := `
				INSERT INTO {{GatewayConfigTemplateSettingOverrides}} (SoftwareGatewayId, Setting, Value)
				VALUES ($1, $2, $3)
				RETURNING SoftwareGatewayId, Setting, Value`

			err = pg.QueryRowStruct(&override, insertQuery, req.SoftwareGatewayId, req.Setting, req.Value)
			if err != nil {
				logger.Errorf("Failed to insert gateway config template setting override: %v", err)
				return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
			}
		} else {
			logger.Errorf("Failed to update gateway config template setting override: %v", err)
			return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
		}
	}
	return &override, nil
}

// GetGatewayConfigTemplateSettingOverrides retrieves all setting overrides for a software gateway
var getGatewayConfigTemplateSettingOverrides = func(pg connect.DatabaseExecutor, organizationId uuid.UUID, softwareGatewayId uuid.UUID) ([]GatewayConfigTemplateSettingOverride, error) {
	// First, verify that the software gateway belongs to the organization
	gatewayQuery := `
		SELECT 1 FROM {{SoftwareGateway}}
		WHERE Id = $1 AND OrganizationId = $2`

	var existsResult struct {
		Exists int `db:"?column?"`
	}
	err := pg.QueryRowStruct(&existsResult, gatewayQuery, softwareGatewayId, organizationId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrSoftwareGatewayConfigNotFound
		}
		logger.Errorf("Failed to verify software gateway ownership: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	// Now get the overrides
	query := `
		SELECT SoftwareGatewayId, Setting, Value
		FROM {{GatewayConfigTemplateSettingOverrides}}
		WHERE SoftwareGatewayId = $1
		ORDER BY Setting`

	var overrides []GatewayConfigTemplateSettingOverride
	err = pg.QueryGenericSlice(&overrides, query, softwareGatewayId)
	if err != nil {
		logger.Errorf("Failed to get gateway config template setting overrides: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}
	return overrides, nil
}

// DeleteGatewayConfigTemplateSettingOverride deletes a specific setting override
var deleteGatewayConfigTemplateSettingOverride = func(pg connect.DatabaseExecutor, organizationId uuid.UUID, softwareGatewayId uuid.UUID, setting string) error {
	// First, verify that the software gateway belongs to the organization
	gatewayQuery := `
		SELECT 1 FROM {{SoftwareGateway}}
		WHERE Id = $1 AND OrganizationId = $2`

	var existsResult struct {
		Exists int `db:"?column?"`
	}
	err := pg.QueryRowStruct(&existsResult, gatewayQuery, softwareGatewayId, organizationId)
	if err != nil {
		if err == sql.ErrNoRows {
			return ErrSoftwareGatewayConfigNotFound
		}
		logger.Errorf("Failed to verify software gateway ownership: %v", err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	// Now delete the override
	query := `
		DELETE FROM {{GatewayConfigTemplateSettingOverrides}}
		WHERE SoftwareGatewayId = $1 AND Setting = $2`

	result, err := pg.Exec(query, softwareGatewayId, setting)
	if err != nil {
		logger.Errorf("Failed to delete gateway config template setting override: %v", err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Errorf("Failed to get rows affected: %v", err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	if rowsAffected == 0 {
		return ErrGatewayConfigTemplateSettingOverrideNotFound
	}

	return nil
}

// BulkReplaceGatewayConfigTemplateSettingOverrides replaces all setting overrides for a software gateway
var bulkReplaceGatewayConfigTemplateSettingOverrides = func(pg connect.DatabaseExecutor, organizationId uuid.UUID, softwareGatewayId uuid.UUID, overrides []GatewayConfigTemplateSettingOverride) error {
	// Use deadlock retry to handle race conditions and deadlocks
	return connect.WithDeadlockRetry(func() error {
		// First, verify that the software gateway belongs to the organization
		gatewayQuery := `
			SELECT 1 FROM {{SoftwareGateway}}
			WHERE Id = $1 AND OrganizationId = $2`

		var existsResult struct {
			Exists int `db:"?column?"`
		}
		err := pg.QueryRowStruct(&existsResult, gatewayQuery, softwareGatewayId, organizationId)
		if err != nil {
			if err == sql.ErrNoRows {
				return ErrSoftwareGatewayConfigNotFound
			}
			logger.Errorf("Failed to verify software gateway ownership: %v", err)
			return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
		}

		// Delete all existing overrides for this software gateway
		deleteQuery := `DELETE FROM {{GatewayConfigTemplateSettingOverrides}} WHERE SoftwareGatewayId = $1`
		_, err = pg.Exec(deleteQuery, softwareGatewayId)
		if err != nil {
			logger.Errorf("Failed to delete existing setting overrides: %v", err)
			return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
		}

		// Insert new overrides
		if len(overrides) > 0 {
			insertQuery := `
				INSERT INTO {{GatewayConfigTemplateSettingOverrides}} (SoftwareGatewayId, Setting, Value)
				VALUES ($1, $2, $3)`

			for _, override := range overrides {
				_, err = pg.Exec(insertQuery, softwareGatewayId, override.Setting, override.Value)
				if err != nil {
					logger.Errorf("Failed to insert setting override: %v", err)
					return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
				}
			}
		}

		return nil
	})
}

// BulkUpsertGatewayConfigTemplateSettingOverrides upserts (insert or update) setting overrides for a software gateway
var bulkUpsertGatewayConfigTemplateSettingOverrides = func(pg connect.DatabaseExecutor, organizationId uuid.UUID, softwareGatewayId uuid.UUID, settingValues map[string]string) ([]GatewayConfigTemplateSettingOverride, error) {
	// First, verify that the software gateway belongs to the organization
	gatewayQuery := `
		SELECT 1 FROM {{SoftwareGateway}}
		WHERE Id = $1 AND OrganizationId = $2`

	var existsResult struct {
		Exists int `db:"?column?"`
	}
	err := pg.QueryRowStruct(&existsResult, gatewayQuery, softwareGatewayId, organizationId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrSoftwareGatewayConfigNotFound
		}
		logger.Errorf("Failed to verify software gateway ownership: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	// Upsert each setting-value pair
	var resultOverrides []GatewayConfigTemplateSettingOverride

	for setting, value := range settingValues {
		// Validate that setting and value are not empty
		if setting == "" || value == "" {
			continue // Skip empty settings or values
		}

		// Ensure base setting exists (create if missing)
		ensureBaseSettingQuery := `
			INSERT INTO {{GatewayConfigTemplateBaseSettings}} (setting, name, description, format, defaultValue, isDeleted)
			VALUES ($1, $2, $3, '{"type": "string"}', '', false)
			ON CONFLICT (setting) DO NOTHING`

		settingName := fmt.Sprintf("Dynamic Setting: %s", setting)
		settingDesc := fmt.Sprintf("Dynamically created setting for %s", setting)
		_, err = pg.Exec(ensureBaseSettingQuery, setting, settingName, settingDesc)
		if err != nil {
			logger.Errorf("Failed to ensure base setting exists for %s: %v", setting, err)
			return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
		}

		// Try to update first
		updateQuery := `
			UPDATE {{GatewayConfigTemplateSettingOverrides}}
			SET Value = $3
			WHERE SoftwareGatewayId = $1 AND Setting = $2
			RETURNING SoftwareGatewayId, Setting, Value`

		var override GatewayConfigTemplateSettingOverride
		err = pg.QueryRowStruct(&override, updateQuery, softwareGatewayId, setting, value)
		if err != nil {
			if err == sql.ErrNoRows {
				// Record doesn't exist, insert it
				insertQuery := `
					INSERT INTO {{GatewayConfigTemplateSettingOverrides}} (SoftwareGatewayId, Setting, Value)
					VALUES ($1, $2, $3)
					RETURNING SoftwareGatewayId, Setting, Value`

				err = pg.QueryRowStruct(&override, insertQuery, softwareGatewayId, setting, value)
				if err != nil {
					logger.Errorf("Failed to insert setting override for setting %s: %v", setting, err)
					return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
				}
			} else {
				logger.Errorf("Failed to update setting override for setting %s: %v", setting, err)
				return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
			}
		}

		resultOverrides = append(resultOverrides, override)
	}

	return resultOverrides, nil
}

// TODO: Remove this legacy function when GUI is updated to include organizationId in URL
// Legacy support: getOrganizationIdBySoftwareGatewayId looks up organizationId from softwareGatewayId
var getOrganizationIdBySoftwareGatewayId = func(pg connect.DatabaseExecutor, softwareGatewayId uuid.UUID) (uuid.UUID, error) {
	query := `
		SELECT OrganizationId 
		FROM {{SoftwareGateway}}
		WHERE Id = $1 AND IsDeleted = false`

	var result struct {
		OrganizationId uuid.UUID `db:"organizationid"`
	}
	err := pg.QueryRowStruct(&result, query, softwareGatewayId)
	if err != nil {
		if err == sql.ErrNoRows {
			return uuid.Nil, ErrSoftwareGatewayConfigNotFound
		}
		logger.Errorf("Failed to get organization ID for software gateway: %v", err)
		return uuid.Nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return result.OrganizationId, nil
}

// === Base Settings HTTP Handlers ===

// CreateBaseSettingHandlerWithDeps creates a new base setting
func CreateBaseSettingHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Parse request body
		var req CreateGatewayConfigTemplateBaseSettingRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			logger.Errorf("Failed to parse request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Basic validation
		if req.Setting == "" || req.Name == "" || req.Description == "" || req.DefaultValue == "" || req.Format == "" {
			logger.Error("Missing required fields in request")
			response.CreateBadRequestResponse(w)
			return
		}

		// Create base setting
		baseSetting := GatewayConfigTemplateBaseSetting{
			Setting:      req.Setting,
			Name:         req.Name,
			Description:  req.Description,
			DefaultValue: req.DefaultValue,
			Format:       req.Format,
		}

		err = deps.CreateGatewayConfigTemplateBaseSetting(connections.Postgres, baseSetting)
		if err != nil {
			logger.Errorf("Failed to create gateway config template base setting: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(baseSetting, w)
	}
}

// GetBaseSettingHandlerWithDeps retrieves a specific base setting
func GetBaseSettingHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Extract setting from path
		vars := mux.Vars(r)
		setting := vars["setting"]
		if setting == "" {
			logger.Error("Missing setting parameter")
			response.CreateBadRequestResponse(w)
			return
		}

		// Get base setting
		baseSetting, err := deps.GetGatewayConfigTemplateBaseSetting(connections.Postgres, setting)
		if err != nil {
			if errors.Is(err, ErrGatewayConfigTemplateBaseSettingNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Failed to get gateway config template base setting: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(baseSetting, w)
	}
}

// UpdateBaseSettingHandlerWithDeps updates an existing base setting
func UpdateBaseSettingHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Extract setting from path
		vars := mux.Vars(r)
		setting := vars["setting"]
		if setting == "" {
			logger.Error("Missing setting parameter")
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse request body
		var req UpdateGatewayConfigTemplateBaseSettingRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			logger.Errorf("Failed to parse request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Basic validation
		if req.Name == "" || req.Description == "" || req.DefaultValue == "" || req.Format == "" {
			logger.Error("Missing required fields in request")
			response.CreateBadRequestResponse(w)
			return
		}

		// Update base setting
		baseSetting := GatewayConfigTemplateBaseSetting{
			Setting:      setting, // Use setting from URL path
			Name:         req.Name,
			Description:  req.Description,
			DefaultValue: req.DefaultValue,
			Format:       req.Format,
		}

		err = deps.UpdateGatewayConfigTemplateBaseSetting(connections.Postgres, setting, baseSetting)
		if err != nil {
			if errors.Is(err, ErrGatewayConfigTemplateBaseSettingNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Failed to update gateway config template base setting: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(baseSetting, w)
	}
}

// DeleteBaseSettingHandlerWithDeps deletes a base setting
func DeleteBaseSettingHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Extract setting from path
		vars := mux.Vars(r)
		setting := vars["setting"]
		if setting == "" {
			logger.Error("Missing setting parameter")
			response.CreateBadRequestResponse(w)
			return
		}

		// Delete base setting
		err = deps.DeleteGatewayConfigTemplateBaseSetting(connections.Postgres, setting)
		if err != nil {
			if errors.Is(err, ErrGatewayConfigTemplateBaseSettingNotFound) {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Failed to delete gateway config template base setting: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(nil, w)
	}
}

// ListBaseSettingsHandlerWithDeps retrieves all base settings
func ListBaseSettingsHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// List base settings
		baseSettings, err := deps.ListGatewayConfigTemplateBaseSettings(connections.Postgres)
		if err != nil {
			logger.Errorf("Failed to list gateway config template base settings: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(baseSettings, w)
	}
}

// === Base Settings CRUD Functions ===

// CreateGatewayConfigTemplateBaseSetting creates a new base setting
var createGatewayConfigTemplateBaseSetting = func(pg connect.DatabaseExecutor, baseSetting GatewayConfigTemplateBaseSetting) error {
	now := time.Now().UTC()
	query := `
		INSERT INTO {{GatewayConfigTemplateBaseSettings}} (
			Setting, Name, Description, DefaultValue, Format, CreatedAt, UpdatedAt
		) VALUES ($1, $2, $3, $4, $5, $6, $7)`

	_, err := pg.Exec(query, baseSetting.Setting, baseSetting.Name, baseSetting.Description,
		baseSetting.DefaultValue, baseSetting.Format, now, now)
	if err != nil {
		logger.Errorf("Failed to create gateway config template base setting: %v", err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return nil
}

// GetGatewayConfigTemplateBaseSetting retrieves a specific base setting
var getGatewayConfigTemplateBaseSetting = func(pg connect.DatabaseExecutor, setting string) (*GatewayConfigTemplateBaseSetting, error) {
	query := `
		SELECT Setting, Name, Description, DefaultValue, Format, IsDeleted
		FROM {{GatewayConfigTemplateBaseSettings}}
		WHERE Setting = $1 AND IsDeleted = false`

	var baseSetting GatewayConfigTemplateBaseSetting
	err := pg.QueryRowStruct(&baseSetting, query, setting)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrGatewayConfigTemplateBaseSettingNotFound
		}
		logger.Errorf("Failed to get gateway config template base setting: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return &baseSetting, nil
}

// UpdateGatewayConfigTemplateBaseSetting updates an existing base setting
var updateGatewayConfigTemplateBaseSetting = func(pg connect.DatabaseExecutor, setting string, baseSetting GatewayConfigTemplateBaseSetting) error {
	now := time.Now().UTC()
	query := `
		UPDATE {{GatewayConfigTemplateBaseSettings}}
		SET Name = $1, Description = $2, DefaultValue = $3, Format = $4, UpdatedAt = $5
		WHERE Setting = $6 AND IsDeleted = false`

	result, err := pg.Exec(query, baseSetting.Name, baseSetting.Description,
		baseSetting.DefaultValue, baseSetting.Format, now, setting)
	if err != nil {
		logger.Errorf("Failed to update gateway config template base setting: %v", err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Errorf("Failed to get rows affected: %v", err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	if rowsAffected == 0 {
		return ErrGatewayConfigTemplateBaseSettingNotFound
	}

	return nil
}

// DeleteGatewayConfigTemplateBaseSetting soft deletes a base setting
var deleteGatewayConfigTemplateBaseSetting = func(pg connect.DatabaseExecutor, setting string) error {
	now := time.Now().UTC()
	query := `
		UPDATE {{GatewayConfigTemplateBaseSettings}}
		SET IsDeleted = true, UpdatedAt = $1
		WHERE Setting = $2 AND IsDeleted = false`

	result, err := pg.Exec(query, now, setting)
	if err != nil {
		logger.Errorf("Failed to delete gateway config template base setting: %v", err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Errorf("Failed to get rows affected: %v", err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	if rowsAffected == 0 {
		return ErrGatewayConfigTemplateBaseSettingNotFound
	}

	return nil
}

// ListGatewayConfigTemplateBaseSettings retrieves all base settings
var listGatewayConfigTemplateBaseSettings = func(pg connect.DatabaseExecutor) ([]GatewayConfigTemplateBaseSetting, error) {
	query := `
		SELECT Setting, Name, Description, DefaultValue, Format, IsDeleted
		FROM {{GatewayConfigTemplateBaseSettings}}
		WHERE IsDeleted = false
		ORDER BY Setting`

	var baseSettings []GatewayConfigTemplateBaseSetting
	err := pg.QueryGenericSlice(&baseSettings, query)
	if err != nil {
		logger.Errorf("Failed to list gateway config template base settings: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return baseSettings, nil
}

// Handler is the production-ready HTTP handler using default dependencies.
var (
	// Software Gateway Config Handler
	GetByIdentifierHandler = GetByIdentifierHandlerWithDeps(HandlerDeps{
		GetConnections:           connect.GetConnections,
		GetSoftwareGatewayConfig: getSoftwareGatewayByIdentifier,
	})

	// Template Management Handlers
	CreateTemplateHandler = CreateTemplateHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, CreateGatewayConfigTemplate: createGatewayConfigTemplate})
	GetTemplateHandler    = GetTemplateHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, GetGatewayConfigTemplate: getGatewayConfigTemplate})
	UpdateTemplateHandler = UpdateTemplateHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, UpdateGatewayConfigTemplate: updateGatewayConfigTemplate})
	DeleteTemplateHandler = DeleteTemplateHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, DeleteGatewayConfigTemplate: deleteGatewayConfigTemplate})
	ListTemplatesHandler  = ListTemplatesHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, ListGatewayConfigTemplates: listGatewayConfigTemplates})

	// Template Settings Handlers
	CreateOrUpdateTemplateSettingHandler = CreateOrUpdateTemplateSettingHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, CreateOrUpdateGatewayConfigTemplateSetting: createOrUpdateGatewayConfigTemplateSetting})
	GetTemplateSettingsHandler           = GetTemplateSettingsHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, GetGatewayConfigTemplateSettings: getGatewayConfigTemplateSettings})
	DeleteTemplateSettingHandler         = DeleteTemplateSettingHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, DeleteGatewayConfigTemplateSetting: deleteGatewayConfigTemplateSetting})
	BulkReplaceTemplateSettingsHandler   = BulkReplaceTemplateSettingsHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, BulkReplaceGatewayConfigTemplateSettings: bulkReplaceGatewayConfigTemplateSettings})

	// Override Settings Handlers
	CreateOrUpdateOverrideHandler = CreateOrUpdateOverrideHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, CreateOrUpdateGatewayConfigTemplateSettingOverride: createOrUpdateGatewayConfigTemplateSettingOverride})
	GetOverridesHandler           = GetOverridesHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, GetGatewayConfigTemplateSettingOverrides: getGatewayConfigTemplateSettingOverrides})
	DeleteOverrideHandler         = DeleteOverrideHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, DeleteGatewayConfigTemplateSettingOverride: deleteGatewayConfigTemplateSettingOverride})
	BulkReplaceOverridesHandler   = BulkReplaceOverridesHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, BulkReplaceGatewayConfigTemplateSettingOverrides: bulkReplaceGatewayConfigTemplateSettingOverrides})
	BulkUpsertOverridesHandler    = BulkUpsertOverridesHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, BulkUpsertGatewayConfigTemplateSettingOverrides: bulkUpsertGatewayConfigTemplateSettingOverrides})

	// TODO: Remove this legacy handler when GUI is updated to include organizationId in URL
	// Legacy support: BulkUpsertOverridesLegacyHandler for sites that don't pass organizationId in URL
	BulkUpsertOverridesLegacyHandler = BulkUpsertOverridesLegacyHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, BulkUpsertGatewayConfigTemplateSettingOverrides: bulkUpsertGatewayConfigTemplateSettingOverrides, GetOrganizationIdBySoftwareGatewayId: getOrganizationIdBySoftwareGatewayId})

	// Base Settings Handlers
	CreateBaseSettingHandler = CreateBaseSettingHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, CreateGatewayConfigTemplateBaseSetting: createGatewayConfigTemplateBaseSetting})
	GetBaseSettingHandler    = GetBaseSettingHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, GetGatewayConfigTemplateBaseSetting: getGatewayConfigTemplateBaseSetting})
	UpdateBaseSettingHandler = UpdateBaseSettingHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, UpdateGatewayConfigTemplateBaseSetting: updateGatewayConfigTemplateBaseSetting})
	DeleteBaseSettingHandler = DeleteBaseSettingHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, DeleteGatewayConfigTemplateBaseSetting: deleteGatewayConfigTemplateBaseSetting})
	ListBaseSettingsHandler  = ListBaseSettingsHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, ListGatewayConfigTemplateBaseSettings: listGatewayConfigTemplateBaseSettings})
)
