package starlark

import (
	"encoding/base64"
	"errors"
	"fmt"
	"os"

	"go.starlark.net/starlark"
	"go.starlark.net/syntax"
)

var (
	ErrMissingFunctionSource = errors.New("STARLARK_FUNCTION env is required")
	ErrNoFunctionFound       = errors.New("no callable function found in Starlark module")
	ErrInvalidSignature      = errors.New("function must accept one string and return string")
	ErrBase64Decode          = errors.New("failed to decode base64 encoded Starlark function source")
)

// runtimeService is the concrete implementation of Service.
type runtimeService struct {
	thread *starlark.Thread
	fn     starlark.Callable
}

// NewFromEnv constructs a Service by reading Starlark source from
// the STARLARK_FUNCTION environment variable.
func NewFromEnv() (*runtimeService, error) {
	src := os.Getenv("STARLARK_FUNCTION")
	if src == "" {
		return nil, ErrMissingFunctionSource
	}
	// The environmental variable is base64 encoded due to potential issues with special characters.
	decodedSrc, err := base64.StdEncoding.DecodeString(src)
	if err != nil {
		return nil, fmt.Errorf("%w: %w", ErrBase64Decode, err)
	}
	return NewFromSource(string(decodedSrc))
}

// NewFromSource constructs a Service from raw Starlark source code.
// The source must define a callable (preferably named "process").
func NewFromSource(source string) (*runtimeService, error) {
	opts := &syntax.FileOptions{}
	if _, err := opts.Parse("function.star", source, 0); err != nil {
		return nil, fmt.Errorf("parse error: %w", err)
	}

	thread := &starlark.Thread{Name: "starlark_function"}
	module, err := starlark.ExecFileOptions(&syntax.FileOptions{}, thread, "function.star", source, nil)
	if err != nil {
		return nil, fmt.Errorf("exec error: %w", err)
	}

	var callable starlark.Callable
	if v, ok := module["main"]; ok {
		callable, ok = v.(starlark.Callable)
		if !ok {
			return nil, ErrInvalidSignature
		}
	}

	if callable == nil {
		return nil, ErrNoFunctionFound
	}

	// Validate by invoking with a sample string and ensuring string return.
	if err := validateCallable(callable); err != nil {
		return nil, fmt.Errorf("invalid callable: %w", err)
	}

	return &runtimeService{thread: thread, fn: callable}, nil
}

// ExchangeCode applies the callable to transform challenge code to response code.
func (s *runtimeService) ExchangeCode(challengeCode string) (string, error) {
	// Create a new thread for each execution to ensure thread safety
	thread := &starlark.Thread{Name: "starlark_execution"}
	result, err := starlark.Call(thread, s.fn, starlark.Tuple{starlark.String(challengeCode)}, nil)
	if err != nil {
		return "", err
	}
	str, ok := result.(starlark.String)
	if !ok {
		return "", ErrInvalidSignature
	}
	return str.GoString(), nil
}

func validateCallable(fn starlark.Callable) error {
	t := &starlark.Thread{Name: "starlark_validation"}
	out, err := starlark.Call(t, fn, starlark.Tuple{starlark.String("00000000")}, nil)
	if err != nil {
		return err
	}
	if _, ok := out.(starlark.String); !ok {
		return ErrInvalidSignature
	}
	return nil
}
