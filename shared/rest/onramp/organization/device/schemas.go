package device

import (
	"time"

	"github.com/google/uuid"
)

// The data model for the API response
type DeviceResponse struct {
	Id                uuid.UUID  `json:"id" db:"id"`
	OrigId            int64      `json:"origId" db:"origid"`
	SoftwareGatewayId *uuid.UUID `json:"softwareGatewayId" db:"softwaregatewayid"`
	LocationId        *uuid.UUID `json:"locationId" db:"locationid"`
	OrganizationId    uuid.UUID  `json:"organizationId" db:"organizationid"`
	Name              string     `json:"name" db:"name"`
	Description       string     `json:"description" db:"description"`
	IpAddress         string     `json:"ipAddress" db:"ipaddress"`
	Port              int        `json:"port" db:"port"`
	Type              string     `json:"type" db:"type"`
	FlushConnectionMs int        `json:"flushConnectionMs" db:"flushconnectionms"`
	EnableRealtime    bool       `json:"enableRealtime" db:"enablerealtime"`
	IsEnabled         bool       `json:"isEnabled" db:"isenabled"`
	CreatedAt         time.Time  `json:"createdAt" db:"createdat"`
	UpdatedAt         time.Time  `json:"updatedAt" db:"updatedat"`
}

// Data model for the API response
type DevicesResponse struct {
	Devices []DeviceResponse `json:"devices"`
}

// The request body for updating a device
type UpdateRequest struct {
	Name              *string    `json:"name"`
	Description       *string    `json:"description"`
	IpAddress         *string    `json:"ipAddress"`
	Port              *int       `json:"port"`
	Type              *string    `json:"type"`
	FlushConnectionMs *int       `json:"flushConnectionMs"`
	EnableRealtime    *bool      `json:"enableRealtime"`
	LocationId        *uuid.UUID `json:"locationId"`
	SoftwareGatewayId *uuid.UUID `json:"softwareGatewayId"`
	IsEnabled         *bool      `json:"isEnabled"`
}

// Schema for update device
type UpdateDeviceSchema struct {
	Name              string
	Description       string
	IpAddress         string
	Port              int
	Type              string
	FlushConnectionMs int
	EnableRealtime    bool
	LocationId        *uuid.UUID
	SoftwareGatewayId *uuid.UUID
	IsEnabled         bool
}
