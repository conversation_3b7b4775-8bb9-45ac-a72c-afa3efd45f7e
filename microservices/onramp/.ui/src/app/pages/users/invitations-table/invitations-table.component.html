<div class="invitations-container">
  <div class="form-table-invitations">
    <div class="title-invitations-table">
      <h1 id="title-invitations">Invitations</h1>
      <span>These are invitations to the <b>Synapse</b> Organization that have been created as well as their current
        state.</span>
    </div>
    <nz-table id="invitations-list" class="mt-10" #basicTable nzBordered [nzData]="listDataInvitations"
      nzShowSizeChanger nzShowPagination [nzLoading]="isLoadingTable">
      <thead>
        <tr>
          <th nzWidth="15%">E-mail</th>
          <th nzWidth="15%">Created</th>
          <th nzWidth="15%">Times Notified</th>
          <th nzWidth="15%">Last Notification</th>
          <th nzWidth="15%">Accepted</th>
          <th nzWidth="8%">Action</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of basicTable.data; let i = index"
          [ngClass]="data.id === highlightedRowId ? 'highlight-row' : 'default-row'">
          <td>{{ data.email }}</td>
          <td>{{ data.created | date: 'MM-dd-yyyy HH:mm:ss' : 'UTC' }}</td>
          <td>{{ data.retrycount }}</td>
          <td>{{ data.updated | date: 'MM-dd-yyyy HH:mm:ss' : 'UTC' }}</td>
          <td>{{ capitalize(data.status) }}</td>
          <td>
            <div class="btn-action">
              <button id="btn-resend-invite" nz-button nzType="primary" (click)="handleResend(data)"
                [nzLoading]="isLoadingResend[i]" nz-tooltip nzTooltipTitle="Re-send" class="mr-10 br-8">
                <nz-icon nzType="send" nzTheme="outline" />
              </button>
              <button id="btn-delete-invite" nz-button nzType="primary" (click)="handleDelete(data)" nzDanger nz-tooltip
                nzTooltipTitle="Delete" class="br-8">
                <nz-icon nzType="delete" nzTheme="outline" />
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </div>
</div>