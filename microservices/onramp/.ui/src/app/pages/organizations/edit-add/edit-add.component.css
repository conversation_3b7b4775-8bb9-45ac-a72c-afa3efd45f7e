::ng-deep .ant-modal .ant-modal-content {
  border-radius: 8px;
}

::ng-deep .ant-modal .ant-modal-header {
  border-bottom: unset;
  border-radius: 8px 8px 0 0;
  padding: 24px;
}

::ng-deep .ant-modal .ant-modal-header .content-header span {
  margin-top: 4px;
  font-size: 16px;
  color: #757575;
}

.modal-add-organization label {
  font-size: 16px;
  color: #1E1E1E;
}

.modal-add-organization ::ng-deep .ant-form-item .ant-form-item-control-input-content {
  display: grid;
  gap: 8px;
}

.modal-add-organization .select-template ::ng-deep .ant-select-selector {
  height: 40px;
  border-radius: 8px;
}

.button-action {
  display: grid;
  gap: 10px;
}

.btn-submit,
.btn-delete {
  width: 100%;
}