const { When, Then } = require('@cucumber/cucumber');
const { By, until } = require('selenium-webdriver');
const assert = require('assert');

Then('The Users In Organization table contains at least one record', async function () {
  const rows = await this.driver.findElements(By.css('#users-list tr'));
  if (rows.length <= 1) {
    throw new Error('No records found in the permissions table');
  }
});

When('I fill in the email field with {string}', async function (name) {
  let nameField = await this.driver.wait(until.elementLocated(By.id("input-user-email")), 10000)
  let nameEl = await this.driver.wait(until.elementIsVisible(nameField), 3000);
  await nameEl.clear();
  await nameEl.sendKeys(name);
});

When('I fill in the message field with {string}', async function (description) {
  let descriptionField = await this.driver.wait(until.elementLocated(By.id("input-user-message")), 10000)
  let descriptionEl = await this.driver.wait(until.elementIsVisible(descriptionField), 3000);
  await descriptionEl.clear();
  await descriptionEl.sendKeys(description);
});

When('I find the row with name {string} and click the Users button', async function (orgName) {
  const row = await this.driver.wait(until.elementLocated(By.xpath(`//tr[contains(.,'${orgName}')]`)), 10000);
  const usersButton = await row.findElement(By.id('btn-users'));
  await usersButton.click();
});

Then('I see the delete confirmation modal', async function () {
  const modal = await this.driver.wait(until.elementLocated(By.className('ant-modal-confirm')), 15000);
  const modalTitle = await modal.findElement(By.className('ant-modal-confirm-title')).getText();
  assert.strictEqual(modalTitle.includes('Delete Confirmation'), true);
});

When('I confirm the deletion', async function () {
  const okButton = await this.driver.findElement(By.xpath("//button[contains(@class, 'ant-btn-primary') and contains(., 'OK')]"));
  await okButton.click();
});

Then('The user should be deleted', async function () {
  await this.driver.wait(until.stalenessOf(await this.driver.findElement(By.xpath("//tr[contains(.,'<EMAIL>')]"))), 10000);
});
Then('I see the message invitation {string}', async function (expectedMessage) {
  const notifications = await this.driver.wait(until.elementsLocated(By.css('.ant-notification-notice-message, .ant-message-notice-content, [role="alert"]')), 10000);
  let found = false;
  for (const notification of notifications) {
    const text = await notification.getText();
    if (text.includes(expectedMessage)) {
      found = true;
      break;
    }
  }
  assert.strictEqual(found, true, `No notification found "${expectedMessage}" in the announcements`);
});