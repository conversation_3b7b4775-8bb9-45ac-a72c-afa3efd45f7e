package middlewares

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPanicMiddleware(t *testing.T) {
	t.<PERSON>llel()

	tests := []struct {
		name               string
		setupHandler       func() http.Handler
		expectedStatusCode int
		expectPanic        bool
	}{
		{
			name: "Normal request - no panic",
			setupHandler: func() http.Handler {
				return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
					w.WriteHeader(http.StatusOK)
					w.Write([]byte("success"))
				})
			},
			expectedStatusCode: http.StatusOK,
			expectPanic:        false,
		},
		{
			name: "<PERSON><PERSON> panics",
			setupHandler: func() http.Handler {
				return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
					panic("test panic")
				})
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectPanic:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Arrange
			handler := tt.setupHandler()
			middleware := PanicMiddleware(handler)
			req := httptest.NewRequest(http.MethodGet, "/test", nil)
			w := httptest.NewRecorder()

			// Act
			middleware.ServeHTTP(w, req)

			// Assert
			assert.Equal(t, tt.expectedStatusCode, w.Code, "Status code should match expected")

			if tt.expectPanic {
				// When panic occurs, the response should be an internal server error
				assert.Equal(t, http.StatusInternalServerError, w.Code, "Panic should result in internal server error")
			} else {
				// Normal request should work as expected
				assert.Equal(t, http.StatusOK, w.Code, "Normal request should return OK")
				assert.Contains(t, w.Body.String(), "success", "Response body should contain expected content")
			}
		})
	}
}
