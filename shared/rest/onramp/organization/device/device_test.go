package device

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
	"synapse-its.com/shared/rest/onramp/helper"
)

// Test_getDeviceByID tests the getDeviceByID function
func Test_getDeviceByID(t *testing.T) {
	t.<PERSON>llel()

	// Test data
	orgId := uuid.MustParse("123e4567-e89b-12d3-a456-************")
	deviceId := uuid.MustParse("987fcdeb-51a2-43d1-b654-************")
	softwareGatewayId := uuid.MustParse("11111111-1111-1111-1111-111111111111")
	locationId := uuid.MustParse("*************-2222-2222-************")
	createdAt := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
	updatedAt := time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC)

	expectedDevice := &DeviceResponse{
		Id:                deviceId,
		OrigId:            12345,
		SoftwareGatewayId: &softwareGatewayId,
		LocationId:        &locationId,
		OrganizationId:    orgId,
		Name:              "Test Device",
		Description:       "Test Device Description",
		IpAddress:         "*************",
		Port:              8080,
		Type:              "EDI_LEGACY",
		FlushConnectionMs: 5000,
		EnableRealtime:    true,
		IsEnabled:         true,
		CreatedAt:         createdAt,
		UpdatedAt:         updatedAt,
	}

	tests := []struct {
		name               string
		orgId              uuid.UUID
		deviceId           uuid.UUID
		mockQueryRowStruct func(dest interface{}, query string, args ...interface{}) error
		expectedResult     *DeviceResponse
		expectedErr        error
		wantErr            bool
	}{
		{
			name:     "successful_device_retrieval",
			orgId:    orgId,
			deviceId: deviceId,
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				// Mock successful query result
				if destStruct, ok := dest.(*DeviceResponse); ok {
					*destStruct = *expectedDevice
				}
				return nil
			},
			expectedResult: expectedDevice,
			expectedErr:    nil,
			wantErr:        false,
		},
		{
			name:     "device_not_found",
			orgId:    orgId,
			deviceId: deviceId,
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				return sql.ErrNoRows
			},
			expectedResult: nil,
			expectedErr:    ErrDatabaseOperation,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: tt.mockQueryRowStruct,
			}

			// Execute the function under test
			result, err := getDeviceByID(mockDB, tt.orgId, tt.deviceId)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.expectedResult, result)
			}

			// Verify the mock was called
			assert.Equal(t, 1, mockDB.QueryRowStructCallCount, "QueryRowStruct should be called exactly once")
		})
	}
}

// Test_getDeviceByOrganizationID tests the getDeviceByOrganizationID function
func Test_getDeviceByOrganizationID(t *testing.T) {
	t.Parallel()

	// Test data
	orgId := uuid.MustParse("123e4567-e89b-12d3-a456-************")
	softwareGatewayId := uuid.MustParse("11111111-1111-1111-1111-111111111111")
	locationId := uuid.MustParse("*************-2222-2222-************")
	createdAt := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
	updatedAt := time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC)

	expectedDevices := []DeviceResponse{
		{
			Id:                uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
			OrigId:            12345,
			SoftwareGatewayId: &softwareGatewayId,
			LocationId:        &locationId,
			OrganizationId:    orgId,
			Name:              "Device A",
			Description:       "First Device Description",
			IpAddress:         "*************",
			Port:              8080,
			Type:              "EDI_LEGACY",
			FlushConnectionMs: 5000,
			EnableRealtime:    true,
			IsEnabled:         true,
			CreatedAt:         createdAt,
			UpdatedAt:         updatedAt,
		},
		{
			Id:                uuid.MustParse("876fcdeb-51a2-43d1-b654-************"),
			OrigId:            12346,
			SoftwareGatewayId: nil,
			LocationId:        nil,
			OrganizationId:    orgId,
			Name:              "Device B",
			Description:       "Second Device Description",
			IpAddress:         "*************",
			Port:              8081,
			Type:              "EDI_NEXT_GEN",
			FlushConnectionMs: 0,
			EnableRealtime:    false,
			IsEnabled:         false,
			CreatedAt:         createdAt,
			UpdatedAt:         updatedAt,
		},
	}

	tests := []struct {
		name                  string
		orgId                 uuid.UUID
		mockQueryGenericSlice func(dest interface{}, query string, args ...interface{}) error
		expectedResult        *DevicesResponse
		expectedErr           error
		wantErr               bool
	}{
		{
			name:  "successful_devices_retrieval",
			orgId: orgId,
			mockQueryGenericSlice: func(dest interface{}, query string, args ...interface{}) error {
				// Mock successful query result
				if destSlice, ok := dest.(*[]DeviceResponse); ok {
					*destSlice = expectedDevices
				}
				return nil
			},
			expectedResult: &DevicesResponse{Devices: expectedDevices},
			expectedErr:    nil,
			wantErr:        false,
		},
		{
			name:  "no_devices_found",
			orgId: orgId,
			mockQueryGenericSlice: func(dest interface{}, query string, args ...interface{}) error {
				// Mock successful query result with empty slice
				if destSlice, ok := dest.(*[]DeviceResponse); ok {
					*destSlice = []DeviceResponse{}
				}
				return nil
			},
			expectedResult: &DevicesResponse{Devices: []DeviceResponse{}},
			expectedErr:    nil,
			wantErr:        false,
		},
		{
			name:  "database_connection_error",
			orgId: orgId,
			mockQueryGenericSlice: func(dest interface{}, query string, args ...interface{}) error {
				return sql.ErrConnDone
			},
			expectedResult: nil,
			expectedErr:    ErrDatabaseOperation,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: tt.mockQueryGenericSlice,
			}

			// Execute the function under test
			result, err := getDeviceByOrganizationID(mockDB, tt.orgId)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.expectedResult, result)
			}

			// Verify the mock was called
			assert.Equal(t, 1, mockDB.QueryGenericSliceCallCount, "QueryGenericSlice should be called exactly once")
		})
	}
}

// Test_createDevice tests the createDevice function
func Test_createDevice(t *testing.T) {
	t.Parallel()

	// Helper functions for pointer creation
	stringPtr := func(s string) *string { return &s }
	intPtr := func(i int) *int { return &i }
	boolPtr := func(b bool) *bool { return &b }

	// Test data
	orgId := uuid.MustParse("123e4567-e89b-12d3-a456-************")
	softwareGatewayId := uuid.MustParse("11111111-1111-1111-1111-111111111111")
	locationId := uuid.MustParse("*************-2222-2222-************")
	createdAt := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
	updatedAt := time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC)

	expectedDevice := &DeviceResponse{
		Id:                uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
		OrigId:            12345,
		SoftwareGatewayId: &softwareGatewayId,
		LocationId:        &locationId,
		OrganizationId:    orgId,
		Name:              "Test Device",
		Description:       "Test Device Description",
		IpAddress:         "*************",
		Port:              8080,
		Type:              "EDI_LEGACY",
		FlushConnectionMs: 5000,
		EnableRealtime:    true,
		IsEnabled:         true,
		CreatedAt:         createdAt,
		UpdatedAt:         updatedAt,
	}

	tests := []struct {
		name               string
		orgId              uuid.UUID
		req                *UpdateRequest
		mockQueryRowStruct func(dest interface{}, query string, args ...interface{}) error
		expectedResult     *DeviceResponse
		expectedErr        error
		wantErr            bool
	}{
		{
			name:  "successful_device_creation_with_all_fields",
			orgId: orgId,
			req: &UpdateRequest{
				Name:              stringPtr("Test Device"),
				Description:       stringPtr("Test Device Description"),
				IpAddress:         stringPtr("*************"),
				Port:              intPtr(8080),
				Type:              stringPtr("EDI_LEGACY"),
				FlushConnectionMs: intPtr(5000),
				EnableRealtime:    boolPtr(true),
				IsEnabled:         boolPtr(true),
			},
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				// Mock successful query result
				if destStruct, ok := dest.(*DeviceResponse); ok {
					*destStruct = *expectedDevice
				}
				return nil
			},
			expectedResult: expectedDevice,
			expectedErr:    nil,
			wantErr:        false,
		},
		{
			name:  "successful_device_creation_with_defaults",
			orgId: orgId,
			req: &UpdateRequest{
				Name:        stringPtr("Default Device"),
				Description: stringPtr("Default Device Description"),
				IpAddress:   stringPtr("*************"),
				Port:        intPtr(8081),
				Type:        stringPtr("EDI_NEXT_GEN"),
			},
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				// Mock successful query result with default values
				if destStruct, ok := dest.(*DeviceResponse); ok {
					*destStruct = DeviceResponse{
						Id:                uuid.MustParse("876fcdeb-51a2-43d1-b654-************"),
						OrigId:            12346,
						SoftwareGatewayId: nil,
						LocationId:        nil,
						OrganizationId:    orgId,
						Name:              "Default Device",
						Description:       "Default Device Description",
						IpAddress:         "*************",
						Port:              8081,
						Type:              "EDI_NEXT_GEN",
						FlushConnectionMs: 500,  // Default value
						EnableRealtime:    true, // Default value
						IsEnabled:         true, // Default value
						CreatedAt:         createdAt,
						UpdatedAt:         updatedAt,
					}
				}
				return nil
			},
			expectedResult: &DeviceResponse{
				Id:                uuid.MustParse("876fcdeb-51a2-43d1-b654-************"),
				OrigId:            12346,
				SoftwareGatewayId: nil,
				LocationId:        nil,
				OrganizationId:    orgId,
				Name:              "Default Device",
				Description:       "Default Device Description",
				IpAddress:         "*************",
				Port:              8081,
				Type:              "EDI_NEXT_GEN",
				FlushConnectionMs: 500,  // Default value
				EnableRealtime:    true, // Default value
				IsEnabled:         true, // Default value
				CreatedAt:         createdAt,
				UpdatedAt:         updatedAt,
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:  "database_connection_error",
			orgId: orgId,
			req: &UpdateRequest{
				Name:        stringPtr("Error Device"),
				Description: stringPtr("Error Device Description"),
				IpAddress:   stringPtr("*************"),
				Port:        intPtr(8082),
				Type:        stringPtr("EDI_LEGACY"),
			},
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				return sql.ErrConnDone
			},
			expectedResult: nil,
			expectedErr:    ErrDatabaseOperation,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: tt.mockQueryRowStruct,
			}

			// Execute the function under test
			result, err := createDevice(mockDB, tt.orgId, tt.req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.expectedResult, result)
			}

			// Verify the mock was called
			assert.Equal(t, 1, mockDB.QueryRowStructCallCount, "QueryRowStruct should be called exactly once")
		})
	}
}

// Test_updateDevice tests the updateDevice function
func Test_updateDevice(t *testing.T) {
	t.Parallel()

	// Test data
	orgId := uuid.MustParse("123e4567-e89b-12d3-a456-************")
	deviceId := uuid.MustParse("987fcdeb-51a2-43d1-b654-************")
	softwareGatewayId := uuid.MustParse("11111111-1111-1111-1111-111111111111")
	locationId := uuid.MustParse("*************-2222-2222-************")
	createdAt := time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC)
	updatedAt := time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC)

	currentDevice := &DeviceResponse{
		Id:                deviceId,
		OrigId:            12345,
		SoftwareGatewayId: &softwareGatewayId,
		LocationId:        &locationId,
		OrganizationId:    orgId,
		Name:              "Original Device",
		Description:       "Original Description",
		IpAddress:         "*************",
		Port:              8080,
		Type:              "EDI_LEGACY",
		FlushConnectionMs: 5000,
		EnableRealtime:    true,
		IsEnabled:         true,
		CreatedAt:         createdAt,
		UpdatedAt:         updatedAt,
	}

	tests := []struct {
		name               string
		orgId              uuid.UUID
		deviceId           uuid.UUID
		req                *UpdateRequest
		mockQueryRowStruct func(dest interface{}, query string, args ...interface{}) error
		expectedResult     *DeviceResponse
		expectedErr        error
		wantErr            bool
		expectedCallCount  int
	}{
		{
			name:     "successful_device_update_with_all_fields",
			orgId:    orgId,
			deviceId: deviceId,
			req: &UpdateRequest{
				Name:              &[]string{"Updated Device"}[0],
				Description:       &[]string{"Updated Description"}[0],
				IpAddress:         &[]string{"*************"}[0],
				Port:              &[]int{9090}[0],
				Type:              &[]string{"EDI_NEXT_GEN"}[0],
				FlushConnectionMs: &[]int{3000}[0],
				EnableRealtime:    &[]bool{false}[0],
				IsEnabled:         &[]bool{false}[0],
				LocationId:        &locationId,
				SoftwareGatewayId: &softwareGatewayId,
			},
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				// First call to getDeviceByID returns current device
				if args[0] == orgId && args[1] == deviceId {
					if destStruct, ok := dest.(*DeviceResponse); ok {
						*destStruct = *currentDevice
					}
					return nil
				}
				// Second call to update returns updated device
				if destStruct, ok := dest.(*DeviceResponse); ok {
					*destStruct = DeviceResponse{
						Id:                deviceId,
						OrigId:            12345,
						SoftwareGatewayId: &softwareGatewayId,
						LocationId:        &locationId,
						OrganizationId:    orgId,
						Name:              "Updated Device",
						Description:       "Updated Description",
						IpAddress:         "*************",
						Port:              9090,
						Type:              "EDI_NEXT_GEN",
						FlushConnectionMs: 3000,
						EnableRealtime:    false,
						IsEnabled:         false,
						CreatedAt:         createdAt,
						UpdatedAt:         time.Now().UTC(),
					}
				}
				return nil
			},
			expectedResult: &DeviceResponse{
				Id:                deviceId,
				OrigId:            12345,
				SoftwareGatewayId: &softwareGatewayId,
				LocationId:        &locationId,
				OrganizationId:    orgId,
				Name:              "Updated Device",
				Description:       "Updated Description",
				IpAddress:         "*************",
				Port:              9090,
				Type:              "EDI_NEXT_GEN",
				FlushConnectionMs: 3000,
				EnableRealtime:    false,
				IsEnabled:         false,
				CreatedAt:         createdAt,
				UpdatedAt:         time.Now().UTC(),
			},
			expectedErr:       nil,
			wantErr:           false,
			expectedCallCount: 2,
		},
		{
			name:     "successful_device_update_with_partial_fields",
			orgId:    orgId,
			deviceId: deviceId,
			req: &UpdateRequest{
				Name:        &[]string{"Partial Update"}[0],
				Description: &[]string{"Partial Description"}[0],
				// Other fields remain unchanged
			},
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				// First call to getDeviceByID returns current device
				if args[0] == orgId && args[1] == deviceId {
					if destStruct, ok := dest.(*DeviceResponse); ok {
						*destStruct = *currentDevice
					}
					return nil
				}
				// Second call to update returns updated device
				if destStruct, ok := dest.(*DeviceResponse); ok {
					*destStruct = DeviceResponse{
						Id:                deviceId,
						OrigId:            12345,
						SoftwareGatewayId: &softwareGatewayId,
						LocationId:        &locationId,
						OrganizationId:    orgId,
						Name:              "Partial Update",
						Description:       "Partial Description",
						IpAddress:         "*************", // Unchanged
						Port:              8080,            // Unchanged
						Type:              "EDI_LEGACY",    // Unchanged
						FlushConnectionMs: 5000,            // Unchanged
						EnableRealtime:    true,            // Unchanged
						IsEnabled:         true,            // Unchanged
						CreatedAt:         createdAt,
						UpdatedAt:         time.Now().UTC(),
					}
				}
				return nil
			},
			expectedResult: &DeviceResponse{
				Id:                deviceId,
				OrigId:            12345,
				SoftwareGatewayId: &softwareGatewayId,
				LocationId:        &locationId,
				OrganizationId:    orgId,
				Name:              "Partial Update",
				Description:       "Partial Description",
				IpAddress:         "*************", // Unchanged
				Port:              8080,            // Unchanged
				Type:              "EDI_LEGACY",    // Unchanged
				FlushConnectionMs: 5000,            // Unchanged
				EnableRealtime:    true,            // Unchanged
				IsEnabled:         true,            // Unchanged
				CreatedAt:         createdAt,
				UpdatedAt:         time.Now().UTC(),
			},
			expectedErr:       nil,
			wantErr:           false,
			expectedCallCount: 2,
		},
		{
			name:     "device_not_found_error",
			orgId:    orgId,
			deviceId: deviceId,
			req: &UpdateRequest{
				Name: &[]string{"Not Found Device"}[0],
			},
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				// First call to getDeviceByID returns no rows
				if args[0] == orgId && args[1] == deviceId {
					return sql.ErrNoRows
				}
				// This should not be reached
				return nil
			},
			expectedResult:    nil,
			expectedErr:       ErrDeviceNotFound,
			wantErr:           true,
			expectedCallCount: 1, // Only called once for getDeviceByID, then returns error
		},
		{
			name:     "database_connection_error",
			orgId:    orgId,
			deviceId: deviceId,
			req: &UpdateRequest{
				Name: &[]string{"Error Device"}[0],
			},
			mockQueryRowStruct: func(dest interface{}, query string, args ...interface{}) error {
				// First call to getDeviceByID succeeds
				if args[0] == orgId && args[1] == deviceId {
					if destStruct, ok := dest.(*DeviceResponse); ok {
						*destStruct = *currentDevice
					}
					return nil
				}
				// Second call to update fails
				return sql.ErrConnDone
			},
			expectedResult:    nil,
			expectedErr:       ErrDatabaseOperation,
			wantErr:           true,
			expectedCallCount: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: tt.mockQueryRowStruct,
			}

			// Execute the function under test
			result, err := updateDevice(mockDB, tt.orgId, tt.deviceId, tt.req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				// For time comparisons, only check that UpdatedAt is recent (within last second)
				if tt.expectedResult != nil && result != nil {
					expected := tt.expectedResult
					actual := result

					// Copy the actual result and set UpdatedAt to expected for comparison
					expectedCopy := *expected
					expectedCopy.UpdatedAt = actual.UpdatedAt

					assert.Equal(t, &expectedCopy, actual)

					// Verify UpdatedAt is recent (within last second)
					timeDiff := time.Since(actual.UpdatedAt)
					assert.True(t, timeDiff < time.Second, "UpdatedAt should be recent")
				}
			}

			// Verify the mock was called the expected number of times
			assert.Equal(t, tt.expectedCallCount, mockDB.QueryRowStructCallCount,
				"QueryRowStruct should be called exactly %d times", tt.expectedCallCount)
		})
	}
}

// Test_parseDeviceRequest tests the parseDeviceRequest function for create requests
func Test_parseDeviceRequest_Create(t *testing.T) {
	t.Parallel()

	// Helper functions for pointer creation
	stringPtr := func(s string) *string { return &s }
	intPtr := func(i int) *int { return &i }
	boolPtr := func(b bool) *bool { return &b }

	tests := []struct {
		name        string
		requestBody string
		expectedReq *UpdateRequest
		expectedErr error
		wantErr     bool
	}{
		{
			name:        "valid_create_request_with_all_fields",
			requestBody: `{"name":"Test Device","description":"Test Device Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY","flushConnectionMs":5000,"enableRealtime":true,"isEnabled":true}`,
			expectedReq: &UpdateRequest{
				Name:              stringPtr("Test Device"),
				Description:       stringPtr("Test Device Description"),
				IpAddress:         stringPtr("*************"),
				Port:              intPtr(8080),
				Type:              stringPtr("EDI_LEGACY"),
				FlushConnectionMs: intPtr(5000),
				EnableRealtime:    boolPtr(true),
				IsEnabled:         boolPtr(true),
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "valid_create_request_with_required_fields_only",
			requestBody: `{"name":"Basic Device","description":"Basic Description","ipAddress":"*************","port":8081,"type":"EDI_NEXT_GEN"}`,
			expectedReq: &UpdateRequest{
				Name:        stringPtr("Basic Device"),
				Description: stringPtr("Basic Description"),
				IpAddress:   stringPtr("*************"),
				Port:        intPtr(8081),
				Type:        stringPtr("EDI_NEXT_GEN"),
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "valid_create_request_with_whitespace",
			requestBody: `{"name":"  Trimmed Name  ","description":"  Trimmed Description  ","ipAddress":"  *************  ","type":"  EDI_LEGACY  "}`,
			expectedReq: &UpdateRequest{
				Name:        stringPtr("Trimmed Name"),
				Description: stringPtr("Trimmed Description"),
				IpAddress:   stringPtr("*************"),
				Port:        nil, // Port not provided in JSON
				Type:        stringPtr("EDI_LEGACY"),
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "invalid_json_syntax",
			requestBody: `{"name":"Invalid JSON`,
			expectedReq: nil,
			expectedErr: ErrInvalidRequestBody,
			wantErr:     true,
		},
		{
			name:        "unknown_field_rejected",
			requestBody: `{"name":"Valid Name","description":"Valid Description","ipAddress":"*************","type":"EDI_LEGACY","unknownField":"value"}`,
			expectedReq: nil,
			expectedErr: ErrUnexpectedFields,
			wantErr:     true,
		},
		{
			name:        "empty_name_field",
			requestBody: `{"name":"","description":"Valid Description","ipAddress":"*************","type":"EDI_LEGACY"}`,
			expectedReq: nil,
			expectedErr: ErrInvalidDeviceName,
			wantErr:     true,
		},
		{
			name:        "whitespace_only_name_field",
			requestBody: `{"name":"   ","description":"Valid Description","ipAddress":"*************","type":"EDI_LEGACY"}`,
			expectedReq: nil,
			expectedErr: ErrInvalidDeviceName,
			wantErr:     true,
		},
		{
			name:        "empty_description_field_allowed",
			requestBody: `{"name":"Valid Name","description":"","ipAddress":"*************","type":"EDI_LEGACY"}`,
			expectedReq: &UpdateRequest{
				Name:        stringPtr("Valid Name"),
				Description: stringPtr(""),
				IpAddress:   stringPtr("*************"),
				Port:        nil,
				Type:        stringPtr("EDI_LEGACY"),
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "whitespace_only_description_field_allowed",
			requestBody: `{"name":"Valid Name","description":"   ","ipAddress":"*************","type":"EDI_LEGACY"}`,
			expectedReq: &UpdateRequest{
				Name:        stringPtr("Valid Name"),
				Description: stringPtr(""), // Should be trimmed to empty string
				IpAddress:   stringPtr("*************"),
				Port:        nil,
				Type:        stringPtr("EDI_LEGACY"),
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "empty_ipaddress_field_allowed",
			requestBody: `{"name":"Valid Name","description":"Valid Description","ipAddress":"","type":"EDI_LEGACY"}`,
			expectedReq: &UpdateRequest{
				Name:        stringPtr("Valid Name"),
				Description: stringPtr("Valid Description"),
				IpAddress:   stringPtr(""),
				Port:        nil,
				Type:        stringPtr("EDI_LEGACY"),
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "whitespace_only_ipaddress_field_allowed",
			requestBody: `{"name":"Valid Name","description":"Valid Description","ipAddress":"   ","type":"EDI_LEGACY"}`,
			expectedReq: &UpdateRequest{
				Name:        stringPtr("Valid Name"),
				Description: stringPtr("Valid Description"),
				IpAddress:   stringPtr(""), // Should be trimmed to empty string
				Port:        nil,
				Type:        stringPtr("EDI_LEGACY"),
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "empty_type_field",
			requestBody: `{"name":"Valid Name","description":"Valid Description","ipAddress":"*************","type":""}`,
			expectedReq: nil,
			expectedErr: ErrInvalidDeviceType,
			wantErr:     true,
		},
		{
			name:        "whitespace_only_type_field",
			requestBody: `{"name":"Valid Name","description":"Valid Description","ipAddress":"*************","type":"   "}`,
			expectedReq: nil,
			expectedErr: ErrInvalidDeviceType,
			wantErr:     true,
		},
		{
			name:        "invalid_type_value",
			requestBody: `{"name":"Valid Name","description":"Valid Description","ipAddress":"*************","type":"INVALID_TYPE"}`,
			expectedReq: nil,
			expectedErr: ErrInvalidDeviceTypeValue,
			wantErr:     true,
		},
		{
			name:        "missing_name_field",
			requestBody: `{"description":"Valid Description","ipAddress":"*************","type":"EDI_LEGACY"}`,
			expectedReq: nil,
			expectedErr: ErrInvalidDeviceName,
			wantErr:     true,
		},
		{
			name:        "missing_type_field",
			requestBody: `{"name":"Valid Name","description":"Valid Description","ipAddress":"*************"}`,
			expectedReq: nil,
			expectedErr: ErrInvalidDeviceType,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create HTTP request with the test body
			req, err := http.NewRequest("POST", "/test", strings.NewReader(tt.requestBody))
			if err != nil {
				t.Fatalf("Failed to create test request: %v", err)
			}

			// Execute the function under test
			result, err := parseDeviceRequest(req, false)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				// Type assert the result to UpdateRequest
				updateReq, ok := result.(*UpdateRequest)
				assert.True(t, ok, "Result should be *UpdateRequest")
				assert.Equal(t, tt.expectedReq, updateReq)
			}
		})
	}
}

// Test_parseDeviceRequest tests the parseDeviceRequest function for update requests
func Test_parseDeviceRequest_Update(t *testing.T) {
	t.Parallel()

	// Helper functions for pointer creation
	stringPtr := func(s string) *string { return &s }

	tests := []struct {
		name        string
		requestBody string
		expectedReq *UpdateRequest
		expectedErr error
		wantErr     bool
	}{
		{
			name:        "valid_update_request_with_all_fields",
			requestBody: `{"name":"Updated Device","description":"Updated Description","ipAddress":"*************","port":9090,"type":"EDI_NEXT_GEN","flushConnectionMs":3000,"enableRealtime":false,"isEnabled":false}`,
			expectedReq: &UpdateRequest{
				Name:              &[]string{"Updated Device"}[0],
				Description:       &[]string{"Updated Description"}[0],
				IpAddress:         &[]string{"*************"}[0],
				Port:              &[]int{9090}[0],
				Type:              &[]string{"EDI_NEXT_GEN"}[0],
				FlushConnectionMs: &[]int{3000}[0],
				EnableRealtime:    &[]bool{false}[0],
				IsEnabled:         &[]bool{false}[0],
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "valid_update_request_with_partial_fields",
			requestBody: `{"name":"Partial Update","description":"Partial Description"}`,
			expectedReq: &UpdateRequest{
				Name:        &[]string{"Partial Update"}[0],
				Description: &[]string{"Partial Description"}[0],
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "valid_update_request_with_whitespace",
			requestBody: `{"name":"  Trimmed Name  ","description":"  Trimmed Description  "}`,
			expectedReq: &UpdateRequest{
				Name:        &[]string{"Trimmed Name"}[0],
				Description: &[]string{"Trimmed Description"}[0],
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "valid_update_request_with_nil_fields",
			requestBody: `{}`,
			expectedReq: &UpdateRequest{},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "invalid_json_syntax",
			requestBody: `{"name":"Invalid JSON`,
			expectedReq: nil,
			expectedErr: ErrInvalidRequestBody,
			wantErr:     true,
		},
		{
			name:        "unknown_field_rejected",
			requestBody: `{"name":"Valid Name","unknownField":"value"}`,
			expectedReq: nil,
			expectedErr: ErrUnexpectedFields,
			wantErr:     true,
		},
		{
			name:        "empty_name_field",
			requestBody: `{"name":""}`,
			expectedReq: nil,
			expectedErr: ErrInvalidDeviceName,
			wantErr:     true,
		},
		{
			name:        "whitespace_only_name_field",
			requestBody: `{"name":"   "}`,
			expectedReq: nil,
			expectedErr: ErrInvalidDeviceName,
			wantErr:     true,
		},
		{
			name:        "empty_description_field_allowed",
			requestBody: `{"description":""}`,
			expectedReq: &UpdateRequest{
				Description: stringPtr(""),
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "whitespace_only_description_field_allowed",
			requestBody: `{"description":"   "}`,
			expectedReq: &UpdateRequest{
				Description: stringPtr(""), // Should be trimmed to empty string
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "empty_ipaddress_field_allowed",
			requestBody: `{"ipAddress":""}`,
			expectedReq: &UpdateRequest{
				IpAddress: stringPtr(""),
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "whitespace_only_ipaddress_field_allowed",
			requestBody: `{"ipAddress":"   "}`,
			expectedReq: &UpdateRequest{
				IpAddress: stringPtr(""), // Should be trimmed to empty string
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "empty_type_field",
			requestBody: `{"type":""}`,
			expectedReq: nil,
			expectedErr: ErrInvalidDeviceType,
			wantErr:     true,
		},
		{
			name:        "whitespace_only_type_field",
			requestBody: `{"type":"   "}`,
			expectedReq: nil,
			expectedErr: ErrInvalidDeviceType,
			wantErr:     true,
		},
		{
			name:        "invalid_type_value",
			requestBody: `{"type":"INVALID_TYPE"}`,
			expectedReq: nil,
			expectedErr: ErrInvalidDeviceTypeValue,
			wantErr:     true,
		},
		{
			name:        "valid_type_edi_legacy",
			requestBody: `{"type":"EDI_LEGACY"}`,
			expectedReq: &UpdateRequest{
				Type: &[]string{"EDI_LEGACY"}[0],
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "valid_type_edi_next_gen",
			requestBody: `{"type":"EDI_NEXT_GEN"}`,
			expectedReq: &UpdateRequest{
				Type: &[]string{"EDI_NEXT_GEN"}[0],
			},
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create HTTP request with the test body
			req, err := http.NewRequest("PUT", "/test", strings.NewReader(tt.requestBody))
			if err != nil {
				t.Fatalf("Failed to create test request: %v", err)
			}

			// Execute the function under test
			result, err := parseDeviceRequest(req, true)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				// Type assert the result to UpdateRequest
				updateReq, ok := result.(*UpdateRequest)
				assert.True(t, ok, "Result should be *UpdateRequest")
				assert.Equal(t, tt.expectedReq, updateReq)
			}
		})
	}
}

// Test_deleteDevice tests the deleteDevice function
func Test_deleteDevice(t *testing.T) {
	t.Parallel()

	// Test data
	orgId := uuid.MustParse("123e4567-e89b-12d3-a456-************")
	deviceId := uuid.MustParse("987fcdeb-51a2-43d1-b654-************")

	tests := []struct {
		name              string
		orgId             uuid.UUID
		deviceId          uuid.UUID
		mockExec          func(query string, args ...interface{}) (sql.Result, error)
		expectedErr       error
		wantErr           bool
		expectedCallCount int
	}{
		{
			name:     "successful_device_deletion",
			orgId:    orgId,
			deviceId: deviceId,
			mockExec: func(query string, args ...interface{}) (sql.Result, error) {
				// Mock successful execution with 1 row affected
				return &mockResult{rowsAffected: 1}, nil
			},
			expectedErr:       nil,
			wantErr:           false,
			expectedCallCount: 1,
		},
		{
			name:     "device_not_found",
			orgId:    orgId,
			deviceId: deviceId,
			mockExec: func(query string, args ...interface{}) (sql.Result, error) {
				// Mock successful execution with 0 rows affected
				return &mockResult{rowsAffected: 0}, nil
			},
			expectedErr:       ErrDeviceNotFound,
			wantErr:           true,
			expectedCallCount: 1,
		},
		{
			name:     "database_connection_error",
			orgId:    orgId,
			deviceId: deviceId,
			mockExec: func(query string, args ...interface{}) (sql.Result, error) {
				return nil, sql.ErrConnDone
			},
			expectedErr:       ErrDatabaseOperation,
			wantErr:           true,
			expectedCallCount: 1,
		},
		{
			name:     "rows_affected_error",
			orgId:    orgId,
			deviceId: deviceId,
			mockExec: func(query string, args ...interface{}) (sql.Result, error) {
				// Mock successful execution but RowsAffected() fails
				return &mockResult{rowsAffectedError: true}, nil
			},
			expectedErr:       ErrDatabaseOperation,
			wantErr:           true,
			expectedCallCount: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{
				ExecFunc: tt.mockExec,
			}

			// Execute the function under test
			err := deleteDevice(mockDB, tt.orgId, tt.deviceId)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify the mock was called the expected number of times
			assert.Equal(t, tt.expectedCallCount, mockDB.ExecCallCount,
				"Exec should be called exactly %d times", tt.expectedCallCount)
		})
	}
}

// Test_DeleteDeviceWithDeps tests the DeleteDeviceWithDeps function
func Test_DeleteDeviceWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name                 string
		requestPath          string
		mockGetConnections   func(context.Context, ...bool) (*connect.Connections, error)
		mockDeleteDevice     func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
		expectedStatusCode   int
		expectedResponseBody string
		setupRequest         func() *http.Request
	}{
		{
			name:                 "successful_device_deletion",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************",
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":null,"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockDeleteDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("DELETE", "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************", nil)
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "get_connections_error",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************",
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection error")
			},
			mockDeleteDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("DELETE", "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************", nil)
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "invalid_organization_id",
			requestPath:          "/organizations/invalid-uuid/devices/987fcdeb-51a2-43d1-b654-************",
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockDeleteDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("DELETE", "/organizations/invalid-uuid/devices/987fcdeb-51a2-43d1-b654-************", nil)
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "invalid-uuid",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "invalid_device_id",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/invalid-uuid",
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockDeleteDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("DELETE", "/organizations/123e4567-e89b-12d3-a456-************/devices/invalid-uuid", nil)
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "invalid-uuid",
				})
				return req
			},
		},
		{
			name:                 "delete_device_error",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************",
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockDeleteDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID) error {
				return ErrDatabaseOperation
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("DELETE", "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************", nil)
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "device_not_found_error",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************",
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockDeleteDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID) error {
				return ErrDeviceNotFound
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("DELETE", "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************", nil)
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "nil_uuid_organization",
			requestPath:          "/organizations/00000000-0000-0000-0000-000000000000/devices/987fcdeb-51a2-43d1-b654-************",
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":null,"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockDeleteDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("DELETE", "/organizations/00000000-0000-0000-0000-000000000000/devices/987fcdeb-51a2-43d1-b654-************", nil)
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "00000000-0000-0000-0000-000000000000",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "nil_uuid_device",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/00000000-0000-0000-0000-000000000000",
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":null,"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockDeleteDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("DELETE", "/organizations/123e4567-e89b-12d3-a456-************/devices/00000000-0000-0000-0000-000000000000", nil)
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "00000000-0000-0000-0000-000000000000",
				})
				return req
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with mocked dependencies
			handler := DeleteDeviceWithDeps(HandlerDeps{
				GetConnections: tt.mockGetConnections,
				DeleteDevice:   tt.mockDeleteDevice,
			})

			// Create request
			req := tt.setupRequest()

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert response status code
			assert.Equal(t, tt.expectedStatusCode, rr.Code,
				"Expected status code %d, got %d", tt.expectedStatusCode, rr.Code)

			// Assert response body
			assert.JSONEq(t, tt.expectedResponseBody, rr.Body.String(),
				"Expected response body %s, got %s", tt.expectedResponseBody, rr.Body.String())
		})
	}
}

// Test_UpdateDeviceWithDeps tests the UpdateDeviceWithDeps function
func Test_UpdateDeviceWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name                        string
		requestPath                 string
		requestBody                 string
		mockGetConnections          func(context.Context, ...bool) (*connect.Connections, error)
		mockUpdateDevice            func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, *UpdateRequest) (*DeviceResponse, error)
		mockValidateSoftwareGateway func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
		mockValidateLocation        func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
		expectedStatusCode          int
		expectedResponseBody        string
		setupRequest                func() *http.Request
	}{
		{
			name:                 "successful_device_update",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************",
			requestBody:          `{"name":"Updated Device","description":"Updated Description"}`,
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":{"id":"987fcdeb-51a2-43d1-b654-************","origId":0,"softwareGatewayId":null,"locationId":null,"organizationId":"123e4567-e89b-12d3-a456-************","name":"Updated Device","description":"Updated Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY","flushConnectionMs":0,"enableRealtime":false,"isEnabled":true,"createdAt":"2023-01-01T12:00:00Z","updatedAt":"2023-01-02T12:00:00Z"},"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockUpdateDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return &DeviceResponse{
					Id:                uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
					OrigId:            0,
					SoftwareGatewayId: nil,
					LocationId:        nil,
					OrganizationId:    uuid.MustParse("123e4567-e89b-12d3-a456-************"),
					Name:              "Updated Device",
					Description:       "Updated Description",
					IpAddress:         "*************",
					Port:              8080,
					Type:              "EDI_LEGACY",
					FlushConnectionMs: 0,
					EnableRealtime:    false,
					IsEnabled:         true,
					CreatedAt:         time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
					UpdatedAt:         time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC),
				}, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("PUT", "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************", strings.NewReader(`{"name":"Updated Device","description":"Updated Description"}`))
				req.Header.Set("Content-Type", "application/json")
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "get_connections_error",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************",
			requestBody:          `{"name":"Test Device"}`,
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection error")
			},
			mockUpdateDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return nil, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("PUT", "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************", strings.NewReader(`{"name":"Test Device"}`))
				req.Header.Set("Content-Type", "application/json")
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "invalid_organization_id",
			requestPath:          "/organizations/invalid-uuid/devices/987fcdeb-51a2-43d1-b654-************",
			requestBody:          `{"name":"Test Device"}`,
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockUpdateDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return nil, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("PUT", "/organizations/invalid-uuid/devices/987fcdeb-51a2-43d1-b654-************", strings.NewReader(`{"name":"Test Device"}`))
				req.Header.Set("Content-Type", "application/json")
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "invalid-uuid",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "invalid_device_id",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/invalid-uuid",
			requestBody:          `{"name":"Test Device"}`,
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockUpdateDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return nil, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("PUT", "/organizations/123e4567-e89b-12d3-a456-************/devices/invalid-uuid", strings.NewReader(`{"name":"Test Device"}`))
				req.Header.Set("Content-Type", "application/json")
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "invalid-uuid",
				})
				return req
			},
		},
		{
			name:                 "invalid_request_body",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************",
			requestBody:          `{"name":""}`,
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockUpdateDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return nil, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("PUT", "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************", strings.NewReader(`{"name":""}`))
				req.Header.Set("Content-Type", "application/json")
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "update_device_error",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************",
			requestBody:          `{"name":"Test Device"}`,
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockUpdateDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return nil, ErrDatabaseOperation
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("PUT", "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************", strings.NewReader(`{"name":"Test Device"}`))
				req.Header.Set("Content-Type", "application/json")
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "nil_uuid_organization",
			requestPath:          "/organizations/00000000-0000-0000-0000-000000000000/devices/987fcdeb-51a2-43d1-b654-************",
			requestBody:          `{"name":"Test Device"}`,
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":{"id":"987fcdeb-51a2-43d1-b654-************","origId":0,"softwareGatewayId":null,"locationId":null,"organizationId":"00000000-0000-0000-0000-000000000000","name":"Test Device","description":"Test Description","ipAddress":"0.0.0.0","port":0,"type":"test","flushConnectionMs":0,"enableRealtime":false,"isEnabled":false,"createdAt":"2023-01-01T12:00:00Z","updatedAt":"2023-01-02T12:00:00Z"},"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockUpdateDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return &DeviceResponse{
					Id:                uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
					OrigId:            0,
					SoftwareGatewayId: nil,
					LocationId:        nil,
					OrganizationId:    uuid.Nil,
					Name:              "Test Device",
					Description:       "Test Description",
					IpAddress:         "0.0.0.0",
					Port:              0,
					Type:              "test",
					FlushConnectionMs: 0,
					EnableRealtime:    false,
					IsEnabled:         false,
					CreatedAt:         time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
					UpdatedAt:         time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC),
				}, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("PUT", "/organizations/00000000-0000-0000-0000-000000000000/devices/987fcdeb-51a2-43d1-b654-************", strings.NewReader(`{"name":"Test Device"}`))
				req.Header.Set("Content-Type", "application/json")
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "00000000-0000-0000-0000-000000000000",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "nil_uuid_device",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/00000000-0000-0000-0000-000000000000",
			requestBody:          `{"name":"Test Device"}`,
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":{"id":"00000000-0000-0000-0000-000000000000","origId":0,"softwareGatewayId":null,"locationId":null,"organizationId":"123e4567-e89b-12d3-a456-************","name":"Test Device","description":"Test Description","ipAddress":"0.0.0.0","port":0,"type":"test","flushConnectionMs":0,"enableRealtime":false,"isEnabled":false,"createdAt":"2023-01-01T12:00:00Z","updatedAt":"2023-01-02T12:00:00Z"},"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockUpdateDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return &DeviceResponse{
					Id:                uuid.Nil,
					OrigId:            0,
					SoftwareGatewayId: nil,
					LocationId:        nil,
					OrganizationId:    uuid.MustParse("123e4567-e89b-12d3-a456-************"),
					Name:              "Test Device",
					Description:       "Test Description",
					IpAddress:         "0.0.0.0",
					Port:              0,
					Type:              "test",
					FlushConnectionMs: 0,
					EnableRealtime:    false,
					IsEnabled:         false,
					CreatedAt:         time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
					UpdatedAt:         time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC),
				}, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("PUT", "/organizations/123e4567-e89b-12d3-a456-************/devices/00000000-0000-0000-0000-000000000000", strings.NewReader(`{"name":"Test Device"}`))
				req.Header.Set("Content-Type", "application/json")
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "00000000-0000-0000-0000-000000000000",
				})
				return req
			},
		},
		{
			name:                 "software_gateway_validation_error_update",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************",
			requestBody:          `{"softwareGatewayId":"11111111-1111-1111-1111-111111111111"}`,
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockUpdateDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return nil, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return helper.ErrSoftwareGatewayNotFound
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("PUT", "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************", strings.NewReader(`{"softwareGatewayId":"11111111-1111-1111-1111-111111111111"}`))
				req.Header.Set("Content-Type", "application/json")
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "successful_device_update_with_software_gateway",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************",
			requestBody:          `{"softwareGatewayId":"11111111-1111-1111-1111-111111111111"}`,
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":{"id":"987fcdeb-51a2-43d1-b654-************","origId":0,"softwareGatewayId":"11111111-1111-1111-1111-111111111111","locationId":null,"organizationId":"123e4567-e89b-12d3-a456-************","name":"Updated Device","description":"Updated Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY","flushConnectionMs":0,"enableRealtime":false,"isEnabled":true,"createdAt":"2023-01-01T12:00:00Z","updatedAt":"2023-01-02T12:00:00Z"},"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockUpdateDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				softwareGatewayId := uuid.MustParse("11111111-1111-1111-1111-111111111111")
				return &DeviceResponse{
					Id:                uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
					OrigId:            0,
					SoftwareGatewayId: &softwareGatewayId,
					LocationId:        nil,
					OrganizationId:    uuid.MustParse("123e4567-e89b-12d3-a456-************"),
					Name:              "Updated Device",
					Description:       "Updated Description",
					IpAddress:         "*************",
					Port:              8080,
					Type:              "EDI_LEGACY",
					FlushConnectionMs: 0,
					EnableRealtime:    false,
					IsEnabled:         true,
					CreatedAt:         time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
					UpdatedAt:         time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC),
				}, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("PUT", "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************", strings.NewReader(`{"softwareGatewayId":"11111111-1111-1111-1111-111111111111"}`))
				req.Header.Set("Content-Type", "application/json")
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "successful_device_update_with_both_software_gateway_and_location",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************",
			requestBody:          `{"softwareGatewayId":"11111111-1111-1111-1111-111111111111","locationId":"*************-2222-2222-************"}`,
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":{"id":"987fcdeb-51a2-43d1-b654-************","origId":0,"softwareGatewayId":"11111111-1111-1111-1111-111111111111","locationId":"*************-2222-2222-************","organizationId":"123e4567-e89b-12d3-a456-************","name":"Updated Device","description":"Updated Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY","flushConnectionMs":0,"enableRealtime":false,"isEnabled":true,"createdAt":"2023-01-01T12:00:00Z","updatedAt":"2023-01-02T12:00:00Z"},"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockUpdateDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				softwareGatewayId := uuid.MustParse("11111111-1111-1111-1111-111111111111")
				locationId := uuid.MustParse("*************-2222-2222-************")
				return &DeviceResponse{
					Id:                uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
					OrigId:            0,
					SoftwareGatewayId: &softwareGatewayId,
					LocationId:        &locationId,
					OrganizationId:    uuid.MustParse("123e4567-e89b-12d3-a456-************"),
					Name:              "Updated Device",
					Description:       "Updated Description",
					IpAddress:         "*************",
					Port:              8080,
					Type:              "EDI_LEGACY",
					FlushConnectionMs: 0,
					EnableRealtime:    false,
					IsEnabled:         true,
					CreatedAt:         time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
					UpdatedAt:         time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC),
				}, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("PUT", "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************", strings.NewReader(`{"softwareGatewayId":"11111111-1111-1111-1111-111111111111","locationId":"*************-2222-2222-************"}`))
				req.Header.Set("Content-Type", "application/json")
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "software_gateway_validation_fails_with_both_ids",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************",
			requestBody:          `{"softwareGatewayId":"11111111-1111-1111-1111-111111111111","locationId":"*************-2222-2222-************"}`,
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockUpdateDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return nil, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return helper.ErrSoftwareGatewayNotFound
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("PUT", "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************", strings.NewReader(`{"softwareGatewayId":"11111111-1111-1111-1111-111111111111","locationId":"*************-2222-2222-************"}`))
				req.Header.Set("Content-Type", "application/json")
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "location_validation_fails_with_both_ids",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************",
			requestBody:          `{"softwareGatewayId":"11111111-1111-1111-1111-111111111111","locationId":"*************-2222-2222-************"}`,
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockUpdateDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return nil, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return helper.ErrLocationNotFound
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("PUT", "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************", strings.NewReader(`{"softwareGatewayId":"11111111-1111-1111-1111-111111111111","locationId":"*************-2222-2222-************"}`))
				req.Header.Set("Content-Type", "application/json")
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "location_validation_error_update",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************",
			requestBody:          `{"locationId":"*************-2222-2222-************"}`,
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockUpdateDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return nil, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return helper.ErrLocationNotFound
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("PUT", "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************", strings.NewReader(`{"locationId":"*************-2222-2222-************"}`))
				req.Header.Set("Content-Type", "application/json")
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "successful_device_update_with_location",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************",
			requestBody:          `{"locationId":"*************-2222-2222-************"}`,
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":{"id":"987fcdeb-51a2-43d1-b654-************","origId":0,"softwareGatewayId":null,"locationId":"*************-2222-2222-************","organizationId":"123e4567-e89b-12d3-a456-************","name":"Updated Device","description":"Updated Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY","flushConnectionMs":0,"enableRealtime":false,"isEnabled":true,"createdAt":"2023-01-01T12:00:00Z","updatedAt":"2023-01-02T12:00:00Z"},"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockUpdateDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				locationId := uuid.MustParse("*************-2222-2222-************")
				return &DeviceResponse{
					Id:                uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
					OrigId:            0,
					SoftwareGatewayId: nil,
					LocationId:        &locationId,
					OrganizationId:    uuid.MustParse("123e4567-e89b-12d3-a456-************"),
					Name:              "Updated Device",
					Description:       "Updated Description",
					IpAddress:         "*************",
					Port:              8080,
					Type:              "EDI_LEGACY",
					FlushConnectionMs: 0,
					EnableRealtime:    false,
					IsEnabled:         true,
					CreatedAt:         time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
					UpdatedAt:         time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC),
				}, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("PUT", "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************", strings.NewReader(`{"locationId":"*************-2222-2222-************"}`))
				req.Header.Set("Content-Type", "application/json")
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "empty_request_body",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************",
			requestBody:          `{}`,
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":{"id":"987fcdeb-51a2-43d1-b654-************","origId":0,"softwareGatewayId":null,"locationId":null,"organizationId":"123e4567-e89b-12d3-a456-************","name":"Original Name","description":"Original Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY","flushConnectionMs":0,"enableRealtime":false,"isEnabled":true,"createdAt":"2023-01-01T12:00:00Z","updatedAt":"2023-01-02T12:00:00Z"},"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockUpdateDevice: func(pg connect.DatabaseExecutor, _, _ uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return &DeviceResponse{
					Id:                uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
					OrigId:            0,
					SoftwareGatewayId: nil,
					LocationId:        nil,
					OrganizationId:    uuid.MustParse("123e4567-e89b-12d3-a456-************"),
					Name:              "Original Name",
					Description:       "Original Description",
					IpAddress:         "*************",
					Port:              8080,
					Type:              "EDI_LEGACY",
					FlushConnectionMs: 0,
					EnableRealtime:    false,
					IsEnabled:         true,
					CreatedAt:         time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
					UpdatedAt:         time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC),
				}, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("PUT", "/organizations/123e4567-e89b-12d3-a456-************/devices/987fcdeb-51a2-43d1-b654-************", strings.NewReader(`{}`))
				req.Header.Set("Content-Type", "application/json")
				// Set up the request with URL variables that the helper functions expect
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
					"deviceId":       "987fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with mocked dependencies
			handler := UpdateDeviceWithDeps(HandlerDeps{
				GetConnections: tt.mockGetConnections,
				UpdateDevice:   tt.mockUpdateDevice,
				ValidateSoftwareGateWayBelongsToOrganization: tt.mockValidateSoftwareGateway,
				ValidateLocationBelongsToOrganization:        tt.mockValidateLocation,
			})

			// Create request
			req := tt.setupRequest()

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert response status code
			assert.Equal(t, tt.expectedStatusCode, rr.Code,
				"Expected status code %d, got %d", tt.expectedStatusCode, rr.Code)

			// Assert response body
			assert.JSONEq(t, tt.expectedResponseBody, rr.Body.String(),
				"Expected response body %s, got %s", tt.expectedResponseBody, rr.Body.String())
		})
	}
}

// Test_GetDeviceByOrganizationIDWithDeps tests the GetDeviceByOrganizationIDWithDeps function
func Test_GetDeviceByOrganizationIDWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name                          string
		requestPath                   string
		mockGetConnections            func(context.Context, ...bool) (*connect.Connections, error)
		mockGetDeviceByOrganizationID func(connect.DatabaseExecutor, uuid.UUID) (*DevicesResponse, error)
		expectedStatusCode            int
		expectedResponseBody          string
		setupRequest                  func() *http.Request
	}{
		{
			name:                 "successful_devices_retrieval",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices",
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":{"devices":[{"id":"987fcdeb-51a2-43d1-b654-************","origId":0,"softwareGatewayId":null,"locationId":null,"organizationId":"123e4567-e89b-12d3-a456-************","name":"Test Device","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY","flushConnectionMs":500,"enableRealtime":true,"isEnabled":true,"createdAt":"2023-01-01T12:00:00Z","updatedAt":"2023-01-02T12:00:00Z"}]},"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockGetDeviceByOrganizationID: func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*DevicesResponse, error) {
				return &DevicesResponse{
					Devices: []DeviceResponse{
						{
							Id:                uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
							OrigId:            0,
							SoftwareGatewayId: nil,
							LocationId:        nil,
							OrganizationId:    orgId,
							Name:              "Test Device",
							Description:       "Test Description",
							IpAddress:         "*************",
							Port:              8080,
							Type:              "EDI_LEGACY",
							FlushConnectionMs: 500,
							EnableRealtime:    true,
							IsEnabled:         true,
							CreatedAt:         time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
							UpdatedAt:         time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC),
						},
					},
				}, nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("GET", "/organizations/123e4567-e89b-12d3-a456-************/devices", nil)
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
				})
				return req
			},
		},
		{
			name:                 "get_connections_error",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices",
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection error")
			},
			mockGetDeviceByOrganizationID: func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*DevicesResponse, error) {
				return nil, nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("GET", "/organizations/123e4567-e89b-12d3-a456-************/devices", nil)
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
				})
				return req
			},
		},
		{
			name:                 "invalid_organization_id",
			requestPath:          "/organizations/invalid-uuid/devices",
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockGetDeviceByOrganizationID: func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*DevicesResponse, error) {
				return nil, nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("GET", "/organizations/invalid-uuid/devices", nil)
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "invalid-uuid",
				})
				return req
			},
		},
		{
			name:                 "database_operation_error",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices",
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockGetDeviceByOrganizationID: func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*DevicesResponse, error) {
				return nil, ErrDatabaseOperation
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("GET", "/organizations/123e4567-e89b-12d3-a456-************/devices", nil)
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
				})
				return req
			},
		},
		{
			name:                 "empty_devices_list",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices",
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":{"devices":[]},"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockGetDeviceByOrganizationID: func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*DevicesResponse, error) {
				return &DevicesResponse{
					Devices: []DeviceResponse{},
				}, nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("GET", "/organizations/123e4567-e89b-12d3-a456-************/devices", nil)
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
				})
				return req
			},
		},
		{
			name:                 "nil_uuid_organization",
			requestPath:          "/organizations/00000000-0000-0000-0000-000000000000/devices",
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":{"devices":[]},"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockGetDeviceByOrganizationID: func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*DevicesResponse, error) {
				return &DevicesResponse{
					Devices: []DeviceResponse{},
				}, nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("GET", "/organizations/00000000-0000-0000-0000-000000000000/devices", nil)
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "00000000-0000-0000-0000-000000000000",
				})
				return req
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with mocked dependencies
			handler := GetDeviceByOrganizationIDWithDeps(HandlerDeps{
				GetConnections:            tt.mockGetConnections,
				GetDeviceByOrganizationID: tt.mockGetDeviceByOrganizationID,
			})

			// Create request
			req := tt.setupRequest()

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert response status code
			assert.Equal(t, tt.expectedStatusCode, rr.Code,
				"Expected status code %d, got %d", tt.expectedStatusCode, rr.Code)

			// Assert response body
			assert.JSONEq(t, tt.expectedResponseBody, rr.Body.String(),
				"Expected response body %s, got %s", tt.expectedResponseBody, rr.Body.String())
		})
	}
}

// Test_CreateDeviceWithDeps tests the CreateDeviceWithDeps function
func Test_CreateDeviceWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name                        string
		requestPath                 string
		requestBody                 string
		mockGetConnections          func(context.Context, ...bool) (*connect.Connections, error)
		mockCreateDevice            func(connect.DatabaseExecutor, uuid.UUID, *UpdateRequest) (*DeviceResponse, error)
		mockValidateSoftwareGateway func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
		mockValidateLocation        func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
		expectedStatusCode          int
		expectedResponseBody        string
		setupRequest                func() *http.Request
	}{
		{
			name:                 "successful_device_creation",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices",
			requestBody:          `{"name":"Test Device","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY"}`,
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":{"id":"987fcdeb-51a2-43d1-b654-************","origId":0,"softwareGatewayId":null,"locationId":null,"organizationId":"123e4567-e89b-12d3-a456-************","name":"Test Device","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY","flushConnectionMs":500,"enableRealtime":true,"isEnabled":true,"createdAt":"2023-01-01T12:00:00Z","updatedAt":"2023-01-02T12:00:00Z"},"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockCreateDevice: func(pg connect.DatabaseExecutor, orgId uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return &DeviceResponse{
					Id:                uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
					OrigId:            0,
					SoftwareGatewayId: nil,
					LocationId:        nil,
					OrganizationId:    orgId,
					Name:              "Test Device",
					Description:       "Test Description",
					IpAddress:         "*************",
					Port:              8080,
					Type:              "EDI_LEGACY",
					FlushConnectionMs: 500,
					EnableRealtime:    true,
					IsEnabled:         true,
					CreatedAt:         time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
					UpdatedAt:         time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC),
				}, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("POST", "/organizations/123e4567-e89b-12d3-a456-************/devices", strings.NewReader(`{"name":"Test Device","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY"}`))
				req.Header.Set("Content-Type", "application/json")
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
				})
				return req
			},
		},
		{
			name:                 "get_connections_error",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices",
			requestBody:          `{"name":"Test Device","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY"}`,
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection error")
			},
			mockCreateDevice: func(pg connect.DatabaseExecutor, orgId uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return nil, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("POST", "/organizations/123e4567-e89b-12d3-a456-************/devices", strings.NewReader(`{"name":"Test Device","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY"}`))
				req.Header.Set("Content-Type", "application/json")
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
				})
				return req
			},
		},
		{
			name:                 "invalid_organization_id",
			requestPath:          "/organizations/invalid-uuid/devices",
			requestBody:          `{"name":"Test Device","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY"}`,
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockCreateDevice: func(pg connect.DatabaseExecutor, orgId uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return nil, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("POST", "/organizations/invalid-uuid/devices", strings.NewReader(`{"name":"Test Device","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY"}`))
				req.Header.Set("Content-Type", "application/json")
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "invalid-uuid",
				})
				return req
			},
		},
		{
			name:                 "invalid_request_body",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices",
			requestBody:          `{"name":"","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY"}`,
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockCreateDevice: func(pg connect.DatabaseExecutor, orgId uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return nil, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("POST", "/organizations/123e4567-e89b-12d3-a456-************/devices", strings.NewReader(`{"name":"","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY"}`))
				req.Header.Set("Content-Type", "application/json")
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
				})
				return req
			},
		},
		{
			name:                 "software_gateway_validation_error",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices",
			requestBody:          `{"name":"Test Device","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY","softwareGatewayId":"11111111-1111-1111-1111-111111111111"}`,
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockCreateDevice: func(pg connect.DatabaseExecutor, orgId uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return nil, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return helper.ErrSoftwareGatewayNotFound
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("POST", "/organizations/123e4567-e89b-12d3-a456-************/devices", strings.NewReader(`{"name":"Test Device","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY","softwareGatewayId":"11111111-1111-1111-1111-111111111111"}`))
				req.Header.Set("Content-Type", "application/json")
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
				})
				return req
			},
		},
		{
			name:                 "location_validation_error",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices",
			requestBody:          `{"name":"Test Device","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY","locationId":"*************-2222-2222-************"}`,
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockCreateDevice: func(pg connect.DatabaseExecutor, orgId uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return nil, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return helper.ErrLocationNotFound
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("POST", "/organizations/123e4567-e89b-12d3-a456-************/devices", strings.NewReader(`{"name":"Test Device","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY","locationId":"*************-2222-2222-************"}`))
				req.Header.Set("Content-Type", "application/json")
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
				})
				return req
			},
		},
		{
			name:                 "successful_device_creation_with_location",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices",
			requestBody:          `{"name":"Test Device","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY","locationId":"*************-2222-2222-************"}`,
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":{"id":"987fcdeb-51a2-43d1-b654-************","origId":0,"softwareGatewayId":null,"locationId":"*************-2222-2222-************","organizationId":"123e4567-e89b-12d3-a456-************","name":"Test Device","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY","flushConnectionMs":500,"enableRealtime":true,"isEnabled":true,"createdAt":"2023-01-01T12:00:00Z","updatedAt":"2023-01-02T12:00:00Z"},"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockCreateDevice: func(pg connect.DatabaseExecutor, orgId uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				locationId := uuid.MustParse("*************-2222-2222-************")
				return &DeviceResponse{
					Id:                uuid.MustParse("987fcdeb-51a2-43d1-b654-************"),
					OrigId:            0,
					SoftwareGatewayId: nil,
					LocationId:        &locationId,
					OrganizationId:    orgId,
					Name:              "Test Device",
					Description:       "Test Description",
					IpAddress:         "*************",
					Port:              8080,
					Type:              "EDI_LEGACY",
					FlushConnectionMs: 500,
					EnableRealtime:    true,
					IsEnabled:         true,
					CreatedAt:         time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
					UpdatedAt:         time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC),
				}, nil
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("POST", "/organizations/123e4567-e89b-12d3-a456-************/devices", strings.NewReader(`{"name":"Test Device","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY","locationId":"*************-2222-2222-************"}`))
				req.Header.Set("Content-Type", "application/json")
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
				})
				return req
			},
		},
		{
			name:                 "create_device_error",
			requestPath:          "/organizations/123e4567-e89b-12d3-a456-************/devices",
			requestBody:          `{"name":"Test Device","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY"}`,
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockCreateDevice: func(pg connect.DatabaseExecutor, orgId uuid.UUID, req *UpdateRequest) (*DeviceResponse, error) {
				return nil, ErrDatabaseOperation
			},
			mockValidateSoftwareGateway: func(pg connect.DatabaseExecutor, orgId uuid.UUID, softwareGatewayId uuid.UUID) error {
				return nil
			},
			mockValidateLocation: func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				req, _ := http.NewRequest("POST", "/organizations/123e4567-e89b-12d3-a456-************/devices", strings.NewReader(`{"name":"Test Device","description":"Test Description","ipAddress":"*************","port":8080,"type":"EDI_LEGACY"}`))
				req.Header.Set("Content-Type", "application/json")
				req = mux.SetURLVars(req, map[string]string{
					"organizationId": "123e4567-e89b-12d3-a456-************",
				})
				return req
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with mocked dependencies
			handler := CreateDeviceWithDeps(HandlerDeps{
				GetConnections: tt.mockGetConnections,
				CreateDevice:   tt.mockCreateDevice,
				ValidateSoftwareGateWayBelongsToOrganization: tt.mockValidateSoftwareGateway,
				ValidateLocationBelongsToOrganization:        tt.mockValidateLocation,
			})

			// Create request
			req := tt.setupRequest()

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert response status code
			assert.Equal(t, tt.expectedStatusCode, rr.Code,
				"Expected status code %d, got %d", tt.expectedStatusCode, rr.Code)

			// Assert response body
			assert.JSONEq(t, tt.expectedResponseBody, rr.Body.String(),
				"Expected response body %s, got %s", tt.expectedResponseBody, rr.Body.String())
		})
	}
}

// mockResult is a mock implementation of sql.Result for testing
type mockResult struct {
	rowsAffected      int64
	rowsAffectedError bool
}

func (m *mockResult) LastInsertId() (int64, error) {
	return 0, nil
}

func (m *mockResult) RowsAffected() (int64, error) {
	if m.rowsAffectedError {
		return 0, sql.ErrConnDone
	}
	return m.rowsAffected, nil
}
