package pkg

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewPasswordHasher(t *testing.T) {
	hasher := NewPasswordHasher()

	assert.NotNil(t, hasher, "NewPasswordHasher should return a non-nil hasher")
	assert.Implements(t, (*PasswordHasher)(nil), hasher, "Should implement PasswordHasher interface")

	// Verify it returns the concrete type
	_, ok := hasher.(*passwordHasher)
	assert.True(t, ok, "Should return a *passwordHasher instance")
}

func TestPasswordHasher_HashPassword(t *testing.T) {
	hasher := NewPasswordHasher()

	tests := []struct {
		name     string
		password string
	}{
		{
			name:     "simple password",
			password: "password123",
		},
		{
			name:     "empty password",
			password: "",
		},
		{
			name:     "complex password",
			password: "P@ssw0rd!2023",
		},
		{
			name:     "long password",
			password: "this_is_a_very_long_password_with_many_characters_1234567890",
		},
		{
			name:     "password with spaces",
			password: "password with spaces",
		},
		{
			name:     "unicode password",
			password: "\u043f\u0430\u0440\u043e\u043b\u044c123",
		},
		{
			name:     "special characters",
			password: "!@#$%^&*()_+-=[]{}|;:,.<>?",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			hash := hasher.HashPassword(tc.password)

			assert.NotEmpty(t, hash, "Hash should not be empty")
			assert.NotEqual(t, tc.password, hash, "Hash should be different from original password")

			// Verify that the same password produces the same hash
			hash2 := hasher.HashPassword(tc.password)
			assert.Equal(t, hash, hash2, "Same password should produce same hash")
		})
	}
}

func TestPasswordHasher_ComparePassword(t *testing.T) {
	hasher := NewPasswordHasher()

	tests := []struct {
		name          string
		password      string
		expectedMatch bool
		hashToCompare string // If empty, will use hashed version of password
	}{
		{
			name:          "correct password",
			password:      "password123",
			expectedMatch: true,
		},
		{
			name:          "incorrect password",
			password:      "password123",
			expectedMatch: false,
			hashToCompare: hasher.HashPassword("wrongpassword"),
		},
		{
			name:          "empty password",
			password:      "",
			expectedMatch: true,
		},
		{
			name:          "complex password",
			password:      "P@ssw0rd!2023",
			expectedMatch: true,
		},
		{
			name:          "case sensitive",
			password:      "Password123",
			expectedMatch: false,
			hashToCompare: hasher.HashPassword("password123"),
		},
		{
			name:          "unicode password",
			password:      "\u043f\u0430\u0440\u043e\u043b\u044c123",
			expectedMatch: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			var hashToCompare string
			if tc.hashToCompare != "" {
				hashToCompare = tc.hashToCompare
			} else {
				hashToCompare = hasher.HashPassword(tc.password)
			}

			result := hasher.ComparePassword(tc.password, hashToCompare)
			assert.Equal(t, tc.expectedMatch, result, "Password comparison should match expected result")
		})
	}
}

func TestPasswordHasher_HashConsistency(t *testing.T) {
	hasher := NewPasswordHasher()
	password := "testpassword"

	// Generate multiple hashes of the same password
	hashes := make([]string, 5)
	for i := 0; i < 5; i++ {
		hashes[i] = hasher.HashPassword(password)
	}

	// All hashes should be identical (SHA256 is deterministic)
	for i := 1; i < 5; i++ {
		assert.Equal(t, hashes[0], hashes[i], "All hashes of the same password should be identical")
	}

	// All hashes should be valid for the original password
	for i := 0; i < 5; i++ {
		assert.True(t, hasher.ComparePassword(password, hashes[i]), "All hashes should be valid for the original password")
	}
}

func TestPasswordHasher_DifferentPasswords(t *testing.T) {
	hasher := NewPasswordHasher()

	passwords := []string{
		"password1",
		"password2",
		"Password1",  // Different case
		"password1 ", // Extra space
		"password11", // Extra character
	}

	hashes := make([]string, len(passwords))
	for i, password := range passwords {
		hashes[i] = hasher.HashPassword(password)
	}

	// All hashes should be different
	for i := 0; i < len(hashes); i++ {
		for j := i + 1; j < len(hashes); j++ {
			assert.NotEqual(t, hashes[i], hashes[j], "Different passwords should produce different hashes")
		}
	}

	// Each password should only match its own hash
	for i, password := range passwords {
		for j, hash := range hashes {
			expected := i == j
			result := hasher.ComparePassword(password, hash)
			assert.Equal(t, expected, result, "Password should only match its own hash")
		}
	}
}

func TestPasswordHasher_Interface_Compliance(t *testing.T) {
	// Verify that passwordHasher implements PasswordHasher interface
	var _ PasswordHasher = (*passwordHasher)(nil)

	// This test will fail at compile time if the interface is not implemented correctly
	hasher := &passwordHasher{}
	assert.NotNil(t, hasher)

	// Test that all methods exist and have correct signatures
	hash := hasher.HashPassword("test")
	assert.NotEmpty(t, hash)

	result := hasher.ComparePassword("test", hash)
	assert.True(t, result)
}

func TestPasswordHasher_EdgeCases(t *testing.T) {
	hasher := NewPasswordHasher()

	// Test with very long strings
	longPassword := string(make([]byte, 10000))
	hash := hasher.HashPassword(longPassword)
	assert.NotEmpty(t, hash)
	assert.True(t, hasher.ComparePassword(longPassword, hash))

	// Test with null bytes
	passwordWithNull := "password\x00withNull"
	hashWithNull := hasher.HashPassword(passwordWithNull)
	assert.NotEmpty(t, hashWithNull)
	assert.True(t, hasher.ComparePassword(passwordWithNull, hashWithNull))
}
