package notifications

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_MockNotificationService_SendSMS(t *testing.T) {
	tests := []struct {
		name        string
		toPhone     string
		messageBody string
		setupMock   func(*MockNotificationService)
		wantErr     bool
		expectedErr error
	}{
		{
			name:        "success - sends SMS without error",
			toPhone:     "+1234567890",
			messageBody: "Test message",
			setupMock: func(m *MockNotificationService) {
				m.On("SendSMS", context.Background(), "+1234567890", "Test message").Return(nil)
			},
			wantErr: false,
		},
		{
			name:        "failure - SMS sending fails",
			toPhone:     "+1234567890",
			messageBody: "Test message",
			setupMock: func(m *MockNotificationService) {
				m.On("SendSMS", context.Background(), "+1234567890", "Test message").Return(errors.New("failed to send SMS"))
			},
			wantErr:     true,
			expectedErr: errors.New("failed to send SMS"),
		},
		{
			name:        "failure - invalid phone number",
			toPhone:     "invalid",
			messageBody: "Test message",
			setupMock: func(m *MockNotificationService) {
				m.On("SendSMS", context.Background(), "invalid", "Test message").Return(errors.New("invalid phone number"))
			},
			wantErr:     true,
			expectedErr: errors.New("invalid phone number"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock service
			mockService := new(MockNotificationService)

			// Setup mock expectations
			if tt.setupMock != nil {
				tt.setupMock(mockService)
			}

			// Execute test
			err := mockService.SendSMS(context.Background(), tt.toPhone, tt.messageBody)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "expected error but got none")
				assert.Equal(t, tt.expectedErr.Error(), err.Error(), "error message mismatch")
			} else {
				assert.NoError(t, err, "unexpected error")
			}

			// Verify all expectations were met
			mockService.AssertExpectations(t)
		})
	}
}

func Test_MockNotificationService_SendEmail(t *testing.T) {
	tests := []struct {
		name        string
		to          string
		subject     string
		body        string
		setupMock   func(*MockNotificationService)
		wantErr     bool
		expectedErr error
	}{
		{
			name:    "success - sends email without error",
			to:      "<EMAIL>",
			subject: "Test Subject",
			body:    "Test message body",
			setupMock: func(m *MockNotificationService) {
				m.On("SendEmail", context.Background(), "<EMAIL>", "Test Subject", "Test message body").Return(nil)
			},
			wantErr: false,
		},
		{
			name:    "failure - email sending fails",
			to:      "<EMAIL>",
			subject: "Test Subject",
			body:    "Test message body",
			setupMock: func(m *MockNotificationService) {
				m.On("SendEmail", context.Background(), "<EMAIL>", "Test Subject", "Test message body").Return(errors.New("failed to send email"))
			},
			wantErr:     true,
			expectedErr: errors.New("failed to send email"),
		},
		{
			name:    "failure - invalid email address",
			to:      "invalid-email",
			subject: "Test Subject",
			body:    "Test message body",
			setupMock: func(m *MockNotificationService) {
				m.On("SendEmail", context.Background(), "invalid-email", "Test Subject", "Test message body").Return(errors.New("invalid email address"))
			},
			wantErr:     true,
			expectedErr: errors.New("invalid email address"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock service
			mockService := new(MockNotificationService)

			// Setup mock expectations
			if tt.setupMock != nil {
				tt.setupMock(mockService)
			}

			// Execute test
			err := mockService.SendEmail(context.Background(), tt.to, tt.subject, tt.body)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "expected error but got none")
				assert.Equal(t, tt.expectedErr.Error(), err.Error(), "error message mismatch")
			} else {
				assert.NoError(t, err, "unexpected error")
			}

			// Verify all expectations were met
			mockService.AssertExpectations(t)
		})
	}
}

func Test_MockSMSService_SendSMS(t *testing.T) {
	tests := []struct {
		name        string
		toPhone     string
		messageBody string
		setupMock   func(*MockSMSService)
		wantErr     bool
		expectedErr error
	}{
		{
			name:        "success - sends SMS without error",
			toPhone:     "+1234567890",
			messageBody: "Test SMS message",
			setupMock: func(m *MockSMSService) {
				m.On("SendSMS", context.Background(), "+1234567890", "Test SMS message").Return(nil)
			},
			wantErr: false,
		},
		{
			name:        "failure - SMS sending fails",
			toPhone:     "+1234567890",
			messageBody: "Test SMS message",
			setupMock: func(m *MockSMSService) {
				m.On("SendSMS", context.Background(), "+1234567890", "Test SMS message").Return(errors.New("SMS service unavailable"))
			},
			wantErr:     true,
			expectedErr: errors.New("SMS service unavailable"),
		},
		{
			name:        "failure - invalid phone number",
			toPhone:     "invalid-phone",
			messageBody: "Test SMS message",
			setupMock: func(m *MockSMSService) {
				m.On("SendSMS", context.Background(), "invalid-phone", "Test SMS message").Return(errors.New("invalid phone format"))
			},
			wantErr:     true,
			expectedErr: errors.New("invalid phone format"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock service
			mockService := new(MockSMSService)

			// Setup mock expectations
			if tt.setupMock != nil {
				tt.setupMock(mockService)
			}

			// Execute test
			err := mockService.SendSMS(context.Background(), tt.toPhone, tt.messageBody)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "expected error but got none")
				assert.Equal(t, tt.expectedErr.Error(), err.Error(), "error message mismatch")
			} else {
				assert.NoError(t, err, "unexpected error")
			}

			// Verify all expectations were met
			mockService.AssertExpectations(t)
		})
	}
}

func Test_MockEmailService_SendEmail(t *testing.T) {
	tests := []struct {
		name        string
		to          string
		subject     string
		body        string
		setupMock   func(*MockEmailService)
		wantErr     bool
		expectedErr error
	}{
		{
			name:    "success - sends email without error",
			to:      "<EMAIL>",
			subject: "Test Email Subject",
			body:    "Test email body content",
			setupMock: func(m *MockEmailService) {
				m.On("SendEmail", context.Background(), "<EMAIL>", "Test Email Subject", "Test email body content").Return(nil)
			},
			wantErr: false,
		},
		{
			name:    "failure - email service error",
			to:      "<EMAIL>",
			subject: "Test Email Subject",
			body:    "Test email body content",
			setupMock: func(m *MockEmailService) {
				m.On("SendEmail", context.Background(), "<EMAIL>", "Test Email Subject", "Test email body content").Return(errors.New("email service down"))
			},
			wantErr:     true,
			expectedErr: errors.New("email service down"),
		},
		{
			name:    "failure - malformed email address",
			to:      "malformed-email",
			subject: "Test Email Subject",
			body:    "Test email body content",
			setupMock: func(m *MockEmailService) {
				m.On("SendEmail", context.Background(), "malformed-email", "Test Email Subject", "Test email body content").Return(errors.New("malformed email address"))
			},
			wantErr:     true,
			expectedErr: errors.New("malformed email address"),
		},
		{
			name:    "failure - empty subject",
			to:      "<EMAIL>",
			subject: "",
			body:    "Test email body content",
			setupMock: func(m *MockEmailService) {
				m.On("SendEmail", context.Background(), "<EMAIL>", "", "Test email body content").Return(errors.New("subject cannot be empty"))
			},
			wantErr:     true,
			expectedErr: errors.New("subject cannot be empty"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock service
			mockService := new(MockEmailService)

			// Setup mock expectations
			if tt.setupMock != nil {
				tt.setupMock(mockService)
			}

			// Execute test
			err := mockService.SendEmail(context.Background(), tt.to, tt.subject, tt.body)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "expected error but got none")
				assert.Equal(t, tt.expectedErr.Error(), err.Error(), "error message mismatch")
			} else {
				assert.NoError(t, err, "unexpected error")
			}

			// Verify all expectations were met
			mockService.AssertExpectations(t)
		})
	}
}
