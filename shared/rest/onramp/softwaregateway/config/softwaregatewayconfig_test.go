package config

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// mockSQLResult implements sql.Result interface for testing
type mockSQLResult struct {
	rowsAffected int64
	lastInsertId int64
	err          error
}

func (m *mockSQLResult) LastInsertId() (int64, error) {
	return m.lastInsertId, m.err
}

func (m *mockSQLResult) RowsAffected() (int64, error) {
	return m.rowsAffected, m.err
}

func Test_SoftwareGateway_ToResponse(t *testing.T) {
	t.Parallel()

	testId := uuid.New()
	templateId := uuid.New()
	config := `{"log_level": "error", "log_filename": "test.log"}`

	gateway := SoftwareGateway{
		Id:          testId,
		Name:        "Test Gateway",
		Description: "Test Gateway Description",
		Config:      config,
		TemplateId:  &templateId,
	}

	response := gateway.ToResponse()

	assert.Equal(t, gateway.Id, response.Id)
	assert.Equal(t, gateway.Name, response.Name)
	assert.Equal(t, gateway.Description, response.Description)
	assert.Equal(t, gateway.Config, response.Config)
	assert.Equal(t, gateway.TemplateId, response.TemplateId)
}

func Test_SoftwareGateway_ToResponseWithSettings(t *testing.T) {
	t.Parallel()

	testId := uuid.New()
	templateId := uuid.New()

	gateway := SoftwareGateway{
		Id:          testId,
		Name:        "Test Gateway",
		Description: "Test Gateway Description",
		TemplateId:  &templateId,
	}

	settings := []ResolvedGatewayConfigSetting{
		{
			Setting:     "log_level",
			Value:       "debug",
			Source:      "override",
			Name:        "Log Level",
			Description: "Logging level for the gateway",
			Format:      `{"type":"string","enum":["debug","info","warn","error"]}`,
		},
		{
			Setting:     "log_filename",
			Value:       "gateway.log",
			Source:      "default",
			Name:        "Log Filename",
			Description: "Name of the log file",
			Format:      `{"type":"string"}`,
		},
	}

	response := gateway.ToResponseWithSettings(settings)

	assert.Equal(t, gateway.Id, response.Id)
	assert.Equal(t, gateway.Name, response.Name)
	assert.Equal(t, gateway.Description, response.Description)
	assert.Equal(t, gateway.TemplateId, response.TemplateId)
	assert.Equal(t, settings, response.Settings)

	// Config should contain JSON representation of resolved settings
	expectedConfig := `{"log_filename":"gateway.log","log_level":"debug"}`
	assert.Equal(t, expectedConfig, response.Config)
}

func Test_getSoftwareGatewayByIdentifier(t *testing.T) {
	t.Parallel()

	testId := uuid.New()

	tests := []struct {
		name              string
		softwareGatewayId uuid.UUID
		mockSetup         func(*mocks.FakeDBExecutor)
		expectedResult    *SoftwareGateway
		expectedError     error
	}{
		{
			name:              "successful retrieval",
			softwareGatewayId: testId,
			mockSetup: func(mock *dbexecutor.FakeDBExecutor) {
				expectedGateway := SoftwareGateway{
					Id:          testId,
					Name:        "Test Gateway",
					Description: "Test Gateway Description",
					Config:      `{"log_level": "error"}`,
					TemplateId:  nil, // No template for this test
				}

				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					if gateway, ok := dest.(*SoftwareGateway); ok {
						*gateway = expectedGateway
					}
					return nil
				}

				// Add required mocks for the new function signature
				mock.ReplaceNamespaceFunc = func(query string) string {
					return strings.ReplaceAll(strings.ReplaceAll(query, "{{", "\""), "}}", "\"")
				}

				mock.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					settings := dest.(*[]ResolvedGatewayConfigSetting)
					*settings = []ResolvedGatewayConfigSetting{} // Empty since no template
					return nil
				}
			},
			expectedResult: &SoftwareGateway{
				Id:          testId,
				Name:        "Test Gateway",
				Description: "Test Gateway Description",
				Config:      `{"log_level": "error"}`,
			},
			expectedError: nil,
		},
		{
			name:              "not found",
			softwareGatewayId: testId,
			mockSetup: func(mock *dbexecutor.FakeDBExecutor) {
				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}

				// Add namespace replacement mock
				mock.ReplaceNamespaceFunc = func(query string) string {
					return strings.ReplaceAll(strings.ReplaceAll(query, "{{", "\""), "}}", "\"")
				}
			},
			expectedResult: nil,
			expectedError:  ErrSoftwareGatewayConfigNotFound,
		},
		{
			name:              "database error",
			softwareGatewayId: testId,
			mockSetup: func(mock *dbexecutor.FakeDBExecutor) {
				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrConnDone
				}

				// Add namespace replacement mock
				mock.ReplaceNamespaceFunc = func(query string) string {
					return strings.ReplaceAll(strings.ReplaceAll(query, "{{", "\""), "}}", "\"")
				}
			},
			expectedResult: nil,
			expectedError:  fmt.Errorf("%w: %v", ErrDatabaseOperation, sql.ErrConnDone),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock database executor
			mockDB := &mocks.FakeDBExecutor{}
			tt.mockSetup(mockDB)

			// Call the function under test
			result, settings, err := getSoftwareGatewayByIdentifier(mockDB, tt.softwareGatewayId)

			// Assert results
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err)
				assert.Nil(t, result)
				assert.Nil(t, settings)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.expectedResult.Id, result.Id)
				assert.Equal(t, tt.expectedResult.Name, result.Name)
				assert.Equal(t, tt.expectedResult.Description, result.Description)
				assert.Equal(t, tt.expectedResult.Config, result.Config)
				// Settings should contain default values even when TemplateId is nil
				// In this test mock, QueryGenericSlice returns empty, but in real usage
				// it would return default values from GatewayConfigTemplateBaseSettings
				assert.NotNil(t, settings)
			}

			// Verify the function was called
			assert.Equal(t, 1, mockDB.QueryRowStructCallCount)
		})
	}
}

// TestGetSoftwareGatewayByIdentifier tests the exported wrapper function
func TestGetSoftwareGatewayByIdentifier(t *testing.T) {
	t.Parallel()

	testId := uuid.New()
	expectedGateway := &SoftwareGateway{
		Id:          testId,
		Name:        "Test Gateway",
		Description: "Test Description",
		Config:      `{"test": "config"}`,
	}
	expectedSettings := []ResolvedGatewayConfigSetting{
		{Setting: "test_setting", Value: "test_value"},
	}

	// Create mock database executor
	mockDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			gateway, ok := dest.(*SoftwareGateway)
			if !ok {
				return errors.New("dest must be of type *SoftwareGateway")
			}
			*gateway = *expectedGateway
			return nil
		},
		QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
			settings, ok := dest.(*[]ResolvedGatewayConfigSetting)
			if !ok {
				return errors.New("dest must be of type *[]ResolvedGatewayConfigSetting")
			}
			*settings = expectedSettings
			return nil
		},
		ReplaceNamespaceFunc: func(query string) string {
			return strings.ReplaceAll(strings.ReplaceAll(query, "{{", "\""), "}}", "\"")
		},
	}

	// Call the exported function (this should cover line 968-970)
	result, settings, err := GetSoftwareGatewayByIdentifier(mockDB, testId)

	// Assert results
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, expectedGateway.Id, result.Id)
	assert.Equal(t, expectedGateway.Name, result.Name)
	assert.Equal(t, expectedGateway.Description, result.Description)
	assert.Equal(t, expectedGateway.Config, result.Config)
	assert.NotNil(t, settings)
	assert.Equal(t, len(expectedSettings), len(settings))
	if len(settings) > 0 {
		assert.Equal(t, expectedSettings[0].Setting, settings[0].Setting)
		assert.Equal(t, expectedSettings[0].Value, settings[0].Value)
	}
}

// === Template CRUD Function Tests ===

func Test_createGatewayConfigTemplate(t *testing.T) {
	t.Parallel()

	req := &CreateGatewayConfigTemplateRequest{
		OrganizationId: uuid.New(),
		Name:           "Test Template",
		Description:    "A test template",
	}

	tests := []struct {
		name        string
		request     *CreateGatewayConfigTemplateRequest
		mockError   error
		expectError error
	}{
		{
			name:    "successful creation",
			request: req,
		},
		{
			name:        "database error",
			request:     req,
			mockError:   fmt.Errorf("database connection error"),
			expectError: ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					if template, ok := dest.(*GatewayConfigTemplate); ok {
						*template = GatewayConfigTemplate{
							Id:             uuid.New(),
							OrganizationId: tt.request.OrganizationId,
							Name:           tt.request.Name,
							Description:    tt.request.Description,
						}
					}
					return nil
				},
			}

			result, err := createGatewayConfigTemplate(mockPG, tt.request)

			if tt.expectError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectError)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.request.Name, result.Name)
			}
		})
	}
}

func Test_getGatewayConfigTemplate(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()

	tests := []struct {
		name           string
		organizationId uuid.UUID
		templateId     uuid.UUID
		mockError      error
		expectError    error
	}{
		{
			name:           "successful retrieval",
			organizationId: organizationId,
			templateId:     templateId,
		},
		{
			name:           "not found",
			organizationId: organizationId,
			templateId:     templateId,
			mockError:      sql.ErrNoRows,
			expectError:    ErrGatewayConfigTemplateNotFound,
		},
		{
			name:           "database error",
			organizationId: organizationId,
			templateId:     templateId,
			mockError:      fmt.Errorf("connection failed"),
			expectError:    ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					if template, ok := dest.(*GatewayConfigTemplate); ok {
						*template = GatewayConfigTemplate{
							Id:             tt.templateId,
							OrganizationId: tt.organizationId,
							Name:           "Test Template",
							Description:    "Test Description",
						}
					}
					return nil
				},
			}

			result, err := getGatewayConfigTemplate(mockPG, tt.organizationId, tt.templateId)

			if tt.expectError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectError)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.templateId, result.Id)
			}
		})
	}
}

func Test_updateGatewayConfigTemplate(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()
	req := &UpdateGatewayConfigTemplateRequest{
		Name:        "Updated Template",
		Description: "Updated Description",
	}

	tests := []struct {
		name           string
		organizationId uuid.UUID
		templateId     uuid.UUID
		request        *UpdateGatewayConfigTemplateRequest
		mockError      error
		expectError    error
	}{
		{
			name:           "successful update",
			organizationId: organizationId,
			templateId:     templateId,
			request:        req,
		},
		{
			name:           "database error",
			organizationId: organizationId,
			templateId:     templateId,
			request:        req,
			mockError:      fmt.Errorf("update failed"),
			expectError:    ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					if template, ok := dest.(*GatewayConfigTemplate); ok {
						*template = GatewayConfigTemplate{
							Id:             tt.templateId,
							OrganizationId: tt.organizationId,
							Name:           tt.request.Name,
							Description:    tt.request.Description,
						}
					}
					return nil
				},
			}

			result, err := updateGatewayConfigTemplate(mockPG, tt.organizationId, tt.templateId, tt.request)

			if tt.expectError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectError)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.request.Name, result.Name)
			}
		})
	}
}

func Test_deleteGatewayConfigTemplate(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()

	tests := []struct {
		name           string
		organizationId uuid.UUID
		templateId     uuid.UUID
		mockError      error
		rowsAffected   int64
		expectError    error
	}{
		{
			name:           "successful deletion",
			organizationId: organizationId,
			templateId:     templateId,
			rowsAffected:   1,
		},
		{
			name:           "template not found",
			organizationId: organizationId,
			templateId:     templateId,
			rowsAffected:   0,
			expectError:    ErrGatewayConfigTemplateNotFound,
		},
		{
			name:           "database error",
			organizationId: organizationId,
			templateId:     templateId,
			mockError:      fmt.Errorf("delete failed"),
			expectError:    ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					if tt.mockError != nil {
						return nil, tt.mockError
					}
					return &mockSQLResult{rowsAffected: tt.rowsAffected}, nil
				},
			}

			err := deleteGatewayConfigTemplate(mockPG, tt.organizationId, tt.templateId)

			if tt.expectError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectError)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func Test_listGatewayConfigTemplates(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()

	tests := []struct {
		name           string
		organizationId uuid.UUID
		mockError      error
		expectError    error
	}{
		{
			name:           "successful list",
			organizationId: organizationId,
		},
		{
			name:           "database error",
			organizationId: organizationId,
			mockError:      fmt.Errorf("query failed"),
			expectError:    ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					templates := dest.(*[]GatewayConfigTemplate)
					*templates = []GatewayConfigTemplate{
						{
							Id:             uuid.New(),
							OrganizationId: tt.organizationId,
							Name:           "Template 1",
							Description:    "First template",
						},
					}
					return nil
				},
			}

			result, err := listGatewayConfigTemplates(mockPG, tt.organizationId)

			if tt.expectError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectError)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, result, 1)
			}
		})
	}
}

// === Base Settings CRUD Function Tests ===

func Test_createGatewayConfigTemplateBaseSetting(t *testing.T) {
	t.Parallel()

	baseSetting := GatewayConfigTemplateBaseSetting{
		Setting:      "test_setting",
		Name:         "Test Setting",
		Description:  "A test setting",
		DefaultValue: "default_value",
		Format:       "string",
	}

	tests := []struct {
		name        string
		baseSetting GatewayConfigTemplateBaseSetting
		mockError   error
		expectError error
	}{
		{
			name:        "successful creation",
			baseSetting: baseSetting,
		},
		{
			name:        "database error",
			baseSetting: baseSetting,
			mockError:   fmt.Errorf("database connection error"),
			expectError: ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					if tt.mockError != nil {
						return nil, tt.mockError
					}
					return &mockSQLResult{rowsAffected: 1}, nil
				},
			}

			err := createGatewayConfigTemplateBaseSetting(mockPG, tt.baseSetting)

			if tt.expectError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectError)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func Test_getGatewayConfigTemplateBaseSetting(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		setting     string
		mockError   error
		expectError error
	}{
		{
			name:    "successful retrieval",
			setting: "test_setting",
		},
		{
			name:        "not found",
			setting:     "nonexistent",
			mockError:   sql.ErrNoRows,
			expectError: ErrGatewayConfigTemplateBaseSettingNotFound,
		},
		{
			name:        "database error",
			setting:     "test_setting",
			mockError:   fmt.Errorf("query failed"),
			expectError: ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					if baseSetting, ok := dest.(*GatewayConfigTemplateBaseSetting); ok {
						*baseSetting = GatewayConfigTemplateBaseSetting{
							Setting:      tt.setting,
							Name:         "Test Setting",
							Description:  "A test setting",
							DefaultValue: "default_value",
							Format:       "string",
						}
					}
					return nil
				},
			}

			result, err := getGatewayConfigTemplateBaseSetting(mockPG, tt.setting)

			if tt.expectError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectError)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.setting, result.Setting)
			}
		})
	}
}

func Test_updateGatewayConfigTemplateBaseSetting(t *testing.T) {
	t.Parallel()

	baseSetting := GatewayConfigTemplateBaseSetting{
		Setting:      "test_setting",
		Name:         "Updated Setting",
		Description:  "Updated description",
		DefaultValue: "updated_value",
		Format:       "string",
	}

	tests := []struct {
		name         string
		setting      string
		baseSetting  GatewayConfigTemplateBaseSetting
		mockError    error
		rowsAffected int64
		expectError  error
	}{
		{
			name:         "successful update",
			setting:      "test_setting",
			baseSetting:  baseSetting,
			rowsAffected: 1,
		},
		{
			name:         "setting not found",
			setting:      "nonexistent",
			baseSetting:  baseSetting,
			rowsAffected: 0,
			expectError:  ErrGatewayConfigTemplateBaseSettingNotFound,
		},
		{
			name:        "database error",
			setting:     "test_setting",
			baseSetting: baseSetting,
			mockError:   fmt.Errorf("update failed"),
			expectError: ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					if tt.mockError != nil {
						return nil, tt.mockError
					}
					return &mockSQLResult{rowsAffected: tt.rowsAffected}, nil
				},
			}

			err := updateGatewayConfigTemplateBaseSetting(mockPG, tt.setting, tt.baseSetting)

			if tt.expectError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectError)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func Test_deleteGatewayConfigTemplateBaseSetting(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name         string
		setting      string
		mockError    error
		rowsAffected int64
		expectError  error
	}{
		{
			name:         "successful deletion",
			setting:      "test_setting",
			rowsAffected: 1,
		},
		{
			name:         "setting not found",
			setting:      "nonexistent",
			rowsAffected: 0,
			expectError:  ErrGatewayConfigTemplateBaseSettingNotFound,
		},
		{
			name:        "database error",
			setting:     "test_setting",
			mockError:   fmt.Errorf("delete failed"),
			expectError: ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					if tt.mockError != nil {
						return nil, tt.mockError
					}
					return &mockSQLResult{rowsAffected: tt.rowsAffected}, nil
				},
			}

			err := deleteGatewayConfigTemplateBaseSetting(mockPG, tt.setting)

			if tt.expectError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectError)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func Test_listGatewayConfigTemplateBaseSettings(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		mockError   error
		expectError error
	}{
		{
			name: "successful list",
		},
		{
			name:        "database error",
			mockError:   fmt.Errorf("query failed"),
			expectError: ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					baseSettings := dest.(*[]GatewayConfigTemplateBaseSetting)
					*baseSettings = []GatewayConfigTemplateBaseSetting{
						{
							Setting:      "setting1",
							Name:         "Setting 1",
							Description:  "First setting",
							DefaultValue: "default1",
							Format:       "string",
						},
					}
					return nil
				},
			}

			result, err := listGatewayConfigTemplateBaseSettings(mockPG)

			if tt.expectError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectError)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, result, 1)
			}
		})
	}
}

// === Template Settings CRUD Function Tests ===

func Test_createOrUpdateGatewayConfigTemplateSetting(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()
	req := &CreateOrUpdateGatewayConfigTemplateSettingRequest{
		GatewayConfigTemplateId: templateId,
		Setting:                 "log_level",
		Value:                   "debug",
	}

	tests := []struct {
		name           string
		organizationId uuid.UUID
		request        *CreateOrUpdateGatewayConfigTemplateSettingRequest
		mockError      error
		expectError    error
	}{
		{
			name:           "successful create or update",
			organizationId: organizationId,
			request:        req,
		},
		{
			name:           "template not in organization",
			organizationId: organizationId,
			request:        req,
			mockError:      sql.ErrNoRows,
			expectError:    ErrGatewayConfigTemplateNotFound,
		},
		{
			name:           "database error",
			organizationId: organizationId,
			request:        req,
			mockError:      fmt.Errorf("connection failed"),
			expectError:    ErrDatabaseOperation,
		},
		{
			name:           "base setting creation error",
			organizationId: organizationId,
			request:        req,
			mockError:      fmt.Errorf("base setting creation failed"),
			expectError:    ErrDatabaseOperation,
		},
		{
			name:           "insert error after update returns no rows",
			organizationId: organizationId,
			request:        req,
			mockError:      fmt.Errorf("insert failed"),
			expectError:    ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			callCount := 0
			execCallCount := 0
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					callCount++
					if callCount == 1 {
						// First call is template validation
						if tt.mockError != nil && tt.mockError == sql.ErrNoRows {
							return tt.mockError
						}
						return nil // Template exists
					}
					// For "insert error after update returns no rows" test case
					if tt.name == "insert error after update returns no rows" {
						if callCount == 2 && strings.Contains(query, "UPDATE") {
							// UPDATE returns no rows, triggering INSERT path
							return sql.ErrNoRows
						} else if callCount == 3 && strings.Contains(query, "INSERT") {
							// INSERT fails
							return tt.mockError
						}
					}
					// Second call is the UPSERT returning the setting
					if tt.mockError != nil && tt.mockError != sql.ErrNoRows && execCallCount > 0 {
						return tt.mockError
					}
					if setting, ok := dest.(*GatewayConfigTemplateSetting); ok {
						*setting = GatewayConfigTemplateSetting{
							GatewayConfigTemplateId: tt.request.GatewayConfigTemplateId,
							Setting:                 tt.request.Setting,
							Value:                   tt.request.Value,
						}
					}
					return nil
				},
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					execCallCount++
					// First Exec call is base setting creation
					if tt.name == "base setting creation error" && execCallCount == 1 {
						return nil, tt.mockError
					}
					return &mockSQLResult{rowsAffected: 1}, nil
				},
			}

			result, err := createOrUpdateGatewayConfigTemplateSetting(mockPG, tt.organizationId, tt.request)

			if tt.expectError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectError)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.request.Setting, result.Setting)
			}
		})
	}
}

func Test_getGatewayConfigTemplateSettings(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()

	tests := []struct {
		name           string
		organizationId uuid.UUID
		templateId     uuid.UUID
		mockError      error
		expectError    error
	}{
		{
			name:           "successful retrieval",
			organizationId: organizationId,
			templateId:     templateId,
		},
		{
			name:           "template not in organization",
			organizationId: organizationId,
			templateId:     templateId,
			mockError:      sql.ErrNoRows,
			expectError:    ErrGatewayConfigTemplateNotFound,
		},
		{
			name:           "database error",
			organizationId: organizationId,
			templateId:     templateId,
			mockError:      fmt.Errorf("query failed"),
			expectError:    ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			callCount := 0
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					callCount++
					if callCount == 1 {
						// First call is template validation
						if tt.mockError == sql.ErrNoRows {
							return tt.mockError
						}
						return nil // Template exists
					}
					return nil
				},
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil && tt.mockError != sql.ErrNoRows {
						return tt.mockError
					}
					settings := dest.(*[]GatewayConfigTemplateSetting)
					*settings = []GatewayConfigTemplateSetting{
						{
							GatewayConfigTemplateId: tt.templateId,
							Setting:                 "log_level",
							Value:                   "debug",
						},
					}
					return nil
				},
			}

			result, err := getGatewayConfigTemplateSettings(mockPG, tt.organizationId, tt.templateId)

			if tt.expectError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectError)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, result, 1)
			}
		})
	}
}

func Test_deleteGatewayConfigTemplateSetting(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()
	setting := "log_level"

	tests := []struct {
		name           string
		organizationId uuid.UUID
		templateId     uuid.UUID
		setting        string
		mockError      error
		rowsAffected   int64
		expectError    error
	}{
		{
			name:           "successful deletion",
			organizationId: organizationId,
			templateId:     templateId,
			setting:        setting,
			rowsAffected:   1,
		},
		{
			name:           "template not in organization",
			organizationId: organizationId,
			templateId:     templateId,
			setting:        setting,
			mockError:      sql.ErrNoRows,
			expectError:    ErrGatewayConfigTemplateNotFound,
		},
		{
			name:           "setting not found",
			organizationId: organizationId,
			templateId:     templateId,
			setting:        setting,
			rowsAffected:   0,
			expectError:    ErrGatewayConfigTemplateSettingNotFound,
		},
		{
			name:           "database error",
			organizationId: organizationId,
			templateId:     templateId,
			setting:        setting,
			mockError:      fmt.Errorf("delete failed"),
			expectError:    ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			callCount := 0
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					callCount++
					if callCount == 1 {
						// First call is template validation
						if tt.mockError == sql.ErrNoRows {
							return tt.mockError
						}
						return nil // Template exists
					}
					return nil
				},
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					if tt.mockError != nil && tt.mockError != sql.ErrNoRows {
						return nil, tt.mockError
					}
					return &mockSQLResult{rowsAffected: tt.rowsAffected}, nil
				},
			}

			err := deleteGatewayConfigTemplateSetting(mockPG, tt.organizationId, tt.templateId, tt.setting)

			if tt.expectError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectError)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func Test_bulkReplaceGatewayConfigTemplateSettings(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()
	settings := []GatewayConfigTemplateSetting{
		{
			GatewayConfigTemplateId: templateId,
			Setting:                 "log_level",
			Value:                   "debug",
		},
	}

	tests := []struct {
		name           string
		organizationId uuid.UUID
		templateId     uuid.UUID
		settings       []GatewayConfigTemplateSetting
		mockError      error
		insertError    error
		expectError    error
	}{
		{
			name:           "successful bulk replace",
			organizationId: organizationId,
			templateId:     templateId,
			settings:       settings,
		},
		{
			name:           "template not in organization",
			organizationId: organizationId,
			templateId:     templateId,
			settings:       settings,
			mockError:      sql.ErrNoRows,
			expectError:    ErrGatewayConfigTemplateNotFound,
		},
		{
			name:           "insert error after delete",
			organizationId: organizationId,
			templateId:     templateId,
			settings:       settings,
			insertError:    fmt.Errorf("insert failed"),
			expectError:    ErrDatabaseOperation,
		},
		{
			name:           "base setting creation error during bulk replace",
			organizationId: organizationId,
			templateId:     templateId,
			settings:       settings,
			insertError:    fmt.Errorf("base setting creation failed"),
			expectError:    ErrDatabaseOperation,
		},
		{
			name:           "template setting insert error",
			organizationId: organizationId,
			templateId:     templateId,
			settings:       settings,
			insertError:    fmt.Errorf("template setting insert failed"),
			expectError:    ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			callCount := 0
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError == sql.ErrNoRows {
						return tt.mockError
					}
					return nil // Template exists
				},
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					callCount++
					if callCount == 1 {
						// First call is DELETE (always succeeds)
						return &mockSQLResult{rowsAffected: 1}, nil
					}
					// For base setting creation error test case (second call)
					if tt.name == "base setting creation error during bulk replace" && callCount == 2 {
						return nil, tt.insertError
					}
					// For template setting insert error (third call - after base setting creation)
					if tt.name == "template setting insert error" && callCount == 3 {
						return nil, tt.insertError
					}
					// General insert error after delete (for backwards compatibility)
					if tt.insertError != nil && tt.name == "insert error after delete" && callCount == 2 {
						return nil, tt.insertError
					}
					return &mockSQLResult{rowsAffected: 1}, nil
				},
			}

			err := bulkReplaceGatewayConfigTemplateSettings(mockPG, tt.organizationId, tt.templateId, tt.settings)

			if tt.expectError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectError)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// === Override CRUD Function Tests ===

func Test_createOrUpdateGatewayConfigTemplateSettingOverride(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	softwareGatewayId := uuid.New()
	req := &CreateOrUpdateGatewayConfigTemplateSettingOverrideRequest{
		SoftwareGatewayId: softwareGatewayId,
		Setting:           "log_level",
		Value:             "error",
	}

	tests := []struct {
		name           string
		organizationId uuid.UUID
		request        *CreateOrUpdateGatewayConfigTemplateSettingOverrideRequest
		mockError      error
		baseSettingErr error
		insertErr      error
		expectError    error
	}{
		{
			name:           "successful create or update",
			organizationId: organizationId,
			request:        req,
		},
		{
			name:           "gateway not in organization",
			organizationId: organizationId,
			request:        req,
			mockError:      sql.ErrNoRows,
			expectError:    ErrSoftwareGatewayConfigNotFound,
		},
		{
			name:           "database error",
			organizationId: organizationId,
			request:        req,
			mockError:      fmt.Errorf("connection failed"),
			expectError:    ErrDatabaseOperation,
		},
		{
			name:           "base setting creation error",
			organizationId: organizationId,
			request:        req,
			baseSettingErr: fmt.Errorf("base setting creation failed"),
			expectError:    ErrDatabaseOperation,
		},
		{
			name:           "insert error after update returns no rows",
			organizationId: organizationId,
			request:        req,
			insertErr:      fmt.Errorf("insert failed"),
			expectError:    ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			callCount := 0
			execCallCount := 0
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					callCount++
					if callCount == 1 {
						// First call is gateway validation
						if tt.mockError != nil && tt.mockError == sql.ErrNoRows {
							return tt.mockError
						}
						return nil // Gateway exists
					}
					// Second call is the UPDATE returning the override
					if tt.insertErr != nil {
						// Simulate UPDATE returning no rows to trigger INSERT path
						return sql.ErrNoRows
					}
					if tt.mockError != nil && tt.mockError != sql.ErrNoRows {
						return tt.mockError
					}
					if override, ok := dest.(*GatewayConfigTemplateSettingOverride); ok {
						*override = GatewayConfigTemplateSettingOverride{
							SoftwareGatewayId: tt.request.SoftwareGatewayId,
							Setting:           tt.request.Setting,
							Value:             tt.request.Value,
						}
					}
					return nil
				},
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					execCallCount++
					if execCallCount == 1 {
						// First Exec call is base setting creation
						if tt.baseSettingErr != nil {
							return nil, tt.baseSettingErr
						}
						return &mockSQLResult{rowsAffected: 1}, nil
					}
					// Second Exec call is INSERT when UPDATE returns no rows
					if tt.insertErr != nil {
						return nil, tt.insertErr
					}
					return &mockSQLResult{rowsAffected: 1}, nil
				},
			}

			result, err := createOrUpdateGatewayConfigTemplateSettingOverride(mockPG, tt.organizationId, tt.request)

			if tt.expectError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectError)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.request.Setting, result.Setting)
			}
		})
	}
}

func Test_getGatewayConfigTemplateSettingOverrides(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	softwareGatewayId := uuid.New()

	tests := []struct {
		name              string
		organizationId    uuid.UUID
		softwareGatewayId uuid.UUID
		mockError         error
		expectError       error
	}{
		{
			name:              "successful retrieval",
			organizationId:    organizationId,
			softwareGatewayId: softwareGatewayId,
		},
		{
			name:              "gateway not in organization",
			organizationId:    organizationId,
			softwareGatewayId: softwareGatewayId,
			mockError:         sql.ErrNoRows,
			expectError:       ErrSoftwareGatewayConfigNotFound,
		},
		{
			name:              "database error",
			organizationId:    organizationId,
			softwareGatewayId: softwareGatewayId,
			mockError:         fmt.Errorf("query failed"),
			expectError:       ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			callCount := 0
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					callCount++
					if callCount == 1 {
						// First call is gateway validation
						if tt.mockError == sql.ErrNoRows {
							return tt.mockError
						}
						return nil // Gateway exists
					}
					return nil
				},
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil && tt.mockError != sql.ErrNoRows {
						return tt.mockError
					}
					overrides := dest.(*[]GatewayConfigTemplateSettingOverride)
					*overrides = []GatewayConfigTemplateSettingOverride{
						{
							SoftwareGatewayId: tt.softwareGatewayId,
							Setting:           "log_level",
							Value:             "error",
						},
					}
					return nil
				},
			}

			result, err := getGatewayConfigTemplateSettingOverrides(mockPG, tt.organizationId, tt.softwareGatewayId)

			if tt.expectError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectError)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, result, 1)
			}
		})
	}
}

func Test_deleteGatewayConfigTemplateSettingOverride(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	softwareGatewayId := uuid.New()
	setting := "log_level"

	tests := []struct {
		name              string
		organizationId    uuid.UUID
		softwareGatewayId uuid.UUID
		setting           string
		mockError         error
		rowsAffected      int64
		expectError       error
	}{
		{
			name:              "successful deletion",
			organizationId:    organizationId,
			softwareGatewayId: softwareGatewayId,
			setting:           setting,
			rowsAffected:      1,
		},
		{
			name:              "gateway not in organization",
			organizationId:    organizationId,
			softwareGatewayId: softwareGatewayId,
			setting:           setting,
			mockError:         sql.ErrNoRows,
			expectError:       ErrSoftwareGatewayConfigNotFound,
		},
		{
			name:              "override not found",
			organizationId:    organizationId,
			softwareGatewayId: softwareGatewayId,
			setting:           setting,
			rowsAffected:      0,
			expectError:       ErrGatewayConfigTemplateSettingOverrideNotFound,
		},
		{
			name:              "database error",
			organizationId:    organizationId,
			softwareGatewayId: softwareGatewayId,
			setting:           setting,
			mockError:         fmt.Errorf("delete failed"),
			expectError:       ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			callCount := 0
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					callCount++
					if callCount == 1 {
						// First call is gateway validation
						if tt.mockError == sql.ErrNoRows {
							return tt.mockError
						}
						return nil // Gateway exists
					}
					return nil
				},
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					if tt.mockError != nil && tt.mockError != sql.ErrNoRows {
						return nil, tt.mockError
					}
					return &mockSQLResult{rowsAffected: tt.rowsAffected}, nil
				},
			}

			err := deleteGatewayConfigTemplateSettingOverride(mockPG, tt.organizationId, tt.softwareGatewayId, tt.setting)

			if tt.expectError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectError)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func Test_bulkReplaceGatewayConfigTemplateSettingOverrides(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	softwareGatewayId := uuid.New()
	overrides := []GatewayConfigTemplateSettingOverride{
		{
			SoftwareGatewayId: softwareGatewayId,
			Setting:           "log_level",
			Value:             "error",
		},
	}

	tests := []struct {
		name              string
		organizationId    uuid.UUID
		softwareGatewayId uuid.UUID
		overrides         []GatewayConfigTemplateSettingOverride
		mockError         error
		insertError       error
		expectError       error
	}{
		{
			name:              "successful bulk replace",
			organizationId:    organizationId,
			softwareGatewayId: softwareGatewayId,
			overrides:         overrides,
		},
		{
			name:              "gateway not in organization",
			organizationId:    organizationId,
			softwareGatewayId: softwareGatewayId,
			overrides:         overrides,
			mockError:         sql.ErrNoRows,
			expectError:       ErrSoftwareGatewayConfigNotFound,
		},
		{
			name:              "insert error after delete",
			organizationId:    organizationId,
			softwareGatewayId: softwareGatewayId,
			overrides:         overrides,
			insertError:       fmt.Errorf("insert failed"),
			expectError:       ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			callCount := 0
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError == sql.ErrNoRows {
						return tt.mockError
					}
					return nil // Gateway exists
				},
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					callCount++
					if callCount == 1 {
						// First call is DELETE (always succeeds)
						return &mockSQLResult{rowsAffected: 1}, nil
					}
					// Second call is INSERT
					if tt.insertError != nil {
						return nil, tt.insertError
					}
					return &mockSQLResult{rowsAffected: 1}, nil
				},
			}

			err := bulkReplaceGatewayConfigTemplateSettingOverrides(mockPG, tt.organizationId, tt.softwareGatewayId, tt.overrides)

			if tt.expectError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectError)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func Test_GetByIdentifierHandlerWithDeps(t *testing.T) {
	t.Parallel()

	testId := uuid.New()

	tests := []struct {
		name               string
		softwareGatewayId  string
		mockSetup          func(*mocks.FakeDBExecutor)
		getConnections     func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
		expectedStatusCode int
		expectedResponse   interface{}
	}{
		{
			name:              "successful get",
			softwareGatewayId: testId.String(),
			mockSetup: func(mock *dbexecutor.FakeDBExecutor) {
				expectedGateway := SoftwareGateway{
					Id:          testId,
					Name:        "Test Gateway",
					Description: "Test Gateway Description",
					Config:      `{"log_level": "error"}`,
					TemplateId:  nil, // No template for this test
				}

				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					if gateway, ok := dest.(*SoftwareGateway); ok {
						*gateway = expectedGateway
					}
					return nil
				}

				// Add namespace replacement mock
				mock.ReplaceNamespaceFunc = func(query string) string {
					return strings.ReplaceAll(strings.ReplaceAll(query, "{{", "\""), "}}", "\"")
				}

				// Add empty settings mock for this test
				mock.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Return empty settings for this test (in real usage would return defaults)
					settings := dest.(*[]ResolvedGatewayConfigSetting)
					*settings = []ResolvedGatewayConfigSetting{}
					return nil
				}
			},
			expectedStatusCode: http.StatusOK,
			expectedResponse: SoftwareGatewayConfigResponse{
				Id:          testId,
				Name:        "Test Gateway",
				Description: "Test Gateway Description",
				Config:      `{}`, // Empty JSON object since mock returns no settings
			},
		},
		{
			name:              "getconnections error",
			softwareGatewayId: testId.String(),
			mockSetup:         func(mock *mocks.FakeDBExecutor) {},
			getConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, fmt.Errorf("connection error")
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectedResponse:   nil,
		},
		{
			name:               "invalid uuid",
			softwareGatewayId:  "invalid-uuid",
			mockSetup:          func(mock *mocks.FakeDBExecutor) {},
			expectedStatusCode: http.StatusBadRequest,
			expectedResponse:   nil,
		},
		{
			name:              "not found",
			softwareGatewayId: uuid.New().String(),
			mockSetup: func(mock *dbexecutor.FakeDBExecutor) {
				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}

				// Add namespace replacement mock
				mock.ReplaceNamespaceFunc = func(query string) string {
					return strings.ReplaceAll(strings.ReplaceAll(query, "{{", "\""), "}}", "\"")
				}
			},
			expectedStatusCode: http.StatusNotFound,
			expectedResponse:   nil,
		},
		{
			name:              "database error",
			softwareGatewayId: uuid.New().String(),
			mockSetup: func(mock *dbexecutor.FakeDBExecutor) {
				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrConnDone
				}

				// Add namespace replacement mock
				mock.ReplaceNamespaceFunc = func(query string) string {
					return strings.ReplaceAll(strings.ReplaceAll(query, "{{", "\""), "}}", "\"")
				}
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectedResponse:   nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDB := &mocks.FakeDBExecutor{}
			tt.mockSetup(mockDB)

			mockConnections := &connect.Connections{
				Postgres: mockDB,
			}

			getConns := tt.getConnections
			if getConns == nil {
				getConns = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return mockConnections, nil
				}
			}

			handler := GetByIdentifierHandlerWithDeps(HandlerDeps{
				GetConnections:           getConns,
				GetSoftwareGatewayConfig: getSoftwareGatewayByIdentifier,
			})

			req := httptest.NewRequest("GET", "/api/softwaregateway/"+tt.softwareGatewayId+"/config", nil)
			req = mux.SetURLVars(req, map[string]string{
				"identifier": tt.softwareGatewayId,
			})

			w := httptest.NewRecorder()
			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatusCode, w.Code)
			if tt.name == "getconnections error" {
				assert.Equal(t, 0, mockDB.QueryRowStructCallCount)
			} else if tt.softwareGatewayId != "invalid-uuid" {
				assert.Equal(t, 1, mockDB.QueryRowStructCallCount)
			}
		})
	}
}

// === HTTP Handler Tests ===

// Template HTTP Handler Tests
func Test_CreateTemplateHandlerWithDeps(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	req := &CreateGatewayConfigTemplateRequest{
		OrganizationId: organizationId,
		Name:           "Test Template",
		Description:    "A test template",
	}

	tests := []struct {
		name           string
		organizationId string
		requestBody    interface{}
		mockError      error
		expectedStatus int
	}{
		{
			name:           "successful creation",
			organizationId: organizationId.String(),
			requestBody:    req,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "invalid organization id",
			organizationId: "invalid-uuid",
			requestBody:    req,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid request body",
			organizationId: organizationId.String(),
			requestBody:    "invalid json",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "database error",
			organizationId: organizationId.String(),
			requestBody:    req,
			mockError:      fmt.Errorf("database error"),
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					if template, ok := dest.(*GatewayConfigTemplate); ok {
						*template = GatewayConfigTemplate{
							Id:             uuid.New(),
							OrganizationId: organizationId,
							Name:           "Test Template",
							Description:    "A test template",
						}
					}
					return nil
				},
			}

			mockConnections := &connect.Connections{Postgres: mockPG}
			getConns := func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return mockConnections, nil
			}

			handler := CreateTemplateHandlerWithDeps(HandlerDeps{
				GetConnections:              getConns,
				CreateGatewayConfigTemplate: createGatewayConfigTemplate,
			})

			var requestBody string
			if tt.requestBody == "invalid json" {
				requestBody = "invalid json"
			} else {
				body, _ := json.Marshal(tt.requestBody)
				requestBody = string(body)
			}

			req := httptest.NewRequest("POST", "/api/organization/"+tt.organizationId+"/softwaregatewayconfig/templates", strings.NewReader(requestBody))
			req = mux.SetURLVars(req, map[string]string{"organizationId": tt.organizationId})
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response struct {
					Data GatewayConfigTemplate `json:"data"`
				}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, "Test Template", response.Data.Name)
			}
		})
	}
}

func Test_GetTemplateHandlerWithDeps(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()

	tests := []struct {
		name           string
		organizationId string
		templateId     string
		mockError      error
		expectedStatus int
	}{
		{
			name:           "successful retrieval",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			expectedStatus: http.StatusOK,
		},
		{
			name:           "invalid organization id",
			organizationId: "invalid-uuid",
			templateId:     templateId.String(),
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid template id",
			organizationId: organizationId.String(),
			templateId:     "invalid-uuid",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "template not found",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			mockError:      ErrGatewayConfigTemplateNotFound,
			expectedStatus: http.StatusNotFound,
		},
		{
			name:           "database error",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			mockError:      errors.New("database connection failed"),
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					if template, ok := dest.(*GatewayConfigTemplate); ok {
						*template = GatewayConfigTemplate{
							Id:             templateId,
							OrganizationId: organizationId,
							Name:           "Test Template",
							Description:    "A test template",
						}
					}
					return nil
				},
			}

			mockConnections := &connect.Connections{Postgres: mockPG}
			getConns := func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return mockConnections, nil
			}

			handler := GetTemplateHandlerWithDeps(HandlerDeps{
				GetConnections: getConns,
				GetGatewayConfigTemplate: func(pg connect.DatabaseExecutor, organizationId, templateId uuid.UUID) (*GatewayConfigTemplate, error) {
					if tt.mockError != nil {
						return nil, tt.mockError
					}
					return getGatewayConfigTemplate(pg, organizationId, templateId)
				},
			})

			req := httptest.NewRequest("GET", "/api/organization/"+tt.organizationId+"/softwaregatewayconfig/templates/"+tt.templateId, nil)
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.organizationId,
				"templateId":     tt.templateId,
			})
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response struct {
					Data GatewayConfigTemplate `json:"data"`
				}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, templateId, response.Data.Id)
			}
		})
	}
}

func Test_GetTemplateHandlerWithDeps_ErrorCases(t *testing.T) {
	t.Parallel()

	t.Run("connection error", func(t *testing.T) {
		organizationId := uuid.New()
		templateId := uuid.New()

		handler := GetTemplateHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
		})

		req := httptest.NewRequest("GET", "/api/organization/"+organizationId.String()+"/softwaregatewayconfig/templates/"+templateId.String(), nil)
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"templateId":     templateId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func Test_ListTemplatesHandlerWithDeps(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()

	tests := []struct {
		name           string
		organizationId string
		mockError      error
		expectedStatus int
	}{
		{
			name:           "successful list",
			organizationId: organizationId.String(),
			expectedStatus: http.StatusOK,
		},
		{
			name:           "invalid organization id",
			organizationId: "invalid-uuid",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "database error",
			organizationId: organizationId.String(),
			mockError:      fmt.Errorf("database error"),
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					templates := dest.(*[]GatewayConfigTemplate)
					*templates = []GatewayConfigTemplate{
						{
							Id:             uuid.New(),
							OrganizationId: organizationId,
							Name:           "Template 1",
							Description:    "First template",
						},
					}
					return nil
				},
			}

			mockConnections := &connect.Connections{Postgres: mockPG}
			getConns := func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return mockConnections, nil
			}

			handler := ListTemplatesHandlerWithDeps(HandlerDeps{
				GetConnections:             getConns,
				ListGatewayConfigTemplates: listGatewayConfigTemplates,
			})

			req := httptest.NewRequest("GET", "/api/organization/"+tt.organizationId+"/softwaregatewayconfig/templates", nil)
			req = mux.SetURLVars(req, map[string]string{"organizationId": tt.organizationId})
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response struct {
					Data []GatewayConfigTemplate `json:"data"`
				}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Len(t, response.Data, 1)
			}
		})
	}
}

func Test_ListTemplatesHandlerWithDeps_ErrorCases(t *testing.T) {
	t.Parallel()

	t.Run("connection error", func(t *testing.T) {
		organizationId := uuid.New()

		handler := ListTemplatesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
		})

		req := httptest.NewRequest("GET", "/api/organization/"+organizationId.String()+"/softwaregatewayconfig/templates", nil)
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

// Base Settings HTTP Handler Tests
func Test_CreateBaseSettingHandlerWithDeps(t *testing.T) {
	t.Parallel()

	req := &CreateGatewayConfigTemplateBaseSettingRequest{
		Setting:      "test_setting",
		Name:         "Test Setting",
		Description:  "A test setting",
		DefaultValue: "default_value",
		Format:       "string",
	}

	tests := []struct {
		name           string
		requestBody    interface{}
		mockError      error
		expectedStatus int
	}{
		{
			name:           "successful creation",
			requestBody:    req,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "invalid request body",
			requestBody:    "invalid json",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "empty setting",
			requestBody:    &CreateGatewayConfigTemplateBaseSettingRequest{Setting: "", Name: "Test"},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "database error",
			requestBody:    req,
			mockError:      fmt.Errorf("database error"),
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					if tt.mockError != nil {
						return nil, tt.mockError
					}
					return &mockSQLResult{rowsAffected: 1}, nil
				},
			}

			mockConnections := &connect.Connections{Postgres: mockPG}
			getConns := func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return mockConnections, nil
			}

			handler := CreateBaseSettingHandlerWithDeps(HandlerDeps{
				GetConnections:                         getConns,
				CreateGatewayConfigTemplateBaseSetting: createGatewayConfigTemplateBaseSetting,
			})

			var requestBody string
			if tt.requestBody == "invalid json" {
				requestBody = "invalid json"
			} else {
				body, _ := json.Marshal(tt.requestBody)
				requestBody = string(body)
			}

			req := httptest.NewRequest("POST", "/api/softwaregatewayconfig/basesettings", strings.NewReader(requestBody))
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func Test_UpdateTemplateHandlerWithDeps(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()
	req := &UpdateGatewayConfigTemplateRequest{
		Name:        "Updated Template",
		Description: "Updated description",
	}

	tests := []struct {
		name           string
		organizationId string
		templateId     string
		requestBody    interface{}
		mockError      error
		expectedStatus int
	}{
		{
			name:           "successful update",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			requestBody:    req,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "invalid organization id",
			organizationId: "invalid-uuid",
			templateId:     templateId.String(),
			requestBody:    req,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid template id",
			organizationId: organizationId.String(),
			templateId:     "invalid-uuid",
			requestBody:    req,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "template not found",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			requestBody:    req,
			mockError:      ErrGatewayConfigTemplateNotFound,
			expectedStatus: http.StatusNotFound,
		},
		{
			name:           "invalid JSON body",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			requestBody:    "invalid json",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "database error",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			requestBody:    req,
			mockError:      errors.New("database connection failed"),
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					if template, ok := dest.(*GatewayConfigTemplate); ok {
						*template = GatewayConfigTemplate{
							Id:             templateId,
							OrganizationId: organizationId,
							Name:           req.Name,
							Description:    req.Description,
						}
					}
					return nil
				},
			}

			mockConnections := &connect.Connections{Postgres: mockPG}
			getConns := func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return mockConnections, nil
			}

			handler := UpdateTemplateHandlerWithDeps(HandlerDeps{
				GetConnections: getConns,
				UpdateGatewayConfigTemplate: func(pg connect.DatabaseExecutor, organizationId, templateId uuid.UUID, req *UpdateGatewayConfigTemplateRequest) (*GatewayConfigTemplate, error) {
					if tt.mockError != nil {
						return nil, tt.mockError
					}
					return updateGatewayConfigTemplate(pg, organizationId, templateId, req)
				},
			})

			var requestBody string
			if tt.requestBody == "invalid json" {
				requestBody = "invalid json"
			} else {
				body, _ := json.Marshal(tt.requestBody)
				requestBody = string(body)
			}

			req := httptest.NewRequest("PATCH", "/api/organization/"+tt.organizationId+"/softwaregatewayconfig/templates/"+tt.templateId, strings.NewReader(requestBody))
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.organizationId,
				"templateId":     tt.templateId,
			})
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func Test_UpdateTemplateHandlerWithDeps_ErrorCases(t *testing.T) {
	t.Parallel()

	t.Run("connection error", func(t *testing.T) {
		organizationId := uuid.New()
		templateId := uuid.New()
		req := &UpdateGatewayConfigTemplateRequest{
			Name:        "Updated Template",
			Description: "Updated description",
		}

		handler := UpdateTemplateHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
		})

		body, _ := json.Marshal(req)
		httpReq := httptest.NewRequest("PATCH", "/api/organization/"+organizationId.String()+"/softwaregatewayconfig/templates/"+templateId.String(), strings.NewReader(string(body)))
		httpReq = mux.SetURLVars(httpReq, map[string]string{
			"organizationId": organizationId.String(),
			"templateId":     templateId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, httpReq)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func Test_DeleteTemplateHandlerWithDeps(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()

	tests := []struct {
		name           string
		organizationId string
		templateId     string
		mockError      error
		rowsAffected   int64
		expectedStatus int
	}{
		{
			name:           "successful deletion",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			rowsAffected:   1,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "template not found",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			rowsAffected:   0,
			expectedStatus: http.StatusNotFound,
		},
		{
			name:           "invalid organization ID",
			organizationId: "invalid-uuid",
			templateId:     templateId.String(),
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid template ID",
			organizationId: organizationId.String(),
			templateId:     "invalid-uuid",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "database error",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			mockError:      errors.New("database connection failed"),
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					if tt.mockError != nil {
						return nil, tt.mockError
					}
					return &mockSQLResult{rowsAffected: tt.rowsAffected}, nil
				},
			}

			mockConnections := &connect.Connections{Postgres: mockPG}
			getConns := func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return mockConnections, nil
			}

			handler := DeleteTemplateHandlerWithDeps(HandlerDeps{
				GetConnections: getConns,
				DeleteGatewayConfigTemplate: func(pg connect.DatabaseExecutor, organizationId, templateId uuid.UUID) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					return deleteGatewayConfigTemplate(pg, organizationId, templateId)
				},
			})

			req := httptest.NewRequest("DELETE", "/api/organization/"+tt.organizationId+"/softwaregatewayconfig/templates/"+tt.templateId, nil)
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.organizationId,
				"templateId":     tt.templateId,
			})
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func Test_DeleteTemplateHandlerWithDeps_ErrorCases(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()

	t.Run("connection error", func(t *testing.T) {
		handler := DeleteTemplateHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
		})

		req := httptest.NewRequest("DELETE", "/api/organization/"+organizationId.String()+"/softwaregatewayconfig/templates/"+templateId.String(), nil)
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"templateId":     templateId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("template not found", func(t *testing.T) {
		handler := DeleteTemplateHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			DeleteGatewayConfigTemplate: func(pg connect.DatabaseExecutor, orgId uuid.UUID, templateId uuid.UUID) error {
				return ErrGatewayConfigTemplateNotFound
			},
		})

		req := httptest.NewRequest("DELETE", "/api/organization/"+organizationId.String()+"/softwaregatewayconfig/templates/"+templateId.String(), nil)
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"templateId":     templateId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}

// Template Settings HTTP Handler Tests
func Test_CreateOrUpdateTemplateSettingHandlerWithDeps(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()
	req := &CreateOrUpdateGatewayConfigTemplateSettingRequest{
		GatewayConfigTemplateId: templateId,
		Setting:                 "log_level",
		Value:                   "debug",
	}

	tests := []struct {
		name           string
		organizationId string
		templateId     string
		requestBody    interface{}
		rawBody        string // For invalid JSON cases
		mockError      error
		expectedStatus int
	}{
		{
			name:           "successful create or update",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			requestBody:    req,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "empty setting",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			requestBody:    &CreateOrUpdateGatewayConfigTemplateSettingRequest{Setting: "", Value: "test"},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "empty value",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			requestBody:    &CreateOrUpdateGatewayConfigTemplateSettingRequest{Setting: "test", Value: ""},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid organization ID",
			organizationId: "invalid-uuid",
			templateId:     templateId.String(),
			requestBody:    req,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid template ID",
			organizationId: organizationId.String(),
			templateId:     "invalid-uuid",
			requestBody:    req,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid JSON body",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			rawBody:        "{invalid json",
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			callCount := 0
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					callCount++
					if callCount == 1 {
						// Template validation
						return nil
					}
					// Setting creation
					if setting, ok := dest.(*GatewayConfigTemplateSetting); ok {
						*setting = GatewayConfigTemplateSetting{
							GatewayConfigTemplateId: templateId,
							Setting:                 "log_level",
							Value:                   "debug",
						}
					}
					return nil
				},
			}

			mockConnections := &connect.Connections{Postgres: mockPG}
			getConns := func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return mockConnections, nil
			}

			handler := CreateOrUpdateTemplateSettingHandlerWithDeps(HandlerDeps{
				GetConnections: getConns,
				CreateOrUpdateGatewayConfigTemplateSetting: createOrUpdateGatewayConfigTemplateSetting,
			})

			var bodyReader *strings.Reader
			if tt.rawBody != "" {
				bodyReader = strings.NewReader(tt.rawBody)
			} else {
				body, _ := json.Marshal(tt.requestBody)
				bodyReader = strings.NewReader(string(body))
			}
			req := httptest.NewRequest("POST", "/api/organization/"+tt.organizationId+"/softwaregatewayconfig/templates/"+tt.templateId+"/settings", bodyReader)
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.organizationId,
				"templateId":     tt.templateId,
			})
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func Test_CreateOrUpdateTemplateSettingHandlerWithDeps_ErrorCases(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()
	req := &CreateOrUpdateGatewayConfigTemplateSettingRequest{
		GatewayConfigTemplateId: templateId,
		Setting:                 "log_level",
		Value:                   "debug",
	}

	t.Run("connection error", func(t *testing.T) {
		handler := CreateOrUpdateTemplateSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
		})

		body, _ := json.Marshal(req)
		req := httptest.NewRequest("POST", "/api/organization/"+organizationId.String()+"/softwaregatewayconfig/templates/"+templateId.String()+"/settings", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"templateId":     templateId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("template not found", func(t *testing.T) {
		handler := CreateOrUpdateTemplateSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			CreateOrUpdateGatewayConfigTemplateSetting: func(pg connect.DatabaseExecutor, orgId uuid.UUID, request *CreateOrUpdateGatewayConfigTemplateSettingRequest) (*GatewayConfigTemplateSetting, error) {
				return nil, ErrGatewayConfigTemplateNotFound
			},
		})

		body, _ := json.Marshal(req)
		req := httptest.NewRequest("POST", "/api/organization/"+organizationId.String()+"/softwaregatewayconfig/templates/"+templateId.String()+"/settings", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"templateId":     templateId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("invalid organization ID", func(t *testing.T) {
		handler := CreateOrUpdateTemplateSettingHandlerWithDeps(HandlerDeps{})

		body, _ := json.Marshal(req)
		req := httptest.NewRequest("POST", "/api/organization/invalid-uuid/softwaregatewayconfig/templates/"+templateId.String()+"/settings", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": "invalid-uuid",
			"templateId":     templateId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid template ID", func(t *testing.T) {
		handler := CreateOrUpdateTemplateSettingHandlerWithDeps(HandlerDeps{})

		body, _ := json.Marshal(req)
		req := httptest.NewRequest("POST", "/api/organization/"+organizationId.String()+"/softwaregatewayconfig/templates/invalid-uuid/settings", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"templateId":     "invalid-uuid",
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid JSON", func(t *testing.T) {
		handler := CreateOrUpdateTemplateSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
		})

		req := httptest.NewRequest("POST", "/api/organization/"+organizationId.String()+"/softwaregatewayconfig/templates/"+templateId.String()+"/settings", strings.NewReader("invalid json"))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"templateId":     templateId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("empty setting", func(t *testing.T) {
		handler := CreateOrUpdateTemplateSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
		})

		emptySettingReq := &CreateOrUpdateGatewayConfigTemplateSettingRequest{
			GatewayConfigTemplateId: templateId,
			Setting:                 "", // Empty setting
			Value:                   "debug",
		}

		body, _ := json.Marshal(emptySettingReq)
		req := httptest.NewRequest("POST", "/api/organization/"+organizationId.String()+"/softwaregatewayconfig/templates/"+templateId.String()+"/settings", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"templateId":     templateId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("empty value", func(t *testing.T) {
		handler := CreateOrUpdateTemplateSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
		})

		emptyValueReq := &CreateOrUpdateGatewayConfigTemplateSettingRequest{
			GatewayConfigTemplateId: templateId,
			Setting:                 "log_level",
			Value:                   "", // Empty value
		}

		body, _ := json.Marshal(emptyValueReq)
		req := httptest.NewRequest("POST", "/api/organization/"+organizationId.String()+"/softwaregatewayconfig/templates/"+templateId.String()+"/settings", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"templateId":     templateId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("database error", func(t *testing.T) {
		handler := CreateOrUpdateTemplateSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			CreateOrUpdateGatewayConfigTemplateSetting: func(pg connect.DatabaseExecutor, orgId uuid.UUID, request *CreateOrUpdateGatewayConfigTemplateSettingRequest) (*GatewayConfigTemplateSetting, error) {
				return nil, errors.New("database error")
			},
		})

		body, _ := json.Marshal(req)
		req := httptest.NewRequest("POST", "/api/organization/"+organizationId.String()+"/softwaregatewayconfig/templates/"+templateId.String()+"/settings", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"templateId":     templateId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func Test_GetTemplateSettingsHandlerWithDeps(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()

	tests := []struct {
		name           string
		organizationId string
		templateId     string
		mockError      error
		expectedStatus int
	}{
		{
			name:           "successful retrieval",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			expectedStatus: http.StatusOK,
		},
		{
			name:           "template not found",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			mockError:      ErrGatewayConfigTemplateNotFound,
			expectedStatus: http.StatusNotFound,
		},
		{
			name:           "database error",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			mockError:      errors.New("database connection failed"),
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name:           "invalid organization ID",
			organizationId: "invalid-uuid",
			templateId:     templateId.String(),
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid template ID",
			organizationId: organizationId.String(),
			templateId:     "invalid-uuid",
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			callCount := 0
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					callCount++
					// For template existence check
					return nil // Template exists (will be handled by dependency injection)
				},
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					settings := dest.(*[]GatewayConfigTemplateSetting)
					*settings = []GatewayConfigTemplateSetting{
						{
							GatewayConfigTemplateId: templateId,
							Setting:                 "log_level",
							Value:                   "debug",
						},
					}
					return nil
				},
			}

			mockConnections := &connect.Connections{Postgres: mockPG}
			getConns := func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return mockConnections, nil
			}

			handler := GetTemplateSettingsHandlerWithDeps(HandlerDeps{
				GetConnections: getConns,
				GetGatewayConfigTemplateSettings: func(pg connect.DatabaseExecutor, organizationId, templateId uuid.UUID) ([]GatewayConfigTemplateSetting, error) {
					if tt.mockError != nil {
						return nil, tt.mockError
					}
					return getGatewayConfigTemplateSettings(pg, organizationId, templateId)
				},
			})

			req := httptest.NewRequest("GET", "/api/organization/"+tt.organizationId+"/softwaregatewayconfig/templates/"+tt.templateId+"/settings", nil)
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.organizationId,
				"templateId":     tt.templateId,
			})
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response struct {
					Data []GatewayConfigTemplateSetting `json:"data"`
				}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Len(t, response.Data, 1)
			}
		})
	}
}

func Test_GetTemplateSettingsHandlerWithDeps_ErrorCases(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()

	t.Run("connection error", func(t *testing.T) {
		handler := GetTemplateSettingsHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
		})

		req := httptest.NewRequest("GET", "/api/organization/"+organizationId.String()+"/softwaregatewayconfig/templates/"+templateId.String()+"/settings", nil)
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"templateId":     templateId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("template not found", func(t *testing.T) {
		handler := GetTemplateSettingsHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			GetGatewayConfigTemplateSettings: func(pg connect.DatabaseExecutor, orgId uuid.UUID, templateId uuid.UUID) ([]GatewayConfigTemplateSetting, error) {
				return nil, ErrGatewayConfigTemplateNotFound
			},
		})

		req := httptest.NewRequest("GET", "/api/organization/"+organizationId.String()+"/softwaregatewayconfig/templates/"+templateId.String()+"/settings", nil)
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"templateId":     templateId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}

func Test_DeleteTemplateSettingHandlerWithDeps(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()
	setting := "log_level"

	tests := []struct {
		name           string
		organizationId string
		templateId     string
		setting        string
		mockError      error
		rowsAffected   int64
		expectedStatus int
	}{
		{
			name:           "successful deletion",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			setting:        setting,
			rowsAffected:   1,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "setting not found",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			setting:        setting,
			rowsAffected:   0,
			expectedStatus: http.StatusNotFound,
		},
		{
			name:           "invalid organization ID",
			organizationId: "invalid-uuid",
			templateId:     templateId.String(),
			setting:        setting,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid template ID",
			organizationId: organizationId.String(),
			templateId:     "invalid-uuid",
			setting:        setting,
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					return nil // Template validation passes
				},
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					if tt.mockError != nil {
						return nil, tt.mockError
					}
					return &mockSQLResult{rowsAffected: tt.rowsAffected}, nil
				},
			}

			mockConnections := &connect.Connections{Postgres: mockPG}
			getConns := func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return mockConnections, nil
			}

			handler := DeleteTemplateSettingHandlerWithDeps(HandlerDeps{
				GetConnections:                     getConns,
				DeleteGatewayConfigTemplateSetting: deleteGatewayConfigTemplateSetting,
			})

			req := httptest.NewRequest("DELETE", "/api/organization/"+tt.organizationId+"/softwaregatewayconfig/templates/"+tt.templateId+"/settings/"+tt.setting, nil)
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.organizationId,
				"templateId":     tt.templateId,
				"setting":        tt.setting,
			})
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func Test_DeleteTemplateSettingHandlerWithDeps_ErrorCases(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()
	setting := "log_level"

	t.Run("connection error", func(t *testing.T) {
		handler := DeleteTemplateSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
		})

		req := httptest.NewRequest("DELETE", "/api/organization/"+organizationId.String()+"/softwaregatewayconfig/templates/"+templateId.String()+"/settings/"+setting, nil)
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"templateId":     templateId.String(),
			"setting":        setting,
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("template not found", func(t *testing.T) {
		handler := DeleteTemplateSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			DeleteGatewayConfigTemplateSetting: func(pg connect.DatabaseExecutor, orgId uuid.UUID, templateId uuid.UUID, setting string) error {
				return ErrGatewayConfigTemplateNotFound
			},
		})

		req := httptest.NewRequest("DELETE", "/api/organization/"+organizationId.String()+"/softwaregatewayconfig/templates/"+templateId.String()+"/settings/"+setting, nil)
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"templateId":     templateId.String(),
			"setting":        setting,
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("setting not found", func(t *testing.T) {
		handler := DeleteTemplateSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			DeleteGatewayConfigTemplateSetting: func(pg connect.DatabaseExecutor, orgId uuid.UUID, templateId uuid.UUID, setting string) error {
				return ErrGatewayConfigTemplateSettingNotFound
			},
		})

		req := httptest.NewRequest("DELETE", "/api/organization/"+organizationId.String()+"/softwaregatewayconfig/templates/"+templateId.String()+"/settings/"+setting, nil)
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"templateId":     templateId.String(),
			"setting":        setting,
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}

func Test_BulkReplaceTemplateSettingsHandlerWithDeps(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()
	settings := []GatewayConfigTemplateSetting{
		{
			GatewayConfigTemplateId: templateId,
			Setting:                 "log_level",
			Value:                   "debug",
		},
	}

	tests := []struct {
		name           string
		organizationId string
		templateId     string
		requestBody    interface{}
		rawBody        string // For cases where we need to send invalid JSON
		mockError      error
		expectedStatus int
	}{
		{
			name:           "successful bulk replace",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			requestBody:    settings,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "invalid organization ID",
			organizationId: "invalid-uuid",
			templateId:     templateId.String(),
			requestBody:    settings,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid template ID",
			organizationId: organizationId.String(),
			templateId:     "invalid-uuid",
			requestBody:    settings,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid JSON body",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			rawBody:        "{invalid json",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "template not found",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			requestBody:    settings,
			mockError:      ErrGatewayConfigTemplateNotFound,
			expectedStatus: http.StatusNotFound,
		},
		{
			name:           "database error",
			organizationId: organizationId.String(),
			templateId:     templateId.String(),
			requestBody:    settings,
			mockError:      errors.New("database connection failed"),
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			callCount := 0
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					return nil // Template validation passes
				},
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					callCount++
					return &mockSQLResult{rowsAffected: 1}, nil
				},
			}

			mockConnections := &connect.Connections{Postgres: mockPG}
			getConns := func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return mockConnections, nil
			}

			handler := BulkReplaceTemplateSettingsHandlerWithDeps(HandlerDeps{
				GetConnections: getConns,
				BulkReplaceGatewayConfigTemplateSettings: func(pg connect.DatabaseExecutor, organizationId, templateId uuid.UUID, settings []GatewayConfigTemplateSetting) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					return bulkReplaceGatewayConfigTemplateSettings(pg, organizationId, templateId, settings)
				},
			})

			var bodyReader *strings.Reader
			if tt.rawBody != "" {
				bodyReader = strings.NewReader(tt.rawBody)
			} else {
				body, _ := json.Marshal(tt.requestBody)
				bodyReader = strings.NewReader(string(body))
			}
			req := httptest.NewRequest("PUT", "/api/organization/"+tt.organizationId+"/softwaregatewayconfig/templates/"+tt.templateId+"/settings", bodyReader)
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.organizationId,
				"templateId":     tt.templateId,
			})
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func Test_BulkReplaceTemplateSettingsHandlerWithDeps_ErrorCases(t *testing.T) {
	t.Parallel()

	t.Run("connection error", func(t *testing.T) {
		organizationId := uuid.New()
		templateId := uuid.New()
		settings := []GatewayConfigTemplateSetting{
			{
				GatewayConfigTemplateId: templateId,
				Setting:                 "log_level",
				Value:                   "debug",
			},
		}

		handler := BulkReplaceTemplateSettingsHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
		})

		body, _ := json.Marshal(settings)
		bodyReader := strings.NewReader(string(body))
		req := httptest.NewRequest("PUT", "/api/organization/"+organizationId.String()+"/softwaregatewayconfig/templates/"+templateId.String()+"/settings", bodyReader)
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"templateId":     templateId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

// Override HTTP Handler Tests
func Test_CreateOrUpdateOverrideHandlerWithDeps(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	softwareGatewayId := uuid.New()
	req := &CreateOrUpdateGatewayConfigTemplateSettingOverrideRequest{
		SoftwareGatewayId: softwareGatewayId,
		Setting:           "log_level",
		Value:             "error",
	}

	tests := []struct {
		name              string
		organizationId    string
		softwareGatewayId string
		requestBody       interface{}
		rawBody           string // For invalid JSON cases
		mockError         error
		expectedStatus    int
	}{
		{
			name:              "successful create or update",
			organizationId:    organizationId.String(),
			softwareGatewayId: softwareGatewayId.String(),
			requestBody:       req,
			expectedStatus:    http.StatusOK,
		},
		{
			name:              "empty setting",
			organizationId:    organizationId.String(),
			softwareGatewayId: softwareGatewayId.String(),
			requestBody:       &CreateOrUpdateGatewayConfigTemplateSettingOverrideRequest{Setting: "", Value: "test"},
			expectedStatus:    http.StatusBadRequest,
		},
		{
			name:              "empty value",
			organizationId:    organizationId.String(),
			softwareGatewayId: softwareGatewayId.String(),
			requestBody:       &CreateOrUpdateGatewayConfigTemplateSettingOverrideRequest{Setting: "test", Value: ""},
			expectedStatus:    http.StatusBadRequest,
		},
		{
			name:              "invalid organization ID",
			organizationId:    "invalid-uuid",
			softwareGatewayId: softwareGatewayId.String(),
			requestBody:       req,
			expectedStatus:    http.StatusBadRequest,
		},
		{
			name:              "invalid software gateway ID",
			organizationId:    organizationId.String(),
			softwareGatewayId: "invalid-uuid",
			requestBody:       req,
			expectedStatus:    http.StatusBadRequest,
		},
		{
			name:              "invalid JSON body",
			organizationId:    organizationId.String(),
			softwareGatewayId: softwareGatewayId.String(),
			rawBody:           "{invalid json",
			expectedStatus:    http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			callCount := 0
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					callCount++
					if callCount == 1 {
						// Gateway validation
						return nil
					}
					// Override creation
					if override, ok := dest.(*GatewayConfigTemplateSettingOverride); ok {
						*override = GatewayConfigTemplateSettingOverride{
							SoftwareGatewayId: softwareGatewayId,
							Setting:           "log_level",
							Value:             "error",
						}
					}
					return nil
				},
			}

			mockConnections := &connect.Connections{Postgres: mockPG}
			getConns := func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return mockConnections, nil
			}

			handler := CreateOrUpdateOverrideHandlerWithDeps(HandlerDeps{
				GetConnections: getConns,
				CreateOrUpdateGatewayConfigTemplateSettingOverride: createOrUpdateGatewayConfigTemplateSettingOverride,
			})

			var bodyReader *strings.Reader
			if tt.rawBody != "" {
				bodyReader = strings.NewReader(tt.rawBody)
			} else {
				body, _ := json.Marshal(tt.requestBody)
				bodyReader = strings.NewReader(string(body))
			}
			req := httptest.NewRequest("POST", "/api/organization/"+tt.organizationId+"/softwaregateway/"+tt.softwareGatewayId+"/config/overrides", bodyReader)
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.organizationId,
				"identifier":     tt.softwareGatewayId,
			})
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func Test_CreateOrUpdateOverrideHandlerWithDeps_ErrorCases(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	softwareGatewayId := uuid.New()
	req := &CreateOrUpdateGatewayConfigTemplateSettingOverrideRequest{
		SoftwareGatewayId: softwareGatewayId,
		Setting:           "log_level",
		Value:             "error",
	}

	t.Run("connection error", func(t *testing.T) {
		handler := CreateOrUpdateOverrideHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
		})

		body, _ := json.Marshal(req)
		req := httptest.NewRequest("POST", "/api/organization/"+organizationId.String()+"/softwaregateway/"+softwareGatewayId.String()+"/config/overrides", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"identifier":     softwareGatewayId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("software gateway not found", func(t *testing.T) {
		handler := CreateOrUpdateOverrideHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			CreateOrUpdateGatewayConfigTemplateSettingOverride: func(pg connect.DatabaseExecutor, orgId uuid.UUID, request *CreateOrUpdateGatewayConfigTemplateSettingOverrideRequest) (*GatewayConfigTemplateSettingOverride, error) {
				return nil, ErrSoftwareGatewayConfigNotFound
			},
		})

		body, _ := json.Marshal(req)
		req := httptest.NewRequest("POST", "/api/organization/"+organizationId.String()+"/softwaregateway/"+softwareGatewayId.String()+"/config/overrides", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"identifier":     softwareGatewayId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}

func Test_GetOverridesHandlerWithDeps(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	softwareGatewayId := uuid.New()

	tests := []struct {
		name              string
		organizationId    string
		softwareGatewayId string
		mockError         error
		expectedStatus    int
	}{
		{
			name:              "successful retrieval",
			organizationId:    organizationId.String(),
			softwareGatewayId: softwareGatewayId.String(),
			expectedStatus:    http.StatusOK,
		},
		{
			name:              "gateway not found",
			organizationId:    organizationId.String(),
			softwareGatewayId: softwareGatewayId.String(),
			mockError:         ErrSoftwareGatewayConfigNotFound,
			expectedStatus:    http.StatusNotFound,
		},
		{
			name:              "database error",
			organizationId:    organizationId.String(),
			softwareGatewayId: softwareGatewayId.String(),
			mockError:         errors.New("database connection failed"),
			expectedStatus:    http.StatusInternalServerError,
		},
		{
			name:              "invalid organization ID",
			organizationId:    "invalid-uuid",
			softwareGatewayId: softwareGatewayId.String(),
			expectedStatus:    http.StatusBadRequest,
		},
		{
			name:              "invalid software gateway ID",
			organizationId:    organizationId.String(),
			softwareGatewayId: "invalid-uuid",
			expectedStatus:    http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			callCount := 0
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					callCount++
					// For gateway existence check
					return nil // Gateway exists (will be handled by dependency injection)
				},
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					overrides := dest.(*[]GatewayConfigTemplateSettingOverride)
					*overrides = []GatewayConfigTemplateSettingOverride{
						{
							SoftwareGatewayId: softwareGatewayId,
							Setting:           "log_level",
							Value:             "error",
						},
					}
					return nil
				},
			}

			mockConnections := &connect.Connections{Postgres: mockPG}
			getConns := func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return mockConnections, nil
			}

			handler := GetOverridesHandlerWithDeps(HandlerDeps{
				GetConnections: getConns,
				GetGatewayConfigTemplateSettingOverrides: func(pg connect.DatabaseExecutor, organizationId, softwareGatewayId uuid.UUID) ([]GatewayConfigTemplateSettingOverride, error) {
					if tt.mockError != nil {
						return nil, tt.mockError
					}
					return getGatewayConfigTemplateSettingOverrides(pg, organizationId, softwareGatewayId)
				},
			})

			req := httptest.NewRequest("GET", "/api/organization/"+tt.organizationId+"/softwaregateway/"+tt.softwareGatewayId+"/config/overrides", nil)
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.organizationId,
				"identifier":     tt.softwareGatewayId,
			})
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response struct {
					Data []GatewayConfigTemplateSettingOverride `json:"data"`
				}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Len(t, response.Data, 1)
			}
		})
	}
}

func Test_GetOverridesHandlerWithDeps_ErrorCases(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	softwareGatewayId := uuid.New()

	t.Run("connection error", func(t *testing.T) {
		handler := GetOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
		})

		req := httptest.NewRequest("GET", "/api/organization/"+organizationId.String()+"/softwaregateway/"+softwareGatewayId.String()+"/config/overrides", nil)
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"identifier":     softwareGatewayId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("software gateway not found", func(t *testing.T) {
		handler := GetOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			GetGatewayConfigTemplateSettingOverrides: func(pg connect.DatabaseExecutor, orgId uuid.UUID, sgId uuid.UUID) ([]GatewayConfigTemplateSettingOverride, error) {
				return nil, ErrSoftwareGatewayConfigNotFound
			},
		})

		req := httptest.NewRequest("GET", "/api/organization/"+organizationId.String()+"/softwaregateway/"+softwareGatewayId.String()+"/config/overrides", nil)
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"identifier":     softwareGatewayId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}

func Test_DeleteOverrideHandlerWithDeps(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	softwareGatewayId := uuid.New()
	setting := "log_level"

	tests := []struct {
		name              string
		organizationId    string
		softwareGatewayId string
		setting           string
		mockError         error
		rowsAffected      int64
		expectedStatus    int
	}{
		{
			name:              "successful deletion",
			organizationId:    organizationId.String(),
			softwareGatewayId: softwareGatewayId.String(),
			setting:           setting,
			rowsAffected:      1,
			expectedStatus:    http.StatusOK,
		},
		{
			name:              "override not found",
			organizationId:    organizationId.String(),
			softwareGatewayId: softwareGatewayId.String(),
			setting:           setting,
			rowsAffected:      0,
			expectedStatus:    http.StatusNotFound,
		},
		{
			name:              "invalid organization ID",
			organizationId:    "invalid-uuid",
			softwareGatewayId: softwareGatewayId.String(),
			setting:           setting,
			expectedStatus:    http.StatusBadRequest,
		},
		{
			name:              "invalid software gateway ID",
			organizationId:    organizationId.String(),
			softwareGatewayId: "invalid-uuid",
			setting:           setting,
			expectedStatus:    http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					return nil // Gateway validation passes
				},
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					if tt.mockError != nil {
						return nil, tt.mockError
					}
					return &mockSQLResult{rowsAffected: tt.rowsAffected}, nil
				},
			}

			mockConnections := &connect.Connections{Postgres: mockPG}
			getConns := func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return mockConnections, nil
			}

			handler := DeleteOverrideHandlerWithDeps(HandlerDeps{
				GetConnections: getConns,
				DeleteGatewayConfigTemplateSettingOverride: deleteGatewayConfigTemplateSettingOverride,
			})

			req := httptest.NewRequest("DELETE", "/api/organization/"+tt.organizationId+"/softwaregateway/"+tt.softwareGatewayId+"/config/overrides/"+tt.setting, nil)
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.organizationId,
				"identifier":     tt.softwareGatewayId,
				"setting":        tt.setting,
			})
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func Test_DeleteOverrideHandlerWithDeps_ErrorCases(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	softwareGatewayId := uuid.New()
	setting := "log_level"

	t.Run("connection error", func(t *testing.T) {
		handler := DeleteOverrideHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
		})

		req := httptest.NewRequest("DELETE", "/api/organization/"+organizationId.String()+"/softwaregateway/"+softwareGatewayId.String()+"/config/overrides/"+setting, nil)
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"identifier":     softwareGatewayId.String(),
			"setting":        setting,
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("software gateway not found", func(t *testing.T) {
		handler := DeleteOverrideHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			DeleteGatewayConfigTemplateSettingOverride: func(pg connect.DatabaseExecutor, orgId uuid.UUID, sgId uuid.UUID, setting string) error {
				return ErrSoftwareGatewayConfigNotFound
			},
		})

		req := httptest.NewRequest("DELETE", "/api/organization/"+organizationId.String()+"/softwaregateway/"+softwareGatewayId.String()+"/config/overrides/"+setting, nil)
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"identifier":     softwareGatewayId.String(),
			"setting":        setting,
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("override not found", func(t *testing.T) {
		handler := DeleteOverrideHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			DeleteGatewayConfigTemplateSettingOverride: func(pg connect.DatabaseExecutor, orgId uuid.UUID, sgId uuid.UUID, setting string) error {
				return ErrGatewayConfigTemplateSettingOverrideNotFound
			},
		})

		req := httptest.NewRequest("DELETE", "/api/organization/"+organizationId.String()+"/softwaregateway/"+softwareGatewayId.String()+"/config/overrides/"+setting, nil)
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"identifier":     softwareGatewayId.String(),
			"setting":        setting,
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("invalid organization ID", func(t *testing.T) {
		handler := DeleteOverrideHandlerWithDeps(HandlerDeps{})

		req := httptest.NewRequest("DELETE", "/api/organization/invalid-uuid/softwaregateway/"+softwareGatewayId.String()+"/config/overrides/"+setting, nil)
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": "invalid-uuid",
			"identifier":     softwareGatewayId.String(),
			"setting":        setting,
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid software gateway ID", func(t *testing.T) {
		handler := DeleteOverrideHandlerWithDeps(HandlerDeps{})

		req := httptest.NewRequest("DELETE", "/api/organization/"+organizationId.String()+"/softwaregateway/invalid-uuid/config/overrides/"+setting, nil)
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"identifier":     "invalid-uuid",
			"setting":        setting,
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("database error", func(t *testing.T) {
		handler := DeleteOverrideHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			DeleteGatewayConfigTemplateSettingOverride: func(pg connect.DatabaseExecutor, orgId uuid.UUID, sgId uuid.UUID, setting string) error {
				return errors.New("database error")
			},
		})

		req := httptest.NewRequest("DELETE", "/api/organization/"+organizationId.String()+"/softwaregateway/"+softwareGatewayId.String()+"/config/overrides/"+setting, nil)
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"identifier":     softwareGatewayId.String(),
			"setting":        setting,
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func Test_BulkReplaceOverridesHandlerWithDeps(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	softwareGatewayId := uuid.New()
	overrides := []GatewayConfigTemplateSettingOverride{
		{
			SoftwareGatewayId: softwareGatewayId,
			Setting:           "log_level",
			Value:             "error",
		},
	}

	tests := []struct {
		name              string
		organizationId    string
		softwareGatewayId string
		requestBody       interface{}
		rawBody           string // For invalid JSON cases
		mockError         error
		expectedStatus    int
	}{
		{
			name:              "successful bulk replace",
			organizationId:    organizationId.String(),
			softwareGatewayId: softwareGatewayId.String(),
			requestBody:       overrides,
			expectedStatus:    http.StatusOK,
		},
		{
			name:              "invalid organization ID",
			organizationId:    "invalid-uuid",
			softwareGatewayId: softwareGatewayId.String(),
			requestBody:       overrides,
			expectedStatus:    http.StatusBadRequest,
		},
		{
			name:              "invalid software gateway ID",
			organizationId:    organizationId.String(),
			softwareGatewayId: "invalid-uuid",
			requestBody:       overrides,
			expectedStatus:    http.StatusBadRequest,
		},
		{
			name:              "invalid JSON body",
			organizationId:    organizationId.String(),
			softwareGatewayId: softwareGatewayId.String(),
			rawBody:           "{invalid json",
			expectedStatus:    http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			callCount := 0
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					return nil // Gateway validation passes
				},
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					callCount++
					return &mockSQLResult{rowsAffected: 1}, nil
				},
			}

			mockConnections := &connect.Connections{Postgres: mockPG}
			getConns := func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return mockConnections, nil
			}

			handler := BulkReplaceOverridesHandlerWithDeps(HandlerDeps{
				GetConnections: getConns,
				BulkReplaceGatewayConfigTemplateSettingOverrides: bulkReplaceGatewayConfigTemplateSettingOverrides,
			})

			var bodyReader *strings.Reader
			if tt.rawBody != "" {
				bodyReader = strings.NewReader(tt.rawBody)
			} else {
				body, _ := json.Marshal(tt.requestBody)
				bodyReader = strings.NewReader(string(body))
			}
			req := httptest.NewRequest("PUT", "/api/organization/"+tt.organizationId+"/softwaregateway/"+tt.softwareGatewayId+"/config/overrides", bodyReader)
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.organizationId,
				"identifier":     tt.softwareGatewayId,
			})
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func Test_BulkReplaceOverridesHandlerWithDeps_ErrorCases(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	softwareGatewayId := uuid.New()
	overrides := []GatewayConfigTemplateSettingOverride{
		{
			SoftwareGatewayId: softwareGatewayId,
			Setting:           "log_level",
			Value:             "error",
		},
	}

	t.Run("connection error", func(t *testing.T) {
		handler := BulkReplaceOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
		})

		body, _ := json.Marshal(overrides)
		req := httptest.NewRequest("PUT", "/api/organization/"+organizationId.String()+"/softwaregateway/"+softwareGatewayId.String()+"/config/overrides", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"identifier":     softwareGatewayId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("software gateway not found", func(t *testing.T) {
		handler := BulkReplaceOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			BulkReplaceGatewayConfigTemplateSettingOverrides: func(pg connect.DatabaseExecutor, orgId uuid.UUID, sgId uuid.UUID, overrides []GatewayConfigTemplateSettingOverride) error {
				return ErrSoftwareGatewayConfigNotFound
			},
		})

		body, _ := json.Marshal(overrides)
		req := httptest.NewRequest("PUT", "/api/organization/"+organizationId.String()+"/softwaregateway/"+softwareGatewayId.String()+"/config/overrides", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"identifier":     softwareGatewayId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}

// Additional Base Settings HTTP Handler Tests
func Test_GetBaseSettingHandlerWithDeps(t *testing.T) {
	t.Parallel()

	setting := "test_setting"

	tests := []struct {
		name           string
		setting        string
		mockError      error
		expectedStatus int
	}{
		{
			name:           "successful retrieval",
			setting:        setting,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "setting not found",
			setting:        setting,
			mockError:      sql.ErrNoRows,
			expectedStatus: http.StatusNotFound,
		},
		{
			name:           "database error",
			setting:        setting,
			mockError:      errors.New("database connection failed"),
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					if baseSetting, ok := dest.(*GatewayConfigTemplateBaseSetting); ok {
						*baseSetting = GatewayConfigTemplateBaseSetting{
							Setting:      setting,
							Name:         "Test Setting",
							Description:  "A test setting",
							DefaultValue: "default_value",
							Format:       "string",
						}
					}
					return nil
				},
			}

			mockConnections := &connect.Connections{Postgres: mockPG}
			getConns := func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return mockConnections, nil
			}

			handler := GetBaseSettingHandlerWithDeps(HandlerDeps{
				GetConnections:                      getConns,
				GetGatewayConfigTemplateBaseSetting: getGatewayConfigTemplateBaseSetting,
			})

			req := httptest.NewRequest("GET", "/api/softwaregatewayconfig/basesettings/"+tt.setting, nil)
			req = mux.SetURLVars(req, map[string]string{"setting": tt.setting})
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response struct {
					Data GatewayConfigTemplateBaseSetting `json:"data"`
				}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, setting, response.Data.Setting)
			}
		})
	}
}

func Test_GetBaseSettingHandlerWithDeps_ErrorCases(t *testing.T) {
	t.Parallel()

	t.Run("connection error", func(t *testing.T) {
		handler := GetBaseSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
		})

		req := httptest.NewRequest("GET", "/api/softwaregatewayconfig/basesettings/test_setting", nil)
		req = mux.SetURLVars(req, map[string]string{"setting": "test_setting"})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("missing setting parameter", func(t *testing.T) {
		handler := GetBaseSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
		})

		req := httptest.NewRequest("GET", "/api/softwaregatewayconfig/basesettings/", nil)
		// Don't set setting parameter in URL vars to simulate missing parameter
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func Test_UpdateBaseSettingHandlerWithDeps(t *testing.T) {
	t.Parallel()

	setting := "test_setting"
	req := &UpdateGatewayConfigTemplateBaseSettingRequest{
		Name:         "Updated Setting",
		Description:  "Updated description",
		DefaultValue: "updated_value",
		Format:       "string",
	}

	tests := []struct {
		name           string
		setting        string
		requestBody    interface{}
		rawBody        string // For invalid JSON cases
		mockError      error
		rowsAffected   int64
		expectedStatus int
	}{
		{
			name:           "successful update",
			setting:        setting,
			requestBody:    req,
			rowsAffected:   1,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "setting not found",
			setting:        setting,
			requestBody:    req,
			rowsAffected:   0,
			expectedStatus: http.StatusNotFound,
		},
		{
			name:           "empty name",
			setting:        setting,
			requestBody:    &UpdateGatewayConfigTemplateBaseSettingRequest{Name: "", Description: "Test"},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "empty description",
			setting:        setting,
			requestBody:    &UpdateGatewayConfigTemplateBaseSettingRequest{Name: "Test", Description: "", DefaultValue: "test", Format: "string"},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "empty default value",
			setting:        setting,
			requestBody:    &UpdateGatewayConfigTemplateBaseSettingRequest{Name: "Test", Description: "Test", DefaultValue: "", Format: "string"},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "empty format",
			setting:        setting,
			requestBody:    &UpdateGatewayConfigTemplateBaseSettingRequest{Name: "Test", Description: "Test", DefaultValue: "test", Format: ""},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "missing setting parameter",
			setting:        "",
			requestBody:    req,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid JSON body",
			setting:        setting,
			rawBody:        "{invalid json",
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					if tt.mockError != nil {
						return nil, tt.mockError
					}
					return &mockSQLResult{rowsAffected: tt.rowsAffected}, nil
				},
			}

			mockConnections := &connect.Connections{Postgres: mockPG}
			getConns := func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return mockConnections, nil
			}

			handler := UpdateBaseSettingHandlerWithDeps(HandlerDeps{
				GetConnections:                         getConns,
				UpdateGatewayConfigTemplateBaseSetting: updateGatewayConfigTemplateBaseSetting,
			})

			var bodyReader *strings.Reader
			if tt.rawBody != "" {
				bodyReader = strings.NewReader(tt.rawBody)
			} else {
				body, _ := json.Marshal(tt.requestBody)
				bodyReader = strings.NewReader(string(body))
			}
			req := httptest.NewRequest("PATCH", "/api/softwaregatewayconfig/basesettings/"+tt.setting, bodyReader)
			req = mux.SetURLVars(req, map[string]string{"setting": tt.setting})
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func Test_UpdateBaseSettingHandlerWithDeps_ErrorCases(t *testing.T) {
	t.Parallel()

	setting := "test_setting"
	req := &UpdateGatewayConfigTemplateBaseSettingRequest{
		Name:         "Updated Setting",
		Description:  "Updated description",
		DefaultValue: "updated_value",
		Format:       "string",
	}

	t.Run("connection error", func(t *testing.T) {
		handler := UpdateBaseSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
		})

		body, _ := json.Marshal(req)
		req := httptest.NewRequest("PATCH", "/api/softwaregatewayconfig/basesettings/"+setting, strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{"setting": setting})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("base setting not found", func(t *testing.T) {
		handler := UpdateBaseSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			UpdateGatewayConfigTemplateBaseSetting: func(pg connect.DatabaseExecutor, setting string, baseSetting GatewayConfigTemplateBaseSetting) error {
				return ErrGatewayConfigTemplateBaseSettingNotFound
			},
		})

		body, _ := json.Marshal(req)
		req := httptest.NewRequest("PATCH", "/api/softwaregatewayconfig/basesettings/"+setting, strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{"setting": setting})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("missing setting parameter", func(t *testing.T) {
		handler := UpdateBaseSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
		})

		body, _ := json.Marshal(req)
		req := httptest.NewRequest("PATCH", "/api/softwaregatewayconfig/basesettings/", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{"setting": ""}) // Empty setting
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid JSON", func(t *testing.T) {
		handler := UpdateBaseSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
		})

		req := httptest.NewRequest("PATCH", "/api/softwaregatewayconfig/basesettings/"+setting, strings.NewReader("invalid json"))
		req = mux.SetURLVars(req, map[string]string{"setting": setting})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("missing name", func(t *testing.T) {
		handler := UpdateBaseSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
		})

		missingNameReq := &UpdateGatewayConfigTemplateBaseSettingRequest{
			Name:         "", // Missing name
			Description:  "Updated description",
			DefaultValue: "updated_value",
			Format:       "string",
		}

		body, _ := json.Marshal(missingNameReq)
		req := httptest.NewRequest("PATCH", "/api/softwaregatewayconfig/basesettings/"+setting, strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{"setting": setting})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("missing description", func(t *testing.T) {
		handler := UpdateBaseSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
		})

		missingDescReq := &UpdateGatewayConfigTemplateBaseSettingRequest{
			Name:         "Updated Setting",
			Description:  "", // Missing description
			DefaultValue: "updated_value",
			Format:       "string",
		}

		body, _ := json.Marshal(missingDescReq)
		req := httptest.NewRequest("PATCH", "/api/softwaregatewayconfig/basesettings/"+setting, strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{"setting": setting})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("missing default value", func(t *testing.T) {
		handler := UpdateBaseSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
		})

		missingDefaultReq := &UpdateGatewayConfigTemplateBaseSettingRequest{
			Name:         "Updated Setting",
			Description:  "Updated description",
			DefaultValue: "", // Missing default value
			Format:       "string",
		}

		body, _ := json.Marshal(missingDefaultReq)
		req := httptest.NewRequest("PATCH", "/api/softwaregatewayconfig/basesettings/"+setting, strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{"setting": setting})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("missing format", func(t *testing.T) {
		handler := UpdateBaseSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
		})

		missingFormatReq := &UpdateGatewayConfigTemplateBaseSettingRequest{
			Name:         "Updated Setting",
			Description:  "Updated description",
			DefaultValue: "updated_value",
			Format:       "", // Missing format
		}

		body, _ := json.Marshal(missingFormatReq)
		req := httptest.NewRequest("PATCH", "/api/softwaregatewayconfig/basesettings/"+setting, strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{"setting": setting})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("database error", func(t *testing.T) {
		handler := UpdateBaseSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			UpdateGatewayConfigTemplateBaseSetting: func(pg connect.DatabaseExecutor, setting string, baseSetting GatewayConfigTemplateBaseSetting) error {
				return errors.New("database error")
			},
		})

		body, _ := json.Marshal(req)
		req := httptest.NewRequest("PATCH", "/api/softwaregatewayconfig/basesettings/"+setting, strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{"setting": setting})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func Test_DeleteBaseSettingHandlerWithDeps(t *testing.T) {
	t.Parallel()

	setting := "test_setting"

	tests := []struct {
		name           string
		setting        string
		mockError      error
		rowsAffected   int64
		expectedStatus int
	}{
		{
			name:           "successful deletion",
			setting:        setting,
			rowsAffected:   1,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "setting not found",
			setting:        setting,
			rowsAffected:   0,
			expectedStatus: http.StatusNotFound,
		},
		{
			name:           "database error",
			setting:        setting,
			mockError:      errors.New("database connection failed"),
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					if tt.mockError != nil {
						return nil, tt.mockError
					}
					return &mockSQLResult{rowsAffected: tt.rowsAffected}, nil
				},
			}

			mockConnections := &connect.Connections{Postgres: mockPG}
			getConns := func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return mockConnections, nil
			}

			handler := DeleteBaseSettingHandlerWithDeps(HandlerDeps{
				GetConnections:                         getConns,
				DeleteGatewayConfigTemplateBaseSetting: deleteGatewayConfigTemplateBaseSetting,
			})

			req := httptest.NewRequest("DELETE", "/api/softwaregatewayconfig/basesettings/"+tt.setting, nil)
			req = mux.SetURLVars(req, map[string]string{"setting": tt.setting})
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func Test_DeleteBaseSettingHandlerWithDeps_ErrorCases(t *testing.T) {
	t.Parallel()

	t.Run("connection error", func(t *testing.T) {
		handler := DeleteBaseSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
		})

		req := httptest.NewRequest("DELETE", "/api/softwaregatewayconfig/basesettings/test_setting", nil)
		req = mux.SetURLVars(req, map[string]string{"setting": "test_setting"})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("missing setting parameter", func(t *testing.T) {
		handler := DeleteBaseSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
		})

		req := httptest.NewRequest("DELETE", "/api/softwaregatewayconfig/basesettings/", nil)
		// Don't set setting parameter in URL vars to simulate missing parameter
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func Test_ListBaseSettingsHandlerWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name            string
		mockError       error
		connectionError error
		expectedStatus  int
	}{
		{
			name:           "successful list",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "database error",
			mockError:      fmt.Errorf("database error"),
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name:            "connection error",
			connectionError: fmt.Errorf("connection failed"),
			expectedStatus:  http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					if tt.mockError != nil {
						return tt.mockError
					}
					baseSettings := dest.(*[]GatewayConfigTemplateBaseSetting)
					*baseSettings = []GatewayConfigTemplateBaseSetting{
						{
							Setting:      "setting1",
							Name:         "Setting 1",
							Description:  "First setting",
							DefaultValue: "default1",
							Format:       "string",
						},
					}
					return nil
				},
			}

			mockConnections := &connect.Connections{Postgres: mockPG}
			getConns := func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				if tt.connectionError != nil {
					return nil, tt.connectionError
				}
				return mockConnections, nil
			}

			handler := ListBaseSettingsHandlerWithDeps(HandlerDeps{
				GetConnections:                        getConns,
				ListGatewayConfigTemplateBaseSettings: listGatewayConfigTemplateBaseSettings,
			})

			req := httptest.NewRequest("GET", "/api/softwaregatewayconfig/basesettings", nil)
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response struct {
					Data []GatewayConfigTemplateBaseSetting `json:"data"`
				}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Len(t, response.Data, 1)
			}
		})
	}
}

// Production Handler Tests - Basic smoke tests for handler wiring
func Test_ProductionHandlers(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		handler  http.Handler
		method   string
		path     string
		body     string
		urlVars  map[string]string
		expected int // Expected to be != 200 due to missing DB connections or invalid UUIDs
	}{
		{
			name:     "GetByIdentifierHandler invalid UUID",
			handler:  GetByIdentifierHandler,
			method:   "GET",
			path:     "/api/softwaregateway/invalid-uuid/config",
			urlVars:  map[string]string{"identifier": "invalid-uuid"},
			expected: http.StatusBadRequest,
		},
		{
			name:     "CreateTemplateHandler invalid UUID",
			handler:  CreateTemplateHandler,
			method:   "POST",
			path:     "/api/organization/invalid-uuid/templates",
			body:     "{}",
			urlVars:  map[string]string{"organizationId": "invalid-uuid"},
			expected: http.StatusBadRequest,
		},
		{
			name:     "GetTemplateHandler invalid UUID",
			handler:  GetTemplateHandler,
			method:   "GET",
			path:     "/api/organization/invalid-uuid/templates/test",
			urlVars:  map[string]string{"organizationId": "invalid-uuid", "templateId": "test"},
			expected: http.StatusBadRequest,
		},
		{
			name:     "CreateBaseSettingHandler missing DB",
			handler:  CreateBaseSettingHandler,
			method:   "POST",
			path:     "/api/softwaregatewayconfig/basesettings",
			body:     `{"setting":"test","name":"test","description":"test","defaultValue":"test","format":"string"}`,
			expected: http.StatusInternalServerError, // DB connection failure
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest(tt.method, tt.path, strings.NewReader(tt.body))
			if tt.urlVars != nil {
				req = mux.SetURLVars(req, tt.urlVars)
			}
			w := httptest.NewRecorder()

			tt.handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expected, w.Code)
		})
	}
}

// Additional error path tests for 100% coverage
func Test_getSoftwareGatewayByIdentifier_WithTemplates(t *testing.T) {
	t.Parallel()

	testId := uuid.New()
	templateId := uuid.New()

	tests := []struct {
		name                string
		softwareGatewayId   uuid.UUID
		mockSetup           func(*dbexecutor.FakeDBExecutor)
		settingsQueryResult []ResolvedGatewayConfigSetting
		expectedResult      *SoftwareGateway
		expectedSettings    []ResolvedGatewayConfigSetting
		expectedError       error
	}{
		{
			name:              "gateway found with template",
			softwareGatewayId: testId,
			mockSetup: func(mock *dbexecutor.FakeDBExecutor) {
				expectedGateway := SoftwareGateway{
					Id:          testId,
					Name:        "Test Gateway",
					Description: "Test Gateway Description",
					TemplateId:  &templateId,
				}

				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					if gateway, ok := dest.(*SoftwareGateway); ok {
						*gateway = expectedGateway
					}
					return nil
				}

				mock.ReplaceNamespaceFunc = func(query string) string {
					return strings.ReplaceAll(strings.ReplaceAll(query, "{{", "\""), "}}", "\"")
				}

				mock.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					settings := dest.(*[]ResolvedGatewayConfigSetting)
					*settings = []ResolvedGatewayConfigSetting{
						{
							Setting:     "log_level",
							Value:       "debug",
							Source:      "template",
							Name:        "Log Level",
							Description: "Logging level",
							Format:      "string",
						},
					}
					return nil
				}
			},
			settingsQueryResult: []ResolvedGatewayConfigSetting{
				{
					Setting:     "log_level",
					Value:       "debug",
					Source:      "template",
					Name:        "Log Level",
					Description: "Logging level",
					Format:      "string",
				},
			},
			expectedResult: &SoftwareGateway{
				Id:          testId,
				Name:        "Test Gateway",
				Description: "Test Gateway Description",
				TemplateId:  &templateId,
			},
			expectedSettings: []ResolvedGatewayConfigSetting{
				{
					Setting:     "log_level",
					Value:       "debug",
					Source:      "template",
					Name:        "Log Level",
					Description: "Logging level",
					Format:      "string",
				},
			},
			expectedError: nil,
		},
		{
			name:              "gateway found but no template ID",
			softwareGatewayId: testId,
			mockSetup: func(mock *dbexecutor.FakeDBExecutor) {
				expectedGateway := SoftwareGateway{
					Id:          testId,
					Name:        "Test Gateway",
					Description: "Test Gateway Description",
					TemplateId:  nil,
				}

				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					if gateway, ok := dest.(*SoftwareGateway); ok {
						*gateway = expectedGateway
					}
					return nil
				}

				mock.ReplaceNamespaceFunc = func(query string) string {
					return strings.ReplaceAll(strings.ReplaceAll(query, "{{", "\""), "}}", "\"")
				}

				mock.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					settings := dest.(*[]ResolvedGatewayConfigSetting)
					*settings = []ResolvedGatewayConfigSetting{} // Empty since no template
					return nil
				}
			},
			settingsQueryResult: []ResolvedGatewayConfigSetting{},
			expectedResult: &SoftwareGateway{
				Id:          testId,
				Name:        "Test Gateway",
				Description: "Test Gateway Description",
				TemplateId:  nil,
			},
			expectedSettings: []ResolvedGatewayConfigSetting{},
			expectedError:    nil,
		},
		{
			name:              "settings query error",
			softwareGatewayId: testId,
			mockSetup: func(mock *dbexecutor.FakeDBExecutor) {
				expectedGateway := SoftwareGateway{
					Id:          testId,
					Name:        "Test Gateway",
					Description: "Test Gateway Description",
					TemplateId:  &templateId,
				}

				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					if gateway, ok := dest.(*SoftwareGateway); ok {
						*gateway = expectedGateway
					}
					return nil
				}

				mock.ReplaceNamespaceFunc = func(query string) string {
					return strings.ReplaceAll(strings.ReplaceAll(query, "{{", "\""), "}}", "\"")
				}

				mock.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return fmt.Errorf("settings query failed")
				}
			},
			expectedResult: nil,
			expectedError:  ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.mockSetup(mockDB)

			result, settings, err := getSoftwareGatewayByIdentifier(mockDB, tt.softwareGatewayId)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedError)
				assert.Nil(t, result)
				assert.Nil(t, settings)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.expectedResult.Id, result.Id)
				assert.Equal(t, tt.expectedResult.Name, result.Name)
				assert.Equal(t, tt.expectedResult.Description, result.Description)
				assert.Equal(t, tt.expectedSettings, settings)
			}

			assert.Equal(t, 1, mockDB.QueryRowStructCallCount)
		})
	}
}

// Test common error scenarios across handlers
func Test_HandlerCommonErrors(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()

	tests := []struct {
		name           string
		handlerFactory func(HandlerDeps) http.HandlerFunc
		deps           HandlerDeps
		method         string
		body           string
		vars           map[string]string
		expectedStatus int
	}{
		// Connection errors
		{
			name: "CreateTemplateHandler connection error",
			handlerFactory: func(deps HandlerDeps) http.HandlerFunc {
				return CreateTemplateHandlerWithDeps(deps)
			},
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return nil, fmt.Errorf("connection failed")
				},
			},
			method:         "POST",
			body:           `{"name":"test","description":"test"}`,
			vars:           map[string]string{"organizationId": organizationId.String()},
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name: "GetTemplateHandler connection error",
			handlerFactory: func(deps HandlerDeps) http.HandlerFunc {
				return GetTemplateHandlerWithDeps(deps)
			},
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return nil, fmt.Errorf("connection failed")
				},
			},
			method:         "GET",
			vars:           map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()},
			expectedStatus: http.StatusInternalServerError,
		},
		// Invalid JSON errors
		{
			name: "CreateTemplateHandler invalid JSON",
			handlerFactory: func(deps HandlerDeps) http.HandlerFunc {
				return CreateTemplateHandlerWithDeps(deps)
			},
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
				},
			},
			method:         "POST",
			body:           `{invalid json`,
			vars:           map[string]string{"organizationId": organizationId.String()},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "UpdateTemplateHandler invalid JSON",
			handlerFactory: func(deps HandlerDeps) http.HandlerFunc {
				return UpdateTemplateHandlerWithDeps(deps)
			},
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
				},
			},
			method:         "PATCH",
			body:           `{invalid json`,
			vars:           map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()},
			expectedStatus: http.StatusBadRequest,
		},
		// Invalid UUID errors
		{
			name: "CreateTemplateHandler invalid organization UUID",
			handlerFactory: func(deps HandlerDeps) http.HandlerFunc {
				return CreateTemplateHandlerWithDeps(deps)
			},
			deps:           HandlerDeps{},
			method:         "POST",
			body:           `{"name":"test","description":"test"}`,
			vars:           map[string]string{"organizationId": "invalid-uuid"},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "GetTemplateHandler invalid template UUID",
			handlerFactory: func(deps HandlerDeps) http.HandlerFunc {
				return GetTemplateHandlerWithDeps(deps)
			},
			deps:           HandlerDeps{},
			method:         "GET",
			vars:           map[string]string{"organizationId": organizationId.String(), "templateId": "invalid-uuid"},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "CreateOrUpdateOverrideHandler invalid software gateway UUID",
			handlerFactory: func(deps HandlerDeps) http.HandlerFunc {
				return CreateOrUpdateOverrideHandlerWithDeps(deps)
			},
			deps:           HandlerDeps{},
			method:         "POST",
			body:           `{"setting":"test","value":"value"}`,
			vars:           map[string]string{"organizationId": organizationId.String(), "identifier": "invalid-uuid"},
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := tt.handlerFactory(tt.deps)
			req := httptest.NewRequest(tt.method, "/test", strings.NewReader(tt.body))
			if tt.vars != nil {
				req = mux.SetURLVars(req, tt.vars)
			}
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// Test RowsAffected errors
func Test_RowsAffectedErrors(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		function    func() error
		expectError error
	}{
		{
			name: "deleteGatewayConfigTemplateSetting RowsAffected error",
			function: func() error {
				mockPG := &dbexecutor.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return nil // Template validation passes
					},
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return &mockSQLResult{rowsAffected: -1, err: fmt.Errorf("RowsAffected failed")}, nil
					},
				}
				return deleteGatewayConfigTemplateSetting(mockPG, uuid.New(), uuid.New(), "test_setting")
			},
			expectError: ErrDatabaseOperation,
		},
		{
			name: "deleteGatewayConfigTemplateSettingOverride RowsAffected error",
			function: func() error {
				mockPG := &dbexecutor.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return nil // Gateway validation passes
					},
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return &mockSQLResult{rowsAffected: -1, err: fmt.Errorf("RowsAffected failed")}, nil
					},
				}
				return deleteGatewayConfigTemplateSettingOverride(mockPG, uuid.New(), uuid.New(), "test_setting")
			},
			expectError: ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.function()
			assert.Error(t, err)
			assert.ErrorIs(t, err, tt.expectError)
		})
	}
}

// Test bulk operations insert errors
func Test_BulkOperationInsertErrors(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		function    func() error
		expectError error
	}{
		{
			name: "bulkReplaceGatewayConfigTemplateSettings insert error",
			function: func() error {
				callCount := 0
				mockPG := &dbexecutor.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return nil // Template validation passes
					},
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						callCount++
						if callCount == 1 {
							return &mockSQLResult{rowsAffected: 1}, nil // DELETE succeeds
						}
						return nil, fmt.Errorf("insert failed") // INSERT fails
					},
				}
				settings := []GatewayConfigTemplateSetting{
					{GatewayConfigTemplateId: uuid.New(), Setting: "test", Value: "value"},
				}
				return bulkReplaceGatewayConfigTemplateSettings(mockPG, uuid.New(), uuid.New(), settings)
			},
			expectError: ErrDatabaseOperation,
		},
		{
			name: "bulkReplaceGatewayConfigTemplateSettingOverrides insert error",
			function: func() error {
				callCount := 0
				mockPG := &dbexecutor.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return nil // Gateway validation passes
					},
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						callCount++
						if callCount == 1 {
							return &mockSQLResult{rowsAffected: 1}, nil // DELETE succeeds
						}
						return nil, fmt.Errorf("insert failed") // INSERT fails
					},
				}
				overrides := []GatewayConfigTemplateSettingOverride{
					{SoftwareGatewayId: uuid.New(), Setting: "test", Value: "value"},
				}
				return bulkReplaceGatewayConfigTemplateSettingOverrides(mockPG, uuid.New(), uuid.New(), overrides)
			},
			expectError: ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.function()
			assert.Error(t, err)
			assert.ErrorIs(t, err, tt.expectError)
		})
	}
}

// Test validation errors and specific error paths
func Test_ValidationAndSpecificErrors(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()
	softwareGatewayId := uuid.New()

	tests := []struct {
		name           string
		handlerFactory func() http.HandlerFunc
		method         string
		body           string
		vars           map[string]string
		expectedStatus int
	}{
		// Validation errors
		{
			name: "CreateTemplateHandler empty name",
			handlerFactory: func() http.HandlerFunc {
				return CreateTemplateHandlerWithDeps(HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
					},
					CreateGatewayConfigTemplate: createGatewayConfigTemplate,
				})
			},
			method:         "POST",
			body:           `{"name": "", "description": "test"}`,
			vars:           map[string]string{"organizationId": organizationId.String()},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "CreateTemplateHandler empty description",
			handlerFactory: func() http.HandlerFunc {
				return CreateTemplateHandlerWithDeps(HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
					},
					CreateGatewayConfigTemplate: createGatewayConfigTemplate,
				})
			},
			method:         "POST",
			body:           `{"name": "test", "description": ""}`,
			vars:           map[string]string{"organizationId": organizationId.String()},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "CreateOrUpdateTemplateSettingHandler empty setting",
			handlerFactory: func() http.HandlerFunc {
				return CreateOrUpdateTemplateSettingHandlerWithDeps(HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
					},
				})
			},
			method:         "POST",
			body:           `{"setting":"","value":"value"}`,
			vars:           map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "CreateOrUpdateOverrideHandler empty setting",
			handlerFactory: func() http.HandlerFunc {
				return CreateOrUpdateOverrideHandlerWithDeps(HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
					},
				})
			},
			method:         "POST",
			body:           `{"setting":"","value":"value"}`,
			vars:           map[string]string{"organizationId": organizationId.String(), "identifier": softwareGatewayId.String()},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "CreateBaseSettingHandler empty setting",
			handlerFactory: func() http.HandlerFunc {
				return CreateBaseSettingHandlerWithDeps(HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
					},
				})
			},
			method:         "POST",
			body:           `{"setting":"","name":"test","description":"test","defaultValue":"test","format":"string"}`,
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			handler := tt.handlerFactory()
			req := httptest.NewRequest(tt.method, "/test", strings.NewReader(tt.body))
			if tt.vars != nil {
				req = mux.SetURLVars(req, tt.vars)
			}
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// Test database errors for various operations
func Test_DatabaseErrors(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	templateId := uuid.New()
	softwareGatewayId := uuid.New()

	t.Run("GetTemplateHandler database error", func(t *testing.T) {
		databaseError := fmt.Errorf("database connection timeout")

		handler := GetTemplateHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return databaseError // This should trigger lines 187-189
					},
				}}, nil
			},
			GetGatewayConfigTemplate: getGatewayConfigTemplate,
		})

		req := httptest.NewRequest("GET", "/templates/"+templateId.String(), nil)
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("UpdateTemplateHandler connection error - lines 229-233", func(t *testing.T) {
		handler := UpdateTemplateHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, fmt.Errorf("connection failed")
			},
			UpdateGatewayConfigTemplate: updateGatewayConfigTemplate,
		})

		body := `{"name":"test","description":"test"}`
		req := httptest.NewRequest("PATCH", "/templates/"+templateId.String(), strings.NewReader(body))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("UpdateTemplateHandler database error (not not-found) - lines 242-244", func(t *testing.T) {
		handler := UpdateTemplateHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return fmt.Errorf("database update failed") // Non-not-found error
					},
				}}, nil
			},
			UpdateGatewayConfigTemplate: updateGatewayConfigTemplate,
		})

		body := `{"name":"test","description":"test"}`
		req := httptest.NewRequest("PATCH", "/templates/"+templateId.String(), strings.NewReader(body))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("DeleteTemplateHandler connection error - lines 268-272", func(t *testing.T) {
		handler := DeleteTemplateHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, fmt.Errorf("connection failed")
			},
			DeleteGatewayConfigTemplate: deleteGatewayConfigTemplate,
		})

		req := httptest.NewRequest("DELETE", "/templates/"+templateId.String(), nil)
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("DeleteTemplateHandler database error - lines 276-280", func(t *testing.T) {
		handler := DeleteTemplateHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return nil, fmt.Errorf("database delete failed") // Non-not-found error
					},
				}}, nil
			},
			DeleteGatewayConfigTemplate: deleteGatewayConfigTemplate,
		})

		req := httptest.NewRequest("DELETE", "/templates/"+templateId.String(), nil)
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("DeleteTemplateHandler rows affected error - lines 289-291", func(t *testing.T) {
		handler := DeleteTemplateHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return &mockSQLResult{rowsAffected: -1, err: fmt.Errorf("RowsAffected failed")}, nil
					},
				}}, nil
			},
			DeleteGatewayConfigTemplate: deleteGatewayConfigTemplate,
		})

		req := httptest.NewRequest("DELETE", "/templates/"+templateId.String(), nil)
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("ListTemplatesHandler connection error - lines 315-319", func(t *testing.T) {
		handler := ListTemplatesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, fmt.Errorf("connection failed")
			},
		})

		req := httptest.NewRequest("GET", "/templates", nil)
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("CreateOrUpdateTemplateSettingHandler connection error - lines 352-356", func(t *testing.T) {
		handler := CreateOrUpdateTemplateSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, fmt.Errorf("connection failed")
			},
		})

		body := `{"gatewayConfigTemplateId":"` + templateId.String() + `","setting":"test","value":"value"}`
		req := httptest.NewRequest("POST", "/templates/"+templateId.String()+"/settings", strings.NewReader(body))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("CreateOrUpdateTemplateSettingHandler validation error - lines 360-364", func(t *testing.T) {
		handler := CreateOrUpdateTemplateSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
		})

		body := `{"gatewayConfigTemplateId":"` + templateId.String() + `","setting":"","value":"value"}` // Empty setting
		req := httptest.NewRequest("POST", "/templates/"+templateId.String()+"/settings", strings.NewReader(body))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("GetTemplateSettingsHandler connection error - lines 378-382", func(t *testing.T) {
		handler := GetTemplateSettingsHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, fmt.Errorf("connection failed")
			},
		})

		req := httptest.NewRequest("GET", "/templates/"+templateId.String()+"/settings", nil)
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("GetTemplateSettingsHandler database error - lines 386-393", func(t *testing.T) {
		handler := GetTemplateSettingsHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						return fmt.Errorf("database error")
					},
				}}, nil
			},
			GetGatewayConfigTemplateSettings: getGatewayConfigTemplateSettings,
		})

		req := httptest.NewRequest("GET", "/templates/"+templateId.String()+"/settings", nil)
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("DeleteTemplateSettingHandler connection error - lines 417-421", func(t *testing.T) {
		handler := DeleteTemplateSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, fmt.Errorf("connection failed")
			},
		})

		req := httptest.NewRequest("DELETE", "/templates/"+templateId.String()+"/settings/test", nil)
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String(), "setting": "test"})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("DeleteTemplateSettingHandler database error - lines 425-429", func(t *testing.T) {
		handler := DeleteTemplateSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return fmt.Errorf("database error") // Non-not-found error
					},
				}}, nil
			},
			DeleteGatewayConfigTemplateSetting: deleteGatewayConfigTemplateSetting,
		})

		req := httptest.NewRequest("DELETE", "/templates/"+templateId.String()+"/settings/test", nil)
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String(), "setting": "test"})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("DeleteTemplateSettingHandler rows affected error - lines 438-440", func(t *testing.T) {
		handler := DeleteTemplateSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return nil // Template validation passes
					},
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return &mockSQLResult{rowsAffected: -1, err: fmt.Errorf("RowsAffected failed")}, nil
					},
				}}, nil
			},
			DeleteGatewayConfigTemplateSetting: deleteGatewayConfigTemplateSetting,
		})

		req := httptest.NewRequest("DELETE", "/templates/"+templateId.String()+"/settings/test", nil)
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String(), "setting": "test"})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("BulkReplaceTemplateSettingsHandler invalid organization ID - lines 509-513", func(t *testing.T) {
		handler := BulkReplaceTemplateSettingsHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			BulkReplaceGatewayConfigTemplateSettings: bulkReplaceGatewayConfigTemplateSettings,
		})

		req := httptest.NewRequest("PUT", "/templates/"+templateId.String()+"/settings", strings.NewReader(`[]`))
		req = mux.SetURLVars(req, map[string]string{"organizationId": "invalid-uuid", "templateId": templateId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("BulkReplaceTemplateSettingsHandler invalid template ID - lines 517-521", func(t *testing.T) {
		handler := BulkReplaceTemplateSettingsHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			BulkReplaceGatewayConfigTemplateSettings: bulkReplaceGatewayConfigTemplateSettings,
		})

		req := httptest.NewRequest("PUT", "/templates/invalid-uuid/settings", strings.NewReader(`[]`))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": "invalid-uuid"})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("BulkReplaceTemplateSettingsHandler JSON decode error - lines 525-529", func(t *testing.T) {
		handler := BulkReplaceTemplateSettingsHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			BulkReplaceGatewayConfigTemplateSettings: bulkReplaceGatewayConfigTemplateSettings,
		})

		req := httptest.NewRequest("PUT", "/templates/"+templateId.String()+"/settings", strings.NewReader(`{invalid json`))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("BulkReplaceTemplateSettingsHandler template not found error - lines 542-545", func(t *testing.T) {
		handler := BulkReplaceTemplateSettingsHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			BulkReplaceGatewayConfigTemplateSettings: func(pg connect.DatabaseExecutor, organizationId uuid.UUID, templateId uuid.UUID, settings []GatewayConfigTemplateSetting) error {
				return ErrGatewayConfigTemplateNotFound
			},
		})

		req := httptest.NewRequest("PUT", "/templates/"+templateId.String()+"/settings", strings.NewReader(`[]`))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("BulkReplaceTemplateSettingsHandler connection error - lines 533-537", func(t *testing.T) {
		handler := BulkReplaceTemplateSettingsHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, fmt.Errorf("connection failed")
			},
		})

		req := httptest.NewRequest("PUT", "/templates/"+templateId.String()+"/settings", strings.NewReader(`[]`))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("BulkReplaceTemplateSettingsHandler database error - lines 474-478", func(t *testing.T) {
		handler := BulkReplaceTemplateSettingsHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return nil, fmt.Errorf("database error")
					},
				}}, nil
			},
			BulkReplaceGatewayConfigTemplateSettings: bulkReplaceGatewayConfigTemplateSettings,
		})

		req := httptest.NewRequest("PUT", "/templates/"+templateId.String()+"/settings", strings.NewReader(`[]`))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("BulkReplaceTemplateSettingsHandler insert error - lines 483-486", func(t *testing.T) {
		// This should test the bulk operation insert error path
		callCount := 0
		handler := BulkReplaceTemplateSettingsHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						callCount++
						if callCount == 1 {
							// First call (DELETE) succeeds
							return &mockSQLResult{rowsAffected: 0}, nil
						}
						// Second call (INSERT) fails
						return nil, fmt.Errorf("insert failed")
					},
				}}, nil
			},
			BulkReplaceGatewayConfigTemplateSettings: bulkReplaceGatewayConfigTemplateSettings,
		})

		settings := `[{"gatewayConfigTemplateId":"` + templateId.String() + `","setting":"test","value":"value"}]`
		req := httptest.NewRequest("PUT", "/templates/"+templateId.String()+"/settings", strings.NewReader(settings))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("BulkReplaceTemplateSettingsHandler exec error - lines 1065-1069", func(t *testing.T) {
		callCount := 0
		handler := BulkReplaceTemplateSettingsHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return nil // Template validation passes
					},
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						callCount++
						if callCount == 1 {
							// First call (DELETE) fails
							return nil, fmt.Errorf("delete failed")
						}
						return &mockSQLResult{rowsAffected: 1}, nil
					},
				}}, nil
			},
			BulkReplaceGatewayConfigTemplateSettings: bulkReplaceGatewayConfigTemplateSettings,
		})

		settings := `[{"gatewayConfigTemplateId":"` + templateId.String() + `","setting":"test","value":"value"}]`
		req := httptest.NewRequest("PUT", "/templates/"+templateId.String()+"/settings", strings.NewReader(settings))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "templateId": templateId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	// Continue with override handlers...
	t.Run("CreateOrUpdateOverrideHandler connection error - lines 517-521", func(t *testing.T) {
		handler := CreateOrUpdateOverrideHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, fmt.Errorf("connection failed")
			},
		})

		body := `{"softwareGatewayId":"` + softwareGatewayId.String() + `","setting":"test","value":"value"}`
		req := httptest.NewRequest("POST", "/softwaregateway/"+softwareGatewayId.String()+"/config/overrides", strings.NewReader(body))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "identifier": softwareGatewayId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("CreateOrUpdateOverrideHandler validation error - lines 525-529", func(t *testing.T) {
		handler := CreateOrUpdateOverrideHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
		})

		body := `{"softwareGatewayId":"` + softwareGatewayId.String() + `","setting":"","value":"value"}` // Empty setting
		req := httptest.NewRequest("POST", "/softwaregateway/"+softwareGatewayId.String()+"/config/overrides", strings.NewReader(body))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "identifier": softwareGatewayId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("CreateOrUpdateOverrideHandler database error - lines 533-537", func(t *testing.T) {
		handler := CreateOrUpdateOverrideHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return fmt.Errorf("database error")
					},
				}}, nil
			},
			CreateOrUpdateGatewayConfigTemplateSettingOverride: createOrUpdateGatewayConfigTemplateSettingOverride,
		})

		body := `{"softwareGatewayId":"` + softwareGatewayId.String() + `","setting":"test","value":"value"}`
		req := httptest.NewRequest("POST", "/softwaregateway/"+softwareGatewayId.String()+"/config/overrides", strings.NewReader(body))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "identifier": softwareGatewayId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	// Additional override handler error paths
	t.Run("GetOverridesHandler connection error - lines 574-578", func(t *testing.T) {
		handler := GetOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, fmt.Errorf("connection failed")
			},
		})

		req := httptest.NewRequest("GET", "/softwaregateway/"+softwareGatewayId.String()+"/config/overrides", nil)
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "identifier": softwareGatewayId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("GetOverridesHandler validation error - lines 582-586", func(t *testing.T) {
		handler := GetOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return fmt.Errorf("gateway validation failed")
					},
				}}, nil
			},
			GetGatewayConfigTemplateSettingOverrides: getGatewayConfigTemplateSettingOverrides,
		})

		req := httptest.NewRequest("GET", "/softwaregateway/"+softwareGatewayId.String()+"/config/overrides", nil)
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "identifier": softwareGatewayId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("GetOverridesHandler database error - lines 600-604", func(t *testing.T) {
		handler := GetOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return nil // Gateway validation passes
					},
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						return fmt.Errorf("database error")
					},
				}}, nil
			},
			GetGatewayConfigTemplateSettingOverrides: getGatewayConfigTemplateSettingOverrides,
		})

		req := httptest.NewRequest("GET", "/softwaregateway/"+softwareGatewayId.String()+"/config/overrides", nil)
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "identifier": softwareGatewayId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	// Base settings error paths
	t.Run("CreateBaseSettingHandler validation error - lines 1308-1312", func(t *testing.T) {
		handler := CreateBaseSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
		})

		body := `{"setting":"","defaultValue":"test","description":"test"}` // Empty setting
		req := httptest.NewRequest("POST", "/softwaregatewayconfig/basesettings", strings.NewReader(body))
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("CreateBaseSettingHandler database error - lines 1281-1285", func(t *testing.T) {
		handler := CreateBaseSettingHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return nil, fmt.Errorf("database error")
					},
				}}, nil
			},
			CreateGatewayConfigTemplateBaseSetting: createGatewayConfigTemplateBaseSetting,
		})

		body := `{"setting":"test","name":"Test Setting","defaultValue":"test","description":"test","format":"string"}`
		req := httptest.NewRequest("POST", "/softwaregatewayconfig/basesettings", strings.NewReader(body))
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("BulkReplaceOverridesHandler invalid organization ID - lines 731-735", func(t *testing.T) {
		handler := BulkReplaceOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			BulkReplaceGatewayConfigTemplateSettingOverrides: bulkReplaceGatewayConfigTemplateSettingOverrides,
		})

		req := httptest.NewRequest("PUT", "/softwaregateway/"+softwareGatewayId.String()+"/config/overrides", strings.NewReader(`[]`))
		req = mux.SetURLVars(req, map[string]string{"organizationId": "invalid-uuid", "identifier": softwareGatewayId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("BulkReplaceOverridesHandler invalid software gateway ID - lines 738-743", func(t *testing.T) {
		handler := BulkReplaceOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			BulkReplaceGatewayConfigTemplateSettingOverrides: bulkReplaceGatewayConfigTemplateSettingOverrides,
		})

		req := httptest.NewRequest("PUT", "/softwaregateway/invalid-uuid/config/overrides", strings.NewReader(`[]`))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "identifier": "invalid-uuid"})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("BulkReplaceOverridesHandler JSON decode error - lines 747-751", func(t *testing.T) {
		handler := BulkReplaceOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			BulkReplaceGatewayConfigTemplateSettingOverrides: bulkReplaceGatewayConfigTemplateSettingOverrides,
		})

		req := httptest.NewRequest("PUT", "/softwaregateway/"+softwareGatewayId.String()+"/config/overrides", strings.NewReader(`{invalid json`))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "identifier": softwareGatewayId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("BulkReplaceOverridesHandler connection error - lines 754-759", func(t *testing.T) {
		handler := BulkReplaceOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, fmt.Errorf("connection failed")
			},
		})

		req := httptest.NewRequest("PUT", "/softwaregateway/"+softwareGatewayId.String()+"/config/overrides", strings.NewReader(`[]`))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "identifier": softwareGatewayId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("BulkReplaceOverridesHandler software gateway not found error - lines 764-767", func(t *testing.T) {
		handler := BulkReplaceOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			BulkReplaceGatewayConfigTemplateSettingOverrides: func(pg connect.DatabaseExecutor, organizationId uuid.UUID, softwareGatewayId uuid.UUID, overrides []GatewayConfigTemplateSettingOverride) error {
				return ErrSoftwareGatewayConfigNotFound
			},
		})

		req := httptest.NewRequest("PUT", "/softwaregateway/"+softwareGatewayId.String()+"/config/overrides", strings.NewReader(`[]`))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "identifier": softwareGatewayId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	// More comprehensive bulk replace error paths for all remaining lines
	t.Run("BulkReplaceOverridesHandler insert error - line 951-952", func(t *testing.T) {
		callCount := 0
		handler := BulkReplaceOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return nil // Gateway validation passes
					},
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						callCount++
						if callCount == 1 {
							// First call (DELETE) succeeds
							return &mockSQLResult{rowsAffected: 0}, nil
						}
						// Second call (INSERT) fails
						return nil, fmt.Errorf("insert failed")
					},
				}}, nil
			},
			BulkReplaceGatewayConfigTemplateSettingOverrides: bulkReplaceGatewayConfigTemplateSettingOverrides,
		})

		overrides := `[{"softwareGatewayId":"` + softwareGatewayId.String() + `","setting":"test","value":"value"}]`
		req := httptest.NewRequest("PUT", "/softwaregateway/"+softwareGatewayId.String()+"/config/overrides", strings.NewReader(overrides))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "identifier": softwareGatewayId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("BulkReplaceOverridesHandler exec error - lines 1456-1460", func(t *testing.T) {
		callCount := 0
		handler := BulkReplaceOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return nil // Gateway validation passes
					},
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						callCount++
						if callCount == 1 {
							// First call (DELETE) fails
							return nil, fmt.Errorf("delete failed")
						}
						return &mockSQLResult{rowsAffected: 1}, nil
					},
				}}, nil
			},
			BulkReplaceGatewayConfigTemplateSettingOverrides: bulkReplaceGatewayConfigTemplateSettingOverrides,
		})

		overrides := `[{"softwareGatewayId":"` + softwareGatewayId.String() + `","setting":"test","value":"value"}]`
		req := httptest.NewRequest("PUT", "/softwaregateway/"+softwareGatewayId.String()+"/config/overrides", strings.NewReader(overrides))
		req = mux.SetURLVars(req, map[string]string{"organizationId": organizationId.String(), "identifier": softwareGatewayId.String()})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

// === Missing Coverage Tests for RowsAffected Errors ===

// Mock SQL result that returns error on RowsAffected()
type mockSQLResultRowsAffectedError struct {
	rowsAffected int64
	err          error
}

func (m *mockSQLResultRowsAffectedError) LastInsertId() (int64, error) {
	return 0, nil
}

func (m *mockSQLResultRowsAffectedError) RowsAffected() (int64, error) {
	return 0, m.err // Always return error on RowsAffected
}

func TestMissingCoverageRowsAffectedErrors(t *testing.T) {
	organizationId := uuid.New()
	templateId := uuid.New()
	softwareGatewayId := uuid.New()

	t.Run("deleteGatewayConfigTemplate RowsAffected error", func(t *testing.T) {
		executor := &dbexecutor.FakeDBExecutor{
			ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
				return &mockSQLResultRowsAffectedError{err: fmt.Errorf("RowsAffected failed")}, nil
			},
		}

		err := deleteGatewayConfigTemplate(executor, organizationId, templateId)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "RowsAffected failed")
	})

	t.Run("deleteGatewayConfigTemplateSetting RowsAffected error", func(t *testing.T) {
		callCount := 0
		executor := &dbexecutor.FakeDBExecutor{
			QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
				// First call - template verification succeeds
				if callCount == 0 {
					callCount++
					return nil
				}
				return sql.ErrNoRows
			},
			ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
				return &mockSQLResultRowsAffectedError{err: fmt.Errorf("RowsAffected failed")}, nil
			},
		}

		err := deleteGatewayConfigTemplateSetting(executor, organizationId, templateId, "test-setting")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "RowsAffected failed")
	})

	t.Run("deleteGatewayConfigTemplateSettingOverride RowsAffected error", func(t *testing.T) {
		callCount := 0
		executor := &dbexecutor.FakeDBExecutor{
			QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
				// First call - gateway verification succeeds
				if callCount == 0 {
					callCount++
					return nil
				}
				return sql.ErrNoRows
			},
			ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
				return &mockSQLResultRowsAffectedError{err: fmt.Errorf("RowsAffected failed")}, nil
			},
		}

		err := deleteGatewayConfigTemplateSettingOverride(executor, organizationId, softwareGatewayId, "test-setting")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "RowsAffected failed")
	})

	t.Run("updateGatewayConfigTemplateBaseSetting RowsAffected error", func(t *testing.T) {
		baseSetting := GatewayConfigTemplateBaseSetting{
			Setting:      "test-setting",
			Name:         "Test Setting",
			Description:  "Test Description",
			DefaultValue: "test-value",
			Format:       "string",
		}

		executor := &dbexecutor.FakeDBExecutor{
			ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
				return &mockSQLResultRowsAffectedError{err: fmt.Errorf("RowsAffected failed")}, nil
			},
		}

		err := updateGatewayConfigTemplateBaseSetting(executor, "test-setting", baseSetting)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "RowsAffected failed")
	})

	t.Run("deleteGatewayConfigTemplateBaseSetting RowsAffected error", func(t *testing.T) {
		executor := &dbexecutor.FakeDBExecutor{
			ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
				return &mockSQLResultRowsAffectedError{err: fmt.Errorf("RowsAffected failed")}, nil
			},
		}

		err := deleteGatewayConfigTemplateBaseSetting(executor, "test-setting")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "RowsAffected failed")
	})
}

// === Missing Coverage Tests for Database Verification Errors ===

func TestMissingCoverageDatabaseVerificationErrors(t *testing.T) {
	organizationId := uuid.New()
	templateId := uuid.New()
	softwareGatewayId := uuid.New()

	t.Run("updateGatewayConfigTemplate ErrNoRows", func(t *testing.T) {
		executor := &dbexecutor.FakeDBExecutor{
			QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
				return sql.ErrNoRows // Template not found during update
			},
		}

		req := &UpdateGatewayConfigTemplateRequest{
			Name:        "Updated Template",
			Description: "Updated description",
		}

		result, err := updateGatewayConfigTemplate(executor, organizationId, templateId, req)
		assert.Nil(t, result)
		assert.ErrorIs(t, err, ErrGatewayConfigTemplateNotFound)
	})

	t.Run("createOrUpdateGatewayConfigTemplateSetting database error during verification", func(t *testing.T) {
		executor := &dbexecutor.FakeDBExecutor{
			QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
				return errors.New("database verification error") // Database error during template verification
			},
		}

		req := &CreateOrUpdateGatewayConfigTemplateSettingRequest{
			GatewayConfigTemplateId: templateId,
			Setting:                 "log_level",
			Value:                   "debug",
		}

		result, err := createOrUpdateGatewayConfigTemplateSetting(executor, organizationId, req)
		assert.Nil(t, result)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "database verification error")
	})

	t.Run("bulkReplaceGatewayConfigTemplateSettings database error during verification", func(t *testing.T) {
		executor := &dbexecutor.FakeDBExecutor{
			QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
				return errors.New("database verification error") // Database error during template verification
			},
		}

		settings := []GatewayConfigTemplateSetting{
			{
				GatewayConfigTemplateId: templateId,
				Setting:                 "log_level",
				Value:                   "debug",
			},
		}

		err := bulkReplaceGatewayConfigTemplateSettings(executor, organizationId, templateId, settings)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "database verification error")
	})

	t.Run("deleteGatewayConfigTemplateSetting database error during verification", func(t *testing.T) {
		executor := &dbexecutor.FakeDBExecutor{
			QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
				return errors.New("database verification error") // Database error during template verification
			},
		}

		err := deleteGatewayConfigTemplateSetting(executor, organizationId, templateId, "log_level")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "database verification error")
	})

	t.Run("deleteGatewayConfigTemplateSettingOverride database error during verification", func(t *testing.T) {
		executor := &dbexecutor.FakeDBExecutor{
			QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
				return errors.New("database verification error") // Database error during software gateway verification
			},
		}

		err := deleteGatewayConfigTemplateSettingOverride(executor, organizationId, softwareGatewayId, "log_level")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "database verification error")
	})

	t.Run("bulkReplaceGatewayConfigTemplateSettingOverrides database error during verification", func(t *testing.T) {
		executor := &dbexecutor.FakeDBExecutor{
			QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
				return errors.New("database verification error") // Database error during software gateway verification
			},
		}

		overrides := []GatewayConfigTemplateSettingOverride{
			{
				SoftwareGatewayId: softwareGatewayId,
				Setting:           "log_level",
				Value:             "debug",
			},
		}

		err := bulkReplaceGatewayConfigTemplateSettingOverrides(executor, organizationId, softwareGatewayId, overrides)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "database verification error")
	})

	t.Run("getGatewayConfigTemplateSettings database error during verification", func(t *testing.T) {
		executor := &dbexecutor.FakeDBExecutor{
			QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
				return errors.New("database verification error") // Database error during template verification
			},
		}

		result, err := getGatewayConfigTemplateSettings(executor, organizationId, templateId)
		assert.Nil(t, result)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "database verification error")
	})
}

// === Legacy Bulk Upsert Overrides Tests ===

func Test_getOrganizationIdBySoftwareGatewayId(t *testing.T) {
	organizationId := uuid.New()
	softwareGatewayId := uuid.New()

	tests := []struct {
		name        string
		setupMock   func() *dbexecutor.FakeDBExecutor
		expectedOrg uuid.UUID
		expectedErr error
		shouldError bool
	}{
		{
			name: "successful lookup",
			setupMock: func() *dbexecutor.FakeDBExecutor {
				executor := &dbexecutor.FakeDBExecutor{}
				executor.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					if result, ok := dest.(*struct {
						OrganizationId uuid.UUID `db:"organizationid"`
					}); ok {
						result.OrganizationId = organizationId
					}
					return nil
				}
				return executor
			},
			expectedOrg: organizationId,
			expectedErr: nil,
			shouldError: false,
		},
		{
			name: "software gateway not found",
			setupMock: func() *dbexecutor.FakeDBExecutor {
				executor := &dbexecutor.FakeDBExecutor{}
				executor.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
				return executor
			},
			expectedOrg: uuid.Nil,
			expectedErr: ErrSoftwareGatewayConfigNotFound,
			shouldError: true,
		},
		{
			name: "database error",
			setupMock: func() *dbexecutor.FakeDBExecutor {
				executor := &dbexecutor.FakeDBExecutor{}
				executor.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return fmt.Errorf("connection failed")
				}
				return executor
			},
			expectedOrg: uuid.Nil,
			expectedErr: ErrDatabaseOperation,
			shouldError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			executor := tt.setupMock()
			result, err := getOrganizationIdBySoftwareGatewayId(executor, softwareGatewayId)

			if tt.shouldError {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Equal(t, tt.expectedOrg, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedOrg, result)
			}
		})
	}
}

func Test_BulkUpsertOverridesLegacyHandlerWithDeps(t *testing.T) {
	organizationId := uuid.New()
	softwareGatewayId := uuid.New()

	tests := []struct {
		name           string
		urlVars        map[string]string
		requestBody    string
		setupMock      func() HandlerDeps
		expectedStatus int
		expectedInBody []string
	}{
		{
			name:    "successful legacy upsert",
			urlVars: map[string]string{"identifier": softwareGatewayId.String()},
			requestBody: `{
				"setting1": "value1",
				"setting2": "value2"
			}`,
			setupMock: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
					},
					GetOrganizationIdBySoftwareGatewayId: func(pg connect.DatabaseExecutor, sgId uuid.UUID) (uuid.UUID, error) {
						assert.Equal(t, softwareGatewayId, sgId)
						return organizationId, nil
					},
					BulkUpsertGatewayConfigTemplateSettingOverrides: func(pg connect.DatabaseExecutor, orgId uuid.UUID, sgId uuid.UUID, settingValues map[string]string) ([]GatewayConfigTemplateSettingOverride, error) {
						assert.Equal(t, organizationId, orgId)
						assert.Equal(t, softwareGatewayId, sgId)
						assert.Equal(t, "value1", settingValues["setting1"])
						assert.Equal(t, "value2", settingValues["setting2"])
						return []GatewayConfigTemplateSettingOverride{
							{
								SoftwareGatewayId: sgId,
								Setting:           "setting1",
								Value:             "value1",
							},
						}, nil
					},
				}
			},
			expectedStatus: 200,
			expectedInBody: []string{"setting1", "value1"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create request
			req := httptest.NewRequest(http.MethodPatch, "/softwaregateway/"+softwareGatewayId.String()+"/config", strings.NewReader(tt.requestBody))
			req = mux.SetURLVars(req, tt.urlVars)
			req.Header.Set("Content-Type", "application/json")

			// Create response recorder
			rr := httptest.NewRecorder()

			// Setup handler
			deps := tt.setupMock()
			handler := BulkUpsertOverridesLegacyHandlerWithDeps(deps)

			// Execute request
			handler(rr, req)

			// Assert status
			assert.Equal(t, tt.expectedStatus, rr.Code)

			// Assert response body contains expected strings
			for _, expectedStr := range tt.expectedInBody {
				assert.Contains(t, rr.Body.String(), expectedStr)
			}
		})
	}
}

func Test_BulkUpsertOverridesLegacyHandlerWithDeps_ErrorCases(t *testing.T) {
	organizationId := uuid.New()
	softwareGatewayId := uuid.New()

	tests := []struct {
		name           string
		urlVars        map[string]string
		requestBody    string
		setupMock      func() HandlerDeps
		expectedStatus int
	}{
		{
			name:           "invalid software gateway ID",
			urlVars:        map[string]string{"identifier": "invalid-uuid"},
			requestBody:    `{"setting1": "value1"}`,
			setupMock:      func() HandlerDeps { return HandlerDeps{} },
			expectedStatus: 400,
		},
		{
			name:        "connection error",
			urlVars:     map[string]string{"identifier": softwareGatewayId.String()},
			requestBody: `{"setting1": "value1"}`,
			setupMock: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return nil, fmt.Errorf("connection failed")
					},
				}
			},
			expectedStatus: 500,
		},
		{
			name:        "software gateway not found",
			urlVars:     map[string]string{"identifier": softwareGatewayId.String()},
			requestBody: `{"setting1": "value1"}`,
			setupMock: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
					},
					GetOrganizationIdBySoftwareGatewayId: func(pg connect.DatabaseExecutor, sgId uuid.UUID) (uuid.UUID, error) {
						return uuid.Nil, ErrSoftwareGatewayConfigNotFound
					},
				}
			},
			expectedStatus: 404,
		},
		{
			name:        "organization lookup database error",
			urlVars:     map[string]string{"identifier": softwareGatewayId.String()},
			requestBody: `{"setting1": "value1"}`,
			setupMock: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
					},
					GetOrganizationIdBySoftwareGatewayId: func(pg connect.DatabaseExecutor, sgId uuid.UUID) (uuid.UUID, error) {
						return uuid.Nil, fmt.Errorf("%w: connection failed", ErrDatabaseOperation)
					},
				}
			},
			expectedStatus: 500,
		},
		{
			name:        "invalid JSON body",
			urlVars:     map[string]string{"identifier": softwareGatewayId.String()},
			requestBody: `{invalid json}`,
			setupMock: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
					},
					GetOrganizationIdBySoftwareGatewayId: func(pg connect.DatabaseExecutor, sgId uuid.UUID) (uuid.UUID, error) {
						return organizationId, nil
					},
				}
			},
			expectedStatus: 400,
		},
		{
			name:        "empty settings object",
			urlVars:     map[string]string{"identifier": softwareGatewayId.String()},
			requestBody: `{}`,
			setupMock: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
					},
					GetOrganizationIdBySoftwareGatewayId: func(pg connect.DatabaseExecutor, sgId uuid.UUID) (uuid.UUID, error) {
						return organizationId, nil
					},
				}
			},
			expectedStatus: 400,
		},
		{
			name:        "bulk upsert database error",
			urlVars:     map[string]string{"identifier": softwareGatewayId.String()},
			requestBody: `{"setting1": "value1"}`,
			setupMock: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
					},
					GetOrganizationIdBySoftwareGatewayId: func(pg connect.DatabaseExecutor, sgId uuid.UUID) (uuid.UUID, error) {
						return organizationId, nil
					},
					BulkUpsertGatewayConfigTemplateSettingOverrides: func(pg connect.DatabaseExecutor, orgId uuid.UUID, sgId uuid.UUID, settingValues map[string]string) ([]GatewayConfigTemplateSettingOverride, error) {
						return nil, fmt.Errorf("%w: upsert failed", ErrDatabaseOperation)
					},
				}
			},
			expectedStatus: 500,
		},
		{
			name:        "bulk upsert software gateway not found",
			urlVars:     map[string]string{"identifier": softwareGatewayId.String()},
			requestBody: `{"setting1": "value1"}`,
			setupMock: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
					},
					GetOrganizationIdBySoftwareGatewayId: func(pg connect.DatabaseExecutor, sgId uuid.UUID) (uuid.UUID, error) {
						return organizationId, nil
					},
					BulkUpsertGatewayConfigTemplateSettingOverrides: func(pg connect.DatabaseExecutor, orgId uuid.UUID, sgId uuid.UUID, settingValues map[string]string) ([]GatewayConfigTemplateSettingOverride, error) {
						return nil, ErrSoftwareGatewayConfigNotFound
					},
				}
			},
			expectedStatus: 404,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create request
			req := httptest.NewRequest(http.MethodPatch, "/softwaregateway/"+softwareGatewayId.String()+"/config", strings.NewReader(tt.requestBody))
			req = mux.SetURLVars(req, tt.urlVars)
			req.Header.Set("Content-Type", "application/json")

			// Create response recorder
			rr := httptest.NewRecorder()

			// Setup handler
			deps := tt.setupMock()
			handler := BulkUpsertOverridesLegacyHandlerWithDeps(deps)

			// Execute request
			handler(rr, req)

			// Assert status
			assert.Equal(t, tt.expectedStatus, rr.Code)
		})
	}
}

// === Bulk Upsert Overrides Tests ===

func Test_bulkUpsertGatewayConfigTemplateSettingOverrides(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	softwareGatewayId := uuid.New()

	tests := []struct {
		name              string
		organizationId    uuid.UUID
		softwareGatewayId uuid.UUID
		settingValues     map[string]string
		mockError         error
		expectedError     error
	}{
		{
			name:              "successful upsert",
			organizationId:    organizationId,
			softwareGatewayId: softwareGatewayId,
			settingValues:     map[string]string{"log_level": "debug", "timeout": "30s"},
			mockError:         nil,
			expectedError:     nil,
		},
		{
			name:              "software gateway not found",
			organizationId:    organizationId,
			softwareGatewayId: uuid.New(),
			settingValues:     map[string]string{"log_level": "debug"},
			mockError:         sql.ErrNoRows,
			expectedError:     ErrSoftwareGatewayConfigNotFound,
		},
		{
			name:              "database error during verification",
			organizationId:    organizationId,
			softwareGatewayId: softwareGatewayId,
			settingValues:     map[string]string{"log_level": "debug"},
			mockError:         fmt.Errorf("connection failed"),
			expectedError:     ErrDatabaseOperation,
		},
		{
			name:              "empty setting values are skipped",
			organizationId:    organizationId,
			softwareGatewayId: softwareGatewayId,
			settingValues:     map[string]string{"": "value", "setting": "", "valid": "debug"},
			mockError:         nil,
			expectedError:     nil,
		},
		{
			name:              "database error during upsert",
			organizationId:    organizationId,
			softwareGatewayId: softwareGatewayId,
			settingValues:     map[string]string{"log_level": "debug"},
			mockError:         fmt.Errorf("upsert failed"),
			expectedError:     ErrDatabaseOperation,
		},
		{
			name:              "base setting creation error during upsert",
			organizationId:    organizationId,
			softwareGatewayId: softwareGatewayId,
			settingValues:     map[string]string{"log_level": "debug"},
			mockError:         fmt.Errorf("base setting creation failed"),
			expectedError:     ErrDatabaseOperation,
		},
		{
			name:              "insert error after update returns no rows",
			organizationId:    organizationId,
			softwareGatewayId: softwareGatewayId,
			settingValues:     map[string]string{"log_level": "debug"},
			mockError:         fmt.Errorf("insert failed"),
			expectedError:     ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockPG := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					// For gateway verification query (SELECT 1)
					if strings.Contains(query, "SELECT 1") {
						// Handle verification-specific errors
						if tt.name == "software gateway not found" && tt.mockError == sql.ErrNoRows {
							return sql.ErrNoRows
						}
						if tt.name == "database error during verification" {
							return tt.mockError
						}

						// Successful verification
						if intDest, ok := dest.(*int); ok {
							*intDest = 1
						}
						return nil
					}

					// For upsert results (RETURNING query)
					if override, ok := dest.(*GatewayConfigTemplateSettingOverride); ok {
						// Handle upsert-specific errors
						if tt.name == "database error during upsert" {
							return tt.mockError
						}

						// For "insert error after update returns no rows" test case
						if tt.name == "insert error after update returns no rows" {
							if strings.Contains(query, "UPDATE") {
								// First call to UPDATE should return no rows to trigger INSERT
								return sql.ErrNoRows
							} else if strings.Contains(query, "INSERT") {
								// Second call to INSERT should return error
								return tt.mockError
							}
						}

						// Extract setting and value from query args
						setting := "log_level" // default
						value := "debug"       // default
						if len(args) > 1 {
							if s, ok := args[1].(string); ok {
								setting = s
							}
						}
						if len(args) > 2 {
							if v, ok := args[2].(string); ok {
								value = v
							}
						}

						override.SoftwareGatewayId = tt.softwareGatewayId
						override.Setting = setting
						override.Value = value
						return nil
					}

					return nil
				},
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					// For base setting creation
					if strings.Contains(query, "GatewayConfigTemplateBaseSettings") {
						if tt.name == "base setting creation error during upsert" {
							return nil, tt.mockError
						}
						return &mockSQLResult{rowsAffected: 1}, nil
					}
					// For INSERT query (after UPDATE returns no rows)
					if strings.Contains(query, "INSERT") && strings.Contains(query, "GatewayConfigTemplateSettingOverrides") {
						if tt.name == "insert error after update returns no rows" {
							return nil, tt.mockError
						}
					}
					return &mockSQLResult{rowsAffected: 1}, nil
				},
			}

			result, err := bulkUpsertGatewayConfigTemplateSettingOverrides(mockPG, tt.organizationId, tt.softwareGatewayId, tt.settingValues)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedError)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)

				// For "empty setting values are skipped" test, only valid settings should be returned
				if tt.name == "empty setting values are skipped" {
					assert.Len(t, result, 1) // Only "valid" setting should be processed
					assert.Equal(t, "valid", result[0].Setting)
				} else {
					assert.Equal(t, len(tt.settingValues), len(result))
				}
			}
		})
	}
}

func Test_BulkUpsertOverridesHandlerWithDeps(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	softwareGatewayId := uuid.New()

	t.Run("successful upsert", func(t *testing.T) {
		handler := BulkUpsertOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			BulkUpsertGatewayConfigTemplateSettingOverrides: func(pg connect.DatabaseExecutor, orgId uuid.UUID, sgId uuid.UUID, settingValues map[string]string) ([]GatewayConfigTemplateSettingOverride, error) {
				var overrides []GatewayConfigTemplateSettingOverride
				for setting, value := range settingValues {
					overrides = append(overrides, GatewayConfigTemplateSettingOverride{
						SoftwareGatewayId: sgId,
						Setting:           setting,
						Value:             value,
					})
				}
				return overrides, nil
			},
		})

		settingValues := map[string]string{
			"log_level": "debug",
			"timeout":   "30s",
		}
		body, _ := json.Marshal(settingValues)
		req := httptest.NewRequest("POST", "/api/organization/"+organizationId.String()+"/softwaregateway/"+softwareGatewayId.String()+"/config/overrides/bulk", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"identifier":     softwareGatewayId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)

		var response struct {
			Data []GatewayConfigTemplateSettingOverride `json:"data"`
		}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Len(t, response.Data, 2)
	})
}

func Test_BulkUpsertOverridesHandlerWithDeps_ErrorCases(t *testing.T) {
	t.Parallel()

	organizationId := uuid.New()
	softwareGatewayId := uuid.New()

	t.Run("invalid organization ID", func(t *testing.T) {
		handler := BulkUpsertOverridesHandlerWithDeps(HandlerDeps{})

		settingValues := map[string]string{"log_level": "debug"}
		body, _ := json.Marshal(settingValues)
		req := httptest.NewRequest("POST", "/api/organization/invalid-uuid/softwaregateway/"+softwareGatewayId.String()+"/config/overrides/bulk", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": "invalid-uuid",
			"identifier":     softwareGatewayId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid software gateway ID", func(t *testing.T) {
		handler := BulkUpsertOverridesHandlerWithDeps(HandlerDeps{})

		settingValues := map[string]string{"log_level": "debug"}
		body, _ := json.Marshal(settingValues)
		req := httptest.NewRequest("POST", "/api/organization/"+organizationId.String()+"/softwaregateway/invalid-uuid/config/overrides/bulk", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"identifier":     "invalid-uuid",
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid JSON", func(t *testing.T) {
		handler := BulkUpsertOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
		})

		req := httptest.NewRequest("POST", "/api/organization/"+organizationId.String()+"/softwaregateway/"+softwareGatewayId.String()+"/config/overrides/bulk", strings.NewReader("invalid json"))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"identifier":     softwareGatewayId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("empty setting values", func(t *testing.T) {
		handler := BulkUpsertOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
		})

		settingValues := map[string]string{}
		body, _ := json.Marshal(settingValues)
		req := httptest.NewRequest("POST", "/api/organization/"+organizationId.String()+"/softwaregateway/"+softwareGatewayId.String()+"/config/overrides/bulk", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"identifier":     softwareGatewayId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("connection error", func(t *testing.T) {
		handler := BulkUpsertOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
		})

		settingValues := map[string]string{"log_level": "debug"}
		body, _ := json.Marshal(settingValues)
		req := httptest.NewRequest("POST", "/api/organization/"+organizationId.String()+"/softwaregateway/"+softwareGatewayId.String()+"/config/overrides/bulk", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"identifier":     softwareGatewayId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("software gateway not found", func(t *testing.T) {
		handler := BulkUpsertOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			BulkUpsertGatewayConfigTemplateSettingOverrides: func(pg connect.DatabaseExecutor, orgId uuid.UUID, sgId uuid.UUID, settingValues map[string]string) ([]GatewayConfigTemplateSettingOverride, error) {
				return nil, ErrSoftwareGatewayConfigNotFound
			},
		})

		settingValues := map[string]string{"log_level": "debug"}
		body, _ := json.Marshal(settingValues)
		req := httptest.NewRequest("POST", "/api/organization/"+organizationId.String()+"/softwaregateway/"+softwareGatewayId.String()+"/config/overrides/bulk", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"identifier":     softwareGatewayId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("database error", func(t *testing.T) {
		handler := BulkUpsertOverridesHandlerWithDeps(HandlerDeps{
			GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
			},
			BulkUpsertGatewayConfigTemplateSettingOverrides: func(pg connect.DatabaseExecutor, orgId uuid.UUID, sgId uuid.UUID, settingValues map[string]string) ([]GatewayConfigTemplateSettingOverride, error) {
				return nil, errors.New("database error")
			},
		})

		settingValues := map[string]string{"log_level": "debug"}
		body, _ := json.Marshal(settingValues)
		req := httptest.NewRequest("POST", "/api/organization/"+organizationId.String()+"/softwaregateway/"+softwareGatewayId.String()+"/config/overrides/bulk", strings.NewReader(string(body)))
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": organizationId.String(),
			"identifier":     softwareGatewayId.String(),
		})
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})
}
