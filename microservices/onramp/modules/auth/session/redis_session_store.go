package session

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/redis/go-redis/v9"
	"synapse-its.com/onramp/domain"
	"synapse-its.com/onramp/middlewares"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

const (
	RedisSessionKeyPrefix = "OnrampSession:"
)

type redisSessionStore struct {
	redisClient *redis.Client
}

func NewRedisSessionStore(redisClient *redis.Client) domain.SessionStore {
	return &redisSessionStore{
		redisClient: redisClient,
	}
}

func (s *redisSessionStore) GetSession(sessionID string) (*domain.Session, bool) {
	sessionKey := fmt.Sprintf("%s%s", RedisSessionKeyPrefix, sessionID)
	jsonString, err := connect.RedisGet(context.Background(), s.redisClient, sessionKey)
	if err != nil {
		logger.Errorf("Error getting session from Redis: %v", err)
		return nil, false
	}
	var session domain.Session
	err = json.Unmarshal([]byte(jsonString), &session)
	if err != nil {
		logger.Errorf("Error unmarshalling session from Redis: %v", err)
		return nil, false
	}
	return &session, true
}

func (s *redisSessionStore) SetSession(sessionID string, session *domain.Session) {
	sessionKey := fmt.Sprintf("%s%s", RedisSessionKeyPrefix, sessionID)
	if session == nil {
		logger.Errorf("Session data is nil, skipping set")
		return
	}
	jsonData, _ := json.Marshal(session)

	// Set session with TTL
	err := s.redisClient.Set(context.Background(), sessionKey, string(jsonData), SessionTTL).Err()
	if err != nil {
		logger.Errorf("Error setting session in Redis: %v", err)
		return
	}

	logger.Debugf("Set session %s with TTL %v", sessionID, SessionTTL)
}

func (s *redisSessionStore) ClearSession(sessionID string) {
	sessionKey := fmt.Sprintf("%s%s", RedisSessionKeyPrefix, sessionID)
	err := s.redisClient.Del(context.Background(), sessionKey).Err()
	if err != nil {
		logger.Errorf("Error clearing session in Redis: %v", err)
		return
	}
}

// Common errors for session extraction
var (
	ErrSessionNotFound       = errors.New("session not found in context")
	ErrSessionInvalidType    = errors.New("session data is not of correct type")
	ErrSessionUserIDEmpty    = errors.New("session UserID is empty")
	ErrSessionPermissionsNil = errors.New("user permissions not found in session")
)

// GetSessionFromContext extracts session data from the request context.
// This is a helper function to centralize session extraction logic and error handling.
// It validates that both the session and user permissions are present.
// It returns the session data or an appropriate error.
func GetSessionFromContext(ctx context.Context) (*domain.Session, error) {
	// Get session data from context (set by session middleware)
	sessionDataInterface := ctx.Value(middlewares.SessionContextKey)
	if sessionDataInterface == nil {
		return nil, ErrSessionNotFound
	}

	// Type assert to session data
	sessionData, ok := sessionDataInterface.(*domain.Session)
	if !ok {
		return nil, ErrSessionInvalidType
	}

	// Validate that UserID is not empty
	if sessionData.UserID == "" {
		return nil, ErrSessionUserIDEmpty
	}

	// Validate that UserPermissions are present
	if sessionData.UserPermissions == nil {
		return nil, ErrSessionPermissionsNil
	}

	return sessionData, nil
}
