package helper

import "errors"

var (
	ErrInvalidIdentifier       = errors.New("invalid identifier")
	ErrInvalidOrganizationID   = errors.New("invalid organization ID")
	ErrInvalidUserID           = errors.New("invalid user ID")
	ErrInvalidInviteID         = errors.New("invalid invite ID")
	ErrInvalidAuthMethodID     = errors.New("invalid auth method ID")
	ErrOrganizationNotFound    = errors.New("organization not found")
	ErrDatabaseOperation       = errors.New("database operation failed")
	ErrInvalidDeviceID         = errors.New("invalid device ID")
	ErrSoftwareGatewayNotFound = errors.New("software gateway not found")
	ErrLocationNotFound        = errors.New("location not found")
	ErrInvalidUUID             = errors.New("invalid UUID")
	ErrMembershipNotFound      = errors.New("membership not found")
)
