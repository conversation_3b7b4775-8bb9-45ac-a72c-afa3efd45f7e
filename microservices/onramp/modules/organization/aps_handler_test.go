package organization

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"synapse-its.com/onramp/domain"
	"synapse-its.com/onramp/middlewares"
	"synapse-its.com/shared/api/authorizer"
)

// Mock apsFactoryResetService for testing
type mockAPSFactoryResetService struct {
	mock.Mock
}

func (m *mockAPSFactoryResetService) GetAPSFactorResetCode(userID, orgID uuid.UUID, challengeCode string) (string, error) {
	args := m.Called(userID, orgID, challengeCode)
	return args.String(0), args.Error(1)
}

func TestHandler_handleAPSFactorReset(t *testing.T) {
	tests := []struct {
		name                  string
		organizationId        string
		challengeCode         string
		session               *domain.Session
		setupAPSFactoryReset  func(*mockAPSFactoryResetService)
		expectedStatusCode    int
		expectedResponse      interface{}
		expectAPSFactoryReset bool
		expectedUserID        uuid.UUID
		expectedOrgID         uuid.UUID
		expectedChallengeCode string
		invalidJSON           bool
	}{
		{
			name:           "Success - valid request with valid permissions",
			organizationId: "12345678-1234-1234-1234-123456789abc",
			challengeCode:  "ABCD1234",
			session: &domain.Session{
				UserID: "*************-4321-4321-cba987654321",
				UserPermissions: &domain.UserPermissions{
					UserID: "*************-4321-4321-cba987654321",
					Permissions: []authorizer.Permission{
						{
							Scope:          "organization",
							ScopeID:        "12345678-1234-1234-1234-123456789abc",
							OrganizationID: "12345678-1234-1234-1234-123456789abc",
							Permissions:    []string{"synapse_aps_factory_reset"},
						},
					},
				},
			},
			setupAPSFactoryReset: func(mockAPS *mockAPSFactoryResetService) {
				userID := uuid.MustParse("*************-4321-4321-cba987654321")
				orgID := uuid.MustParse("12345678-1234-1234-1234-123456789abc")
				mockAPS.On("GetAPSFactorResetCode", userID, orgID, "ABCD1234").Return("RESET123", nil)
			},
			expectedStatusCode:    http.StatusOK,
			expectedResponse:      "RESET123",
			expectAPSFactoryReset: true,
			expectedUserID:        uuid.MustParse("*************-4321-4321-cba987654321"),
			expectedOrgID:         uuid.MustParse("12345678-1234-1234-1234-123456789abc"),
			expectedChallengeCode: "ABCD1234",
		},
		{
			name:           "Error - invalid organizationId UUID",
			organizationId: "invalid-uuid",
			challengeCode:  "ABCD1234",
			session: &domain.Session{
				UserID: "*************-4321-4321-cba987654321",
				UserPermissions: &domain.UserPermissions{
					UserID:      "*************-4321-4321-cba987654321",
					Permissions: []authorizer.Permission{},
				},
			},
			setupAPSFactoryReset: func(mockAPS *mockAPSFactoryResetService) {
				// No setup needed - function should return early
			},
			expectedStatusCode:    http.StatusBadRequest,
			expectedResponse:      nil,
			expectAPSFactoryReset: false,
		},
		{
			name:           "Error - invalid JSON body",
			organizationId: "12345678-1234-1234-1234-123456789abc",
			challengeCode:  "ABCD1234",
			session: &domain.Session{
				UserID: "*************-4321-4321-cba987654321",
				UserPermissions: &domain.UserPermissions{
					UserID:      "*************-4321-4321-cba987654321",
					Permissions: []authorizer.Permission{},
				},
			},
			setupAPSFactoryReset: func(mockAPS *mockAPSFactoryResetService) {
				// No setup needed - function should return early
			},
			expectedStatusCode:    http.StatusBadRequest,
			expectedResponse:      nil,
			expectAPSFactoryReset: false,
			invalidJSON:           true,
		},
		{
			name:           "Error - challengeCode too short",
			organizationId: "12345678-1234-1234-1234-123456789abc",
			challengeCode:  "ABC",
			session: &domain.Session{
				UserID: "*************-4321-4321-cba987654321",
				UserPermissions: &domain.UserPermissions{
					UserID:      "*************-4321-4321-cba987654321",
					Permissions: []authorizer.Permission{},
				},
			},
			setupAPSFactoryReset: func(mockAPS *mockAPSFactoryResetService) {
				// No setup needed - function should return early
			},
			expectedStatusCode:    http.StatusBadRequest,
			expectedResponse:      nil,
			expectAPSFactoryReset: false,
		},
		{
			name:           "Error - challengeCode too long",
			organizationId: "12345678-1234-1234-1234-123456789abc",
			challengeCode:  "ABCDE",
			session: &domain.Session{
				UserID: "*************-4321-4321-cba987654321",
				UserPermissions: &domain.UserPermissions{
					UserID:      "*************-4321-4321-cba987654321",
					Permissions: []authorizer.Permission{},
				},
			},
			setupAPSFactoryReset: func(mockAPS *mockAPSFactoryResetService) {
				// No setup needed - function should return early
			},
			expectedStatusCode:    http.StatusBadRequest,
			expectedResponse:      nil,
			expectAPSFactoryReset: false,
		},
		{
			name:           "Error - nil session in context",
			organizationId: "12345678-1234-1234-1234-123456789abc",
			challengeCode:  "ABCD1234",
			session:        nil,
			setupAPSFactoryReset: func(mockAPS *mockAPSFactoryResetService) {
				// No setup needed - function should return early
			},
			expectedStatusCode:    http.StatusInternalServerError,
			expectedResponse:      nil,
			expectAPSFactoryReset: false,
		},
		{
			name:           "Error - nil user permissions in session",
			organizationId: "12345678-1234-1234-1234-123456789abc",
			challengeCode:  "ABCD1234",
			session: &domain.Session{
				UserID:          "*************-4321-4321-cba987654321",
				UserPermissions: nil,
			},
			setupAPSFactoryReset: func(mockAPS *mockAPSFactoryResetService) {
				// No setup needed - function should return early
			},
			expectedStatusCode:    http.StatusUnauthorized,
			expectedResponse:      nil,
			expectAPSFactoryReset: false,
		},
		{
			name:           "Error - empty UserID",
			organizationId: "12345678-1234-1234-1234-123456789abc",
			challengeCode:  "ABCD1234",
			session: &domain.Session{
				UserID: "",
				UserPermissions: &domain.UserPermissions{
					UserID:      "",
					Permissions: []authorizer.Permission{},
				},
			},
			setupAPSFactoryReset: func(mockAPS *mockAPSFactoryResetService) {
				// No setup needed - function should return early
			},
			expectedStatusCode:    http.StatusInternalServerError,
			expectedResponse:      nil,
			expectAPSFactoryReset: false,
		},
		{
			name:           "Error - invalid UserID UUID",
			organizationId: "12345678-1234-1234-1234-123456789abc",
			challengeCode:  "ABCD1234",
			session: &domain.Session{
				UserID: "invalid-user-uuid",
				UserPermissions: &domain.UserPermissions{
					UserID:      "invalid-user-uuid",
					Permissions: []authorizer.Permission{},
				},
			},
			setupAPSFactoryReset: func(mockAPS *mockAPSFactoryResetService) {
				// No setup needed - function should return early
			},
			expectedStatusCode:    http.StatusInternalServerError,
			expectedResponse:      nil,
			expectAPSFactoryReset: false,
		},
		{
			name:           "Error - user lacks required permissions",
			organizationId: "12345678-1234-1234-1234-123456789abc",
			challengeCode:  "ABCD1234",
			session: &domain.Session{
				UserID: "*************-4321-4321-cba987654321",
				UserPermissions: &domain.UserPermissions{
					UserID: "*************-4321-4321-cba987654321",
					Permissions: []authorizer.Permission{
						{
							Scope:          "organization",
							ScopeID:        "12345678-1234-1234-1234-123456789abc",
							OrganizationID: "12345678-1234-1234-1234-123456789abc",
							Permissions:    []string{"some_other_permission"},
						},
					},
				},
			},
			setupAPSFactoryReset: func(mockAPS *mockAPSFactoryResetService) {
				// No setup needed - function should return early
			},
			expectedStatusCode:    http.StatusForbidden,
			expectedResponse:      nil,
			expectAPSFactoryReset: false,
		},
		{
			name:           "Error - APS factory reset service fails",
			organizationId: "12345678-1234-1234-1234-123456789abc",
			challengeCode:  "ABCD1234",
			session: &domain.Session{
				UserID: "*************-4321-4321-cba987654321",
				UserPermissions: &domain.UserPermissions{
					UserID: "*************-4321-4321-cba987654321",
					Permissions: []authorizer.Permission{
						{
							Scope:          "organization",
							ScopeID:        "12345678-1234-1234-1234-123456789abc",
							OrganizationID: "12345678-1234-1234-1234-123456789abc",
							Permissions:    []string{"synapse_aps_factory_reset"},
						},
					},
				},
			},
			setupAPSFactoryReset: func(mockAPS *mockAPSFactoryResetService) {
				userID := uuid.MustParse("*************-4321-4321-cba987654321")
				orgID := uuid.MustParse("12345678-1234-1234-1234-123456789abc")
				mockAPS.On("GetAPSFactorResetCode", userID, orgID, "ABCD1234").Return("", assert.AnError)
			},
			expectedStatusCode:    http.StatusInternalServerError,
			expectedResponse:      nil,
			expectAPSFactoryReset: true,
			expectedUserID:        uuid.MustParse("*************-4321-4321-cba987654321"),
			expectedOrgID:         uuid.MustParse("12345678-1234-1234-1234-123456789abc"),
			expectedChallengeCode: "ABCD1234",
		},
		{
			name:           "Error - empty challenge code (edge case)",
			organizationId: "12345678-1234-1234-1234-123456789abc",
			challengeCode:  "",
			session: &domain.Session{
				UserID: "*************-4321-4321-cba987654321",
				UserPermissions: &domain.UserPermissions{
					UserID:      "*************-4321-4321-cba987654321",
					Permissions: []authorizer.Permission{},
				},
			},
			setupAPSFactoryReset: func(mockAPS *mockAPSFactoryResetService) {
				// No setup needed - function should return early
			},
			expectedStatusCode:    http.StatusBadRequest,
			expectedResponse:      nil,
			expectAPSFactoryReset: false,
		},
		{
			name:           "Success - nil UUIDs (edge case)",
			organizationId: "00000000-0000-0000-0000-000000000000",
			challengeCode:  "NILU1234",
			session: &domain.Session{
				UserID: "00000000-0000-0000-0000-000000000000",
				UserPermissions: &domain.UserPermissions{
					UserID: "00000000-0000-0000-0000-000000000000",
					Permissions: []authorizer.Permission{
						{
							Scope:          "organization",
							ScopeID:        "00000000-0000-0000-0000-000000000000",
							OrganizationID: "00000000-0000-0000-0000-000000000000",
							Permissions:    []string{"synapse_aps_factory_reset"},
						},
					},
				},
			},
			setupAPSFactoryReset: func(mockAPS *mockAPSFactoryResetService) {
				userID := uuid.Nil
				orgID := uuid.Nil
				mockAPS.On("GetAPSFactorResetCode", userID, orgID, "NILU1234").Return("NIL123", nil)
			},
			expectedStatusCode:    http.StatusOK,
			expectedResponse:      "NIL123",
			expectAPSFactoryReset: true,
			expectedUserID:        uuid.Nil,
			expectedOrgID:         uuid.Nil,
			expectedChallengeCode: "NILU1234",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			mockAPS := &mockAPSFactoryResetService{}

			// Setup mocks
			tt.setupAPSFactoryReset(mockAPS)

			handler := &Handler{
				apsFactoryResetService: mockAPS,
			}

			// Create request body
			var reqBody []byte
			if tt.invalidJSON {
				// Send invalid JSON for testing JSON decode errors
				reqBody = []byte(`{"challengeCode": invalid json`)
			} else {
				requestBody := map[string]string{
					"challengeCode": tt.challengeCode,
				}
				reqBody, _ = json.Marshal(requestBody)
			}

			// Create request
			req := httptest.NewRequest(http.MethodPost, "/organizations/"+tt.organizationId+"/aps-factory-reset", bytes.NewBuffer(reqBody))
			req.Header.Set("Content-Type", "application/json")

			// Set URL variables for mux
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.organizationId,
			})

			// Set context with session data
			ctx := req.Context()
			if tt.session != nil {
				ctx = context.WithValue(ctx, middlewares.SessionContextKey, tt.session)
			}
			req = req.WithContext(ctx)

			// Create response recorder
			w := httptest.NewRecorder()

			// Act
			handler.handleAPSFactorReset(w, req)

			// Assert
			assert.Equal(t, tt.expectedStatusCode, w.Code)

			if tt.expectedResponse != nil {
				var responseBody map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &responseBody)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResponse, responseBody["data"])
			}

			// Verify mock expectations
			if tt.expectAPSFactoryReset {
				mockAPS.AssertExpectations(t)
			}
		})
	}
}
