<div class="invitations-container">
  <div class="gr-header">
    <div class="form-left">
      <div class="title-invitations">
        <h1 id="title-invitations">Invitations Pending</h1>
      </div>
      <div class="form-breadcrumb">
        <nz-breadcrumb>
          <nz-breadcrumb-item>
            My Account
          </nz-breadcrumb-item>
          <nz-breadcrumb-item>
            Invitations Pending
          </nz-breadcrumb-item>
        </nz-breadcrumb>
      </div>
    </div>
  </div>
  <div class="page-description-invitations">
    <span>This is a list of <b>Invitations</b> that you have received. You will not be added to an <b>Organization</b>
      until you <b>Accept</b> the invite.</span>
  </div>
  <div class="form-table-invitations">

    <nz-table id="users-invitations" class="mt-10" #basicTable nzBordered [nzData]="listDataInvitations"
      nzShowSizeChanger nzShowPagination [nzLoading]="isTableLoading">
      <thead>
        <tr>
          <th nzWidth="20%">Organization</th>
          <th nzWidth="20%">Email</th>
          <th nzWidth="20%">Sent</th>
          <th nzWidth="20%">Status</th>
          <th nzWidth="5%">Action</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of basicTable.data; let i = index"
          [ngClass]="data.id === highlightedRowId ? 'highlight-row' : 'default-row'">
          <td>{{ data.nameOrg }}</td>
          <td>{{ data.email }}</td>
          <td>{{ data.sent | date: 'MM-dd-yyyy HH:mm:ss' : 'UTC' }}</td>
          <td>{{ capitalize(data.status) }}</td>
          <td style="text-align: center;">
            <a *ngIf="data.status === 'pending'" nz-button nzType="link" (click)="handleAccept(data)">
              Accept
            </a>
            <button *ngIf="data.status === 'redeemed'" class="br-8" nz-button nzType="primary"
              (click)="handleDelete(data)" nzDanger nz-tooltip nzTooltipTitle="Delete">
              <nz-icon nzType="delete" nzTheme="outline" />
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </div>
</div>