import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-add-edit-user',
  standalone: false,
  templateUrl: './add-edit-user.component.html',
  styleUrl: './add-edit-user.component.css'
})
export class AddEditUserComponent {
  @Input() isVisible = false;
  @Input() isEditMode = false;
  @Input() isDetail = false;
  @Input() orgId: any;
  @Input() listOrgRole: any[] = [];
  @Input() data: any;
  @Input() nameOrg: any;
  @Output() close = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<any>();
  form: FormGroup;

  constructor(
    private fb: FormBuilder
  ) {
    this.form = this.fb.group({
      email: ['', this.isEditMode ? [Validators.maxLength(255)] : [Validators.required, Validators.maxLength(255)]],
      organizationrole: ['', [Validators.required, Validators.maxLength(255)]],
      message: ['', [Validators.maxLength(2000)]]
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isEditMode'] || changes['data'] || changes['listOrgRole']) {
      if (this.isEditMode) {
        this.form.get('email')?.disable();
        this.form.get('message')?.disable();
        this.form.get('email')?.clearValidators();
        this.form.get('message')?.clearValidators();
        this.form.get('email')?.setValidators([Validators.maxLength(255)]);
        this.form.get('message')?.setValidators([Validators.maxLength(2000)]);
      } else {
        this.form.get('email')?.enable();
        this.form.get('message')?.enable();
        this.form.get('email')?.setValidators([Validators.required, Validators.maxLength(255)]);
        this.form.get('message')?.setValidators([Validators.maxLength(2000)]);
        if (this.listOrgRole.length > 0) {
          this.form.get('organizationrole')?.setValue(this.listOrgRole[0].value);
        }
      }
      this.form.get('organizationrole')?.setValidators([Validators.required, Validators.maxLength(255)]);
      this.form.get('email')?.updateValueAndValidity();
      this.form.get('message')?.updateValueAndValidity();
      this.form.get('organizationrole')?.updateValueAndValidity();
      if (this.data && this.isEditMode) {
        const selectedRole = this.listOrgRole.find(role => role.label === this.data?.orgRole);
        this.form.reset({
          email: this.data?.email,
          organizationrole: selectedRole ? selectedRole.value : '',
          message: this.data?.username,
          userId: this.data?.userId
        });
      }
    }
  }

  handleSave() {
    if (this.form.get('organizationrole')?.valid) {
      const formValue = this.form.value;
      const selectedRole = this.listOrgRole.find(role => role.id === formValue.organizationrole);
      const updateUser = {
        email: formValue.email,
        organizationroleId: formValue.organizationrole,
        organizationroleValue: selectedRole ? selectedRole.value : '',
        message: formValue.message,
        roleId: selectedRole ? selectedRole.id : '',
      };
      const createInvitation = {
        email: formValue.email,
        orgId: this.orgId,
        message: formValue.message,
        organizationrole: formValue.organizationrole,
        userId: this.data?.userId
      };
      this.confirm.emit(this.isEditMode ? updateUser : createInvitation);
      this.form.reset({
        email: '',
        organizationrole: this.listOrgRole.length > 0 ? this.listOrgRole[0].value : '',
        message: ''
      }, { emitEvent: false });
    } else {
      this.form.get('organizationrole')?.markAsDirty();
      this.form.get('organizationrole')?.updateValueAndValidity({ onlySelf: true });
    }
  }

  handleCancel() {
    this.form.reset({
      email: '',
      organizationrole: this.listOrgRole.length > 0 ? this.listOrgRole[0].value : '',
      message: ''
    });
    this.close.emit();
  }
}