package biz

import (
	"github.com/google/uuid"
	"synapse-its.com/shared/rest/domain/organization"
)

type membershipBiz struct {
	repo organization.Repository
}

func NewMembershipBiz(repo organization.Repository) *membershipBiz {
	return &membershipBiz{repo: repo}
}

func (b *membershipBiz) GetUsersByOrganizationID(organizationID uuid.UUID) ([]*organization.Membership, error) {
	return b.repo.FindUsersByOrganizationID(organizationID)
}

func (b *membershipBiz) UpdateUserRoleInOrganization(userID uuid.UUID, organizationID uuid.UUID, roleID uuid.UUID) error {
	return b.repo.UpdateUserRoleInOrganization(userID, organizationID, roleID)
}

func (b *membershipBiz) DeleteUserFromOrganization(userID uuid.UUID, organizationID uuid.UUID) error {
	return b.repo.DeleteUserFromOrganization(userID, organizationID)
}
