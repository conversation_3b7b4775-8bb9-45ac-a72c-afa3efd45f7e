CREATE TABLE IF NOT EXISTS {{EdiRawMessages}} (
  topic            STRING    {% OPTIONS(description="The pubsub message topic") %},
  organizationidentifier STRING {% OPTIONS(description="The organization identifier") %},
	softwaregatewayidentifier STRING    {% OPTIONS(description="The softwaregateway identifier which sent the message") %},
	messagetype      STRING    {% OPTIONS(description="The assocated message type") %},
  messageversion   STRING    {% OPTIONS(description="The assocated message version") %},
  pubsubid         STRING    {% OPTIONS(description="The pubsub message identifier") %},
  data             BYTES     {% OPTIONS(description="The pubsub message data") %},
  attributes       ARRAY<STRUCT<key STRING,
                                value STRING
                         >>      {% OPTIONS(description="The pubsub message attribute") %},
  pubsubtimestamp  TIMESTAMP {% OPTIONS(description="The pubsub message timestamp") %},
  orderingkey      STRING    {% OPTIONS(description="The pubsub message ordering key") %},
  deliveryattempt  INT64     {% OPTIONS(description="The pubsub message delivery attempt count") %},
)
{% PARTITION BY DATE(pubsubtimestamp) CLUSTER BY organizationidentifier, softwaregatewayidentifier %};

CREATE TABLE IF NOT EXISTS {{DlqMessages}} (
  topic            STRING    {% OPTIONS(description="The original pubsub message topic") %},
  id               STRING    {% OPTIONS(description="The original pubsub message identifier") %},
  data             BYTES     {% OPTIONS(description="The original pubsub message data") %},
  attributes       ARRAY<STRUCT<key STRING,
                                value STRING
                         >>      {% OPTIONS(description="The pubsub message attribute") %},
  publishtime      TIMESTAMP {% OPTIONS(description="The original pubsub message publish time") %},
  orderingkey      STRING    {% OPTIONS(description="The original pubsub message ordering key") %},
  deliveryattempt  INT64     {% OPTIONS(description="The original pubsub message delivery attempt count") %},
  dlqreason        STRING    {% OPTIONS(description="The reason the original pubsub message was sent to the DLQ topic") %},
  pubsubtimestamp  TIMESTAMP {% OPTIONS(description="The dlq pubsub message timestamp") %},
  pubsubid         STRING    {% OPTIONS(description="The dlq pubsub message identifier") %},
)
{% PARTITION BY DATE(pubsubtimestamp) CLUSTER BY topic %};

CREATE TABLE IF NOT EXISTS {{RmsEngine}} (
  organizationidentifier     STRING    {% OPTIONS(description="OrganizationIdentifier from authentication") %},
  softwaregatewayid          STRING    {% OPTIONS(description="SoftwareGateway identifier from the HTTP headers") %},
  tz                         STRING    {% OPTIONS(description="SoftwareGateway timezone from the HTTP headers") %},
  topic                      STRING    {% OPTIONS(description="The pubsub topic name") %},
  pubsubtimestamp            TIMESTAMP {% OPTIONS(description="The timestamp the pubsub message timestamp") %},
  pubsubid                   STRING    {% OPTIONS(description="The pubsub message identifier") %},
  deviceid                   STRING    {% OPTIONS(description="Unique identifier for the device") %},
  header                     STRUCT<
    commversion      STRING {% OPTIONS(description="Device ECcom revision number") %},
    model            INT64 {% OPTIONS(description="Device model") %},
    firmwareversion  STRING {% OPTIONS(description="Device firmware version") %},
    firmwarerevision STRING {% OPTIONS(description="Device firmware revision") %},
    monitorid        INT64 {% OPTIONS(description="User defined monitor id") %},
    volt220          BOOL  {% OPTIONS(description="Flag for whether device supports monitoring 220 volt") %},
    voltdc           BOOL  {% OPTIONS(description="Flag for whether device supports monitoring dc voltage") %},
    mainsdc          BOOL  {% OPTIONS(description="Flag for whether device supports dc mains power") %},
    powerdownlevel   INT64 {% OPTIONS(description="Voltage level at which device will power down") %},
    blackoutlevel    INT64 {% OPTIONS(description="Voltage level at which device will blackout") %},
    maxchannels      INT64 {% OPTIONS(description="Maximum number of channels monitor can support") %}
                             >         {% OPTIONS(description="Standard fields derived from first 7 bytes of header") %},
  rmsversion                 STRING    {% OPTIONS(description="Version number of the rms engine") %},
  rmsrevision                STRING    {% OPTIONS(description="Revision number of the rms engine") %},
  rawmessage                 BYTES     {% OPTIONS(description="Raw byte message from device") %}
)
{% PARTITION BY DATE(pubsubtimestamp) CLUSTER BY organizationidentifier, softwaregatewayid, deviceid %};

CREATE TABLE IF NOT EXISTS {{MonitorName}} (
  organizationidentifier  STRING    {% OPTIONS(description="OrganizationIdentifier from authentication") %},
  softwaregatewayid       STRING    {% OPTIONS(description="SoftwareGateway identifier from the HTTP headers") %},
  tz                      STRING    {% OPTIONS(description="SoftwareGateway timezone from the HTTP headers") %},
  topic                   STRING    {% OPTIONS(description="The pubsub topic name") %},
  pubsubtimestamp         TIMESTAMP {% OPTIONS(description="The timestamp the pubsub message timestamp") %},
  pubsubid                STRING    {% OPTIONS(description="The pubsub message identifier") %},
  deviceid                STRING    {% OPTIONS(description="Unique identifier for the device") %},
  header                  STRUCT<
    commversion      STRING {% OPTIONS(description="Device ECcom revision number") %},
    model            INT64 {% OPTIONS(description="Device model") %},
    firmwareversion  STRING {% OPTIONS(description="Device firmware version") %},
    firmwarerevision STRING {% OPTIONS(description="Device firmware revision") %},
    monitorid        INT64 {% OPTIONS(description="User defined monitor id") %},
    volt220          BOOL  {% OPTIONS(description="Flag for whether device supports monitoring 220 volt") %},
    voltdc           BOOL  {% OPTIONS(description="Flag for whether device supports monitoring dc voltage") %},
    mainsdc          BOOL  {% OPTIONS(description="Flag for whether device supports dc mains power") %},
    powerdownlevel   INT64 {% OPTIONS(description="Voltage level at which device will power down") %},
    blackoutlevel    INT64 {% OPTIONS(description="Voltage level at which device will blackout") %},
    maxchannels      INT64 {% OPTIONS(description="Maximum number of channels monitor can support") %}
                          >         {% OPTIONS(description="Standard fields derived from first 7 bytes of header") %},
  monitorname             STRING    {% OPTIONS(description="User set monitor name") %},
  rawmessage              BYTES     {% OPTIONS(description="Raw byte message from device") %}
)
{% PARTITION BY DATE(pubsubtimestamp) CLUSTER BY organizationidentifier, softwaregatewayid, deviceid %};

CREATE TABLE IF NOT EXISTS {{MacAddress}} (
  organizationidentifier  STRING    {% OPTIONS(description="OrganizationIdentifier from authentication") %},
  softwaregatewayid       STRING    {% OPTIONS(description="SoftwareGateway identifier from the HTTP headers") %},
  tz                      STRING    {% OPTIONS(description="SoftwareGateway timezone from the HTTP headers") %},
  topic                   STRING    {% OPTIONS(description="The pubsub topic name") %},
  pubsubtimestamp         TIMESTAMP {% OPTIONS(description="The timestamp the pubsub message timestamp") %},
  pubsubid                STRING    {% OPTIONS(description="The pubsub message identifier") %},
  deviceid                STRING    {% OPTIONS(description="Unique identifier for the device") %},
  macaddress              STRING    {% OPTIONS(description="Mac address of monitor device") %}
)
{% PARTITION BY DATE(pubsubtimestamp) CLUSTER BY organizationidentifier, softwaregatewayid, deviceid %};

CREATE TABLE IF NOT EXISTS {{RmsData}} (
  organizationidentifier  STRING    {% OPTIONS(description="OrganizationIdentifier from authentication") %},
  softwaregatewayid       STRING    {% OPTIONS(description="SoftwareGateway identifier from the HTTP headers") %},
  tz                      STRING    {% OPTIONS(description="SoftwareGateway timezone from the HTTP headers") %},
  topic                   STRING    {% OPTIONS(description="The pubsub topic name") %},
  pubsubtimestamp         TIMESTAMP {% OPTIONS(description="The timestamp the pubsub message timestamp") %},
  pubsubid                STRING    {% OPTIONS(description="The pubsub message identifier") %},
  deviceid                STRING    {% OPTIONS(description="Unique identifier for the device") %},
  header                  STRUCT<
    commversion      STRING {% OPTIONS(description="Device ECcom revision number") %},
    model            INT64 {% OPTIONS(description="Device model") %},
    firmwareversion  STRING {% OPTIONS(description="Device firmware version") %},
    firmwarerevision STRING {% OPTIONS(description="Device firmware revision") %},
    monitorid        INT64 {% OPTIONS(description="User defined monitor id") %},
    volt220          BOOL  {% OPTIONS(description="Flag for whether device supports monitoring 220 volt") %},
    voltdc           BOOL  {% OPTIONS(description="Flag for whether device supports monitoring dc voltage") %},
    mainsdc          BOOL  {% OPTIONS(description="Flag for whether device supports dc mains power") %},
    powerdownlevel   INT64 {% OPTIONS(description="Voltage level at which device will power down") %},
    blackoutlevel    INT64 {% OPTIONS(description="Voltage level at which device will blackout") %},
    maxchannels      INT64 {% OPTIONS(description="Maximum number of channels monitor can support") %}
                          >         {% OPTIONS(description="Standard fields derived from first 7 bytes of header") %},
  isfaulted               BOOL      {% OPTIONS(description="Flag indicating if the device is in a falted state") %},
  fault                   STRING    {% OPTIONS(description="The user-friendly fault") %},
  faultstatus             STRING    {% OPTIONS(description="The user-friendly fault status") %},
  monitortime             TIMESTAMP {% OPTIONS(description="The date/time of the monitor - converted to UTC") %},
  temperaturef            INT64     {% OPTIONS(description="Temperature in fahrenheit") %},
  channelgreenstatus      STRUCT<
    channel01 BOOL {% OPTIONS(description="Channel 1 green status flag") %},
    channel02 BOOL {% OPTIONS(description="Channel 2 green status flag") %},
    channel03 BOOL {% OPTIONS(description="Channel 3 green status flag") %},
    channel04 BOOL {% OPTIONS(description="Channel 4 green status flag") %},
    channel05 BOOL {% OPTIONS(description="Channel 5 green status flag") %},
    channel06 BOOL {% OPTIONS(description="Channel 6 green status flag") %},
    channel07 BOOL {% OPTIONS(description="Channel 7 green status flag") %},
    channel08 BOOL {% OPTIONS(description="Channel 8 green status flag") %},
    channel09 BOOL {% OPTIONS(description="Channel 9 green status flag") %},
    channel10 BOOL {% OPTIONS(description="Channel 10 green status flag") %},
    channel11 BOOL {% OPTIONS(description="Channel 11 green status flag") %},
    channel12 BOOL {% OPTIONS(description="Channel 12 green status flag") %},
    channel13 BOOL {% OPTIONS(description="Channel 13 green status flag") %},
    channel14 BOOL {% OPTIONS(description="Channel 14 green status flag") %},
    channel15 BOOL {% OPTIONS(description="Channel 15 green status flag") %},
    channel16 BOOL {% OPTIONS(description="Channel 16 green status flag") %},
    channel17 BOOL {% OPTIONS(description="Channel 17 green status flag") %},
    channel18 BOOL {% OPTIONS(description="Channel 18 green status flag") %},
    channel19 BOOL {% OPTIONS(description="Channel 19 green status flag") %},
    channel20 BOOL {% OPTIONS(description="Channel 20 green status flag") %},
    channel21 BOOL {% OPTIONS(description="Channel 21 green status flag") %},
    channel22 BOOL {% OPTIONS(description="Channel 22 green status flag") %},
    channel23 BOOL {% OPTIONS(description="Channel 23 green status flag") %},
    channel24 BOOL {% OPTIONS(description="Channel 24 green status flag") %},
    channel25 BOOL {% OPTIONS(description="Channel 25 green status flag") %},
    channel26 BOOL {% OPTIONS(description="Channel 26 green status flag") %},
    channel27 BOOL {% OPTIONS(description="Channel 27 green status flag") %},
    channel28 BOOL {% OPTIONS(description="Channel 28 green status flag") %},
    channel29 BOOL {% OPTIONS(description="Channel 29 green status flag") %},
    channel30 BOOL {% OPTIONS(description="Channel 30 green status flag") %},
    channel31 BOOL {% OPTIONS(description="Channel 31 green status flag") %},
    channel32 BOOL {% OPTIONS(description="Channel 32 green status flag") %},
    channel33 BOOL {% OPTIONS(description="Channel 33 green status flag") %},
    channel34 BOOL {% OPTIONS(description="Channel 34 green status flag") %},
    channel35 BOOL {% OPTIONS(description="Channel 35 green status flag") %},
    channel36 BOOL {% OPTIONS(description="Channel 36 green status flag") %}
                          >         {% OPTIONS(description="Struct of green channel status flags") %},
  channelyellowstatus     STRUCT<
    channel01 BOOL {% OPTIONS(description="Channel 1 yellow status flag") %},
    channel02 BOOL {% OPTIONS(description="Channel 2 yellow status flag") %},
    channel03 BOOL {% OPTIONS(description="Channel 3 yellow status flag") %},
    channel04 BOOL {% OPTIONS(description="Channel 4 yellow status flag") %},
    channel05 BOOL {% OPTIONS(description="Channel 5 yellow status flag") %},
    channel06 BOOL {% OPTIONS(description="Channel 6 yellow status flag") %},
    channel07 BOOL {% OPTIONS(description="Channel 7 yellow status flag") %},
    channel08 BOOL {% OPTIONS(description="Channel 8 yellow status flag") %},
    channel09 BOOL {% OPTIONS(description="Channel 9 yellow status flag") %},
    channel10 BOOL {% OPTIONS(description="Channel 10 yellow status flag") %},
    channel11 BOOL {% OPTIONS(description="Channel 11 yellow status flag") %},
    channel12 BOOL {% OPTIONS(description="Channel 12 yellow status flag") %},
    channel13 BOOL {% OPTIONS(description="Channel 13 yellow status flag") %},
    channel14 BOOL {% OPTIONS(description="Channel 14 yellow status flag") %},
    channel15 BOOL {% OPTIONS(description="Channel 15 yellow status flag") %},
    channel16 BOOL {% OPTIONS(description="Channel 16 yellow status flag") %},
    channel17 BOOL {% OPTIONS(description="Channel 17 yellow status flag") %},
    channel18 BOOL {% OPTIONS(description="Channel 18 yellow status flag") %},
    channel19 BOOL {% OPTIONS(description="Channel 19 yellow status flag") %},
    channel20 BOOL {% OPTIONS(description="Channel 20 yellow status flag") %},
    channel21 BOOL {% OPTIONS(description="Channel 21 yellow status flag") %},
    channel22 BOOL {% OPTIONS(description="Channel 22 yellow status flag") %},
    channel23 BOOL {% OPTIONS(description="Channel 23 yellow status flag") %},
    channel24 BOOL {% OPTIONS(description="Channel 24 yellow status flag") %},
    channel25 BOOL {% OPTIONS(description="Channel 25 yellow status flag") %},
    channel26 BOOL {% OPTIONS(description="Channel 26 yellow status flag") %},
    channel27 BOOL {% OPTIONS(description="Channel 27 yellow status flag") %},
    channel28 BOOL {% OPTIONS(description="Channel 28 yellow status flag") %},
    channel29 BOOL {% OPTIONS(description="Channel 29 yellow status flag") %},
    channel30 BOOL {% OPTIONS(description="Channel 30 yellow status flag") %},
    channel31 BOOL {% OPTIONS(description="Channel 31 yellow status flag") %},
    channel32 BOOL {% OPTIONS(description="Channel 32 yellow status flag") %},
    channel33 BOOL {% OPTIONS(description="Channel 33 yellow status flag") %},
    channel34 BOOL {% OPTIONS(description="Channel 34 yellow status flag") %},
    channel35 BOOL {% OPTIONS(description="Channel 35 yellow status flag") %},
    channel36 BOOL {% OPTIONS(description="Channel 36 yellow status flag") %}
                          >         {% OPTIONS(description="Struct of yellow channel status flags") %},
  channelredstatus STRUCT<
    channel01 BOOL {% OPTIONS(description="Channel 1 red status flag") %},
    channel02 BOOL {% OPTIONS(description="Channel 2 red status flag") %},
    channel03 BOOL {% OPTIONS(description="Channel 3 red status flag") %},
    channel04 BOOL {% OPTIONS(description="Channel 4 red status flag") %},
    channel05 BOOL {% OPTIONS(description="Channel 5 red status flag") %},
    channel06 BOOL {% OPTIONS(description="Channel 6 red status flag") %},
    channel07 BOOL {% OPTIONS(description="Channel 7 red status flag") %},
    channel08 BOOL {% OPTIONS(description="Channel 8 red status flag") %},
    channel09 BOOL {% OPTIONS(description="Channel 9 red status flag") %},
    channel10 BOOL {% OPTIONS(description="Channel 10 red status flag") %},
    channel11 BOOL {% OPTIONS(description="Channel 11 red status flag") %},
    channel12 BOOL {% OPTIONS(description="Channel 12 red status flag") %},
    channel13 BOOL {% OPTIONS(description="Channel 13 red status flag") %},
    channel14 BOOL {% OPTIONS(description="Channel 14 red status flag") %},
    channel15 BOOL {% OPTIONS(description="Channel 15 red status flag") %},
    channel16 BOOL {% OPTIONS(description="Channel 16 red status flag") %},
    channel17 BOOL {% OPTIONS(description="Channel 17 red status flag") %},
    channel18 BOOL {% OPTIONS(description="Channel 18 red status flag") %},
    channel19 BOOL {% OPTIONS(description="Channel 19 red status flag") %},
    channel20 BOOL {% OPTIONS(description="Channel 20 red status flag") %},
    channel21 BOOL {% OPTIONS(description="Channel 21 red status flag") %},
    channel22 BOOL {% OPTIONS(description="Channel 22 red status flag") %},
    channel23 BOOL {% OPTIONS(description="Channel 23 red status flag") %},
    channel24 BOOL {% OPTIONS(description="Channel 24 red status flag") %},
    channel25 BOOL {% OPTIONS(description="Channel 25 red status flag") %},
    channel26 BOOL {% OPTIONS(description="Channel 26 red status flag") %},
    channel27 BOOL {% OPTIONS(description="Channel 27 red status flag") %},
    channel28 BOOL {% OPTIONS(description="Channel 28 red status flag") %},
    channel29 BOOL {% OPTIONS(description="Channel 29 red status flag") %},
    channel30 BOOL {% OPTIONS(description="Channel 30 red status flag") %},
    channel31 BOOL {% OPTIONS(description="Channel 31 red status flag") %},
    channel32 BOOL {% OPTIONS(description="Channel 32 red status flag") %},
    channel33 BOOL {% OPTIONS(description="Channel 33 red status flag") %},
    channel34 BOOL {% OPTIONS(description="Channel 34 red status flag") %},
    channel35 BOOL {% OPTIONS(description="Channel 35 red status flag") %},
    channel36 BOOL {% OPTIONS(description="Channel 36 red status flag") %}
                          >         {% OPTIONS(description="Struct of red channel status flags") %},
  channelgreenvoltage     STRUCT<
    channel01 INT64 {% OPTIONS(description="Channel 1 green voltage") %},
    channel02 INT64 {% OPTIONS(description="Channel 2 green voltage") %},
    channel03 INT64 {% OPTIONS(description="Channel 3 green voltage") %},
    channel04 INT64 {% OPTIONS(description="Channel 4 green voltage") %},
    channel05 INT64 {% OPTIONS(description="Channel 5 green voltage") %},
    channel06 INT64 {% OPTIONS(description="Channel 6 green voltage") %},
    channel07 INT64 {% OPTIONS(description="Channel 7 green voltage") %},
    channel08 INT64 {% OPTIONS(description="Channel 8 green voltage") %},
    channel09 INT64 {% OPTIONS(description="Channel 9 green voltage") %},
    channel10 INT64 {% OPTIONS(description="Channel 10 green voltage") %},
    channel11 INT64 {% OPTIONS(description="Channel 11 green voltage") %},
    channel12 INT64 {% OPTIONS(description="Channel 12 green voltage") %},
    channel13 INT64 {% OPTIONS(description="Channel 13 green voltage") %},
    channel14 INT64 {% OPTIONS(description="Channel 14 green voltage") %},
    channel15 INT64 {% OPTIONS(description="Channel 15 green voltage") %},
    channel16 INT64 {% OPTIONS(description="Channel 16 green voltage") %},
    channel17 INT64 {% OPTIONS(description="Channel 17 green voltage") %},
    channel18 INT64 {% OPTIONS(description="Channel 18 green voltage") %},
    channel19 INT64 {% OPTIONS(description="Channel 19 green voltage") %},
    channel20 INT64 {% OPTIONS(description="Channel 20 green voltage") %},
    channel21 INT64 {% OPTIONS(description="Channel 21 green voltage") %},
    channel22 INT64 {% OPTIONS(description="Channel 22 green voltage") %},
    channel23 INT64 {% OPTIONS(description="Channel 23 green voltage") %},
    channel24 INT64 {% OPTIONS(description="Channel 24 green voltage") %},
    channel25 INT64 {% OPTIONS(description="Channel 25 green voltage") %},
    channel26 INT64 {% OPTIONS(description="Channel 26 green voltage") %},
    channel27 INT64 {% OPTIONS(description="Channel 27 green voltage") %},
    channel28 INT64 {% OPTIONS(description="Channel 28 green voltage") %},
    channel29 INT64 {% OPTIONS(description="Channel 29 green voltage") %},
    channel30 INT64 {% OPTIONS(description="Channel 30 green voltage") %},
    channel31 INT64 {% OPTIONS(description="Channel 31 green voltage") %},
    channel32 INT64 {% OPTIONS(description="Channel 32 green voltage") %},
    channel33 INT64 {% OPTIONS(description="Channel 33 green voltage") %},
    channel34 INT64 {% OPTIONS(description="Channel 34 green voltage") %},
    channel35 INT64 {% OPTIONS(description="Channel 35 green voltage") %},
    channel36 INT64 {% OPTIONS(description="Channel 36 green voltage") %}
                          >         {% OPTIONS(description="Struct of green channel voltages") %},
  channelyellowvoltage    STRUCT<
    channel01 INT64 {% OPTIONS(description="Channel 1 yellow voltage") %},
    channel02 INT64 {% OPTIONS(description="Channel 2 yellow voltage") %},
    channel03 INT64 {% OPTIONS(description="Channel 3 yellow voltage") %},
    channel04 INT64 {% OPTIONS(description="Channel 4 yellow voltage") %},
    channel05 INT64 {% OPTIONS(description="Channel 5 yellow voltage") %},
    channel06 INT64 {% OPTIONS(description="Channel 6 yellow voltage") %},
    channel07 INT64 {% OPTIONS(description="Channel 7 yellow voltage") %},
    channel08 INT64 {% OPTIONS(description="Channel 8 yellow voltage") %},
    channel09 INT64 {% OPTIONS(description="Channel 9 yellow voltage") %},
    channel10 INT64 {% OPTIONS(description="Channel 10 yellow voltage") %},
    channel11 INT64 {% OPTIONS(description="Channel 11 yellow voltage") %},
    channel12 INT64 {% OPTIONS(description="Channel 12 yellow voltage") %},
    channel13 INT64 {% OPTIONS(description="Channel 13 yellow voltage") %},
    channel14 INT64 {% OPTIONS(description="Channel 14 yellow voltage") %},
    channel15 INT64 {% OPTIONS(description="Channel 15 yellow voltage") %},
    channel16 INT64 {% OPTIONS(description="Channel 16 yellow voltage") %},
    channel17 INT64 {% OPTIONS(description="Channel 17 yellow voltage") %},
    channel18 INT64 {% OPTIONS(description="Channel 18 yellow voltage") %},
    channel19 INT64 {% OPTIONS(description="Channel 19 yellow voltage") %},
    channel20 INT64 {% OPTIONS(description="Channel 20 yellow voltage") %},
    channel21 INT64 {% OPTIONS(description="Channel 21 yellow voltage") %},
    channel22 INT64 {% OPTIONS(description="Channel 22 yellow voltage") %},
    channel23 INT64 {% OPTIONS(description="Channel 23 yellow voltage") %},
    channel24 INT64 {% OPTIONS(description="Channel 24 yellow voltage") %},
    channel25 INT64 {% OPTIONS(description="Channel 25 yellow voltage") %},
    channel26 INT64 {% OPTIONS(description="Channel 26 yellow voltage") %},
    channel27 INT64 {% OPTIONS(description="Channel 27 yellow voltage") %},
    channel28 INT64 {% OPTIONS(description="Channel 28 yellow voltage") %},
    channel29 INT64 {% OPTIONS(description="Channel 29 yellow voltage") %},
    channel30 INT64 {% OPTIONS(description="Channel 30 yellow voltage") %},
    channel31 INT64 {% OPTIONS(description="Channel 31 yellow voltage") %},
    channel32 INT64 {% OPTIONS(description="Channel 32 yellow voltage") %},
    channel33 INT64 {% OPTIONS(description="Channel 33 yellow voltage") %},
    channel34 INT64 {% OPTIONS(description="Channel 34 yellow voltage") %},
    channel35 INT64 {% OPTIONS(description="Channel 35 yellow voltage") %},
    channel36 INT64 {% OPTIONS(description="Channel 36 yellow voltage") %}
                          >         {% OPTIONS(description="Struct of yellow channel voltages") %},
  channelredvoltage STRUCT<
    channel01 INT64 {% OPTIONS(description="Channel 1 red voltage") %},
    channel02 INT64 {% OPTIONS(description="Channel 2 red voltage") %},
    channel03 INT64 {% OPTIONS(description="Channel 3 red voltage") %},
    channel04 INT64 {% OPTIONS(description="Channel 4 red voltage") %},
    channel05 INT64 {% OPTIONS(description="Channel 5 red voltage") %},
    channel06 INT64 {% OPTIONS(description="Channel 6 red voltage") %},
    channel07 INT64 {% OPTIONS(description="Channel 7 red voltage") %},
    channel08 INT64 {% OPTIONS(description="Channel 8 red voltage") %},
    channel09 INT64 {% OPTIONS(description="Channel 9 red voltage") %},
    channel10 INT64 {% OPTIONS(description="Channel 10 red voltage") %},
    channel11 INT64 {% OPTIONS(description="Channel 11 red voltage") %},
    channel12 INT64 {% OPTIONS(description="Channel 12 red voltage") %},
    channel13 INT64 {% OPTIONS(description="Channel 13 red voltage") %},
    channel14 INT64 {% OPTIONS(description="Channel 14 red voltage") %},
    channel15 INT64 {% OPTIONS(description="Channel 15 red voltage") %},
    channel16 INT64 {% OPTIONS(description="Channel 16 red voltage") %},
    channel17 INT64 {% OPTIONS(description="Channel 17 red voltage") %},
    channel18 INT64 {% OPTIONS(description="Channel 18 red voltage") %},
    channel19 INT64 {% OPTIONS(description="Channel 19 red voltage") %},
    channel20 INT64 {% OPTIONS(description="Channel 20 red voltage") %},
    channel21 INT64 {% OPTIONS(description="Channel 21 red voltage") %},
    channel22 INT64 {% OPTIONS(description="Channel 22 red voltage") %},
    channel23 INT64 {% OPTIONS(description="Channel 23 red voltage") %},
    channel24 INT64 {% OPTIONS(description="Channel 24 red voltage") %},
    channel25 INT64 {% OPTIONS(description="Channel 25 red voltage") %},
    channel26 INT64 {% OPTIONS(description="Channel 26 red voltage") %},
    channel27 INT64 {% OPTIONS(description="Channel 27 red voltage") %},
    channel28 INT64 {% OPTIONS(description="Channel 28 red voltage") %},
    channel29 INT64 {% OPTIONS(description="Channel 29 red voltage") %},
    channel30 INT64 {% OPTIONS(description="Channel 30 red voltage") %},
    channel31 INT64 {% OPTIONS(description="Channel 31 red voltage") %},
    channel32 INT64 {% OPTIONS(description="Channel 32 red voltage") %},
    channel33 INT64 {% OPTIONS(description="Channel 33 red voltage") %},
    channel34 INT64 {% OPTIONS(description="Channel 34 red voltage") %},
    channel35 INT64 {% OPTIONS(description="Channel 35 red voltage") %},
    channel36 INT64 {% OPTIONS(description="Channel 36 red voltage") %}
                          >         {% OPTIONS(description="Struct of red channel voltages") %},
  rawmessage              BYTES     {% OPTIONS(description="Raw byte message from device") %}
)
{% PARTITION BY DATE(pubsubtimestamp) CLUSTER BY organizationidentifier, softwaregatewayid, deviceid %};

CREATE TABLE IF NOT EXISTS {{FaultNotification}} (
  organizationidentifier  STRING    {% OPTIONS(description="OrganizationIdentifier from authentication") %},
  softwaregatewayid       STRING    {% OPTIONS(description="SoftwareGateway identifier from the HTTP headers") %},
  tz                      STRING    {% OPTIONS(description="SoftwareGateway timezone from the HTTP headers") %},
  topic                   STRING    {% OPTIONS(description="The pubsub topic name") %},
  pubsubtimestamp         TIMESTAMP {% OPTIONS(description="The timestamp the pubsub message timestamp") %},
  pubsubid                STRING    {% OPTIONS(description="The pubsub message identifier") %},
  deviceid                STRING    {% OPTIONS(description="Unique identifier for the device") %},
  header                  STRUCT<
    commversion      STRING {% OPTIONS(description="Device ECcom revision number") %},
    model            INT64 {% OPTIONS(description="Device model") %},
    firmwareversion  STRING {% OPTIONS(description="Device firmware version") %},
    firmwarerevision STRING {% OPTIONS(description="Device firmware revision") %},
    monitorid        INT64 {% OPTIONS(description="User defined monitor id") %},
    volt220          BOOL  {% OPTIONS(description="Flag for whether device supports monitoring 220 volt") %},
    voltdc           BOOL  {% OPTIONS(description="Flag for whether device supports monitoring dc voltage") %},
    mainsdc          BOOL  {% OPTIONS(description="Flag for whether device supports dc mains power") %},
    powerdownlevel   INT64 {% OPTIONS(description="Voltage level at which device will power down") %},
    blackoutlevel    INT64 {% OPTIONS(description="Voltage level at which device will blackout") %},
    maxchannels      INT64 {% OPTIONS(description="Maximum number of channels monitor can support") %}
                          >         {% OPTIONS(description="Standard fields derived from first 7 bytes of header") %},
  isfaulted               BOOL      {% OPTIONS(description="Flag indicating if the device is in a falted state") %},
  fault                   STRING    {% OPTIONS(description="The user-friendly fault") %},
  faultstatus             STRING    {% OPTIONS(description="The user-friendly fault status") %},
  monitortime             TIMESTAMP {% OPTIONS(description="The date/time of the monitor - converted to UTC") %},
  temperaturef            INT64     {% OPTIONS(description="Temperature in fahrenheit") %},
  channelgreenstatus      STRUCT<
    channel01 BOOL {% OPTIONS(description="Channel 1 green status flag") %},
    channel02 BOOL {% OPTIONS(description="Channel 2 green status flag") %},
    channel03 BOOL {% OPTIONS(description="Channel 3 green status flag") %},
    channel04 BOOL {% OPTIONS(description="Channel 4 green status flag") %},
    channel05 BOOL {% OPTIONS(description="Channel 5 green status flag") %},
    channel06 BOOL {% OPTIONS(description="Channel 6 green status flag") %},
    channel07 BOOL {% OPTIONS(description="Channel 7 green status flag") %},
    channel08 BOOL {% OPTIONS(description="Channel 8 green status flag") %},
    channel09 BOOL {% OPTIONS(description="Channel 9 green status flag") %},
    channel10 BOOL {% OPTIONS(description="Channel 10 green status flag") %},
    channel11 BOOL {% OPTIONS(description="Channel 11 green status flag") %},
    channel12 BOOL {% OPTIONS(description="Channel 12 green status flag") %},
    channel13 BOOL {% OPTIONS(description="Channel 13 green status flag") %},
    channel14 BOOL {% OPTIONS(description="Channel 14 green status flag") %},
    channel15 BOOL {% OPTIONS(description="Channel 15 green status flag") %},
    channel16 BOOL {% OPTIONS(description="Channel 16 green status flag") %},
    channel17 BOOL {% OPTIONS(description="Channel 17 green status flag") %},
    channel18 BOOL {% OPTIONS(description="Channel 18 green status flag") %},
    channel19 BOOL {% OPTIONS(description="Channel 19 green status flag") %},
    channel20 BOOL {% OPTIONS(description="Channel 20 green status flag") %},
    channel21 BOOL {% OPTIONS(description="Channel 21 green status flag") %},
    channel22 BOOL {% OPTIONS(description="Channel 22 green status flag") %},
    channel23 BOOL {% OPTIONS(description="Channel 23 green status flag") %},
    channel24 BOOL {% OPTIONS(description="Channel 24 green status flag") %},
    channel25 BOOL {% OPTIONS(description="Channel 25 green status flag") %},
    channel26 BOOL {% OPTIONS(description="Channel 26 green status flag") %},
    channel27 BOOL {% OPTIONS(description="Channel 27 green status flag") %},
    channel28 BOOL {% OPTIONS(description="Channel 28 green status flag") %},
    channel29 BOOL {% OPTIONS(description="Channel 29 green status flag") %},
    channel30 BOOL {% OPTIONS(description="Channel 30 green status flag") %},
    channel31 BOOL {% OPTIONS(description="Channel 31 green status flag") %},
    channel32 BOOL {% OPTIONS(description="Channel 32 green status flag") %},
    channel33 BOOL {% OPTIONS(description="Channel 33 green status flag") %},
    channel34 BOOL {% OPTIONS(description="Channel 34 green status flag") %},
    channel35 BOOL {% OPTIONS(description="Channel 35 green status flag") %},
    channel36 BOOL {% OPTIONS(description="Channel 36 green status flag") %}
                          >         {% OPTIONS(description="Struct of green channel status flags") %},
  channelyellowstatus     STRUCT<
    channel01 BOOL {% OPTIONS(description="Channel 1 yellow status flag") %},
    channel02 BOOL {% OPTIONS(description="Channel 2 yellow status flag") %},
    channel03 BOOL {% OPTIONS(description="Channel 3 yellow status flag") %},
    channel04 BOOL {% OPTIONS(description="Channel 4 yellow status flag") %},
    channel05 BOOL {% OPTIONS(description="Channel 5 yellow status flag") %},
    channel06 BOOL {% OPTIONS(description="Channel 6 yellow status flag") %},
    channel07 BOOL {% OPTIONS(description="Channel 7 yellow status flag") %},
    channel08 BOOL {% OPTIONS(description="Channel 8 yellow status flag") %},
    channel09 BOOL {% OPTIONS(description="Channel 9 yellow status flag") %},
    channel10 BOOL {% OPTIONS(description="Channel 10 yellow status flag") %},
    channel11 BOOL {% OPTIONS(description="Channel 11 yellow status flag") %},
    channel12 BOOL {% OPTIONS(description="Channel 12 yellow status flag") %},
    channel13 BOOL {% OPTIONS(description="Channel 13 yellow status flag") %},
    channel14 BOOL {% OPTIONS(description="Channel 14 yellow status flag") %},
    channel15 BOOL {% OPTIONS(description="Channel 15 yellow status flag") %},
    channel16 BOOL {% OPTIONS(description="Channel 16 yellow status flag") %},
    channel17 BOOL {% OPTIONS(description="Channel 17 yellow status flag") %},
    channel18 BOOL {% OPTIONS(description="Channel 18 yellow status flag") %},
    channel19 BOOL {% OPTIONS(description="Channel 19 yellow status flag") %},
    channel20 BOOL {% OPTIONS(description="Channel 20 yellow status flag") %},
    channel21 BOOL {% OPTIONS(description="Channel 21 yellow status flag") %},
    channel22 BOOL {% OPTIONS(description="Channel 22 yellow status flag") %},
    channel23 BOOL {% OPTIONS(description="Channel 23 yellow status flag") %},
    channel24 BOOL {% OPTIONS(description="Channel 24 yellow status flag") %},
    channel25 BOOL {% OPTIONS(description="Channel 25 yellow status flag") %},
    channel26 BOOL {% OPTIONS(description="Channel 26 yellow status flag") %},
    channel27 BOOL {% OPTIONS(description="Channel 27 yellow status flag") %},
    channel28 BOOL {% OPTIONS(description="Channel 28 yellow status flag") %},
    channel29 BOOL {% OPTIONS(description="Channel 29 yellow status flag") %},
    channel30 BOOL {% OPTIONS(description="Channel 30 yellow status flag") %},
    channel31 BOOL {% OPTIONS(description="Channel 31 yellow status flag") %},
    channel32 BOOL {% OPTIONS(description="Channel 32 yellow status flag") %},
    channel33 BOOL {% OPTIONS(description="Channel 33 yellow status flag") %},
    channel34 BOOL {% OPTIONS(description="Channel 34 yellow status flag") %},
    channel35 BOOL {% OPTIONS(description="Channel 35 yellow status flag") %},
    channel36 BOOL {% OPTIONS(description="Channel 36 yellow status flag") %}
                          >         {% OPTIONS(description="Struct of yellow channel status flags") %},
  channelredstatus STRUCT<
    channel01 BOOL {% OPTIONS(description="Channel 1 red status flag") %},
    channel02 BOOL {% OPTIONS(description="Channel 2 red status flag") %},
    channel03 BOOL {% OPTIONS(description="Channel 3 red status flag") %},
    channel04 BOOL {% OPTIONS(description="Channel 4 red status flag") %},
    channel05 BOOL {% OPTIONS(description="Channel 5 red status flag") %},
    channel06 BOOL {% OPTIONS(description="Channel 6 red status flag") %},
    channel07 BOOL {% OPTIONS(description="Channel 7 red status flag") %},
    channel08 BOOL {% OPTIONS(description="Channel 8 red status flag") %},
    channel09 BOOL {% OPTIONS(description="Channel 9 red status flag") %},
    channel10 BOOL {% OPTIONS(description="Channel 10 red status flag") %},
    channel11 BOOL {% OPTIONS(description="Channel 11 red status flag") %},
    channel12 BOOL {% OPTIONS(description="Channel 12 red status flag") %},
    channel13 BOOL {% OPTIONS(description="Channel 13 red status flag") %},
    channel14 BOOL {% OPTIONS(description="Channel 14 red status flag") %},
    channel15 BOOL {% OPTIONS(description="Channel 15 red status flag") %},
    channel16 BOOL {% OPTIONS(description="Channel 16 red status flag") %},
    channel17 BOOL {% OPTIONS(description="Channel 17 red status flag") %},
    channel18 BOOL {% OPTIONS(description="Channel 18 red status flag") %},
    channel19 BOOL {% OPTIONS(description="Channel 19 red status flag") %},
    channel20 BOOL {% OPTIONS(description="Channel 20 red status flag") %},
    channel21 BOOL {% OPTIONS(description="Channel 21 red status flag") %},
    channel22 BOOL {% OPTIONS(description="Channel 22 red status flag") %},
    channel23 BOOL {% OPTIONS(description="Channel 23 red status flag") %},
    channel24 BOOL {% OPTIONS(description="Channel 24 red status flag") %},
    channel25 BOOL {% OPTIONS(description="Channel 25 red status flag") %},
    channel26 BOOL {% OPTIONS(description="Channel 26 red status flag") %},
    channel27 BOOL {% OPTIONS(description="Channel 27 red status flag") %},
    channel28 BOOL {% OPTIONS(description="Channel 28 red status flag") %},
    channel29 BOOL {% OPTIONS(description="Channel 29 red status flag") %},
    channel30 BOOL {% OPTIONS(description="Channel 30 red status flag") %},
    channel31 BOOL {% OPTIONS(description="Channel 31 red status flag") %},
    channel32 BOOL {% OPTIONS(description="Channel 32 red status flag") %},
    channel33 BOOL {% OPTIONS(description="Channel 33 red status flag") %},
    channel34 BOOL {% OPTIONS(description="Channel 34 red status flag") %},
    channel35 BOOL {% OPTIONS(description="Channel 35 red status flag") %},
    channel36 BOOL {% OPTIONS(description="Channel 36 red status flag") %}
                          >         {% OPTIONS(description="Struct of red channel status flags") %},
  channelgreenvoltage     STRUCT<
    channel01 INT64 {% OPTIONS(description="Channel 1 green voltage") %},
    channel02 INT64 {% OPTIONS(description="Channel 2 green voltage") %},
    channel03 INT64 {% OPTIONS(description="Channel 3 green voltage") %},
    channel04 INT64 {% OPTIONS(description="Channel 4 green voltage") %},
    channel05 INT64 {% OPTIONS(description="Channel 5 green voltage") %},
    channel06 INT64 {% OPTIONS(description="Channel 6 green voltage") %},
    channel07 INT64 {% OPTIONS(description="Channel 7 green voltage") %},
    channel08 INT64 {% OPTIONS(description="Channel 8 green voltage") %},
    channel09 INT64 {% OPTIONS(description="Channel 9 green voltage") %},
    channel10 INT64 {% OPTIONS(description="Channel 10 green voltage") %},
    channel11 INT64 {% OPTIONS(description="Channel 11 green voltage") %},
    channel12 INT64 {% OPTIONS(description="Channel 12 green voltage") %},
    channel13 INT64 {% OPTIONS(description="Channel 13 green voltage") %},
    channel14 INT64 {% OPTIONS(description="Channel 14 green voltage") %},
    channel15 INT64 {% OPTIONS(description="Channel 15 green voltage") %},
    channel16 INT64 {% OPTIONS(description="Channel 16 green voltage") %},
    channel17 INT64 {% OPTIONS(description="Channel 17 green voltage") %},
    channel18 INT64 {% OPTIONS(description="Channel 18 green voltage") %},
    channel19 INT64 {% OPTIONS(description="Channel 19 green voltage") %},
    channel20 INT64 {% OPTIONS(description="Channel 20 green voltage") %},
    channel21 INT64 {% OPTIONS(description="Channel 21 green voltage") %},
    channel22 INT64 {% OPTIONS(description="Channel 22 green voltage") %},
    channel23 INT64 {% OPTIONS(description="Channel 23 green voltage") %},
    channel24 INT64 {% OPTIONS(description="Channel 24 green voltage") %},
    channel25 INT64 {% OPTIONS(description="Channel 25 green voltage") %},
    channel26 INT64 {% OPTIONS(description="Channel 26 green voltage") %},
    channel27 INT64 {% OPTIONS(description="Channel 27 green voltage") %},
    channel28 INT64 {% OPTIONS(description="Channel 28 green voltage") %},
    channel29 INT64 {% OPTIONS(description="Channel 29 green voltage") %},
    channel30 INT64 {% OPTIONS(description="Channel 30 green voltage") %},
    channel31 INT64 {% OPTIONS(description="Channel 31 green voltage") %},
    channel32 INT64 {% OPTIONS(description="Channel 32 green voltage") %},
    channel33 INT64 {% OPTIONS(description="Channel 33 green voltage") %},
    channel34 INT64 {% OPTIONS(description="Channel 34 green voltage") %},
    channel35 INT64 {% OPTIONS(description="Channel 35 green voltage") %},
    channel36 INT64 {% OPTIONS(description="Channel 36 green voltage") %}
                          >         {% OPTIONS(description="Struct of green channel voltages") %},
  channelyellowvoltage    STRUCT<
    channel01 INT64 {% OPTIONS(description="Channel 1 yellow voltage") %},
    channel02 INT64 {% OPTIONS(description="Channel 2 yellow voltage") %},
    channel03 INT64 {% OPTIONS(description="Channel 3 yellow voltage") %},
    channel04 INT64 {% OPTIONS(description="Channel 4 yellow voltage") %},
    channel05 INT64 {% OPTIONS(description="Channel 5 yellow voltage") %},
    channel06 INT64 {% OPTIONS(description="Channel 6 yellow voltage") %},
    channel07 INT64 {% OPTIONS(description="Channel 7 yellow voltage") %},
    channel08 INT64 {% OPTIONS(description="Channel 8 yellow voltage") %},
    channel09 INT64 {% OPTIONS(description="Channel 9 yellow voltage") %},
    channel10 INT64 {% OPTIONS(description="Channel 10 yellow voltage") %},
    channel11 INT64 {% OPTIONS(description="Channel 11 yellow voltage") %},
    channel12 INT64 {% OPTIONS(description="Channel 12 yellow voltage") %},
    channel13 INT64 {% OPTIONS(description="Channel 13 yellow voltage") %},
    channel14 INT64 {% OPTIONS(description="Channel 14 yellow voltage") %},
    channel15 INT64 {% OPTIONS(description="Channel 15 yellow voltage") %},
    channel16 INT64 {% OPTIONS(description="Channel 16 yellow voltage") %},
    channel17 INT64 {% OPTIONS(description="Channel 17 yellow voltage") %},
    channel18 INT64 {% OPTIONS(description="Channel 18 yellow voltage") %},
    channel19 INT64 {% OPTIONS(description="Channel 19 yellow voltage") %},
    channel20 INT64 {% OPTIONS(description="Channel 20 yellow voltage") %},
    channel21 INT64 {% OPTIONS(description="Channel 21 yellow voltage") %},
    channel22 INT64 {% OPTIONS(description="Channel 22 yellow voltage") %},
    channel23 INT64 {% OPTIONS(description="Channel 23 yellow voltage") %},
    channel24 INT64 {% OPTIONS(description="Channel 24 yellow voltage") %},
    channel25 INT64 {% OPTIONS(description="Channel 25 yellow voltage") %},
    channel26 INT64 {% OPTIONS(description="Channel 26 yellow voltage") %},
    channel27 INT64 {% OPTIONS(description="Channel 27 yellow voltage") %},
    channel28 INT64 {% OPTIONS(description="Channel 28 yellow voltage") %},
    channel29 INT64 {% OPTIONS(description="Channel 29 yellow voltage") %},
    channel30 INT64 {% OPTIONS(description="Channel 30 yellow voltage") %},
    channel31 INT64 {% OPTIONS(description="Channel 31 yellow voltage") %},
    channel32 INT64 {% OPTIONS(description="Channel 32 yellow voltage") %},
    channel33 INT64 {% OPTIONS(description="Channel 33 yellow voltage") %},
    channel34 INT64 {% OPTIONS(description="Channel 34 yellow voltage") %},
    channel35 INT64 {% OPTIONS(description="Channel 35 yellow voltage") %},
    channel36 INT64 {% OPTIONS(description="Channel 36 yellow voltage") %}
                          >         {% OPTIONS(description="Struct of yellow channel voltages") %},
  channelredvoltage STRUCT<
    channel01 INT64 {% OPTIONS(description="Channel 1 red voltage") %},
    channel02 INT64 {% OPTIONS(description="Channel 2 red voltage") %},
    channel03 INT64 {% OPTIONS(description="Channel 3 red voltage") %},
    channel04 INT64 {% OPTIONS(description="Channel 4 red voltage") %},
    channel05 INT64 {% OPTIONS(description="Channel 5 red voltage") %},
    channel06 INT64 {% OPTIONS(description="Channel 6 red voltage") %},
    channel07 INT64 {% OPTIONS(description="Channel 7 red voltage") %},
    channel08 INT64 {% OPTIONS(description="Channel 8 red voltage") %},
    channel09 INT64 {% OPTIONS(description="Channel 9 red voltage") %},
    channel10 INT64 {% OPTIONS(description="Channel 10 red voltage") %},
    channel11 INT64 {% OPTIONS(description="Channel 11 red voltage") %},
    channel12 INT64 {% OPTIONS(description="Channel 12 red voltage") %},
    channel13 INT64 {% OPTIONS(description="Channel 13 red voltage") %},
    channel14 INT64 {% OPTIONS(description="Channel 14 red voltage") %},
    channel15 INT64 {% OPTIONS(description="Channel 15 red voltage") %},
    channel16 INT64 {% OPTIONS(description="Channel 16 red voltage") %},
    channel17 INT64 {% OPTIONS(description="Channel 17 red voltage") %},
    channel18 INT64 {% OPTIONS(description="Channel 18 red voltage") %},
    channel19 INT64 {% OPTIONS(description="Channel 19 red voltage") %},
    channel20 INT64 {% OPTIONS(description="Channel 20 red voltage") %},
    channel21 INT64 {% OPTIONS(description="Channel 21 red voltage") %},
    channel22 INT64 {% OPTIONS(description="Channel 22 red voltage") %},
    channel23 INT64 {% OPTIONS(description="Channel 23 red voltage") %},
    channel24 INT64 {% OPTIONS(description="Channel 24 red voltage") %},
    channel25 INT64 {% OPTIONS(description="Channel 25 red voltage") %},
    channel26 INT64 {% OPTIONS(description="Channel 26 red voltage") %},
    channel27 INT64 {% OPTIONS(description="Channel 27 red voltage") %},
    channel28 INT64 {% OPTIONS(description="Channel 28 red voltage") %},
    channel29 INT64 {% OPTIONS(description="Channel 29 red voltage") %},
    channel30 INT64 {% OPTIONS(description="Channel 30 red voltage") %},
    channel31 INT64 {% OPTIONS(description="Channel 31 red voltage") %},
    channel32 INT64 {% OPTIONS(description="Channel 32 red voltage") %},
    channel33 INT64 {% OPTIONS(description="Channel 33 red voltage") %},
    channel34 INT64 {% OPTIONS(description="Channel 34 red voltage") %},
    channel35 INT64 {% OPTIONS(description="Channel 35 red voltage") %},
    channel36 INT64 {% OPTIONS(description="Channel 36 red voltage") %}
                          >         {% OPTIONS(description="Struct of red channel voltages") %},
  rawmessage              BYTES     {% OPTIONS(description="Raw byte message from device") %}
)
{% PARTITION BY DATE(pubsubtimestamp) CLUSTER BY organizationidentifier, softwaregatewayid, deviceid %};

CREATE TABLE IF NOT EXISTS {{GatewayPerformanceStatistics}} (
  organizationidentifier       STRING     {% OPTIONS(description="OrganizationIdentifier from authentication") %},
  softwaregatewayid            STRING     {% OPTIONS(description="SoftwareGateway identifier from the HTTP headers") %},
  tz                           STRING     {% OPTIONS(description="SoftwareGateway timezone from the HTTP headers") %},
  topic                        STRING     {% OPTIONS(description="The pubsub topic name") %},
  pubsubtimestamp              TIMESTAMP  {% OPTIONS(description="The timestamp the pubsub message timestamp") %},
  pubsubid                     STRING     {% OPTIONS(description="The pubsub message identifier") %},
  messagetime                  TIMESTAMP  {% OPTIONS(description="The timestamp of the message creation by the gateway") %},
  statistics                   ARRAY<STRUCT<
    deviceid                     STRING     {% OPTIONS(description="Unique identifier for the device") %},
    processes                    ARRAY<STRUCT<
      processname                  STRING     {% OPTIONS(description="One of the measured processes on the gateway") %},
      stats                        STRUCT<
        count                        INT64      {% OPTIONS(description="The number of times the state was executed") %},
        lastexecutedtime             TIMESTAMP  {% OPTIONS(description="The last time the state was executed (in UTC)") %},
        lastexecutedelapsedtime      INT64      {% OPTIONS(description="Elapse time for the state's last execution (in milliseconds)") %},
        totaltime                    INT64      {% OPTIONS(description="The total time spent in the state (in milliseconds)") %},
        mintime                      INT64      {% OPTIONS(description="The minimum time spent in the state (in milliseconds)") %},
        maxtime                      INT64      {% OPTIONS(description="The maximum time spent in the state (in milliseconds)") %},
        errorcount                   INT64      {% OPTIONS(description="The number of errors encountered in this state") %}
                                   >          {% OPTIONS(description="Statistics associated with process") %}
                                 >>         {% OPTIONS(description="Processes running for specific device") %}
                               >>         {% OPTIONS(description="Performance statistics") %},
  rawmessage                   BYTES      {% OPTIONS(description="Raw byte message from device") %}
)
{% PARTITION BY DATE(pubsubtimestamp) CLUSTER BY organizationidentifier, softwaregatewayid %};

CREATE TABLE IF NOT EXISTS {{GatewayLogMessage}} (
  organizationidentifier       STRING     {% OPTIONS(description="OrganizationIdentifier from authentication") %},
  softwaregatewayid            STRING     {% OPTIONS(description="SoftwareGateway identifier from the HTTP headers") %},
  tz                           STRING     {% OPTIONS(description="SoftwareGateway timezone from the HTTP headers") %},
  topic                        STRING     {% OPTIONS(description="The pubsub topic name") %},
  pubsubtimestamp              TIMESTAMP  {% OPTIONS(description="The timestamp the pubsub message timestamp") %},
  pubsubid                     STRING     {% OPTIONS(description="The pubsub message identifier") %},
  messagetime                  TIMESTAMP  {% OPTIONS(description="The timestamp of the message creation by the gateway") %},
  logentries ARRAY<STRUCT<
    level            STRING {% OPTIONS(description="Level of log entry") %},
    logtimestamp     STRING {% OPTIONS(description="Timestamp of log entry") %},
    message          STRING {% OPTIONS(description="Log message") %},
    extras           JSON   {% OPTIONS(description="Catch-all field for anything which couldn't be unmarshalled") %}
  >> {% OPTIONS(description="Structured array of log entries") %},
  rawmessage                   BYTES      {% OPTIONS(description="Raw byte message from device") %}
)
{% PARTITION BY DATE(pubsubtimestamp) CLUSTER BY organizationidentifier, softwaregatewayid %};

CREATE TABLE IF NOT EXISTS {{FaultLogs}} (
  organizationidentifier  STRING    {% OPTIONS(description="OrganizationIdentifier from authentication") %},
  softwaregatewayid       STRING    {% OPTIONS(description="SoftwareGateway identifier from the HTTP headers") %},
  tz                      STRING    {% OPTIONS(description="SoftwareGateway timezone from the HTTP headers") %},
  topic                   STRING    {% OPTIONS(description="The pubsub topic name") %},
  pubsubtimestamp         TIMESTAMP {% OPTIONS(description="The timestamp the pubsub message timestamp") %},
  pubsubid                STRING    {% OPTIONS(description="The pubsub message identifier") %},
  deviceid                STRING    {% OPTIONS(description="Unique identifier for the device") %},
  rawlogmessages STRUCT<
    logmonitorreset         ARRAY<BYTES> {% OPTIONS(description="Raw byte message from LogMonitorReset") %},
    logpreviousfail         ARRAY<BYTES> {% OPTIONS(description="Raw byte message from logPreviousFail") %},
    logaclineevent          ARRAY<BYTES> {% OPTIONS(description="Raw byte message from logACLineEvent") %},
    logfaultsignalsequence  ARRAY<BYTES> {% OPTIONS(description="Raw byte message from logFaultSignalSequence") %},
    logconfiguration        ARRAY<BYTES> {% OPTIONS(description="Raw byte message from logConfiguration") %}
  > {% OPTIONS(description="List of all logs in raw byte form") %},
  loguuid                 STRING    {% OPTIONS(description="The uuid of the full gatewayLog request") %}
)
{% PARTITION BY DATE(pubsubtimestamp) CLUSTER BY organizationidentifier, softwaregatewayid, deviceid %};


CREATE TABLE IF NOT EXISTS {{LogMonitorReset}} (
  organizationidentifier  STRING    {% OPTIONS(description="OrganizationIdentifier from authentication") %},
  softwaregatewayid       STRING    {% OPTIONS(description="SoftwareGateway identifier from the HTTP headers") %},
  tz                      STRING    {% OPTIONS(description="SoftwareGateway timezone from the HTTP headers") %},
  topic                   STRING    {% OPTIONS(description="The pubsub topic name") %},
  pubsubtimestamp         TIMESTAMP {% OPTIONS(description="The timestamp the pubsub message received") %},
  pubsubid                STRING    {% OPTIONS(description="The pubsub message identifier") %},
  deviceid                STRING    {% OPTIONS(description="Unique identifier for the device") %},
  header                  STRUCT<
    commversion      STRING {% OPTIONS(description="Device ECcom revision number") %},
    model            INT64 {% OPTIONS(description="Device model") %},
    firmwareversion  STRING {% OPTIONS(description="Device firmware version") %},
    firmwarerevision STRING {% OPTIONS(description="Device firmware revision") %},
    monitorid        INT64 {% OPTIONS(description="User defined monitor id") %},
    volt220          BOOL  {% OPTIONS(description="Flag for whether device supports monitoring 220 volt") %},
    voltdc           BOOL  {% OPTIONS(description="Flag for whether device supports monitoring dc voltage") %},
    mainsdc          BOOL  {% OPTIONS(description="Flag for whether device supports dc mains power") %},
    powerdownlevel   INT64 {% OPTIONS(description="Voltage level at which device will power down") %},
    blackoutlevel    INT64 {% OPTIONS(description="Voltage level at which device will blackout") %},
    maxchannels      INT64 {% OPTIONS(description="Maximum number of channels monitor can support") %}
  > {% OPTIONS(description="Standard fields derived from first 7 bytes of header") %},
  devicemodel             STRING    {% OPTIONS(description="Model of the device") %},
  records ARRAY<STRUCT<
    eventtimestamp  TIMESTAMP {% OPTIONS(description="The DateTime of the reset event") %},
    resettype       STRING    {% OPTIONS(description="The ResetType of the event") %}
  >> {% OPTIONS(description="Repeated list of monitor reset events") %},
  rawmessage              BYTES     {% OPTIONS(description="Raw byte message from device") %},
  loguuid                 STRING    {% OPTIONS(description="The uuid of the full gatewayLog request") %}
)
{% PARTITION BY DATE(pubsubtimestamp) CLUSTER BY organizationidentifier, softwaregatewayid, deviceid %};


CREATE TABLE IF NOT EXISTS {{logPreviousFail}} (
  organizationidentifier  STRING    {% OPTIONS(description="OrganizationIdentifier from authentication") %},
  softwaregatewayid       STRING    {% OPTIONS(description="SoftwareGateway identifier from the HTTP headers") %},
  tz                      STRING    {% OPTIONS(description="SoftwareGateway timezone from the HTTP headers") %},
  topic                   STRING    {% OPTIONS(description="The pubsub topic name") %},
  pubsubtimestamp         TIMESTAMP {% OPTIONS(description="The timestamp the pubsub message was received") %},
  pubsubid                STRING    {% OPTIONS(description="The pubsub message identifier") %},
  deviceid                STRING    {% OPTIONS(description="Unique identifier for the device") %},
  header                  STRUCT<
    commversion      STRING {% OPTIONS(description="Device ECcom revision number") %},
    model            INT64 {% OPTIONS(description="Device model") %},
    firmwareversion  STRING {% OPTIONS(description="Device firmware version") %},
    firmwarerevision STRING {% OPTIONS(description="Device firmware revision") %},
    monitorid        INT64 {% OPTIONS(description="User defined monitor id") %},
    volt220          BOOL  {% OPTIONS(description="Flag for whether device supports monitoring 220 volt") %},
    voltdc           BOOL  {% OPTIONS(description="Flag for whether device supports monitoring dc voltage") %},
    mainsdc          BOOL  {% OPTIONS(description="Flag for whether device supports dc mains power") %},
    powerdownlevel   INT64 {% OPTIONS(description="Voltage level at which device will power down") %},
    blackoutlevel    INT64 {% OPTIONS(description="Voltage level at which device will blackout") %},
    maxchannels      INT64 {% OPTIONS(description="Maximum number of channels monitor can support") %}
  > {% OPTIONS(description="Standard fields derived from first 7 bytes of header") %},
  devicemodel             STRING    {% OPTIONS(description="Model of the device") %},
  records ARRAY<STRUCT<
    datetime                          TIMESTAMP       {% OPTIONS(description="The DateTime of the previous-fail log entry") %},
    fault                             STRING          {% OPTIONS(description="The user-friendly fault text") %},
    acline                            STRING          {% OPTIONS(description="AC line status string") %},
    t48vdcsignalbus                   STRING          {% OPTIONS(description="48 VDC signal bus") %},
    redenable                         STRING          {% OPTIONS(description="Red enable status") %},
    mccoilee                          STRING          {% OPTIONS(description="MC coil EE status") %},
    specialfunction1                  STRING          {% OPTIONS(description="SpecialFunction1") %},
    specialfunction2                  STRING          {% OPTIONS(description="SpecialFunction2") %},
    wdtmonitor                        STRING          {% OPTIONS(description="Watchdog timer monitor") %},
    t24vdcinput                       STRING          {% OPTIONS(description="24 VDC input") %},
    temperature                       INT64           {% OPTIONS(description="Raw temp minus 40") %},
    lsflashbit                        BOOL            {% OPTIONS(description="LS flash bit") %},
    faultstatus                       ARRAY<BOOL>     {% OPTIONS(description="Decoded fault-status bits") %},
    channelgreenstatus                ARRAY<BOOL>     {% OPTIONS(description="Green channel status bits") %},
    channelyellowstatus               ARRAY<BOOL>     {% OPTIONS(description="Yellow channel status bits") %},
    channelredstatus                  ARRAY<BOOL>     {% OPTIONS(description="Red channel status bits") %},
    channelwalkstatus                 ARRAY<BOOL>     {% OPTIONS(description="Walk channel status bits") %},
    channelgreenfieldcheckstatus      ARRAY<BOOL>     {% OPTIONS(description="Green field-check bits") %},
    channelyellowfieldcheckstatus     ARRAY<BOOL>     {% OPTIONS(description="Yellow field-check bits") %},
    channelredfieldcheckstatus        ARRAY<BOOL>     {% OPTIONS(description="Red field-check bits") %},
    channelwalkfieldcheckstatus       ARRAY<BOOL>     {% OPTIONS(description="Walk field-check bits") %},
    channelgreenrecurrentpulsestatus  ARRAY<BOOL>     {% OPTIONS(description="Green recurrent-pulse bits") %},
    channelyellowrecurrentpulsestatus ARRAY<BOOL>     {% OPTIONS(description="Yellow recurrent-pulse bits") %},
    channelredrecurrentpulsestatus    ARRAY<BOOL>     {% OPTIONS(description="Red recurrent-pulse bits") %},
    channelwalkrecurrentpulsestatus   ARRAY<BOOL>     {% OPTIONS(description="Walk recurrent-pulse bits") %},
    channelgreenrmsvoltage            ARRAY<INT64>    {% OPTIONS(description="Green RMS voltages") %},
    channelyellowrmsvoltage           ARRAY<INT64>    {% OPTIONS(description="Yellow RMS voltages") %},
    channelredrmsvoltage              ARRAY<INT64>    {% OPTIONS(description="Red RMS voltages") %},
    channelwalkrmsvoltage             ARRAY<INT64>    {% OPTIONS(description="Walk RMS voltages") %},
    nextconflictingchannels           ARRAY<BOOL>     {% OPTIONS(description="Next-conflict channel bits") %},
    channelredcurrentstatus           ARRAY<BOOL>     {% OPTIONS(description="Red channel current Status") %},
    channelyellowcurrentstatus        ARRAY<BOOL>     {% OPTIONS(description="Yellow channel current Status") %},
    channelgreencurrentstatus         ARRAY<BOOL>     {% OPTIONS(description="Green channel current Status") %},
    channelredrmscurrent              ARRAY<INT64>     {% OPTIONS(description="Red channel RMS current") %},
    channelyellowrmscurrent           ARRAY<INT64>     {% OPTIONS(description="Yellow channel RMS current") %},
    channelgreenrmscurrent            ARRAY<INT64>     {% OPTIONS(description="Green channel RMS current") %}
  >> {% OPTIONS(description="Repeated list of previous-fail log records") %},
  rawmessage              BYTES     {% OPTIONS(description="Raw byte message from device") %},
  loguuid                 STRING    {% OPTIONS(description="The uuid of the full gatewayLog request") %}
)
{% PARTITION BY DATE(pubsubtimestamp) CLUSTER BY organizationidentifier, softwaregatewayid, deviceid %};


CREATE TABLE IF NOT EXISTS {{logACLineEvent}} (
  organizationidentifier  STRING    {% OPTIONS(description="OrganizationIdentifier from authentication") %},
  softwaregatewayid       STRING    {% OPTIONS(description="SoftwareGateway identifier from the HTTP headers") %},
  tz                      STRING    {% OPTIONS(description="SoftwareGateway timezone from the HTTP headers") %},
  topic                   STRING    {% OPTIONS(description="The pubsub topic name") %},
  pubsubtimestamp         TIMESTAMP {% OPTIONS(description="The timestamp the pubsub message was received") %},
  pubsubid                STRING    {% OPTIONS(description="The pubsub message identifier") %},
  deviceid                STRING    {% OPTIONS(description="Unique identifier for the device") %},
  header                  STRUCT<
    commversion      STRING {% OPTIONS(description="Device ECcom revision number") %},
    model            INT64 {% OPTIONS(description="Device model") %},
    firmwareversion  STRING {% OPTIONS(description="Device firmware version") %},
    firmwarerevision STRING {% OPTIONS(description="Device firmware revision") %},
    monitorid        INT64 {% OPTIONS(description="User defined monitor id") %},
    volt220          BOOL  {% OPTIONS(description="Flag for whether device supports monitoring 220 volt") %},
    voltdc           BOOL  {% OPTIONS(description="Flag for whether device supports monitoring dc voltage") %},
    mainsdc          BOOL  {% OPTIONS(description="Flag for whether device supports dc mains power") %},
    powerdownlevel   INT64 {% OPTIONS(description="Voltage level at which device will power down") %},
    blackoutlevel    INT64 {% OPTIONS(description="Voltage level at which device will blackout") %},
    maxchannels      INT64 {% OPTIONS(description="Maximum number of channels monitor can support") %}
  > {% OPTIONS(description="Standard fields derived from first 7 bytes of header") %},
  devicemodel             STRING    {% OPTIONS(description="Model of the device") %},
  record ARRAY<STRUCT<
    eventtype       STRING    {% OPTIONS(description="Type of AC-line event") %},
    datetime        TIMESTAMP {% OPTIONS(description="Timestamp of the event") %},
    linevoltagerms  INT64     {% OPTIONS(description="Measured RMS voltage") %},
    linefrequencyhz INT64     {% OPTIONS(description="Measured line frequency (may be NULL)") %}
  >> {% OPTIONS(description="Repeated list of AC-line events") %},
  voltagetype             INT64     {% OPTIONS(description="VoltageType code from device") %},
  rawmessage              BYTES     {% OPTIONS(description="Raw byte message from device") %},
  loguuid                 STRING    {% OPTIONS(description="The uuid of the full gatewayLog request") %}
)
{% PARTITION BY DATE(pubsubtimestamp) CLUSTER BY organizationidentifier, softwaregatewayid, deviceid %};


CREATE TABLE IF NOT EXISTS {{logFaultSignalSequence}} (
  organizationidentifier  STRING    {% OPTIONS(description="OrganizationIdentifier from authentication") %},
  softwaregatewayid       STRING    {% OPTIONS(description="SoftwareGateway identifier from the HTTP headers") %},
  tz                      STRING    {% OPTIONS(description="SoftwareGateway timezone from the HTTP headers") %},
  topic                   STRING    {% OPTIONS(description="The pubsub topic name") %},
  pubsubtimestamp         TIMESTAMP {% OPTIONS(description="The timestamp the pubsub message timestamp") %},
  pubsubid                STRING    {% OPTIONS(description="The pubsub message identifier") %},
  deviceid                STRING    {% OPTIONS(description="Unique identifier for the device") %},
  header                  STRUCT<
    commversion      STRING {% OPTIONS(description="Device ECcom revision number") %},
    model            INT64 {% OPTIONS(description="Device model") %},
    firmwareversion  STRING {% OPTIONS(description="Device firmware version") %},
    firmwarerevision STRING {% OPTIONS(description="Device firmware revision") %},
    monitorid        INT64 {% OPTIONS(description="User defined monitor id") %},
    volt220          BOOL  {% OPTIONS(description="Flag for whether device supports monitoring 220 volt") %},
    voltdc           BOOL  {% OPTIONS(description="Flag for whether device supports monitoring dc voltage") %},
    mainsdc          BOOL  {% OPTIONS(description="Flag for whether device supports dc mains power") %},
    powerdownlevel   INT64 {% OPTIONS(description="Voltage level at which device will power down") %},
    blackoutlevel    INT64 {% OPTIONS(description="Voltage level at which device will blackout") %},
    maxchannels      INT64 {% OPTIONS(description="Maximum number of channels monitor can support") %}
  > {% OPTIONS(description="Standard fields derived from first 7 bytes of header") %},
  devicemodel             STRING    {% OPTIONS(description="Model of the device") %},
  records ARRAY<STRUCT<
    tracerawbytes   BYTES        {% OPTIONS(description="traceRawBytes contains all bytes for the trace record") %},
    faulttype       STRING       {% OPTIONS(description="faultType contains the fault type") %},
    records         ARRAY<STRUCT<
      bufferrawbytes BYTES      {% OPTIONS(description="BufferRawBytes contains all bytes for this buffer") %},
      timestamp      INT64      {% OPTIONS(description="Timestamp of the buffer, in 50ms ticks (rolls over at 65530)") %},
      reds           ARRAY<BOOL>{% OPTIONS(description="Red channel bits for this buffer") %},
      yellows        ARRAY<BOOL>{% OPTIONS(description="Yellow channel bits for this buffer") %},
      greens         ARRAY<BOOL>{% OPTIONS(description="Green channel bits for this buffer") %},
      walks          ARRAY<BOOL>{% OPTIONS(description="Walk channel bits for this buffer") %},
      ee_sf_re       BOOL       {% OPTIONS(description="EE_SF_RE flag for this buffer") %},
      acvoltage      INT64      {% OPTIONS(description="AC voltage reading for this buffer") %}
    >> {% OPTIONS(description="Detailed trace-buffer entries") %}
  >> {% OPTIONS(description="Repeated fault-signal-sequence records") %},
  rawmessage              BYTES     {% OPTIONS(description="Raw byte message from device") %},
  loguuid                 STRING    {% OPTIONS(description="The uuid of the full gatewayLog request") %}
)
{% PARTITION BY DATE(pubsubtimestamp) CLUSTER BY organizationidentifier, softwaregatewayid, deviceid %};


CREATE TABLE IF NOT EXISTS {{logConfiguration}} (
  organizationidentifier  STRING    {% OPTIONS(description="OrganizationIdentifier from authentication") %},
  softwaregatewayid       STRING    {% OPTIONS(description="SoftwareGateway identifier from the HTTP headers") %},
  tz                      STRING    {% OPTIONS(description="SoftwareGateway timezone from the HTTP headers") %},
  topic                   STRING    {% OPTIONS(description="The pubsub topic name") %},
  pubsubtimestamp         TIMESTAMP {% OPTIONS(description="The timestamp the pubsub message timestamp") %},
  pubsubid                STRING    {% OPTIONS(description="The pubsub message identifier") %},  
  deviceid                STRING    {% OPTIONS(description="Unique identifier for the device") %},

  header STRUCT<
    commversion      STRING {% OPTIONS(description="Device ECcom revision number") %},
    model            INT64 {% OPTIONS(description="Device model") %},
    firmwareversion  STRING {% OPTIONS(description="Device firmware version") %},
    firmwarerevision STRING {% OPTIONS(description="Device firmware revision") %},
    monitorid        INT64 {% OPTIONS(description="User defined monitor id") %},
    volt220          BOOL  {% OPTIONS(description="Flag for whether device supports monitoring 220 volt") %},
    voltdc           BOOL  {% OPTIONS(description="Flag for whether device supports monitoring dc voltage") %},
    mainsdc          BOOL  {% OPTIONS(description="Flag for whether device supports dc mains power") %},
    powerdownlevel   INT64 {% OPTIONS(description="Voltage level at which device will power down") %},
    blackoutlevel    INT64 {% OPTIONS(description="Voltage level at which device will blackout") %},
    maxchannels      INT64 {% OPTIONS(description="Maximum number of channels monitor can support") %}
  > {% OPTIONS(description="Standard fields derived from first 7 bytes of header") %},

  devicemodel             STRING    {% OPTIONS(description="Model of the device") %},
  record ARRAY<STRUCT<
    datetime                         TIMESTAMP       {% OPTIONS(description="The DateTime of the config‐change event") %},

    -- 31 channel "permissives"
    ch01permissives                   ARRAY<STRING>   {% OPTIONS(description="Ch1 permissives") %},
    ch02permissives                   ARRAY<STRING>   {% OPTIONS(description="Ch2 permissives") %},
    ch03permissives                   ARRAY<STRING>   {% OPTIONS(description="Ch3 permissives") %},
    ch04permissives                   ARRAY<STRING>   {% OPTIONS(description="Ch4 permissives") %},
    ch05permissives                   ARRAY<STRING>   {% OPTIONS(description="Ch5 permissives") %},
    ch06permissives                   ARRAY<STRING>   {% OPTIONS(description="Ch6 permissives") %},
    ch07permissives                   ARRAY<STRING>   {% OPTIONS(description="Ch7 permissives") %},
    ch08permissives                   ARRAY<STRING>   {% OPTIONS(description="Ch8 permissives") %},
    ch09permissives                   ARRAY<STRING>   {% OPTIONS(description="Ch9 permissives") %},
    ch10permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch10 permissives") %},
    ch11permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch11 permissives") %},
    ch12permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch12 permissives") %},
    ch13permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch13 permissives") %},
    ch14permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch14 permissives") %},
    ch15permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch15 permissives") %},
    ch16permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch16 permissives") %},
    ch17permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch17 permissives") %},
    ch18permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch18 permissives") %},
    ch19permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch19 permissives") %},
    ch20permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch20 permissives") %},
    ch21permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch21 permissives") %},
    ch22permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch22 permissives") %},
    ch23permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch23 permissives") %},
    ch24permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch24 permissives") %},
    ch25permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch25 permissives") %},
    ch26permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch26 permissives") %},
    ch27permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch27 permissives") %},
    ch28permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch28 permissives") %},
    ch29permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch29 permissives") %},
    ch30permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch30 permissives") %},
    ch31permissives                  ARRAY<STRING>   {% OPTIONS(description="Ch31 permissives") %},

    -- boolean‐flag arrays
    redfailenable                    ARRAY<BOOL>     {% OPTIONS(description="RedFailEnable flags") %},

    greenyellowdualenable           ARRAY<BOOL>     {% OPTIONS(description="GreenYellowDualEnable flags") %},
    yellowreddualenable              ARRAY<BOOL>     {% OPTIONS(description="YellowRedDualEnable flags") %},
    greenreddualenable               ARRAY<BOOL>     {% OPTIONS(description="GreenRedDualEnable flags") %},
    minimumyellowclearanceenable    ARRAY<BOOL>     {% OPTIONS(description="MinimumYellowClearanceEnable flags") %},
    minimumyellowreduclearanceenable ARRAY<BOOL>    {% OPTIONS(description="MinimumYellowRedClearanceEnable flags") %},
    fieldcheckenablegreen           ARRAY<BOOL>     {% OPTIONS(description="FieldCheckEnableGreen flags") %},
    fieldcheckenableyellow          ARRAY<BOOL>     {% OPTIONS(description="FieldCheckEnableYellow flags") %},
    fieldcheckenablered             ARRAY<BOOL>     {% OPTIONS(description="FieldCheckEnableRed flags") %},
    yellowenable                     ARRAY<BOOL>     {% OPTIONS(description="YellowEnable flags") %},
    hdspchannelenable               ARRAY<BOOL>     {% OPTIONS(description="HDSP channel enable flags") %},

    -- single‐valued fields
    walkenable_ts1                   BOOL            {% OPTIONS(description="WalkEnableTs1 flag") %},
    redfaulttiming                   STRING          {% OPTIONS(description="RedFaultTiming") %},
    recurrentpulse                   BOOL            {% OPTIONS(description="RecurrentPulse flag") %},
    watchdognngtiming                STRING          {% OPTIONS(description="WatchdogTiming") %},
    watchdogenableswitch             BOOL            {% OPTIONS(description="WatchdogEnableSwitch") %},
    programcardmemory                BOOL            {% OPTIONS(description="ProgramCardMemory flag") %},
    gyenable                         BOOL          {% OPTIONS(description="GYEnable flag") %},
    minimumflashtime                 STRING          {% OPTIONS(description="MinimumFlashTime") %},
    cvmlatchenable                   BOOL            {% OPTIONS(description="CvmLatchEnable flag") %},
    logcvmfaults                     BOOL            {% OPTIONS(description="LogCvmFaults flag") %},
    x24viiinputthreshold             STRING          {% OPTIONS(description="X24VIiInputThreshold") %},
    x24vlatchenable                  BOOL            {% OPTIONS(description="X24VLatchEnable") %},
    x24voltinhibit                   BOOL            {% OPTIONS(description="X24VoltInhibit") %},
    x12vpowersupplymonitor          BOOL            {% OPTIONS(description="12V power supply monitor enable") %},
    x48vpowersupplymonitor          BOOL            {% OPTIONS(description="48V power supply monitor enable") %},
    port_1disable                    BOOL            {% OPTIONS(description="Port_1Disable") %},
    typemode                         STRING          {% OPTIONS(description="TypeMode") %},
    ledguardthresholds               BOOL            {% OPTIONS(description="LEDguardThresholds flag") %},
    forcetype_16mode                 BOOL            {% OPTIONS(description="ForceType_16Mode flag") %},
    type_12withsdlcmode              BOOL            {% OPTIONS(description="Type_12WithSdlcMode flag") %},
    vmcvm_24v_3xdaylatch             BOOL            {% OPTIONS(description="VmCvm_24V_3XdayLatch flag") %},
    redfailenabledbyssm              BOOL            {% OPTIONS(description="RedFailEnabledbySSM flag") %},
    dualindicationfaulttiming        STRING          {% OPTIONS(description="DualIndicationFaultTiming") %},
    wdterrorclearonpu                 BOOL            {% OPTIONS(description="WDTErrorClearonPU flag") %},
    minimumflash                     BOOL            {% OPTIONS(description="MinimumFlash flag") %},
    configchangefault                BOOL            {% OPTIONS(description="ConfigChangeFault flag") %},
    redcablefault                    BOOL            {% OPTIONS(description="RedCableFault flag") %},
    aclinebrownout                   STRING          {% OPTIONS(description="AcLineBrownout") %},
    pineepolarity                    STRING          {% OPTIONS(description="PinEEPolarity") %},

    -- string‐array & flags
    flashingyellowarrows             ARRAY<STRING>   {% OPTIONS(description="FlashingYellowArrows") %},
    fyaredandyellowenable            STRING          {% OPTIONS(description="FyaRedAndYellowEnable") %},
    fyaredandgreendisable           STRING          {% OPTIONS(description="FyaRedAndGreenDisable") %},
    fyayellowtrapdetection           BOOL            {% OPTIONS(description="FyaYellowTrapDetection") %},
    fyaflashratefault                BOOL            {% OPTIONS(description="FYAFlashRateFault flag") %},
    fyaflashratedetection            BOOL            {% OPTIONS(description="FyaFlashRateDetection") %},
    pplt5suppression                 STRING          {% OPTIONS(description="Pplt5Suppression") %},
    checkvalue                       STRING          {% OPTIONS(description="CheckValue") %},
    changesource                     STRING          {% OPTIONS(description="ChangeSource") %},
    -- nested VirtualChannel structs
    redvirtualchannel ARRAY<STRUCT<
      color         STRING {% OPTIONS(description="Color") %},
      enabled       BOOL {% OPTIONS(description="Enabled") %},
      sourcechannel INT64 {% OPTIONS(description="SourceChannel") %},
      sourcecolor   STRING  {% OPTIONS(description="SourceColor") %}
    >> {% OPTIONS(description="RedVirtualChannel") %},
    yellowvirtualchannel ARRAY<STRUCT<
      color         STRING {% OPTIONS(description="Color") %},
      enabled       BOOL {% OPTIONS(description="Enabled") %},
      sourcechannel INT64 {% OPTIONS(description="SourceChannel") %},
      sourcecolor   STRING  {% OPTIONS(description="SourceColor") %}
    >> {% OPTIONS(description="YellowVirtualChannel") %},
    greenvirtualchannel ARRAY<STRUCT<
      color         STRING {% OPTIONS(description="Color") %},
      enabled       BOOL {% OPTIONS(description="Enabled") %},
      sourcechannel INT64 {% OPTIONS(description="SourceChannel") %},
      sourcecolor   STRING  {% OPTIONS(description="SourceColor") %}
    >> {% OPTIONS(description="GreenVirtualChannel") %},

    -- current‐sense arrays
    currentsenseredenabled      ARRAY<BOOL> {% OPTIONS(description="CurrentSenseRedenabled") %},
    currentsenseyellowenabled   ARRAY<BOOL> {% OPTIONS(description="CurrentSenseYellowEnabled") %},
    currentsensegreenenabled    ARRAY<BOOL> {% OPTIONS(description="CurrentSenseGreenEnabled") %},
    currentsenseredthreshold    ARRAY<INT64> {% OPTIONS(description="CurrentSenseRedThreshold") %},
    currentsenseyellowthreshold ARRAY<INT64> {% OPTIONS(description="CurrentSenseYellowThreshold") %},
    currentsensegreenthreshold  ARRAY<INT64> {% OPTIONS(description="CurrentSenseGreenThreshold") %},

    -- DarkChannel maps
    darkchannelx01              ARRAY<BOOL> {% OPTIONS(description="DarkChannelx01") %},
    darkchannelx02              ARRAY<BOOL> {% OPTIONS(description="DarkChannelx02") %},
    darkchannelx03              ARRAY<BOOL> {% OPTIONS(description="DarkChannelx03") %},
    darkchannelx04              ARRAY<BOOL> {% OPTIONS(description="DarkChannelx04") %}
  >> {% OPTIONS(description="Repeated list of configuration‐change log records") %},

  rawmessage              BYTES     {% OPTIONS(description="Raw byte payload from device") %},
  loguuid                 STRING    {% OPTIONS(description="UUID of this log entry") %}
)
{% PARTITION BY DATE(pubsubtimestamp) CLUSTER BY organizationidentifier, softwaregatewayid, deviceid %};

CREATE TABLE IF NOT EXISTS {{NotificationMessages}} (
  organizationidentifier  STRING    {% OPTIONS(description="OrganizationIdentifier from authentication") %},
  topic                   STRING    {% OPTIONS(description="The pubsub topic name") %},
  pubsubtimestamp         TIMESTAMP {% OPTIONS(description="The pubsub message timestamp") %},
  pubsubid                STRING    {% OPTIONS(description="The pubsub message identifier") %},
  notificationtype        STRING    {% OPTIONS(description="The type of notification (e.g., sms, email)") %},
  payload                 JSON      {% OPTIONS(description="The notification payload data") %},
  metadata                JSON      {% OPTIONS(description="The notification metadata") %},
  rawmessage              BYTES     {% OPTIONS(description="Raw byte message from pubsub") %}
)
{% PARTITION BY DATE(pubsubtimestamp) CLUSTER BY organizationidentifier %};

CREATE TABLE IF NOT EXISTS {{BatchPerformanceStats}} (
  table                   STRING    {% OPTIONS(description="The BigQuery table name for which metrics are recorded") %},
  timestamp               TIMESTAMP {% OPTIONS(description="The timestamp when the metrics were recorded") %},
  batch_size              INT64     {% OPTIONS(description="Total number of rows processed in the batch") %},
  batch_bytes             INT64     {% OPTIONS(description="Total size in bytes of the batch data") %},
  processing_ms           INT64     {% OPTIONS(description="Time spent on pre-processing (serialization, splitting) in milliseconds") %},
  loading_ms              INT64     {% OPTIONS(description="Time spent on BigQuery operations in milliseconds") %},
  total_duration_ms       INT64     {% OPTIONS(description="Total processing time in milliseconds (processing + loading)") %},
  retries                 INT64     {% OPTIONS(description="Number of retry attempts made") %},
  error                   STRING    {% OPTIONS(description="Error message if the batch failed") %},
  splits                  INT64     {% OPTIONS(description="Number of times the batch was split due to size limits") %},
  dlq_messages            INT64     {% OPTIONS(description="Number of messages sent to DLQ") %},
  dlq_bytes               INT64     {% OPTIONS(description="Total size in bytes of DLQ messages") %},
  current_depth           INT64     {% OPTIONS(description="Current queue depth when metrics were recorded") %}
)
{% PARTITION BY DATE(timestamp) CLUSTER BY table %};

-- Audit table for BigQuery ingestion
CREATE TABLE IF NOT EXISTS {{InviteEvents}} (
  userinviteid  STRING    {% OPTIONS(description="The UUID of the userinvite ") %},
  eventtype     STRING    {% OPTIONS(description=" create, retry, revoke, redeem, rejected") %},
  actor         STRING    {% OPTIONS(description="Either the UUID of the user making the change or the email of the person being invited") %},
  eventtime     TIMESTAMP {% OPTIONS(description="The timestamp the invite event") %},
  organizationidentifier STRING    {% OPTIONS(description="The UUID of the organization") %},
  tokenhash STRING    {% OPTIONS(description="The tokenhash for the email invite ") %},
  email STRING    {% OPTIONS(description="The Email of the person being invited") %},
  inviterid STRING    {% OPTIONS(description="The UUID of the user that did the invite") %},
  organizationrole STRING    {% OPTIONS(description="The UUID of the custom role used assigned to this user") %},
  status STRING    {% OPTIONS(description="The current status of the invite") %},
  message STRING    {% OPTIONS(description="The custom message added to the invite, this is OPTIONAL") %},
  requiresso BOOL {% OPTIONS(description="Flag to require SSO when creating account") %},
  retrycount INT64    {% OPTIONS(description="The count of retrys") %},
  retried TIMESTAMP {% OPTIONS(description="The timestamp of the latest retry") %},
  expired TIMESTAMP {% OPTIONS(description="The timestamp the invite event expires NULL means it wont expire") %},
  created TIMESTAMP {% OPTIONS(description="The timestamp the invite event was created") %},
  sent TIMESTAMP {% OPTIONS(description="The timestamp the invite event was sent") %},
  updated TIMESTAMP {% OPTIONS(description="The timestamp the invite event status was changed") %}
)
{% PARTITION BY DATE(eventtime) CLUSTER BY userinviteid %};

-- APS Factory Reset Logs table for tracking all factory reset requests
CREATE TABLE IF NOT EXISTS {{APSFactoryResetLogs}} (
  user_id            STRING    {% OPTIONS(description="The unique identifier of the user making the request") %},
  organization_id    STRING    {% OPTIONS(description="The unique identifier of the user's organization") %},
  ip_address         STRING    {% OPTIONS(description="The IP address of the user making the request, if available") %},
  challenge_code     STRING    {% OPTIONS(description="The challenge code used for the factory reset request") %},
  success            BOOL      {% OPTIONS(description="Whether the factory reset request was successful") %},
  error_message      STRING    {% OPTIONS(description="Error message if the request failed, NULL if successful") %},
  request_timestamp  TIMESTAMP {% OPTIONS(description="Timestamp when the request was processed") %}
)
{% PARTITION BY DATE(request_timestamp) CLUSTER BY organization_id, user_id %};
