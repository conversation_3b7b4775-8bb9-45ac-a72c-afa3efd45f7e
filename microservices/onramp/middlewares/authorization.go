package middlewares

import (
	"context"
	"net/http"

	"github.com/google/uuid"
	"synapse-its.com/onramp/domain"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/rest/onramp/helper"
)

type authorizationContextKey string

const AuthorizationContextKey = authorizationContextKey("authorization")

// UserAuthorizationMiddleware ensures that users can only access their own resources
// by comparing the user ID in the URL path with the logged-in user's ID from the session
func UserAuthorizationMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Extract session data from context (set by SessionMiddleware)
			sessionData, ok := r.Context().Value(SessionContextKey).(*domain.Session)
			if !ok {
				logger.Warn("UserAuthorizationMiddleware: No session data found in context")
				response.CreateUnauthorizedResponse(w)
				return
			}

			// Extract user ID from session
			if sessionData.UserID == "" {
				logger.Warn("UserAuthorizationMiddleware: No user ID in session")
				response.CreateUnauthorizedResponse(w)
				return
			}

			// Parse the logged-in user's ID
			loggedInUserID, err := uuid.Parse(sessionData.UserID)
			if err != nil {
				logger.Errorf("UserAuthorizationMiddleware: Invalid user ID in session: %v", err)
				response.CreateUnauthorizedResponse(w)
				return
			}

			// Extract user ID from URL path
			// Expected pattern: /user/{userId}/...
			requestedUserID, err := helper.ValidateUserID(r)
			if err != nil {
				logger.Errorf("UserAuthorizationMiddleware: Invalid user ID in URL path: %v", err)
				response.CreateBadRequestResponse(w)
				return
			}

			// Ensure the logged-in user can only access their own resources
			if loggedInUserID != requestedUserID {
				logger.Warnf("UserAuthorizationMiddleware: User %s attempted to access resources for user %s", loggedInUserID, requestedUserID)
				response.CreateForbiddenResponse(w)
				return
			}

			// Authorization successful - add authorization context for future use
			authContext := &AuthorizationContext{
				UserID: loggedInUserID,
			}
			ctx := context.WithValue(r.Context(), AuthorizationContextKey, authContext)

			// User is authorized to access this resource, continue to next handler
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

// AuthorizationContext holds authorization information for the current request
type AuthorizationContext struct {
	UserID uuid.UUID
	// Future fields for additional authorization checks (e.g., admin status, permissions)ss
}

// GetAuthorizationContext extracts authorization context from the request context
func GetAuthorizationContext(ctx context.Context) (*AuthorizationContext, bool) {
	authContext, ok := ctx.Value(AuthorizationContextKey).(*AuthorizationContext)
	return authContext, ok
}
