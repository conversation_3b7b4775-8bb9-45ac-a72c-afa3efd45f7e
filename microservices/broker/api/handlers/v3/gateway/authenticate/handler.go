package authenticate

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"time"

	"github.com/google/uuid"
	response "synapse-its.com/shared/api/response"
	security "synapse-its.com/shared/api/security"
	softwareGateway "synapse-its.com/shared/api/softwaregateway"
	connect "synapse-its.com/shared/connect"
	logger "synapse-its.com/shared/logger"
	gatewayConfig "synapse-its.com/shared/rest/onramp/softwaregateway/config"
)

// <PERSON><PERSON> serves as the handler for the main function for the gateway authentication endpoint.
func Handler(w http.ResponseWriter, r *http.Request) {
	// get the following attributes passed in via the header:
	//    gateway-device-id
	//    message-type
	//    x-api-key is not used and can be ignored
	// get the following from the body:
	//    {"token": "thetokenvalue"} the value may be empty

	// obtain the software gateway record from the db (lookup SoftwareGateway)
	//    message-validation
	//    if the gateway-device-id empty - reject
	//    if the message-type <> authenticate - reject
	//    if token missing from body - reject
	//
	//    db-validation
	//    if the gateway-device-id not found in db - reject
	//    if db software gateway not enabled - reject
	//    if software gateway token does not match the db token (when the db token is not blank) - reject
	//
	//	  if the db software gateway token does match - return the settings and rotate the token
	//
	//	  if the db software gateway token matches and there is not a db token - return the setting and establish a token
	//

	gatewayId, rqToken, gatewayVersion, err := parseRequest(r)
	if err != nil {
		logger.Infof("Unable to parse request: %v", err)
		response.CreateUnauthorizedResponse(w)
		return
	}

	// Get the postgres connection.
	connections, err := connect.GetConnections(r.Context())
	if err != nil {
		logger.Errorf("%v", err)
		response.CreateInternalErrorResponse(w)
		return
	}
	pg := connections.Postgres

	// Query database for token information and get template-based configuration
	gatewayInfo, templateSettings, err := getGatewayConfigWithTemplates(pg, gatewayId, rqToken)
	if err != nil {
		logger.Infof("Error getting gateway info: %v", err)
		response.CreateUnauthorizedResponse(w)
		return
	}

	// Extract API version from the URL path
	apiVersion := extractAPIVersion(r.URL.Path)
	logger.Debugf("Extracted API version: %s from URL path: %s", apiVersion, r.URL.Path)

	// Convert template-based settings to dynamic configuration map
	var gatewayConfigMap map[string]interface{}
	if templateSettings != nil {
		// Use template-based configuration with version filtering
		gatewayConfigMap, err = convertSettingsToGatewaySettingsWithVersion(templateSettings, apiVersion)
		if err != nil {
			logger.Errorf("Error converting template settings to gateway settings: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		logger.Debugf("Using template-based configuration for gateway %s with API version %s", gatewayId, apiVersion)
	} else {
		// No template configuration found - use empty map
		gatewayConfigMap = make(map[string]interface{})
		logger.Infof("No template configuration found for gateway %s, using empty configuration", gatewayId)
	}

	// Pull the firestore encrypted credentials from firestore
	googleConfig := connections.Firestore.EncryptedCreds()

	// Get JWT public key
	_, publicKey, err := security.GetJWTAsymmetric()
	if err != nil {
		logger.Infof("Public key not found in secrets manager: %v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	// Query the database for all associated devices to the gateway
	deviceSettings, err := getDeviceSettings(pg, gatewayInfo.Id)
	if err != nil {
		logger.Infof("Error device config: %v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	// Generate new token and update the database with a new token and the current utc time
	cloudInfo, err := setNewGatewayToken(pg, gatewayId, gatewayVersion)
	if err != nil {
		logger.Errorf("Error setting gateway token: %v", err)
		response.CreateInternalErrorResponse(w)
		return
	}
	cloudInfo.ApiKey = gatewayInfo.APIKey

	globalSettings := &softwareGateway.GlobalSettings{}
	globalSettings.OrganizationId = gatewayInfo.OrganizationId
	globalSettings.PublicKey = publicKey
	globalSettings.AWS = *cloudInfo
	globalSettings.Devices = *deviceSettings
	globalSettings.Google = googleConfig
	globalSettings.Gateway = gatewayConfigMap

	logger.Debugf("Successfully authenticated: %v", gatewayId)
	response.CreateSuccessResponse(globalSettings, w)
}

var parseRequest = func(r *http.Request) (string, string, string, error) {
	gatewayId := r.Header.Get("gateway-device-id")
	if gatewayId == "" {
		return "", "", "", errors.New("header gateway-device-id is not present")
	}

	messageType := r.Header.Get("message-type")
	if messageType == "" {
		return "", "", "", errors.New("header message-type is not present")
	}
	if messageType != "authenticate" {
		return "", "", "", errors.New("header message-type != authenticate")
	}

	gatewayVersion := r.Header.Get("gateway-version")

	var body requestBody
	err := json.NewDecoder(r.Body).Decode(&body)
	if err != nil {
		return "", "", "", err
	}

	if body.Token == nil {
		return "", "", "", errors.New("token does not exist in the body")
	}
	return gatewayId, *body.Token, gatewayVersion, nil
}

var getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGatewayInfo, error) {
	query := `
		SELECT 
			s.Id, 
			s.OrganizationId, 
			s.APIKey,
			s.Config,
			s.Token
		FROM {{SoftwareGateway}} s
		WHERE s.IsEnabled AND s.MachineKey = $1 AND (s.Token = $2 OR s.Token = '')`
	gatewayInfo := &dbGatewayInfo{}
	err := pg.QueryRowStruct(gatewayInfo, query, gatewayId, rqToken)

	// Query returns no rows
	if errors.Is(err, sql.ErrNoRows) {
		return nil, fmt.Errorf("GatewayId (%s) was not found. Either the gatewayId does not exist, the gatewayId is disabled, etc", gatewayId)
	}
	if err != nil {
		return nil, err
	}
	return gatewayInfo, nil
}

// getSoftwareGatewayByIdentifierFunc is a variable wrapper for the external function call to make it testable
var getSoftwareGatewayByIdentifierFunc = gatewayConfig.GetSoftwareGatewayByIdentifier

// getGatewayConfigWithTemplates retrieves gateway info with template-based configuration
var getGatewayConfigWithTemplates = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGatewayInfo, []gatewayConfig.ResolvedGatewayConfigSetting, error) {
	// First get basic gateway info to validate authentication
	gatewayInfo, err := getGatewayInfo(pg, gatewayId, rqToken)
	if err != nil {
		return nil, nil, err
	}

	// Parse the ID as UUID for template config lookup
	logger.Debugf("Checking if gateway database ID %s can be parsed as UUID (request ID was %s)", gatewayInfo.Id, gatewayId)
	gatewayUUID, err := uuid.Parse(gatewayInfo.Id)
	if err != nil {
		// If ID is not a valid UUID, return no template settings
		logger.Infof("Gateway ID %s is not a valid UUID, no template configuration available: %v", gatewayInfo.Id, err)
		return gatewayInfo, nil, nil
	}

	// Use the newly exported function to get template-based configuration
	_, templateSettings, err := getSoftwareGatewayByIdentifierFunc(pg, gatewayUUID)
	if err != nil {
		// If template config fails, return no template settings
		if errors.Is(err, gatewayConfig.ErrSoftwareGatewayConfigNotFound) {
			logger.Infof("No template configuration found for gateway %s", gatewayId)
			return gatewayInfo, nil, nil
		}
		logger.Errorf("Failed to get template configuration for gateway %s: %v", gatewayId, err)
		return gatewayInfo, nil, nil
	}

	logger.Infof("Successfully retrieved template-based configuration for gateway %s", gatewayId)
	return gatewayInfo, templateSettings, nil
}

// extractAPIVersion extracts the API version from the URL path
// For URLs like "/api/v3/gateway/authenticate", it returns "v3"
func extractAPIVersion(urlPath string) string {
	// Use regex to match version pattern like v1, v2, v3, etc.
	versionRegex := regexp.MustCompile(`/v(\d+)/`)
	matches := versionRegex.FindStringSubmatch(urlPath)

	if len(matches) >= 2 {
		return "v" + matches[1]
	}

	// Default to v1 if no version found in URL
	logger.Warnf("Could not extract API version from URL path: %s, defaulting to v1", urlPath)
	return "v1"
}

// convertSettingsToGatewaySettingsWithVersion converts resolved gateway config settings to a dynamic map
// This uses the Format field from the database to determine the correct data type
// Only includes settings that support the specified API version
var convertSettingsToGatewaySettingsWithVersion = func(settings []gatewayConfig.ResolvedGatewayConfigSetting, apiVersion string) (map[string]interface{}, error) {
	// Create a map of settings with format-based type conversion
	configMap := make(map[string]interface{})

	for _, setting := range settings {
		// Check if this setting supports the specified API version
		if !isSettingSupportedForVersion(setting.Format, apiVersion) {
			logger.Debugf("Skipping setting %s: not supported for API version %s", setting.Setting, apiVersion)
			continue
		}

		value := setting.Value

		// Use the Format field to determine the correct type conversion
		convertedValue, err := convertValueUsingFormat(setting.Setting, value, setting.Format)
		if err != nil {
			logger.Errorf("Error converting setting %s with format %s: %v", setting.Setting, setting.Format, err)
			// Fall back to string value if conversion fails
			convertedValue = value
		}

		configMap[setting.Setting] = convertedValue

		logger.Debugf("Template setting %s: %s -> %v (type: %T) using format: %s", setting.Setting, value, convertedValue, convertedValue, setting.Format)
	}

	return configMap, nil
}

// convertSettingsToGatewaySettings converts resolved gateway config settings to a dynamic map
// This is the legacy function that defaults to v3 for backward compatibility
var convertSettingsToGatewaySettings = func(settings []gatewayConfig.ResolvedGatewayConfigSetting) (map[string]interface{}, error) {
	return convertSettingsToGatewaySettingsWithVersion(settings, "v3")
}

// SettingFormat represents the JSON structure stored in the Format field
type SettingFormat struct {
	Type     string   `json:"type"`
	Versions []string `json:"versions,omitempty"`
}

// isSettingSupportedForVersion checks if a setting supports the specified API version
func isSettingSupportedForVersion(formatJSON, version string) bool {
	// Parse the format JSON
	var format SettingFormat
	if err := json.Unmarshal([]byte(formatJSON), &format); err != nil {
		logger.Warnf("Failed to parse format JSON for version check: %v", err)
		// If we can't parse the format, include the setting to maintain backward compatibility
		return true
	}

	// If versions array is empty or nil, include the setting for all versions (backward compatibility)
	if len(format.Versions) == 0 {
		return true
	}

	// Check if the specified version is in the versions array
	for _, supportedVersion := range format.Versions {
		if supportedVersion == version {
			return true
		}
	}

	// Version not found in the list
	return false
}

// convertValueUsingFormat converts a string value to the appropriate type based on the format specification
func convertValueUsingFormat(settingName, value, formatJSON string) (interface{}, error) {
	// Parse the format JSON
	var format SettingFormat
	if err := json.Unmarshal([]byte(formatJSON), &format); err != nil {
		return nil, fmt.Errorf("failed to parse format JSON: %w", err)
	}

	// Convert based on the specified type
	switch format.Type {
	case "integer":
		if value == "" {
			return 0, nil
		}
		intVal, err := strconv.Atoi(value)
		if err != nil {
			return nil, fmt.Errorf("failed to convert '%s' to integer: %w", value, err)
		}
		return intVal, nil

	case "boolean":
		if value == "" {
			return false, nil
		}
		boolVal, err := strconv.ParseBool(value)
		if err != nil {
			return nil, fmt.Errorf("failed to convert '%s' to boolean: %w", value, err)
		}
		return boolVal, nil

	case "array":
		if value == "" || value == "[]" {
			return []interface{}{}, nil
		}
		// Parse as JSON array
		var arrayVal []interface{}
		if err := json.Unmarshal([]byte(value), &arrayVal); err != nil {
			return nil, fmt.Errorf("failed to convert '%s' to array: %w", value, err)
		}
		return arrayVal, nil

	case "string":
		return value, nil

	case "text":
		// Backward compatibility - treat "text" the same as "string"
		return value, nil

	default:
		logger.Warnf("Unknown format type '%s' for setting '%s', defaulting to string", format.Type, settingName)
		return value, nil
	}
}

var getDeviceSettings = func(pg connect.DatabaseExecutor, dbSoftwareGatewayId string) (*[]softwareGateway.DeviceSettings, error) {
	query := `
		SELECT 
			d.Id AS device_id, 
			l.Latitude, 
			l.Longitude, 
			d.IPAddress AS ip_address, 
			d.Port, 
			d.FlushConnectionMs AS flush_connection_ms, 
			d.Type AS device_type,
			d.EnableRealtime AS enable_realtime
		FROM {{Device}} d
		JOIN {{Location}} l ON l.Id = d.LocationId
		WHERE d.IsEnabled AND d.SoftwareGatewayId = $1`
	deviceSettings := []softwareGateway.DeviceSettings{}
	if err := pg.QueryGenericSlice(&deviceSettings, query, dbSoftwareGatewayId); err != nil {
		return nil, err
	}
	return &deviceSettings, nil
}

var setNewGatewayToken = func(pg connect.DatabaseExecutor, gatewayId string, gatewayVersion string) (*softwareGateway.CloudSettings, error) {
	newToken, err := generateRandomTokenHex(50)
	if err != nil {
		return nil, err
	}
	cloudInfo := softwareGateway.CloudSettings{}
	cloudInfo.Token = newToken
	query := `
		UPDATE {{SoftwareGateway}} 
		SET 
			Token = $1, 
			DateLastCheckedIn = $2,
			GatewayVersion = $3
		WHERE MachineKey = $4`
	if _, err = pg.Exec(query, newToken, time.Now().UTC().Format(time.DateTime), gatewayVersion, gatewayId); err != nil {
		return nil, err
	}
	return &cloudInfo, nil
}
