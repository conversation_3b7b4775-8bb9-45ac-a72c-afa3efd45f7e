package locations

import (
	"time"

	"github.com/google/uuid"
)

// Location represents a location for an organization
type Location struct {
	Id             uuid.UUID `json:"id" db:"id"`
	OrganizationId uuid.UUID `json:"organizationid" db:"organizationid"`
	Name           string    `json:"name" db:"name"`
	Description    string    `json:"description" db:"description"`
	Latitude       float64   `json:"latitude" db:"latitude"`
	Longitude      float64   `json:"longitude" db:"longitude"`
	IsDeleted      bool      `json:"-" db:"isdeleted"`
	CreatedAt      time.Time `json:"createdat" db:"createdat"`
	UpdatedAt      time.Time `json:"updatedat" db:"updatedat"`
}

// CreateAndUpdateLocationRequest represents the request body for creating/updating a location
type CreateAndUpdateLocationRequest struct {
	Name        string  `json:"name"`
	Description string  `json:"description"`
	Latitude    float64 `json:"latitude"`
	Longitude   float64 `json:"longitude"`
}
