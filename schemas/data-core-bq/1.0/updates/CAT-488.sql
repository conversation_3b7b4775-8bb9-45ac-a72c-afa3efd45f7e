-- CAT-488: Add current_depth to BatchPerformanceStats table
-- This script adds the current_depth column to the BatchPerformanceStats table.

-- ===============================================================================
-- STEP 1: Create temporary table with current data
-- ===============================================================================

CREATE TABLE {{BatchPerformanceStats_temp}} AS
SELECT * FROM {{BatchPerformanceStats}};

-- ===============================================================================
-- STEP 2: Drop the original table
-- ===============================================================================

DROP TABLE {{BatchPerformanceStats}};

-- ===============================================================================
-- STEP 3: Recreate the table with the new column
-- ===============================================================================

CREATE TABLE IF NOT EXISTS {{BatchPerformanceStats}} (
  table                   STRING    {% OPTIONS(description="The BigQuery table name for which metrics are recorded") %},
  timestamp               TIMESTAMP {% OPTIONS(description="The timestamp when the metrics were recorded") %},
  batch_size              INT64     {% OPTIONS(description="Total number of rows processed in the batch") %},
  batch_bytes             INT64     {% OPTIONS(description="Total size in bytes of the batch data") %},
  processing_ms           INT64     {% OPTIONS(description="Time spent on pre-processing (serialization, splitting) in milliseconds") %},
  loading_ms              INT64     {% OPTIONS(description="Time spent on BigQuery operations in milliseconds") %},
  total_duration_ms       INT64     {% OPTIONS(description="Total processing time in milliseconds (processing + loading)") %},
  retries                 INT64     {% OPTIONS(description="Number of retry attempts made") %},
  error                   STRING    {% OPTIONS(description="Error message if the batch failed") %},
  splits                  INT64     {% OPTIONS(description="Number of times the batch was split due to size limits") %},
  dlq_messages            INT64     {% OPTIONS(description="Number of messages sent to DLQ") %},
  dlq_bytes               INT64     {% OPTIONS(description="Total size in bytes of DLQ messages") %},
  current_depth           INT64     {% OPTIONS(description="Current queue depth when metrics were recorded") %}
)
{% PARTITION BY DATE(timestamp) CLUSTER BY table %};

-- ===============================================================================
-- STEP 4: Insert data back into the recreated table
-- ===============================================================================

INSERT INTO {{BatchPerformanceStats}} (
  `table`,
  timestamp,
  batch_size,
  batch_bytes,
  processing_ms,
  loading_ms,
  total_duration_ms,
  retries,
  error,
  splits,
  dlq_messages,
  dlq_bytes
)
SELECT
  table,
  timestamp,
  batch_size,
  batch_bytes,
  processing_ms,
  loading_ms,
  total_duration_ms,
  retries,
  error,
  splits,
  dlq_messages,
  dlq_bytes
FROM {{BatchPerformanceStats_temp}};

-- ===============================================================================
-- STEP 5: Drop the temporary table
-- ===============================================================================

DROP TABLE {{BatchPerformanceStats_temp}};