package organization

import (
	"encoding/json"
	"net/http"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"synapse-its.com/onramp/modules/auth/session"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/logger"
)

// handleAPSFactorReset handles APS factory reset requests
func (h *Hand<PERSON>) handleAPSFactorReset(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)

	orgID, err := uuid.Parse(vars["organizationId"])
	if err != nil {
		logger.Errorf("organizationId is not a valid UUID: %v", err)
		response.CreateBadRequestResponse(w)
		return
	}

	var body struct {
		ChallengeCode string `json:"challengeCode"`
	}
	if err = json.NewDecoder(r.Body).Decode(&body); err != nil {
		logger.Errorf("failed to decode request body: %v", err)
		response.CreateBadRequestResponse(w)
		return
	}

	challengeCode := body.ChallengeCode
	if len(challengeCode) != 8 {
		logger.Errorf("challengeCode must be 8 characters long: %v", challengeCode)
		response.CreateBadRequestResponse(w)
		return
	}

	// Get session data from context using helper function
	sessionData, err := session.GetSessionFromContext(r.Context())
	if err != nil {
		logger.Errorf("failed to get session from context: %v", err)
		if err == session.ErrSessionPermissionsNil {
			response.CreateUnauthorizedResponse(w)
		} else {
			response.CreateInternalErrorResponse(w)
		}
		return
	}

	userID, err := uuid.Parse(sessionData.UserID)
	if err != nil {
		logger.Errorf("session.UserID is not a valid UUID: %v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	userPermissions := sessionData.UserPermissions

	// Convert to authorizer.UserPermissions for permission checking
	authorizerPermissions := (*authorizer.UserPermissions)(userPermissions)
	if !authorizerPermissions.HasAnyPermissionInScope([]string{"synapse_aps_factory_reset", "org_aps_factory_reset"}, orgID.String()) {
		logger.Errorf("user does not have permission to perform APS factory reset")
		response.CreateForbiddenResponse(w)
		return
	}

	logger.Infof("Calling GetAPSFactorResetCode with userID: %s, orgID: %s, challengeCode: %s", userID, orgID, challengeCode)
	code, err := h.apsFactoryResetService.GetAPSFactorResetCode(userID, orgID, challengeCode)
	if err != nil {
		logger.Errorf("failed to get APS factory reset code: %v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	response.CreateSuccessResponse(code, w)
}
