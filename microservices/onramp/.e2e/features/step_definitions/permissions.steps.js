const { Given, When, Then } = require('@cucumber/cucumber');
const { By, until } = require('selenium-webdriver');
const fs = require('fs').promises;
const { waitForPageLoad } = require('../support/utils');

// Generic retry function for handling flaky interactions
const retry = async (fn, retries = 5, delay = 3000) => {
  for (let i = 0; i < retries; i++) {
    try {
      return await fn();
    } catch (err) {
      if (i < retries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      throw err;
    }
  }
};

Then('I am redirected to the {string} page', async function (pageName) {
  const expectedUrlPart = pageName.toLowerCase().replace(/\s+/g, '');
  await this.driver.wait(
    until.urlContains(expectedUrlPart),
    60000,
    `Timeout waiting for redirection to ${pageName} page`
  );
});

Given('I find the row with name {string} in the table', async function (orgName) {
  const normalizedOrgName = orgName.replace(/’/g, "'").replace(/-/g, "\\-").trim();
  const xpath = `//table[@id='organization-list']//tr[td[contains(translate(normalize-space(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), "${normalizedOrgName.toLowerCase()}")]]`;

  await this.driver.wait(until.elementLocated(By.id('organization-list')), 60000);
  const row = await this.driver.wait(until.elementLocated(By.xpath(xpath)), 60000);
  await this.driver.wait(until.elementIsVisible(row), 15000);
  const orgIdElement = await row.findElement(By.css('[data-org-id], .org-id'));
  this.orgId = await orgIdElement.getAttribute('data-org-id') || await orgIdElement.getText();
  if (!this.orgId) {
    throw new Error(`orgId not found for organization "${orgName}"`);
  }
});

When('I click the {string} button with id {string} in that row', async function (buttonText, buttonId) {
  const button = await this.driver.wait(until.elementLocated(By.id(buttonId)), 60000);
  await this.driver.wait(until.elementIsVisible(button), 10000);
  await this.driver.wait(until.elementIsEnabled(button), 5000);
  button.click();
});

Then('I see the page title {string} with id {string}', async function (title, titleId) {
  let permisionTitle = await this.driver.wait(until.elementLocated(By.id(titleId)), 60000);
  await this.driver.wait(until.elementIsVisible(permisionTitle), 15000);

});

Then('I see a table with id {string}', async function (tableId) {
  let permisionList = await this.driver.wait(until.elementLocated(By.id("permissions-list")), 60000);
  await this.driver.wait(until.elementIsVisible(permisionList), 15000);

  let permisionContainer = await this.driver.wait(until.elementLocated(By.id("permission-container")), 60000);
  await this.driver.wait(until.elementIsVisible(permisionContainer), 15000);
});

Then('the table contains at least one record', async function () {
  const rows = await this.driver.findElements(By.css('#permissions-list tr'));
  if (rows.length <= 1) {
    throw new Error('No records found in the permissions table');
  }
});

When('I click the button with id {string}', async function (buttonId) {
  const button = await this.driver.wait(until.elementLocated(By.id(buttonId)), 60000);
  await this.driver.wait(until.elementIsEnabled(button), 15000);
  await this.driver.executeScript('arguments[0].click();', button);
  await waitForPageLoad(this.driver);
});

Then('I see a modal with class {string}', async function (modalClass) {
  const selectors = [`.${modalClass}`, '.ant-modal', '.modal', '[role="dialog"]'];
  let found = false;
  let lastError;
  for (const selector of selectors) {
    try {
      const modal = await this.driver.wait(until.elementLocated(By.css(selector)), 60000);
      await this.driver.wait(until.elementIsVisible(modal), 15000);
      found = true;
      break;
    } catch (err) {
      lastError = err;
    }
  }
  if (!found) {
    throw new Error(`Modal with class "${modalClass}" not found: ${lastError.message}`);
  }
});

Then('I see the modal title {string}', async function (expectedTitle) {
  const modal = await this.driver.wait(
    until.elementLocated(By.css('.modal-create-role, .ant-modal, .modal, [role="dialog"]')),
    60000
  );
  const titleElements = await modal.findElements(By.css('.ant-modal-title, .modal-title, h1, h2, h3, .modal-header'));
  let actualTitle = '';
  for (const el of titleElements) {
    actualTitle = await el.getText();
    if (actualTitle.trim()) break;
  }
});

When('I fill in the name field with {string}', async function (name) {
  let nameField = await this.driver.wait(until.elementLocated(By.id("new-organization-name-role")), 10000)
  let nameEl = await this.driver.wait(until.elementIsVisible(nameField), 3000);
  await nameEl.clear();
  await nameEl.sendKeys(name);
});

When('I fill in the description field with {string}', async function (description) {
  let descriptionField = await this.driver.wait(until.elementLocated(By.id("new-permission-role")), 10000)
  let descriptionEl = await this.driver.wait(until.elementIsVisible(descriptionField), 3000);
  await descriptionEl.clear();
  await descriptionEl.sendKeys(description);
});

Then('I see the message {string}', async function (expectedMessage) {

  const notifications = await this.driver.wait(until.elementsLocated(By.css('.ant-notification-notice-message, .ant-message-notice-content, [role="alert"], .notification, .message, .success-message, .toast')), 15000);
  let found = false;
  for (const notification of notifications) {
    const text = await notification.getText();
    if (text.trim() === expectedMessage) {
      found = true;
      break;
    }
  }
  // keep for later check
  // let messageElement;
  // messageElement = await this.driver.findElement(
  //   By.css('.ant-notification-notice-message, .notification-message, .toast-message, .ant-message-notice-content, div[role="alert"], .success-message')
  // );
  // await this.driver.wait(until.elementIsVisible(messageElement), 15000);
  // const actualMessage = await messageElement.getText();
  // if (!actualMessage.includes(expectedMessage)) {
  //   throw new Error(`Expected message to be "${expectedMessage}" but got "${actualMessage}"`);
  // }
});

Then('the table with id {string} contains {string}', async function (tableId, roleName) {
  await this.driver.wait(until.elementLocated(By.id(tableId)), 60000);
  let found = false;
  await retry(async () => {
    const roleLinks = await this.driver.findElements(By.css(`#${tableId} tr td a, #${tableId} tr td`));
    for (let link of roleLinks) {
      const text = await link.getText();
      if (text.trim().toLowerCase() === roleName.toLowerCase()) {
        found = true;
        break;
      }
    }
    if (!found) {
      throw new Error(`"${roleName}" not found in table "${tableId}"`);
    }
  }, 5, 3000);
});