package app

import (
	"context"
	"encoding/json"
	"maps"
	"net/http"
	"time"

	"github.com/gorilla/mux"

	"synapse-its.com/rushhour/modules/socketio"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// App represents the main application structure
type App struct {
	muxRouter       *mux.Router
	socketioService *socketio.Service
	socketioHandler *socketio.Handler
	connections     *connect.Connections
	batch           bqbatch.Batcher
}

// NewApp creates a new application instance with all dependencies wired up
func NewApp(connections *connect.Connections, batch bqbatch.Batcher) (*App, error) {
	// Create Socket.io service with database parameter
	socketioService, err := socketio.NewService(context.Background(), &connections.Postgres)
	if err != nil {
		logger.Errorf("Failed to create Socket.io service: %v", err)
		return nil, err
	}

	// Create Socket.io handler with new signature
	socketioHandler := socketio.NewHandler(socketioService.GetServer(), socketioService, &connections.Postgres)

	// Setup handlers for the service
	socketioHandler.SetupHandlers()

	return &App{
		muxRouter:       NewRouter(connections, batch),
		socketioService: socketioService,
		socketioHandler: socketioHandler,
		connections:     connections,
		batch:           batch,
	}, nil
}

// baseInfo contains static metadata for the /info endpoint.
var baseInfo = map[string]interface{}{
	"service":     "rushhour",
	"description": "Rush Hour Socket.io real-time communication service",
	"version":     "1.0.0",
	"endpoints": map[string]string{
		"socketio": "/socket.io/",
		"info":     "/info",
	},
}

// InfoHandler provides basic service information
func InfoHandler(w http.ResponseWriter, r *http.Request) {
	info := maps.Clone(baseInfo)
	info["timestamp"] = time.Now().Format(time.RFC3339)

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(info); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
	}
}

// Serve sets up all routes and returns the configured router
func (a *App) Serve() *mux.Router {
	router := a.muxRouter

	// Register service info endpoint
	router.HandleFunc("/info", InfoHandler).Methods(http.MethodGet)

	// Register Socket.io endpoint
	router.Handle("/socket.io/", a.socketioService.GetServer().ServeHandler(nil))

	// Root endpoint for basic service info
	router.HandleFunc("/", InfoHandler).Methods(http.MethodGet)

	return router
}
