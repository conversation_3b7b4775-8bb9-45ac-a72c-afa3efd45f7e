package locationgrouproleassignments

import (
	"time"

	"github.com/google/uuid"
)

type CreateAndUpdateLocationGroupRoleAssignmentRequest struct {
	RoleIDs []uuid.UUID `json:"roleIds" validate:"required,min=1"`
}

type LocationGroupRoleAssignmentResponse struct {
	LocationGroupRoleAssignment []LocationGroupRoleAssignment `json:"locationGroupRoleAssignment"`
}

type LocationGroupRoleAssignment struct {
	MembershipID    uuid.UUID `json:"membershipId" db:"membershipid"`
	LocationGroupID uuid.UUID `json:"locationGroupId" db:"locationgroupid"`
	RoleID          uuid.UUID `json:"roleId" db:"roleid"`
	CreatedAt       time.Time `json:"createdAt" db:"createdat"`
	UpdatedAt       time.Time `json:"updatedAt" db:"updatedat"`
}
