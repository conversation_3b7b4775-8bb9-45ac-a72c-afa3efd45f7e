package authmethods

import (
	"context"
	"net/http"
	"time"

	"github.com/google/uuid"
	"synapse-its.com/shared/api/authtypes"
	"synapse-its.com/shared/api/password"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/rest/onramp/helper"
)

// HandlerDeps is a struct that contains the dependencies for the auth methods handler
type HandlerDeps struct {
	GetAuthMethods          func(pg connect.DatabaseExecutor, userID uuid.UUID) ([]AuthMethodRecord, error)
	GetConnections          func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ValidateAuthMethod      func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error
	DeleteAuthMethod        func(pg connect.DatabaseExecutor, authMethodID uuid.UUID) error
	ValidateCurrentPassword func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, currentPasswordHash string) error
	UpdatePassword          func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, newPasswordHash string) error
	PasswordHasher          password.PasswordHasher
}

// Handles the GET /auth-methods request
func GetAuthMethodsHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate user ID from URL path
		userID, err := helper.ValidateUserID(r)
		if err != nil {
			logger.Errorf("Error validating user ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get auth methods for the user
		authMethods, err := deps.GetAuthMethods(pg, userID)
		if err != nil {
			logger.Errorf("Error getting auth methods: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Create response
		response.CreateSuccessResponse(authMethods, w)
	}
}

// DeleteAuthMethodHandlerWithDeps deletes an auth method for a user
func DeleteAuthMethodHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate user ID from URL path
		userID, err := helper.ValidateUserID(r)
		if err != nil {
			logger.Errorf("Error validating user ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate auth method ID from URL path
		authMethodID, err := helper.ValidateAuthMethodID(r)
		if err != nil {
			logger.Errorf("Error validating auth method ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate auth method exists and type is OIDC
		err = deps.ValidateAuthMethod(pg, authMethodID, userID, string(authtypes.AuthMethodTypeOIDC))
		if err != nil {
			logger.Errorf("Error getting auth method: %v", err)
			response.CreateForbiddenResponse(w)
			return
		}

		// Delete auth method
		err = deps.DeleteAuthMethod(pg, authMethodID)
		if err != nil {
			logger.Errorf("Error deleting auth method: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Create response
		response.CreateSuccessResponse(nil, w)
	}
}

// UpdateAuthMethodHandlerWithDeps updates an auth method for a user
func UpdatePasswordHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate user ID from URL path
		userID, err := helper.ValidateUserID(r)
		if err != nil {
			logger.Errorf("Error validating user ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate auth method ID from URL path
		authMethodID, err := helper.ValidateAuthMethodID(r)
		if err != nil {
			logger.Errorf("Error validating auth method ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate auth method exists and type is USERNAME_PASSWORD
		err = deps.ValidateAuthMethod(pg, authMethodID, userID, string(authtypes.AuthMethodTypeUsernamePassword))
		if err != nil {
			logger.Errorf("Error getting auth method: %v", err)
			response.CreateForbiddenResponse(w)
			return
		}

		// Parse and validate request body
		requestBody, err := password.ExtractAndValidatePasswordUpdate(r.Body)
		if err != nil {
			logger.Errorf("Error validating request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Hash the current password
		hashedCurrentPassword := deps.PasswordHasher.HashPassword(requestBody.CurrentPassword)

		// Validate current password matches the auth method
		err = deps.ValidateCurrentPassword(pg, authMethodID, hashedCurrentPassword)
		if err != nil {
			logger.Errorf("Error validating current password: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Hash the new password
		hashedNewPassword := deps.PasswordHasher.HashPassword(requestBody.NewPassword)
		err = deps.UpdatePassword(pg, authMethodID, hashedNewPassword)
		if err != nil {
			logger.Errorf("Error updating password: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Create response
		response.CreateSuccessResponse(nil, w)
	}
}

// getAuthMethods that returns the auth methods for a user
var getAuthMethods = func(pg connect.DatabaseExecutor, userID uuid.UUID) ([]AuthMethodRecord, error) {
	query := `
	SELECT
			am.Id as id,
			am.Type as type,
			COALESCE(am.UserName, '') as username,
			COALESCE(am.Email, '') as email,
			COALESCE(u.FirstName, '') as firstname,
			COALESCE(u.LastName, '') as lastname,
			COALESCE(
					array_agg(
							DISTINCT o.Name ORDER BY o.Name
					) FILTER (WHERE o.Name IS NOT NULL),
					'{}'::text[]
			) as organizations,
			am.IsEnabled as isenabled,
			COALESCE(am.LastLogin, '1970-01-01'::timestamp) as lastlogin
	FROM {{AuthMethod}} am
	INNER JOIN {{User}} u ON am.UserId = u.Id
	LEFT JOIN {{Memberships}} m ON am.Id = m.AuthMethodId AND m.IsDeleted = false
	LEFT JOIN {{Organization}} o ON m.OrganizationId = o.Id AND o.IsDeleted = false
	WHERE u.Id = $1 AND am.IsDeleted = false
	GROUP BY am.Id, am.Type, am.UserName, am.Email, u.FirstName, u.LastName, am.IsEnabled, am.LastLogin, am.UserId
	ORDER BY am.CreatedAt ASC`

	authMethods := &[]AuthMethodRecord{}
	err := pg.QueryGenericSlice(authMethods, query, userID)
	if err != nil {
		logger.Errorf("Error getting auth methods: %v", err)
		return nil, err
	}

	return *authMethods, nil
}

// validateAuthMethod that validates the auth method
var validateAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
	query := `
	SELECT
		Id,
		Type
	FROM {{AuthMethod}}
	WHERE Id = $1 AND UserId = $2 AND IsDeleted = false`

	authMethod := &ValidateAuthMethodRecord{}
	err := pg.QueryRowStruct(authMethod, query, authMethodID, userID)
	if err != nil {
		logger.Errorf("Error getting auth method: %v", err)
		return ErrInvalidAuthMethodID
	}

	if authMethod.AuthMethodType != authMethodType {
		return ErrInvalidAuthMethodType
	}

	return nil
}

// deleteAuthMethod that deletes the auth method
var deleteAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID) error {
	now := time.Now()
	// Soft delete the auth method
	query := `
	UPDATE {{AuthMethod}}
	SET IsDeleted = true,
	UpdatedAt = $2
	WHERE Id = $1`

	result, err := pg.Exec(query, authMethodID, now)
	if err != nil {
		logger.Errorf("Error deleting auth method: %v", err)
		return ErrDatabaseOperation
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Errorf("Error getting rows affected: %v", err)
		return ErrDatabaseOperation
	}

	if rowsAffected == 0 {
		return ErrAuthMethodNotFound
	}

	return nil
}

// validateCurrentPassword that validates the current password
var validateCurrentPassword = func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, currentPasswordHash string) error {
	query := `
	SELECT
		PasswordHash
	FROM {{AuthMethod}}
	WHERE Id = $1 AND Type =$2 AND IsDeleted = false`

	authMethod := &ValidateCurrentPasswordRecord{}
	err := pg.QueryRowStruct(authMethod, query, authMethodId, string(authtypes.AuthMethodTypeUsernamePassword))
	if err != nil {
		logger.Errorf("Error getting auth method: %v", err)
		return ErrInvalidAuthMethodID
	}

	if authMethod.PasswordHash != currentPasswordHash {
		return ErrInvalidCurrentPassword
	}

	return nil
}

// updatePassword that updates the password
var updatePassword = func(pg connect.DatabaseExecutor, authMethodId uuid.UUID, NewPasswordHash string) error {
	now := time.Now()

	query := `
	UPDATE {{AuthMethod}}
	SET PasswordHash = $2,
	UpdatedAt = $3
	WHERE Id = $1 AND Type = $4 AND IsDeleted = false`

	result, err := pg.Exec(query, authMethodId, NewPasswordHash, now, string(authtypes.AuthMethodTypeUsernamePassword))
	if err != nil {
		logger.Errorf("Error updating password: %v", err)
		return ErrDatabaseOperation
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Errorf("Error getting rows affected: %v", err)
		return ErrDatabaseOperation
	}

	if rowsAffected == 0 {
		return ErrAuthMethodNotFound
	}

	return nil
}

// Handler is the production-ready HTTP handler using default dependencies.
var (
	GetAuthMethodsHandler = GetAuthMethodsHandlerWithDeps(HandlerDeps{
		GetConnections: connect.GetConnections,
		GetAuthMethods: getAuthMethods,
	})

	DeleteAuthMethodHandler = DeleteAuthMethodHandlerWithDeps(HandlerDeps{
		GetConnections:     connect.GetConnections,
		ValidateAuthMethod: validateAuthMethod,
		DeleteAuthMethod:   deleteAuthMethod,
	})

	UpdatePasswordHandler = UpdatePasswordHandlerWithDeps(HandlerDeps{
		GetConnections:          connect.GetConnections,
		ValidateAuthMethod:      validateAuthMethod,
		ValidateCurrentPassword: validateCurrentPassword,
		UpdatePassword:          updatePassword,
		PasswordHasher:          password.NewPasswordHasher(),
	})
)
