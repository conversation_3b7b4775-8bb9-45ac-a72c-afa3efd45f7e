import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';

export interface Organization {
  id: string;
  name: string;
  description?: string;
  orgtypeidentifier?: string;
  createdat?: string;
  updatedat?: string;
}
@Injectable({
  providedIn: 'root'
})
export class OrganizationsService {
  constructor(private http: HttpClient) { }

  getOrganizations(): Observable<Organization[]> {
    return this.http.get<Organization[]>('/api/organizations');
  }

  getOrganizationsId(id: string): Observable<Organization> {
    return this.http.get<Organization>(`/api/organizations/${id}`).pipe(
      map((res: any) => res.data)
    );
  }

  getOrganizationsGroup(id: string): Observable<any[]> {
    return this.http.get<any[]>(`/api/organizations/${id}/permissions`);
  }

  createOrganization(data: { name: string; description: string; orgtypeidentifier: string }): Observable<any> {
    return this.http.post<any>('/api/organizations', data);
  }

  updateOrganization(id: string, payload: { name: string; description: string }): Observable<any> {
    return this.http.patch<any>(`/api/organizations/${id}`, payload);
  }

  deleteOrganization(id: string): Observable<any> {
    return this.http.delete<any>(`/api/organizations/${id}`);
  }
}