package locationgrouproleassignments

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
)

// Test helper constants
var (
	testMembershipID    = uuid.MustParse("987fcdeb-51a2-43d1-b654-************")
	testLocationGroupID = uuid.MustParse("876fcdeb-51a2-43d1-b654-************")
	testRoleID1         = uuid.MustParse("11111111-1111-1111-1111-111111111111")
	testRoleID2         = uuid.MustParse("*************-2222-2222-************")
)

// Test helper functions to eliminate DRY in setupRequest
func createTestRequest(method, path string, body interface{}, urlVars map[string]string) *http.Request {
	var req *http.Request

	if body != nil {
		var buf bytes.Buffer
		_ = json.NewEncoder(&buf).Encode(body)
		req, _ = http.NewRequest(method, path, &buf)
		req.Header.Set("Content-Type", "application/json")
	} else {
		req, _ = http.NewRequest(method, path, nil)
	}

	if urlVars != nil {
		req = mux.SetURLVars(req, urlVars)
	}

	return req
}

func createCreateRequest(organizationID, userID, locationGroupID string, roleIDs []uuid.UUID) *http.Request {
	body := CreateAndUpdateLocationGroupRoleAssignmentRequest{RoleIDs: roleIDs}
	urlVars := map[string]string{
		"organizationId":  organizationID,
		"userId":          userID,
		"locationgroupId": locationGroupID,
	}
	return createTestRequest(http.MethodPost, "/organizations/"+organizationID+"/users/"+userID+"/locationgroups/"+locationGroupID, body, urlVars)
}

func createGetRequest(organizationID, userID string) *http.Request {
	urlVars := map[string]string{
		"organizationId": organizationID,
		"userId":         userID,
	}
	return createTestRequest(http.MethodGet, "/organizations/"+organizationID+"/users/"+userID+"/locationgroups", nil, urlVars)
}

func createUpdateRequest(organizationID, userID, locationGroupID string, roleIDs []uuid.UUID) *http.Request {
	body := CreateAndUpdateLocationGroupRoleAssignmentRequest{RoleIDs: roleIDs}
	urlVars := map[string]string{
		"organizationId":  organizationID,
		"userId":          userID,
		"locationgroupId": locationGroupID,
	}
	return createTestRequest(http.MethodPut, "/organizations/"+organizationID+"/users/"+userID+"/locationgroups/"+locationGroupID, body, urlVars)
}

func createDeleteRequest(organizationID, userID, locationGroupID string) *http.Request {
	urlVars := map[string]string{
		"organizationId":  organizationID,
		"userId":          userID,
		"locationgroupId": locationGroupID,
	}
	return createTestRequest(http.MethodDelete, "/organizations/"+organizationID+"/users/"+userID+"/locationgroups/"+locationGroupID, nil, urlVars)
}

// fakeSQLResult is a minimal sql.Result implementation for testing
type fakeSQLResult struct {
	rowsAffected int64
	lastInsertID int64
}

func (f *fakeSQLResult) LastInsertId() (int64, error) {
	return f.lastInsertID, nil
}

func (f *fakeSQLResult) RowsAffected() (int64, error) {
	return f.rowsAffected, nil
}

// Test_parseCreateAndUpdateLocationGroupRoleAssignmentRequest tests the parseCreateAndUpdateLocationGroupRoleAssignmentRequest function
func Test_parseCreateAndUpdateLocationGroupRoleAssignmentRequest(t *testing.T) {
	t.Parallel()

	// Create test UUIDs
	testRoleID1 := uuid.New()
	testRoleID2 := uuid.New()

	tests := []struct {
		name        string
		requestBody interface{}
		expected    *CreateAndUpdateLocationGroupRoleAssignmentRequest
		expectedErr error
		wantErr     bool
	}{
		{
			name: "valid_request_with_multiple_roles",
			requestBody: CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{testRoleID1, testRoleID2},
			},
			expected: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{testRoleID1, testRoleID2},
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "nil_role_ids",
			requestBody: CreateAndUpdateLocationGroupRoleAssignmentRequest{RoleIDs: nil},
			expected:    &CreateAndUpdateLocationGroupRoleAssignmentRequest{RoleIDs: nil},
			expectedErr: ErrInvalidRoleIDs,
			wantErr:     true,
		},
		{
			name:        "invalid_json",
			requestBody: `{"roleIds": [invalid]}`,
			expected:    &CreateAndUpdateLocationGroupRoleAssignmentRequest{},
			expectedErr: ErrInvalidRoleIDs,
			wantErr:     true,
		},
		{
			name:        "unknown_fields",
			requestBody: `{"roleIds": ["` + testRoleID1.String() + `"], "unknown": "field"}`,
			expected:    &CreateAndUpdateLocationGroupRoleAssignmentRequest{},
			expectedErr: ErrUnexpectedFields,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create HTTP request with test body
			var body []byte
			var err error

			switch v := tt.requestBody.(type) {
			case string:
				body = []byte(v)
			default:
				body, err = json.Marshal(v)
				assert.NoError(t, err)
			}

			req := httptest.NewRequest(http.MethodPost, "/", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			// Execute the function under test
			result, err := parseCreateAndUpdateLocationGroupRoleAssignmentRequest(req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected.RoleIDs, result.RoleIDs)
			}
		})
	}
}

// Test_createLocationGroupRoleAssignment tests the createLocationGroupRoleAssignment function
func Test_createLocationGroupRoleAssignment(t *testing.T) {
	t.Parallel()

	// Create test UUIDs
	testMembershipID := uuid.New()
	testLocationGroupID := uuid.New()
	testRoleID1 := uuid.New()
	testRoleID2 := uuid.New()

	tests := []struct {
		name            string
		membershipID    uuid.UUID
		locationGroupID uuid.UUID
		req             *CreateAndUpdateLocationGroupRoleAssignmentRequest
		mockDB          func() connect.DatabaseExecutor
		expected        *LocationGroupRoleAssignmentResponse
		expectedErr     error
		wantErr         bool
	}{
		{
			name:            "successful_creation_multiple_roles",
			membershipID:    testMembershipID,
			locationGroupID: testLocationGroupID,
			req: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{testRoleID1, testRoleID2},
			},
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Mock successful query
						if response, ok := dest.(*[]LocationGroupRoleAssignment); ok {
							*response = []LocationGroupRoleAssignment{
								{
									MembershipID:    testMembershipID,
									LocationGroupID: testLocationGroupID,
									RoleID:          testRoleID1,
									CreatedAt:       time.Now().UTC(),
									UpdatedAt:       time.Now().UTC(),
								},
								{
									MembershipID:    testMembershipID,
									LocationGroupID: testLocationGroupID,
									RoleID:          testRoleID2,
									CreatedAt:       time.Now().UTC(),
									UpdatedAt:       time.Now().UTC(),
								},
							}
						}
						return nil
					},
				}
			},
			expected: &LocationGroupRoleAssignmentResponse{
				LocationGroupRoleAssignment: []LocationGroupRoleAssignment{
					{
						MembershipID:    testMembershipID,
						LocationGroupID: testLocationGroupID,
						RoleID:          testRoleID1,
						CreatedAt:       time.Now().UTC(),
						UpdatedAt:       time.Now().UTC(),
					},
					{
						MembershipID:    testMembershipID,
						LocationGroupID: testLocationGroupID,
						RoleID:          testRoleID2,
						CreatedAt:       time.Now().UTC(),
						UpdatedAt:       time.Now().UTC(),
					},
				},
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:            "nil_role_ids",
			membershipID:    testMembershipID,
			locationGroupID: testLocationGroupID,
			req: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: nil,
			},
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{}
			},
			expected:    nil,
			expectedErr: ErrInvalidRoleIDs,
			wantErr:     true,
		},
		{
			name:            "database_operation_error",
			membershipID:    testMembershipID,
			locationGroupID: testLocationGroupID,
			req: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{testRoleID1},
			},
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						return sql.ErrConnDone
					},
				}
			},
			expected:    nil,
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Set up the mock database
			mockDB := tt.mockDB()

			// Execute the function under test
			result, err := createLocationGroupRoleAssignment(mockDB, tt.membershipID, tt.locationGroupID, tt.req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, result.LocationGroupRoleAssignment, len(tt.req.RoleIDs))

				// Verify the mock was called
				if fakeDB, ok := mockDB.(*mocks.FakeDBExecutor); ok {
					assert.Equal(t, 1, fakeDB.QueryGenericSliceCallCount, "QueryGenericSlice should be called exactly once")
				}
			}
		})
	}
}

// Test_getLocationGroupRoleAssignmentsByMembershipID tests the getLocationGroupRoleAssignmentsByMembershipID function
func Test_getLocationGroupRoleAssignmentsByMembershipID(t *testing.T) {
	t.Parallel()

	// Create test UUIDs
	testMembershipID := uuid.New()
	testLocationGroupID1 := uuid.New()
	testLocationGroupID2 := uuid.New()
	testRoleID1 := uuid.New()
	testRoleID2 := uuid.New()

	tests := []struct {
		name         string
		membershipID uuid.UUID
		mockDB       func() connect.DatabaseExecutor
		expected     *LocationGroupRoleAssignmentResponse
		expectedErr  error
		wantErr      bool
	}{
		{
			name:         "successful_retrieval_multiple_assignments",
			membershipID: testMembershipID,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Mock successful query
						if response, ok := dest.(*[]LocationGroupRoleAssignment); ok {
							*response = []LocationGroupRoleAssignment{
								{
									MembershipID:    testMembershipID,
									LocationGroupID: testLocationGroupID1,
									RoleID:          testRoleID1,
									CreatedAt:       time.Now().UTC(),
									UpdatedAt:       time.Now().UTC(),
								},
								{
									MembershipID:    testMembershipID,
									LocationGroupID: testLocationGroupID2,
									RoleID:          testRoleID2,
									CreatedAt:       time.Now().UTC(),
									UpdatedAt:       time.Now().UTC(),
								},
							}
						}
						return nil
					},
				}
			},
			expected: &LocationGroupRoleAssignmentResponse{
				LocationGroupRoleAssignment: []LocationGroupRoleAssignment{
					{
						MembershipID:    testMembershipID,
						LocationGroupID: testLocationGroupID1,
						RoleID:          testRoleID1,
						CreatedAt:       time.Now().UTC(),
						UpdatedAt:       time.Now().UTC(),
					},
					{
						MembershipID:    testMembershipID,
						LocationGroupID: testLocationGroupID2,
						RoleID:          testRoleID2,
						CreatedAt:       time.Now().UTC(),
						UpdatedAt:       time.Now().UTC(),
					},
				},
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:         "no_assignments_found",
			membershipID: testMembershipID,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Mock successful query with no results
						if response, ok := dest.(*[]LocationGroupRoleAssignment); ok {
							*response = []LocationGroupRoleAssignment{}
						}
						return nil
					},
				}
			},
			expected: &LocationGroupRoleAssignmentResponse{
				LocationGroupRoleAssignment: []LocationGroupRoleAssignment{},
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:         "database_operation_error",
			membershipID: testMembershipID,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						return sql.ErrConnDone
					},
				}
			},
			expected:    nil,
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Set up the mock database
			mockDB := tt.mockDB()

			// Execute the function under test
			result, err := getLocationGroupRoleAssignmentsByMembershipID(mockDB, tt.membershipID)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)

				// Verify the mock was called
				if fakeDB, ok := mockDB.(*mocks.FakeDBExecutor); ok {
					assert.Equal(t, 1, fakeDB.QueryGenericSliceCallCount, "QueryGenericSlice should be called exactly once")
				}
			}
		})
	}
}

// Test_updateLocationGroupRoleAssignment tests the updateLocationGroupRoleAssignment function
func Test_updateLocationGroupRoleAssignment(t *testing.T) {
	t.Parallel()

	// Create test UUIDs
	testMembershipID := uuid.New()
	testLocationGroupID := uuid.New()
	testRoleID1 := uuid.New()
	testRoleID2 := uuid.New()

	tests := []struct {
		name            string
		membershipID    uuid.UUID
		locationGroupID uuid.UUID
		req             *CreateAndUpdateLocationGroupRoleAssignmentRequest
		mockDB          func() connect.DatabaseExecutor
		expected        *LocationGroupRoleAssignmentResponse
		expectedErr     error
		wantErr         bool
	}{
		{
			name:            "successful_update_multiple_roles",
			membershipID:    testMembershipID,
			locationGroupID: testLocationGroupID,
			req: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{testRoleID1, testRoleID2},
			},
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Mock successful query
						if response, ok := dest.(*[]LocationGroupRoleAssignment); ok {
							*response = []LocationGroupRoleAssignment{
								{
									MembershipID:    testMembershipID,
									LocationGroupID: testLocationGroupID,
									RoleID:          testRoleID1,
									CreatedAt:       time.Now().UTC(),
									UpdatedAt:       time.Now().UTC(),
								},
								{
									MembershipID:    testMembershipID,
									LocationGroupID: testLocationGroupID,
									RoleID:          testRoleID2,
									CreatedAt:       time.Now().UTC(),
									UpdatedAt:       time.Now().UTC(),
								},
							}
						}
						return nil
					},
				}
			},
			expected: &LocationGroupRoleAssignmentResponse{
				LocationGroupRoleAssignment: []LocationGroupRoleAssignment{
					{
						MembershipID:    testMembershipID,
						LocationGroupID: testLocationGroupID,
						RoleID:          testRoleID1,
						CreatedAt:       time.Now().UTC(),
						UpdatedAt:       time.Now().UTC(),
					},
					{
						MembershipID:    testMembershipID,
						LocationGroupID: testLocationGroupID,
						RoleID:          testRoleID2,
						CreatedAt:       time.Now().UTC(),
						UpdatedAt:       time.Now().UTC(),
					},
				},
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:            "nil_role_ids",
			membershipID:    testMembershipID,
			locationGroupID: testLocationGroupID,
			req: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: nil,
			},
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{}
			},
			expected:    nil,
			expectedErr: ErrInvalidRoleIDs,
			wantErr:     true,
		},
		{
			name:            "database_operation_error",
			membershipID:    testMembershipID,
			locationGroupID: testLocationGroupID,
			req: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{testRoleID1},
			},
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						return sql.ErrConnDone
					},
				}
			},
			expected:    nil,
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Set up the mock database
			mockDB := tt.mockDB()

			// Execute the function under test
			result, err := updateLocationGroupRoleAssignment(mockDB, tt.membershipID, tt.locationGroupID, tt.req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, result.LocationGroupRoleAssignment, len(tt.req.RoleIDs))

				// Verify the mock was called
				if fakeDB, ok := mockDB.(*mocks.FakeDBExecutor); ok {
					assert.Equal(t, 1, fakeDB.QueryGenericSliceCallCount, "QueryGenericSlice should be called exactly once")
				}
			}
		})
	}
}

// Test_deleteLocationGroupRoleAssignment tests the deleteLocationGroupRoleAssignment function
func Test_deleteLocationGroupRoleAssignment(t *testing.T) {
	t.Parallel()

	// Create test UUIDs
	testMembershipID := uuid.New()
	testLocationGroupID := uuid.New()

	tests := []struct {
		name            string
		membershipID    uuid.UUID
		locationGroupID uuid.UUID
		mockDB          func() connect.DatabaseExecutor
		expectedErr     error
		wantErr         bool
	}{
		{
			name:            "successful_deletion",
			membershipID:    testMembershipID,
			locationGroupID: testLocationGroupID,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						// Mock successful execution
						return &fakeSQLResult{rowsAffected: 1}, nil
					},
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:            "database_operation_error",
			membershipID:    testMembershipID,
			locationGroupID: testLocationGroupID,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return nil, sql.ErrConnDone
					},
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:            "no_rows_affected",
			membershipID:    testMembershipID,
			locationGroupID: testLocationGroupID,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						// Mock successful execution but no rows affected
						return &fakeSQLResult{rowsAffected: 0}, nil
					},
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Set up the mock database
			mockDB := tt.mockDB()

			// Execute the function under test
			err := deleteLocationGroupRoleAssignment(mockDB, tt.membershipID, tt.locationGroupID)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
			} else {
				assert.NoError(t, err)

				// Verify the mock was called
				if fakeDB, ok := mockDB.(*mocks.FakeDBExecutor); ok {
					assert.Equal(t, 1, fakeDB.ExecCallCount, "Exec should be called exactly once")
				}
			}
		})
	}
}

// Test_CreateLocationGroupRoleAssignmentWithDeps tests the CreateLocationGroupRoleAssignmentWithDeps function
func Test_CreateLocationGroupRoleAssignmentWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name                                  string
		requestBody                           *CreateAndUpdateLocationGroupRoleAssignmentRequest
		mockGetConnections                    func(context.Context, ...bool) (*connect.Connections, error)
		mockCreateLocationGroupRoleAssignment func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, *CreateAndUpdateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error)
		mockGetMembershipByUserID             func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) (uuid.UUID, error)
		expectedStatusCode                    int
		expectedResponseBody                  string
		setupRequest                          func() *http.Request
	}{
		{
			name: "successful_role_assignment_creation",
			requestBody: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{
					uuid.MustParse("11111111-1111-1111-1111-111111111111"),
					uuid.MustParse("*************-2222-2222-************"),
				},
			},
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":{"locationGroupRoleAssignment":[{"membershipId":"987fcdeb-51a2-43d1-b654-************","locationGroupId":"876fcdeb-51a2-43d1-b654-************","roleId":"11111111-1111-1111-1111-111111111111","createdAt":"2023-01-01T12:00:00Z","updatedAt":"2023-01-02T12:00:00Z"},{"membershipId":"987fcdeb-51a2-43d1-b654-************","locationGroupId":"876fcdeb-51a2-43d1-b654-************","roleId":"*************-2222-2222-************","createdAt":"2023-01-01T12:00:00Z","updatedAt":"2023-01-02T12:00:00Z"}]},"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			mockCreateLocationGroupRoleAssignment: func(db connect.DatabaseExecutor, membershipID uuid.UUID, locationGroupID uuid.UUID, req *CreateAndUpdateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error) {
				return &LocationGroupRoleAssignmentResponse{
					LocationGroupRoleAssignment: []LocationGroupRoleAssignment{
						{
							MembershipID:    membershipID,
							LocationGroupID: locationGroupID,
							RoleID:          req.RoleIDs[0],
							CreatedAt:       time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
							UpdatedAt:       time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC),
						},
						{
							MembershipID:    membershipID,
							LocationGroupID: locationGroupID,
							RoleID:          req.RoleIDs[1],
							CreatedAt:       time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
							UpdatedAt:       time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC),
						},
					},
				}, nil
			},
			setupRequest: func() *http.Request {
				return createCreateRequest(
					"123e4567-e89b-12d3-a456-************",
					"987fcdeb-51a2-43d1-b654-************",
					"876fcdeb-51a2-43d1-b654-************",
					[]uuid.UUID{testRoleID1, testRoleID2},
				)
			},
		},
		{
			name: "get_connections_error",
			requestBody: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{testRoleID1},
			},
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
			mockCreateLocationGroupRoleAssignment: func(db connect.DatabaseExecutor, membershipID uuid.UUID, locationGroupID uuid.UUID, req *CreateAndUpdateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error) {
				return nil, nil
			},
			setupRequest: func() *http.Request {
				return createCreateRequest(
					"123e4567-e89b-12d3-a456-************",
					"987fcdeb-51a2-43d1-b654-************",
					"876fcdeb-51a2-43d1-b654-************",
					[]uuid.UUID{testRoleID1},
				)
			},
		},
		{
			name: "invalid_organization_id",
			requestBody: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{testRoleID1},
			},
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			mockCreateLocationGroupRoleAssignment: func(db connect.DatabaseExecutor, membershipID uuid.UUID, locationGroupID uuid.UUID, req *CreateAndUpdateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error) {
				return nil, nil
			},
			setupRequest: func() *http.Request {
				return createCreateRequest(
					"invalid-uuid",
					"987fcdeb-51a2-43d1-b654-************",
					"876fcdeb-51a2-43d1-b654-************",
					[]uuid.UUID{testRoleID1},
				)
			},
		},
		{
			name: "invalid_user_id",
			requestBody: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{testRoleID1},
			},
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			mockCreateLocationGroupRoleAssignment: func(db connect.DatabaseExecutor, membershipID uuid.UUID, locationGroupID uuid.UUID, req *CreateAndUpdateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error) {
				return nil, nil
			},
			setupRequest: func() *http.Request {
				return createCreateRequest(
					"123e4567-e89b-12d3-a456-************",
					"invalid-uuid",
					"876fcdeb-51a2-43d1-b654-************",
					[]uuid.UUID{testRoleID1},
				)
			},
		},
		{
			name: "invalid_location_group_id",
			requestBody: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{testRoleID1},
			},
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			mockCreateLocationGroupRoleAssignment: func(db connect.DatabaseExecutor, membershipID uuid.UUID, locationGroupID uuid.UUID, req *CreateAndUpdateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error) {
				return nil, nil
			},
			setupRequest: func() *http.Request {
				return createCreateRequest(
					"123e4567-e89b-12d3-a456-************",
					"987fcdeb-51a2-43d1-b654-************",
					"invalid-uuid",
					[]uuid.UUID{testRoleID1},
				)
			},
		},
		{
			name: "error_getting_membership",
			requestBody: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{testRoleID1},
			},
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			mockCreateLocationGroupRoleAssignment: func(db connect.DatabaseExecutor, membershipID uuid.UUID, locationGroupID uuid.UUID, req *CreateAndUpdateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error) {
				return nil, nil
			},
			mockGetMembershipByUserID: func(db connect.DatabaseExecutor, userID uuid.UUID, organizationID uuid.UUID) (uuid.UUID, error) {
				return uuid.Nil, errors.New("error getting membership")
			},
			setupRequest: func() *http.Request {
				return createCreateRequest(
					"123e4567-e89b-12d3-a456-************",
					"987fcdeb-51a2-43d1-b654-************",
					"876fcdeb-51a2-43d1-b654-************",
					[]uuid.UUID{testRoleID1},
				)
			},
		},
		{
			name: "create_assignment_error",
			requestBody: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{testRoleID1},
			},
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			mockCreateLocationGroupRoleAssignment: func(db connect.DatabaseExecutor, membershipID uuid.UUID, locationGroupID uuid.UUID, req *CreateAndUpdateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error) {
				return nil, errors.New("database constraint violation")
			},
			setupRequest: func() *http.Request {
				return createCreateRequest(
					"123e4567-e89b-12d3-a456-************",
					"987fcdeb-51a2-43d1-b654-************",
					"876fcdeb-51a2-43d1-b654-************",
					[]uuid.UUID{testRoleID1},
				)
			},
		},
		{
			name:                 "invalid_request_body",
			requestBody:          nil, // This will cause a parsing error
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			mockCreateLocationGroupRoleAssignment: func(db connect.DatabaseExecutor, membershipID uuid.UUID, locationGroupID uuid.UUID, req *CreateAndUpdateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error) {
				return nil, nil
			},
			setupRequest: func() *http.Request {
				// Create a request with invalid JSON body
				req := httptest.NewRequest(http.MethodPost, "/organizations/123e4567-e89b-12d3-a456-************/users/987fcdeb-51a2-43d1-b654-************/locationgroups/876fcdeb-51a2-43d1-b654-************", bytes.NewBufferString(`{"invalid": json`))
				req.Header.Set("Content-Type", "application/json")
				req = mux.SetURLVars(req, map[string]string{
					"organizationId":  "123e4567-e89b-12d3-a456-************",
					"userId":          "987fcdeb-51a2-43d1-b654-************",
					"locationgroupId": "876fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with mocked dependencies
			handler := CreateLocationGroupRoleAssignmentWithDeps(HandlerDeps{
				GetConnections:                    tt.mockGetConnections,
				CreateLocationGroupRoleAssignment: tt.mockCreateLocationGroupRoleAssignment,
				GetLocationGroupRoleAssignmentsByMembershipID: func(db connect.DatabaseExecutor, membershipID uuid.UUID) (*LocationGroupRoleAssignmentResponse, error) {
					return nil, nil
				},
				UpdateLocationGroupRoleAssignment: func(db connect.DatabaseExecutor, membershipID uuid.UUID, locationGroupID uuid.UUID, req *CreateAndUpdateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error) {
					return nil, nil
				},
				DeleteLocationGroupRoleAssignment: func(db connect.DatabaseExecutor, membershipID uuid.UUID, locationGroupID uuid.UUID) error {
					return nil
				},
				GetMembershipByUserID: func(db connect.DatabaseExecutor, userID uuid.UUID, organizationID uuid.UUID) (uuid.UUID, error) {
					// Use custom mock if provided, otherwise use default
					if tt.mockGetMembershipByUserID != nil {
						return tt.mockGetMembershipByUserID(db, userID, organizationID)
					}
					return uuid.MustParse("987fcdeb-51a2-43d1-b654-************"), nil
				},
			})

			// Create request
			req := tt.setupRequest()

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert response status code
			assert.Equal(t, tt.expectedStatusCode, rr.Code,
				"Expected status code %d, got %d", tt.expectedStatusCode, rr.Code)

			// Assert response body
			assert.JSONEq(t, tt.expectedResponseBody, rr.Body.String(),
				"Expected response body %s, got %s", tt.expectedResponseBody, rr.Body.String())
		})
	}
}

// Test_GetLocationGroupRoleAssignmentsByMembershipIDWithDeps tests the GetLocationGroupRoleAssignmentsByMembershipIDWithDeps function
func Test_GetLocationGroupRoleAssignmentsByMembershipIDWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name                                              string
		mockGetConnections                                func(context.Context, ...bool) (*connect.Connections, error)
		mockGetLocationGroupRoleAssignmentsByMembershipID func(connect.DatabaseExecutor, uuid.UUID) (*LocationGroupRoleAssignmentResponse, error)
		mockGetMembershipByUserID                         func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) (uuid.UUID, error)
		expectedStatusCode                                int
		expectedResponseBody                              string
		setupRequest                                      func() *http.Request
	}{
		{
			name:                 "successful_role_assignments_retrieval",
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":{"locationGroupRoleAssignment":[{"membershipId":"987fcdeb-51a2-43d1-b654-************","locationGroupId":"876fcdeb-51a2-43d1-b654-************","roleId":"11111111-1111-1111-1111-111111111111","createdAt":"2023-01-01T12:00:00Z","updatedAt":"2023-01-02T12:00:00Z"}]},"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			mockGetLocationGroupRoleAssignmentsByMembershipID: func(db connect.DatabaseExecutor, membershipID uuid.UUID) (*LocationGroupRoleAssignmentResponse, error) {
				return &LocationGroupRoleAssignmentResponse{
					LocationGroupRoleAssignment: []LocationGroupRoleAssignment{
						{
							MembershipID:    membershipID,
							LocationGroupID: testLocationGroupID,
							RoleID:          testRoleID1,
							CreatedAt:       time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
							UpdatedAt:       time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC),
						},
					},
				}, nil
			},
			setupRequest: func() *http.Request {
				return createGetRequest("123e4567-e89b-12d3-a456-************", "987fcdeb-51a2-43d1-b654-************")
			},
		},
		{
			name:                 "get_connections_error",
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
			mockGetLocationGroupRoleAssignmentsByMembershipID: func(db connect.DatabaseExecutor, membershipID uuid.UUID) (*LocationGroupRoleAssignmentResponse, error) {
				return nil, nil
			},
			setupRequest: func() *http.Request {
				return createGetRequest("123e4567-e89b-12d3-a456-************", "987fcdeb-51a2-43d1-b654-************")
			},
		},
		{
			name:                 "invalid_organization_id",
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			mockGetLocationGroupRoleAssignmentsByMembershipID: func(db connect.DatabaseExecutor, membershipID uuid.UUID) (*LocationGroupRoleAssignmentResponse, error) {
				return nil, nil
			},
			setupRequest: func() *http.Request {
				return createGetRequest("invalid-uuid", "987fcdeb-51a2-43d1-b654-************")
			},
		},
		{
			name:                 "invalid_user_id",
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			mockGetLocationGroupRoleAssignmentsByMembershipID: func(db connect.DatabaseExecutor, membershipID uuid.UUID) (*LocationGroupRoleAssignmentResponse, error) {
				return nil, nil
			},
			mockGetMembershipByUserID: func(db connect.DatabaseExecutor, userID uuid.UUID, organizationID uuid.UUID) (uuid.UUID, error) {
				return uuid.Nil, nil
			},
			setupRequest: func() *http.Request {
				return createGetRequest("123e4567-e89b-12d3-a456-************", "invalid-uuid")
			},
		},
		{
			name:                 "error_getting_membership",
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			mockGetLocationGroupRoleAssignmentsByMembershipID: func(db connect.DatabaseExecutor, membershipID uuid.UUID) (*LocationGroupRoleAssignmentResponse, error) {
				return nil, nil
			},
			mockGetMembershipByUserID: func(db connect.DatabaseExecutor, userID uuid.UUID, organizationID uuid.UUID) (uuid.UUID, error) {
				return uuid.Nil, errors.New("error getting membership")
			},
			setupRequest: func() *http.Request {
				return createGetRequest("123e4567-e89b-12d3-a456-************", "987fcdeb-51a2-43d1-b654-************")
			},
		},
		{
			name:                 "error_getting_location_group_role_assignments",
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			mockGetLocationGroupRoleAssignmentsByMembershipID: func(db connect.DatabaseExecutor, membershipID uuid.UUID) (*LocationGroupRoleAssignmentResponse, error) {
				return nil, errors.New("error getting location group role assignments")
			},
			setupRequest: func() *http.Request {
				return createGetRequest("123e4567-e89b-12d3-a456-************", "987fcdeb-51a2-43d1-b654-************")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with mocked dependencies
			handler := GetLocationGroupRoleAssignmentsByMembershipIDWithDeps(HandlerDeps{
				GetConnections: tt.mockGetConnections,
				GetLocationGroupRoleAssignmentsByMembershipID: tt.mockGetLocationGroupRoleAssignmentsByMembershipID,
				CreateLocationGroupRoleAssignment: func(db connect.DatabaseExecutor, membershipID uuid.UUID, locationGroupID uuid.UUID, req *CreateAndUpdateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error) {
					return nil, nil
				},
				UpdateLocationGroupRoleAssignment: func(db connect.DatabaseExecutor, membershipID uuid.UUID, locationGroupID uuid.UUID, req *CreateAndUpdateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error) {
					return nil, nil
				},
				DeleteLocationGroupRoleAssignment: func(db connect.DatabaseExecutor, membershipID uuid.UUID, locationGroupID uuid.UUID) error {
					return nil
				},
				GetMembershipByUserID: func(db connect.DatabaseExecutor, userID uuid.UUID, organizationID uuid.UUID) (uuid.UUID, error) {
					// Use custom mock if provided, otherwise use default
					if tt.mockGetMembershipByUserID != nil {
						return tt.mockGetMembershipByUserID(db, userID, organizationID)
					}
					return uuid.MustParse("987fcdeb-51a2-43d1-b654-************"), nil
				},
			})

			// Create request
			req := tt.setupRequest()

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert response status code
			assert.Equal(t, tt.expectedStatusCode, rr.Code,
				"Expected status code %d, got %d", tt.expectedStatusCode, rr.Code)

			// Assert response body
			assert.JSONEq(t, tt.expectedResponseBody, rr.Body.String(),
				"Expected response body %s, got %s", tt.expectedResponseBody, rr.Body.String())
		})
	}
}

// Test_UpdateLocationGroupRoleAssignmentWithDeps tests the UpdateLocationGroupRoleAssignmentWithDeps function
func Test_UpdateLocationGroupRoleAssignmentWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name                                  string
		requestBody                           *CreateAndUpdateLocationGroupRoleAssignmentRequest
		mockGetConnections                    func(context.Context, ...bool) (*connect.Connections, error)
		mockUpdateLocationGroupRoleAssignment func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, *CreateAndUpdateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error)
		mockGetMembershipByUserID             func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) (uuid.UUID, error)
		expectedStatusCode                    int
		expectedResponseBody                  string
		setupRequest                          func() *http.Request
	}{
		{
			name: "successful_role_assignment_update",
			requestBody: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{
					uuid.MustParse("11111111-1111-1111-1111-111111111111"),
					uuid.MustParse("*************-2222-2222-************"),
				},
			},
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":{"locationGroupRoleAssignment":[{"membershipId":"987fcdeb-51a2-43d1-b654-************","locationGroupId":"876fcdeb-51a2-43d1-b654-************","roleId":"11111111-1111-1111-1111-111111111111","createdAt":"2023-01-01T12:00:00Z","updatedAt":"2023-01-02T12:00:00Z"},{"membershipId":"987fcdeb-51a2-43d1-b654-************","locationGroupId":"876fcdeb-51a2-43d1-b654-************","roleId":"*************-2222-2222-************","createdAt":"2023-01-01T12:00:00Z","updatedAt":"2023-01-02T12:00:00Z"}]},"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			mockGetMembershipByUserID: func(db connect.DatabaseExecutor, userID uuid.UUID, organizationID uuid.UUID) (uuid.UUID, error) {
				return uuid.MustParse("987fcdeb-51a2-43d1-b654-************"), nil
			},
			mockUpdateLocationGroupRoleAssignment: func(db connect.DatabaseExecutor, membershipID uuid.UUID, locationGroupID uuid.UUID, req *CreateAndUpdateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error) {
				return &LocationGroupRoleAssignmentResponse{
					LocationGroupRoleAssignment: []LocationGroupRoleAssignment{
						{
							MembershipID:    membershipID,
							LocationGroupID: locationGroupID,
							RoleID:          req.RoleIDs[0],
							CreatedAt:       time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
							UpdatedAt:       time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC),
						},
						{
							MembershipID:    membershipID,
							LocationGroupID: locationGroupID,
							RoleID:          req.RoleIDs[1],
							CreatedAt:       time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
							UpdatedAt:       time.Date(2023, 1, 2, 12, 0, 0, 0, time.UTC),
						},
					},
				}, nil
			},
			setupRequest: func() *http.Request {
				return createUpdateRequest(
					"123e4567-e89b-12d3-a456-************",
					"987fcdeb-51a2-43d1-b654-************",
					"876fcdeb-51a2-43d1-b654-************",
					[]uuid.UUID{testRoleID1, testRoleID2},
				)
			},
		},
		{
			name: "get_connections_error",
			requestBody: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{testRoleID1},
			},
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
			setupRequest: func() *http.Request {
				return createUpdateRequest(
					"123e4567-e89b-12d3-a456-************",
					"987fcdeb-51a2-43d1-b654-************",
					"876fcdeb-51a2-43d1-b654-************",
					[]uuid.UUID{testRoleID1},
				)
			},
		},
		{
			name: "invalid_organization_id",
			requestBody: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{testRoleID1},
			},
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			setupRequest: func() *http.Request {
				return createUpdateRequest(
					"invalid-uuid",
					"987fcdeb-51a2-43d1-b654-************",
					"876fcdeb-51a2-43d1-b654-************",
					[]uuid.UUID{testRoleID1},
				)
			},
		},
		{
			name: "invalid_location_group_id",
			requestBody: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{testRoleID1},
			},
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			setupRequest: func() *http.Request {
				return createUpdateRequest(
					"123e4567-e89b-12d3-a456-************",
					"987fcdeb-51a2-43d1-b654-************",
					"invalid-uuid",
					[]uuid.UUID{testRoleID1},
				)
			},
		},
		{
			name: "invalid_user_id",
			requestBody: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{testRoleID1},
			},
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			setupRequest: func() *http.Request {
				return createUpdateRequest(
					"123e4567-e89b-12d3-a456-************",
					"invalid-uuid",
					"876fcdeb-51a2-43d1-b654-************",
					[]uuid.UUID{testRoleID1},
				)
			},
		},
		{
			name: "error_getting_membership",
			requestBody: &CreateAndUpdateLocationGroupRoleAssignmentRequest{
				RoleIDs: []uuid.UUID{testRoleID1},
			},
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			mockGetMembershipByUserID: func(db connect.DatabaseExecutor, userID uuid.UUID, organizationID uuid.UUID) (uuid.UUID, error) {
				return uuid.Nil, errors.New("error getting membership")
			},
			setupRequest: func() *http.Request {
				return createUpdateRequest(
					"123e4567-e89b-12d3-a456-************",
					"987fcdeb-51a2-43d1-b654-************",
					"876fcdeb-51a2-43d1-b654-************",
					[]uuid.UUID{testRoleID1},
				)
			},
		},
		{
			name:                 "invalid_request_body",
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			setupRequest: func() *http.Request {
				// Create a request with invalid JSON body
				req := httptest.NewRequest(http.MethodPatch, "/organizations/123e4567-e89b-12d3-a456-************/users/987fcdeb-51a2-43d1-b654-************/locationgroups/876fcdeb-51a2-43d1-b654-************", bytes.NewBufferString(`{"invalid": "json"}`))
				req.Header.Set("Content-Type", "application/json")
				req = mux.SetURLVars(req, map[string]string{
					"organizationId":  "123e4567-e89b-12d3-a456-************",
					"userId":          "987fcdeb-51a2-43d1-b654-************",
					"locationgroupId": "876fcdeb-51a2-43d1-b654-************",
				})
				return req
			},
		},
		{
			name:                 "error_updating_location_group_role_assignment",
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			mockUpdateLocationGroupRoleAssignment: func(db connect.DatabaseExecutor, membershipID uuid.UUID, locationGroupID uuid.UUID, req *CreateAndUpdateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error) {
				return nil, errors.New("error updating location group role assignment")
			},
			setupRequest: func() *http.Request {
				return createUpdateRequest(
					"123e4567-e89b-12d3-a456-************",
					"987fcdeb-51a2-43d1-b654-************",
					"876fcdeb-51a2-43d1-b654-************",
					[]uuid.UUID{testRoleID1},
				)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with mocked dependencies
			handler := UpdateLocationGroupRoleAssignmentWithDeps(HandlerDeps{
				GetConnections:                    tt.mockGetConnections,
				UpdateLocationGroupRoleAssignment: tt.mockUpdateLocationGroupRoleAssignment,
				GetMembershipByUserID: func(db connect.DatabaseExecutor, userID uuid.UUID, organizationID uuid.UUID) (uuid.UUID, error) {
					if tt.mockGetMembershipByUserID != nil {
						return tt.mockGetMembershipByUserID(db, userID, organizationID)
					}
					return uuid.MustParse("987fcdeb-51a2-43d1-b654-************"), nil
				},
			})

			// Create request
			req := tt.setupRequest()

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert response status code
			assert.Equal(t, tt.expectedStatusCode, rr.Code,
				"Expected status code %d, got %d", tt.expectedStatusCode, rr.Code)

			// Assert response body
			assert.JSONEq(t, tt.expectedResponseBody, rr.Body.String(),
				"Expected response body %s, got %s", tt.expectedResponseBody, rr.Body.String())
		})
	}
}

// Test_DeleteLocationGroupRoleAssignmentWithDeps tests the DeleteLocationGroupRoleAssignmentWithDeps function
func Test_DeleteLocationGroupRoleAssignmentWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name                                  string
		mockGetConnections                    func(context.Context, ...bool) (*connect.Connections, error)
		mockDeleteLocationGroupRoleAssignment func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
		mockGetMembershipByUserID             func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) (uuid.UUID, error)
		expectedStatusCode                    int
		expectedResponseBody                  string
		setupRequest                          func() *http.Request
	}{
		{
			name:                 "successful_role_assignment_deletion",
			expectedStatusCode:   200,
			expectedResponseBody: `{"status":"success","data":null,"message":"Request Succeeded","code":200}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			mockDeleteLocationGroupRoleAssignment: func(db connect.DatabaseExecutor, membershipID uuid.UUID, locationGroupID uuid.UUID) error {
				return nil
			},
			setupRequest: func() *http.Request {
				return createDeleteRequest(
					"123e4567-e89b-12d3-a456-************",
					"987fcdeb-51a2-43d1-b654-************",
					"876fcdeb-51a2-43d1-b654-************",
				)
			},
		},
		{
			name:                 "get_connections_error",
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
			setupRequest: func() *http.Request {
				return createDeleteRequest(
					"123e4567-e89b-12d3-a456-************",
					"987fcdeb-51a2-43d1-b654-************",
					"876fcdeb-51a2-43d1-b654-************",
				)
			},
		},
		{
			name:                 "invalid_organization_id",
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			setupRequest: func() *http.Request {
				return createDeleteRequest(
					"invalid-uuid",
					"987fcdeb-51a2-43d1-b654-************",
					"876fcdeb-51a2-43d1-b654-************",
				)
			},
		},
		{
			name:                 "invalid_user_id",
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			setupRequest: func() *http.Request {
				return createDeleteRequest(
					"123e4567-e89b-12d3-a456-************",
					"invalid-uuid",
					"876fcdeb-51a2-43d1-b654-************",
				)
			},
		},
		{
			name:                 "invalid_location_group_id",
			expectedStatusCode:   400,
			expectedResponseBody: `{"status":"error","data":null,"message":"Bad Request","code":400}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			setupRequest: func() *http.Request {
				return createDeleteRequest(
					"123e4567-e89b-12d3-a456-************",
					"987fcdeb-51a2-43d1-b654-************",
					"invalid-uuid",
				)
			},
		},
		{
			name:                 "error_getting_membership",
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			mockGetMembershipByUserID: func(db connect.DatabaseExecutor, userID uuid.UUID, organizationID uuid.UUID) (uuid.UUID, error) {
				return uuid.Nil, errors.New("error getting membership")
			},
			setupRequest: func() *http.Request {
				return createDeleteRequest(
					"123e4567-e89b-12d3-a456-************",
					"987fcdeb-51a2-43d1-b654-************",
					"876fcdeb-51a2-43d1-b654-************",
				)
			},
		},
		{
			name:                 "error_deleting_location_group_role_assignment",
			expectedStatusCode:   500,
			expectedResponseBody: `{"status":"error","data":null,"message":"Internal Server Error","code":500}`,
			mockGetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &mocks.FakeDBExecutor{},
				}, nil
			},
			mockDeleteLocationGroupRoleAssignment: func(db connect.DatabaseExecutor, membershipID uuid.UUID, locationGroupID uuid.UUID) error {
				return errors.New("error deleting location group role assignment")
			},
			setupRequest: func() *http.Request {
				return createDeleteRequest(
					"123e4567-e89b-12d3-a456-************",
					"987fcdeb-51a2-43d1-b654-************",
					"876fcdeb-51a2-43d1-b654-************",
				)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with mocked dependencies
			handler := DeleteLocationGroupRoleAssignmentWithDeps(HandlerDeps{
				GetConnections:                    tt.mockGetConnections,
				DeleteLocationGroupRoleAssignment: tt.mockDeleteLocationGroupRoleAssignment,
				GetMembershipByUserID: func(db connect.DatabaseExecutor, userID uuid.UUID, organizationID uuid.UUID) (uuid.UUID, error) {
					if tt.mockGetMembershipByUserID != nil {
						return tt.mockGetMembershipByUserID(db, userID, organizationID)
					}
					return uuid.MustParse("987fcdeb-51a2-43d1-b654-************"), nil
				},
			})

			// Create request
			req := tt.setupRequest()

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert response status code
			assert.Equal(t, tt.expectedStatusCode, rr.Code,
				"Expected status code %d, got %d", tt.expectedStatusCode, rr.Code)

			// Assert response body
			assert.JSONEq(t, tt.expectedResponseBody, rr.Body.String(),
				"Expected response body %s, got %s", tt.expectedResponseBody, rr.Body.String())
		})
	}
}
