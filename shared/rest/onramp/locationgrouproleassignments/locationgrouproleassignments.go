package locationgrouproleassignments

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/rest/onramp/helper"
)

type HandlerDeps struct {
	GetConnections                                func(context.Context, ...bool) (*connect.Connections, error)
	CreateLocationGroupRoleAssignment             func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, *CreateAndUpdateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error)
	GetLocationGroupRoleAssignmentsByMembershipID func(connect.DatabaseExecutor, uuid.UUID) (*LocationGroupRoleAssignmentResponse, error)
	UpdateLocationGroupRoleAssignment             func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, *CreateAndUpdateLocationGroupRoleAssignmentRequest) (*LocationGroupRoleAssignmentResponse, error)
	DeleteLocationGroupRoleAssignment             func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
	GetMembershipByUserID                         func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) (uuid.UUID, error)
}

// Constants
const (
	organizationIDParam  = "organizationId"
	locationGroupIDParam = "locationgroupId"
	userIDParam          = "userId"
)

// CreateLocationGroupRoleAssignmentWithDeps creates a location group role assignment
func CreateLocationGroupRoleAssignmentWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Parse request URL
		organizationID, err := helper.ParseUUIDFromRequest(r, organizationIDParam)
		if err != nil {
			logger.Errorf("Error parsing orgID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		userID, err := helper.ParseUUIDFromRequest(r, userIDParam)
		if err != nil {
			logger.Errorf("Error parsing userID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		locationGroupID, err := helper.ParseUUIDFromRequest(r, locationGroupIDParam)
		if err != nil {
			logger.Errorf("Error parsing locationGroupID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Check membership exists
		membershipID, err := deps.GetMembershipByUserID(pg, userID, organizationID)
		if err != nil {
			logger.Errorf("Error getting membership: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Parse request body
		req, err := parseCreateAndUpdateLocationGroupRoleAssignmentRequest(r)
		if err != nil {
			logger.Errorf("Error parsing request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Create the location group role assignment
		locationGroupRoleAssignment, err := deps.CreateLocationGroupRoleAssignment(pg, membershipID, locationGroupID, req)
		if err != nil {
			logger.Errorf("Error creating location group role assignment: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return the location group role assignment
		response.CreateSuccessResponse(locationGroupRoleAssignment, w)
	}
}

// GetLocationGroupRoleAssignmentsByMembershipIDWithDeps returns the location group role assignments for a given user ID
func GetLocationGroupRoleAssignmentsByMembershipIDWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Parse request URL
		organizationID, err := helper.ParseUUIDFromRequest(r, organizationIDParam)
		if err != nil {
			logger.Errorf("Error parsing orgID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		userID, err := helper.ParseUUIDFromRequest(r, userIDParam)
		if err != nil {
			logger.Errorf("Error parsing userID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Check membership exists
		membershipID, err := deps.GetMembershipByUserID(pg, userID, organizationID)
		if err != nil {
			logger.Errorf("Error getting membership: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Get the location group role assignments
		locationGroupRoleAssignments, err := deps.GetLocationGroupRoleAssignmentsByMembershipID(pg, membershipID)
		if err != nil {
			logger.Errorf("Error getting location group role assignments: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return the location group role assignments
		response.CreateSuccessResponse(locationGroupRoleAssignments, w)
	}
}

// UpdateLocationGroupRoleAssignmentWithDeps updates a location group role assignment
func UpdateLocationGroupRoleAssignmentWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Parse request URL
		organizationID, err := helper.ParseUUIDFromRequest(r, organizationIDParam)
		if err != nil {
			logger.Errorf("Error parsing orgID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		userID, err := helper.ParseUUIDFromRequest(r, userIDParam)
		if err != nil {
			logger.Errorf("Error parsing userID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		locationGroupID, err := helper.ParseUUIDFromRequest(r, locationGroupIDParam)
		if err != nil {
			logger.Errorf("Error parsing locationGroupID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Check membership exists
		membershipID, err := deps.GetMembershipByUserID(pg, userID, organizationID)
		if err != nil {
			logger.Errorf("Error getting membership: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Parse request body
		req, err := parseCreateAndUpdateLocationGroupRoleAssignmentRequest(r)
		if err != nil {
			logger.Errorf("Error parsing request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Update the location group role assignment
		locationGroupRoleAssignment, err := deps.UpdateLocationGroupRoleAssignment(pg, membershipID, locationGroupID, req)
		if err != nil {
			logger.Errorf("Error updating location group role assignment: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return the location group role assignment
		response.CreateSuccessResponse(locationGroupRoleAssignment, w)
	}
}

// DeleteLocationGroupRoleAssignmentWithDeps deletes a location group role assignment
func DeleteLocationGroupRoleAssignmentWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Parse request URL
		organizationID, err := helper.ParseUUIDFromRequest(r, organizationIDParam)
		if err != nil {
			logger.Errorf("Error parsing orgID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		userID, err := helper.ParseUUIDFromRequest(r, userIDParam)
		if err != nil {
			logger.Errorf("Error parsing userID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		locationGroupID, err := helper.ParseUUIDFromRequest(r, locationGroupIDParam)
		if err != nil {
			logger.Errorf("Error parsing locationGroupID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Check membership exists
		membershipID, err := deps.GetMembershipByUserID(pg, userID, organizationID)
		if err != nil {
			logger.Errorf("Error getting membership: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Delete the location group role assignment
		err = deps.DeleteLocationGroupRoleAssignment(pg, membershipID, locationGroupID)
		if err != nil {
			logger.Errorf("Error deleting location group role assignment: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return success response
		response.CreateSuccessResponse(nil, w)
	}
}

// parseCreateAndUpdateLocationGroupRoleAssignmentRequest parses the request body and returns a CreateAndUpdateLocationGroupRoleAssignmentRequest
func parseCreateAndUpdateLocationGroupRoleAssignmentRequest(r *http.Request) (*CreateAndUpdateLocationGroupRoleAssignmentRequest, error) {
	var req CreateAndUpdateLocationGroupRoleAssignmentRequest
	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields() // Reject unexpected fields

	if err := decoder.Decode(&req); err != nil {
		logger.Infof("failed to parse create request: %v", err)
		if strings.Contains(err.Error(), "unknown field") {
			return &req, ErrUnexpectedFields
		}
		return &req, ErrInvalidRoleIDs
	}

	// Validate the role IDs are not empty
	if len(req.RoleIDs) == 0 {
		return &req, ErrInvalidRoleIDs
	}

	return &req, nil
}

// createLocationGroupRoleAssignment creates a location group role assignment
var createLocationGroupRoleAssignment = func(
	pg connect.DatabaseExecutor,
	membershipID uuid.UUID,
	locationGroupID uuid.UUID,
	req *CreateAndUpdateLocationGroupRoleAssignmentRequest,
) (*LocationGroupRoleAssignmentResponse, error) {
	// Check req not empty
	if len(req.RoleIDs) == 0 {
		return nil, ErrInvalidRoleIDs
	}

	// Build a single INSERT statement of the form:
	// INSERT INTO {{LocationGroupRoleAssignments}} (MembershipId, LocationGroupId, RoleId, IsDeleted, CreatedAt, UpdatedAt)
	// VALUES ($1, $2, $3, $4, $5, $6), ($7, $8, $9, $10, $11, $12), ...
	// RETURNING MembershipId, LocationGroupId, RoleId, CreatedAt, UpdatedAt;
	var (
		placeholders []string
		values       []interface{}
	)
	now := time.Now().UTC()

	// For each role ID, we need five values.
	for i, roleID := range req.RoleIDs {
		offset := i * 6
		placeholders = append(placeholders, fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d)",
			offset+1, offset+2, offset+3, offset+4, offset+5, offset+6))
		values = append(values, membershipID, locationGroupID, roleID, false, now, now)
	}

	query := fmt.Sprintf(`
		INSERT INTO {{LocationGroupRoleAssignments}} (
			MembershipId,
			LocationGroupId,
			RoleId,
			IsDeleted,
			CreatedAt,
			UpdatedAt
		)
		VALUES %s
		RETURNING MembershipId, LocationGroupId, RoleId, CreatedAt, UpdatedAt;
	`, strings.Join(placeholders, ","))

	var locationGroupRoleAssignments LocationGroupRoleAssignmentResponse
	err := pg.QueryGenericSlice(&locationGroupRoleAssignments.LocationGroupRoleAssignment, query, values...)
	if err != nil {
		logger.Errorf("Error creating location group role assignment: %v", err)
		return nil, ErrDatabaseOperation
	}

	return &locationGroupRoleAssignments, nil
}

// getLocationGroupRoleAssignmentsByMembershipID returns the location group role assignments for a given membership ID
var getLocationGroupRoleAssignmentsByMembershipID = func(
	pg connect.DatabaseExecutor,
	membershipID uuid.UUID,
) (*LocationGroupRoleAssignmentResponse, error) {
	query := `
		SELECT MembershipId, LocationGroupId, RoleId, CreatedAt, UpdatedAt
		FROM {{LocationGroupRoleAssignments}}
		WHERE MembershipId = $1
			AND NOT IsDeleted
	`
	var locationGroupRoleAssignments LocationGroupRoleAssignmentResponse
	err := pg.QueryGenericSlice(&locationGroupRoleAssignments.LocationGroupRoleAssignment, query, membershipID)
	if err != nil {
		logger.Errorf("Error getting location group role assignments: %v", err)
		return nil, ErrDatabaseOperation
	}

	return &locationGroupRoleAssignments, nil
}

// updateLocationGroupRoleAssignment updates a location group role assignment
var updateLocationGroupRoleAssignment = func(
	pg connect.DatabaseExecutor,
	membershipID uuid.UUID,
	locationGroupID uuid.UUID,
	req *CreateAndUpdateLocationGroupRoleAssignmentRequest,
) (*LocationGroupRoleAssignmentResponse, error) {
	// Check req not empty
	if len(req.RoleIDs) == 0 {
		return nil, ErrInvalidRoleIDs
	}

	// Build a single UPDATE statement of the form:
	// INSERT INTO {{LocationGroupRoleAssignments}} (MembershipId, LocationGroupId, RoleId, IsDeleted, CreatedAt, UpdatedAt)
	// VALUES ($1, $2, $3, $4, $5, $6), ($7, $8, $9, $10, $11, $12), ...
	// ON CONFLICT (MembershipId, LocationGroupId, RoleId) DO UPDATE SET IsDeleted = false
	// RETURNING MembershipId, LocationGroupId, RoleId, CreatedAt, UpdatedAt;
	var (
		placeholders []string
		values       []interface{}
	)
	now := time.Now().UTC()

	// For each role ID, we need five values.
	for i, roleID := range req.RoleIDs {
		offset := i * 6
		placeholders = append(placeholders, fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d)",
			offset+1, offset+2, offset+3, offset+4, offset+5, offset+6))
		values = append(values, membershipID, locationGroupID, roleID, false, now, now)
	}

	query := fmt.Sprintf(`
		INSERT INTO {{LocationGroupRoleAssignments}} (
			MembershipId,
			LocationGroupId,
			RoleId,
			IsDeleted,
			CreatedAt,
			UpdatedAt
		)
		VALUES %s
		ON CONFLICT (MembershipId, LocationGroupId, RoleId) 
		DO UPDATE SET IsDeleted = false, UpdatedAt = NOW()
		RETURNING MembershipId, LocationGroupId, RoleId, CreatedAt, UpdatedAt;
	`, strings.Join(placeholders, ","))

	var locationGroupRoleAssignments LocationGroupRoleAssignmentResponse
	err := pg.QueryGenericSlice(&locationGroupRoleAssignments.LocationGroupRoleAssignment, query, values...)
	if err != nil {
		logger.Errorf("Error updating location group role assignment: %v", err)
		return nil, ErrDatabaseOperation
	}

	return &locationGroupRoleAssignments, nil
}

// deleteLocationGroupRoleAssignment deletes a location group role assignment is soft delete
var deleteLocationGroupRoleAssignment = func(
	pg connect.DatabaseExecutor,
	membershipID uuid.UUID,
	locationGroupID uuid.UUID,
) error {
	now := time.Now().UTC()
	query := `
		UPDATE {{LocationGroupRoleAssignments}}
		SET 
			IsDeleted = true, 
			UpdatedAt = $3
		WHERE 
			MembershipId = $1 
			AND LocationGroupId = $2
			AND NOT IsDeleted
	`

	_, err := pg.Exec(query, membershipID, locationGroupID, now)
	if err != nil {
		logger.Errorf("Error deleting location group role assignment: %v", err)
		return ErrDatabaseOperation
	}

	return nil
}

var (
	CreateLocationGroupRoleAssignmentHandler = CreateLocationGroupRoleAssignmentWithDeps(HandlerDeps{
		GetConnections:                    connect.GetConnections,
		CreateLocationGroupRoleAssignment: createLocationGroupRoleAssignment,
		GetMembershipByUserID:             helper.GetMembershipByUserID,
	})
	GetLocationGroupRoleAssignmentsByMembershipIDHandler = GetLocationGroupRoleAssignmentsByMembershipIDWithDeps(HandlerDeps{
		GetConnections: connect.GetConnections,
		GetLocationGroupRoleAssignmentsByMembershipID: getLocationGroupRoleAssignmentsByMembershipID,
		GetMembershipByUserID:                         helper.GetMembershipByUserID,
	})
	UpdateLocationGroupRoleAssignmentHandler = UpdateLocationGroupRoleAssignmentWithDeps(HandlerDeps{
		GetConnections:                    connect.GetConnections,
		UpdateLocationGroupRoleAssignment: updateLocationGroupRoleAssignment,
		GetMembershipByUserID:             helper.GetMembershipByUserID,
	})
	DeleteLocationGroupRoleAssignmentHandler = DeleteLocationGroupRoleAssignmentWithDeps(HandlerDeps{
		GetConnections:                    connect.GetConnections,
		DeleteLocationGroupRoleAssignment: deleteLocationGroupRoleAssignment,
		GetMembershipByUserID:             helper.GetMembershipByUserID,
	})
)
