package devicegroups

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/rest/onramp/helper"
)

// HandlerDeps defines dependencies for handlers to enable dependency injection
type HandlerDeps struct {
	GetConnections                func(context.Context, ...bool) (*connect.Connections, error)
	GetDeviceGroupsByOrganization func(connect.DatabaseExecutor, uuid.UUID) (*[]DeviceGroups, error)
	CreateDeviceGroups            func(connect.DatabaseExecutor, uuid.UUID, *CreateAndUpdateDeviceGroupsRequest) (*DeviceGroups, error)
	DeleteDevieGroups             func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
	UpdateDevieGroups             func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, string) error
	HasDevices                    func(connect.DatabaseExecutor, uuid.UUID) (bool, error)
}

// GetDeviceGroupsHandlerWithDeps returns a handler for getting device groups by organization with dependency injection
func GetDeviceGroupsHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate organization ID from path
		orgId, err := helper.ValidateOrganizationID(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get device groups for organization
		devicegroups, err := deps.GetDeviceGroupsByOrganization(pg, orgId)
		if err != nil {
			logger.Errorf("failed to get device groups for organization %s: %v", orgId, err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return success response
		response.CreateSuccessResponse(devicegroups, w)
	}
}

// CreateDeviceGroupsHandlerWithDeps returns a handler for creating a new device groups with dependency injection
func CreateDeviceGroupsHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate organization ID from path
		orgId, err := helper.ValidateOrganizationID(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse request body with validation
		requestBody, err := parseCreateAndUpdateDeviceGroupsRequest(r)
		if err != nil {
			logger.Errorf("Error parsing request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}
		// Create the device groups
		devicegroups, err := deps.CreateDeviceGroups(pg, orgId, requestBody)
		if err != nil {
			if err == ErrOrganizationNotFound {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("failed to create device groups for organization %s: %v", orgId, err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return success response
		response.CreateSuccessResponse(devicegroups, w)
	}
}

// DeleteDeviceGroupsHandlerWithDeps returns a handler for deleting a specific device groups with dependency injection
func DeleteDeviceGroupsHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate organization ID from path
		orgId, err := helper.ValidateOrganizationID(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate deviceGroup ID from path
		deviceGroupId, err := helper.ParseUUIDFromRequest(r, "devicegroupId")
		if err != nil {
			logger.Errorf("invalid deviceGroup ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Check for existing devices assigned before deletion
		hasDevices, err := deps.HasDevices(pg, deviceGroupId)
		if err != nil {
			logger.Errorf("failed to check for existing devices assigned with deviceGroupId %s: %v", deviceGroupId, err)
			response.CreateInternalErrorResponse(w)
			return
		}
		if hasDevices {
			message := "Device group cannot be deleted because it has devices assignments"
			logger.Infof("Device group %s cannot be deleted because it has devices assignments", deviceGroupId)
			response.CreateCustomErrorResponse(message, deviceGroupId, http.StatusBadRequest, w)
			return
		}

		// Delete the specific device group
		err = deps.DeleteDevieGroups(pg, orgId, deviceGroupId)
		if err != nil {
			if err == ErrDeviceGroupNotFound {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("failed to delete device group %s for organization %s: %v", deviceGroupId, orgId, err)
			response.CreateInternalErrorResponse(w)
			return
		}
		response.CreateSuccessResponse(nil, w)
	}
}

// UpdateDeviceGroupsHandlerWithDeps returns a handler for getting device groups by organization with dependency injection
func UpdateDeviceGroupsHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate organization ID from path
		orgId, err := helper.ValidateOrganizationID(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate deviceGroup ID from path
		deviceGroupId, err := helper.ParseUUIDFromRequest(r, "devicegroupId")
		if err != nil {
			logger.Errorf("invalid deviceGroup ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse request body with validation
		requestBody, err := parseCreateAndUpdateDeviceGroupsRequest(r)
		if err != nil {
			logger.Errorf("Error parsing request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Update the specific device group
		err = deps.UpdateDevieGroups(pg, orgId, deviceGroupId, requestBody.Name)
		if err != nil {
			if err == ErrDeviceGroupNotFound {
				logger.Errorf("Not found Device Group: %v", deviceGroupId)
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("failed to delete device group %s for organization %s: %v", deviceGroupId, orgId, err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return success response
		response.CreateSuccessResponse(nil, w)
	}
}

// getDeviceGroupsByOrganization retrieves all non-deleted device groups for an organization
var getDeviceGroupsByOrganization = func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*[]DeviceGroups, error) {
	query := `
		SELECT
			dv.Id,
			dv.OrganizationId,
			dv.Name,
			dv.IsDeleted,
			dv.CreatedAt,
			dv.UpdatedAt
		FROM {{DeviceGroups}} dv
		WHERE dv.OrganizationId = $1 AND NOT dv.IsDeleted
		ORDER BY dv.CreatedAt DESC`

	var devicegroups []DeviceGroups
	err := pg.QueryGenericSlice(&devicegroups, query, orgId)
	if err != nil {
		logger.Errorf("Failed to get device groups for organization %s: %v", orgId, err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return &devicegroups, nil
}

// parseCreateAndUpdateDeviceGroupsRequest parses and validates the create device groups request body
func parseCreateAndUpdateDeviceGroupsRequest(r *http.Request) (*CreateAndUpdateDeviceGroupsRequest, error) {
	var req CreateAndUpdateDeviceGroupsRequest
	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields()

	if err := decoder.Decode(&req); err != nil {
		logger.Infof("failed to parse create device group request: %v", err)
		if strings.Contains(err.Error(), "unknown field") {
			return &req, ErrUnexpectedFields
		}
		return &req, ErrInvalidRequestBody
	}
	// Validate device name
	if strings.TrimSpace(req.Name) == "" {
		return &req, ErrInvalidDeviceGroupsName
	}
	return &req, nil
}

// createDeviceGroups creates a new device group
var createDeviceGroups = func(pg connect.DatabaseExecutor, orgId uuid.UUID, req *CreateAndUpdateDeviceGroupsRequest) (*DeviceGroups, error) {
	// Check if organization exists
	checkQuery := `
		SELECT EXISTS(
			SELECT 1 FROM {{Organization}}
			WHERE Id = $1 AND NOT IsDeleted
		) AS exists`

	row, err := pg.QueryRow(checkQuery, orgId)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	existsVal, ok := row["exists"]
	if !ok {
		return nil, fmt.Errorf("%w: missing exists column in query result", ErrDatabaseOperation)
	}

	exists, ok := existsVal.(bool)
	if !ok {
		return nil, fmt.Errorf("%w: invalid type for exists column", ErrDatabaseOperation)
	}

	if !exists {
		return nil, ErrOrganizationNotFound
	}

	// Insert new device group
	now := time.Now().UTC()

	insertQuery := `
		INSERT INTO {{DeviceGroups}} (
			OrganizationId, Name, CreatedAt, UpdatedAt, IsDeleted
		) VALUES ($1, $2, $3, $4, $5)
		RETURNING Id, OrganizationId, Name, IsDeleted, CreatedAt, UpdatedAt`

	var devicegroups DeviceGroups
	err = pg.QueryRowStruct(&devicegroups, insertQuery,
		orgId, req.Name, now, now, false,
	)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return &devicegroups, nil
}

// deleteDeviceGroups soft deletes a deviceGroup
var deleteDeviceGroups = func(pg connect.DatabaseExecutor, orgId uuid.UUID, deviceGroupId uuid.UUID) error {
	query := `
		UPDATE {{DeviceGroups}}
		SET IsDeleted = true, UpdatedAt = NOW()
		WHERE Id = $1 AND OrganizationId = $2 AND NOT IsDeleted`

	result, err := pg.Exec(query, deviceGroupId, orgId)
	if err != nil {
		logger.Errorf("Failed to delete device group %s for organization %s: %v", deviceGroupId, orgId, err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Errorf("Failed to get rows affected for device group deletion %s: %v", deviceGroupId, err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	if rowsAffected == 0 {
		return ErrDeviceGroupNotFound
	}

	return nil
}

// updateDeviceGroups a deviceGroup
var updateDeviceGroups = func(pg connect.DatabaseExecutor, orgId uuid.UUID, deviceGroupId uuid.UUID, newName string) error {
	query := `
		UPDATE {{DeviceGroups}}
		SET Name = $1, UpdatedAt = NOW()
		WHERE Id = $2 AND OrganizationId = $3 AND NOT IsDeleted`

	result, err := pg.Exec(query, newName, deviceGroupId, orgId)
	if err != nil {
		logger.Errorf("Failed to update device group %s for organization %s: %v", deviceGroupId, orgId, err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Errorf("Failed to get rows affected for device group deletion %s: %v", deviceGroupId, err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	if rowsAffected == 0 {
		return ErrDeviceGroupNotFound
	}

	return nil
}

// hasDevices checks if a device group has any devices assigned
var hasDevices = func(pg connect.DatabaseExecutor, dgId uuid.UUID) (bool, error) {
	const query = `
        SELECT EXISTS(
            SELECT 1
            FROM {{DeviceGroupDevices}}
            WHERE devicegroupid = $1
        ) as exists
    `
	var existsResult struct {
		Exists bool `db:"exists"`
	}
	err := pg.QueryRowStruct(&existsResult, query, dgId)
	if err != nil {
		return false, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return existsResult.Exists, nil
}

var (
	GetDeviceGroupsHandler = GetDeviceGroupsHandlerWithDeps(HandlerDeps{
		GetConnections:                connect.GetConnections,
		GetDeviceGroupsByOrganization: getDeviceGroupsByOrganization,
	})
	CreateDeviceGroupsHandler = CreateDeviceGroupsHandlerWithDeps(HandlerDeps{
		GetConnections:     connect.GetConnections,
		CreateDeviceGroups: createDeviceGroups,
	})
	DeleteDeviceGroupsHandler = DeleteDeviceGroupsHandlerWithDeps(HandlerDeps{
		GetConnections:    connect.GetConnections,
		DeleteDevieGroups: deleteDeviceGroups,
		HasDevices:        hasDevices,
	})
	UpdateDeviceGroupsHandler = UpdateDeviceGroupsHandlerWithDeps(HandlerDeps{
		GetConnections:    connect.GetConnections,
		UpdateDevieGroups: updateDeviceGroups,
	})
)
