package rest

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"synapse-its.com/shared/connect"
	config "synapse-its.com/shared/rest/onramp/softwaregateway/config"
)

// TestSoftwareGatewayConfig_DatabaseOperations_Integration tests the core database functions directly
// This ensures the business logic works correctly with real database constraints and relationships
func TestSoftwareGatewayConfig_DatabaseOperations_Integration(t *testing.T) {
	t.Parallel()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Set up database connections
	connections := connect.NewConnections(ctx)
	defer connections.Close()
	pg := connections.Postgres

	// Use test data from dev.sql
	testOrg1ID := uuid.MustParse("550e8400-e29b-41d4-a716-************")
	corneliusOrgID := uuid.MustParse("c469a554-f7a8-5de5-a57e-e1ba16f970d3")
	testSoftwareGatewayID := uuid.MustParse("550e8400-e29b-41d4-a716-************") // Test Gateway 1

	t.Run("Database Template Operations", func(t *testing.T) {
		// Test creating a template
		templateID := uuid.New()
		template := config.GatewayConfigTemplate{
			Id:             templateID,
			Name:           "DB Test Template",
			OrganizationId: testOrg1ID,
			Description:    "Database integration test template",
		}

		createQuery := `
			INSERT INTO {{GatewayConfigTemplate}} (Id, Name, OrganizationId, Description, IsDeleted, CreatedAt, UpdatedAt)
			VALUES ($1, $2, $3, $4, false, NOW(), NOW())`

		_, err := pg.Exec(createQuery, template.Id, template.Name, template.OrganizationId, template.Description)
		require.NoError(t, err, "Should be able to create template")

		// Test retrieving the template
		var retrievedTemplate config.GatewayConfigTemplate
		getQuery := `
			SELECT Id, Name, OrganizationId, Description 
			FROM {{GatewayConfigTemplate}} 
			WHERE Id = $1 AND IsDeleted = false`

		err = pg.QueryRowStruct(&retrievedTemplate, getQuery, templateID)
		require.NoError(t, err, "Should be able to retrieve template")
		assert.Equal(t, template.Name, retrievedTemplate.Name)
		assert.Equal(t, template.OrganizationId, retrievedTemplate.OrganizationId)

		// Test updating the template
		newName := "Updated DB Test Template"
		updateQuery := `
			UPDATE {{GatewayConfigTemplate}} 
			SET Name = $1, UpdatedAt = NOW() 
			WHERE Id = $2`

		_, err = pg.Exec(updateQuery, newName, templateID)
		require.NoError(t, err, "Should be able to update template")

		// Verify update
		err = pg.QueryRowStruct(&retrievedTemplate, getQuery, templateID)
		require.NoError(t, err, "Should be able to retrieve updated template")
		assert.Equal(t, newName, retrievedTemplate.Name)

		// Test soft delete
		deleteQuery := `
			UPDATE {{GatewayConfigTemplate}} 
			SET IsDeleted = true, UpdatedAt = NOW() 
			WHERE Id = $1`

		_, err = pg.Exec(deleteQuery, templateID)
		require.NoError(t, err, "Should be able to soft delete template")

		// Verify template is no longer accessible
		err = pg.QueryRowStruct(&retrievedTemplate, getQuery, templateID)
		assert.Error(t, err, "Should not be able to retrieve deleted template")
	})

	t.Run("Database Template Settings Operations", func(t *testing.T) {
		// Get an existing template
		templateQuery := `SELECT Id FROM {{GatewayConfigTemplate}} WHERE OrganizationId = $1 AND IsDeleted = false LIMIT 1`
		var result struct {
			Id uuid.UUID `db:"id"`
		}
		err := pg.QueryRowStruct(&result, templateQuery, testOrg1ID)
		require.NoError(t, err, "Should find an existing template")
		templateID := result.Id

		// Test creating template settings (ensure base settings exist first)
		testSettings := []struct {
			setting string
			value   string
		}{
			{"log_level", "debug"},
			{"device_state_send_frequency_seconds", "120"},
		}

		// Ensure base settings exist for all test settings
		ensureBaseSettingQuery := `
			INSERT INTO {{GatewayConfigTemplateBaseSettings}} (setting, name, description, format, defaultValue, isDeleted)
			VALUES ($1, $2, $3, '{"type": "string"}', 'default', false)
			ON CONFLICT (setting) DO NOTHING`

		for _, ts := range testSettings {
			settingName := "Test " + ts.setting
			settingDesc := "Test description for " + ts.setting
			_, err := pg.Exec(ensureBaseSettingQuery, ts.setting, settingName, settingDesc)
			require.NoError(t, err, "Should be able to ensure base setting exists")
		}

		for _, ts := range testSettings {
			// Try to update first
			updateQuery := `
				UPDATE {{GatewayConfigTemplateSettings}}
				SET value = $3
				WHERE gatewayConfigTemplateId = $1 AND setting = $2`

			result, err := pg.Exec(updateQuery, templateID, ts.setting, ts.value)
			require.NoError(t, err, "Should be able to update template setting")

			rowsAffected, err := result.RowsAffected()
			require.NoError(t, err, "Should be able to get rows affected")

			if rowsAffected == 0 {
				// Record doesn't exist, insert it
				insertQuery := `
					INSERT INTO {{GatewayConfigTemplateSettings}} (gatewayConfigTemplateId, setting, value)
					VALUES ($1, $2, $3)`

				_, err = pg.Exec(insertQuery, templateID, ts.setting, ts.value)
				require.NoError(t, err, "Should be able to create template setting")
			}
		}

		// Test retrieving template settings
		var settings []config.GatewayConfigTemplateSetting
		getQuery := `
			SELECT gatewayConfigTemplateId, setting, value
			FROM {{GatewayConfigTemplateSettings}}
			WHERE gatewayConfigTemplateId = $1
			ORDER BY setting`

		err = pg.QueryGenericSlice(&settings, getQuery, templateID)
		require.NoError(t, err, "Should be able to retrieve template settings")

		// Find our test settings
		settingMap := make(map[string]string)
		for _, s := range settings {
			settingMap[s.Setting] = s.Value
		}
		assert.Equal(t, "debug", settingMap["log_level"])
		assert.Equal(t, "120", settingMap["device_state_send_frequency_seconds"])

		// Test bulk replace (delete all and insert new ones)
		// Delete existing settings
		deleteQuery := `DELETE FROM {{GatewayConfigTemplateSettings}} WHERE gatewayConfigTemplateId = $1`
		_, err = pg.Exec(deleteQuery, templateID)
		require.NoError(t, err, "Should be able to delete settings")

		// Insert new settings
		newSettings := []struct {
			setting string
			value   string
		}{
			{"bulk_setting_1", "bulk_value_1"},
			{"bulk_setting_2", "bulk_value_2"},
		}

		// Ensure base settings exist for new settings
		ensureBaseSettingQuery = `
			INSERT INTO {{GatewayConfigTemplateBaseSettings}} (setting, name, description, format, defaultValue, isDeleted)
			VALUES ($1, $2, $3, '{"type": "string"}', 'default', false)
			ON CONFLICT (setting) DO NOTHING`

		for _, ns := range newSettings {
			settingName := "Test " + ns.setting
			settingDesc := "Test description for " + ns.setting
			_, err := pg.Exec(ensureBaseSettingQuery, ns.setting, settingName, settingDesc)
			require.NoError(t, err, "Should be able to ensure base setting exists")
		}

		insertSettingQuery := `
			INSERT INTO {{GatewayConfigTemplateSettings}} (gatewayConfigTemplateId, setting, value)
			VALUES ($1, $2, $3)`

		for _, ns := range newSettings {
			_, err = pg.Exec(insertSettingQuery, templateID, ns.setting, ns.value)
			require.NoError(t, err, "Should be able to insert new setting")
		}

		// Verify bulk replace worked - with retry for potential transaction timing issues
		var newSettingsList []config.GatewayConfigTemplateSetting
		var newSettingMap map[string]string
		foundCorrectSettings := false

		// Retry up to 5 times with increasing delays
		for attempt := 0; attempt < 5; attempt++ {
			if attempt > 0 {
				time.Sleep(time.Duration(attempt*50) * time.Millisecond) // 50ms, 100ms, 150ms, 200ms
			}

			newSettingsList = nil // Reset slice
			err = pg.QueryGenericSlice(&newSettingsList, getQuery, templateID)
			require.NoError(t, err, "Should be able to retrieve new settings")

			newSettingMap = make(map[string]string)
			for _, s := range newSettingsList {
				newSettingMap[s.Setting] = s.Value
			}

			// Check if we have the expected settings
			if newSettingMap["bulk_setting_1"] == "bulk_value_1" &&
				newSettingMap["bulk_setting_2"] == "bulk_value_2" {
				// Check that old setting is gone
				_, hasOldSetting := newSettingMap["db_test_setting_1"]
				if !hasOldSetting {
					foundCorrectSettings = true
					break
				}
			}
		}

		assert.True(t, foundCorrectSettings, "Should find the correct bulk settings after retries")
		assert.Equal(t, "bulk_value_1", newSettingMap["bulk_setting_1"])
		assert.Equal(t, "bulk_value_2", newSettingMap["bulk_setting_2"])
		assert.NotContains(t, newSettingMap, "db_test_setting_1", "Old setting should be gone")
	})

	t.Run("Database Override Operations", func(t *testing.T) {
		// Test creating overrides
		testOverrides := []struct {
			setting string
			value   string
		}{
			{"db_override_setting_1", "db_override_value_1"},
			{"db_override_setting_2", "db_override_value_2"},
		}

		// First delete any existing overrides to avoid conflicts
		deleteExistingQuery := `DELETE FROM {{GatewayConfigTemplateSettingOverrides}} WHERE softwareGatewayId = $1`
		_, err := pg.Exec(deleteExistingQuery, testSoftwareGatewayID)
		require.NoError(t, err, "Should be able to delete existing overrides")

		// First ensure base settings exist for the overrides
		ensureBaseSettingQuery := `
			INSERT INTO {{GatewayConfigTemplateBaseSettings}} (setting, name, description, format, defaultValue, isDeleted)
			VALUES ($1, $2, $3, '{"type": "string"}', 'default', false)
			ON CONFLICT (setting) DO NOTHING`

		for _, to := range testOverrides {
			settingName := "Test " + to.setting
			settingDesc := "Test description for " + to.setting
			_, err := pg.Exec(ensureBaseSettingQuery, to.setting, settingName, settingDesc)
			require.NoError(t, err, "Should be able to ensure base setting exists")
		}

		insertQuery := `
			INSERT INTO {{GatewayConfigTemplateSettingOverrides}} (softwareGatewayId, setting, value)
			VALUES ($1, $2, $3)`

		for _, to := range testOverrides {
			_, err := pg.Exec(insertQuery, testSoftwareGatewayID, to.setting, to.value)
			require.NoError(t, err, "Should be able to create override")
		}

		// Test retrieving overrides
		var overrides []config.GatewayConfigTemplateSettingOverride
		getQuery := `
			SELECT softwareGatewayId, setting, value
			FROM {{GatewayConfigTemplateSettingOverrides}}
			WHERE softwareGatewayId = $1
			ORDER BY setting`

		err = pg.QueryGenericSlice(&overrides, getQuery, testSoftwareGatewayID)
		require.NoError(t, err, "Should be able to retrieve overrides")

		// Find our test overrides
		overrideMap := make(map[string]string)
		for _, o := range overrides {
			overrideMap[o.Setting] = o.Value
		}
		assert.Equal(t, "db_override_value_1", overrideMap["db_override_setting_1"])
		assert.Equal(t, "db_override_value_2", overrideMap["db_override_setting_2"])

		// Test upsert operation (update existing, insert new)
		upsertData := []struct {
			setting string
			value   string
		}{
			{"db_override_setting_1", "updated_db_override_value_1"}, // Update existing
			{"db_override_setting_3", "db_override_value_3"},         // Insert new
		}

		// Ensure base settings exist for new upsert data
		for _, ud := range upsertData {
			if ud.setting == "db_override_setting_3" { // Only create for new settings
				settingName := "Test " + ud.setting
				settingDesc := "Test description for " + ud.setting
				_, err = pg.Exec(ensureBaseSettingQuery, ud.setting, settingName, settingDesc)
				require.NoError(t, err, "Should be able to ensure base setting exists for upsert")
			}
		}

		for _, ud := range upsertData {
			// Implement proper upsert logic: try update first, then insert if no rows affected
			updateQuery := `
				UPDATE {{GatewayConfigTemplateSettingOverrides}}
				SET value = $3
				WHERE softwareGatewayId = $1 AND setting = $2`

			result, err := pg.Exec(updateQuery, testSoftwareGatewayID, ud.setting, ud.value)
			require.NoError(t, err, "Should be able to update override")

			rowsAffected, err := result.RowsAffected()
			require.NoError(t, err, "Should be able to get rows affected")

			if rowsAffected == 0 {
				// Record doesn't exist, insert it
				_, err = pg.Exec(insertQuery, testSoftwareGatewayID, ud.setting, ud.value)
				require.NoError(t, err, "Should be able to insert new override")
			}
		}

		// Verify upsert worked
		err = pg.QueryGenericSlice(&overrides, getQuery, testSoftwareGatewayID)
		require.NoError(t, err, "Should be able to retrieve updated overrides")

		newOverrideMap := make(map[string]string)
		for _, o := range overrides {
			newOverrideMap[o.Setting] = o.Value
		}
		assert.Equal(t, "updated_db_override_value_1", newOverrideMap["db_override_setting_1"])
		assert.Equal(t, "db_override_value_2", newOverrideMap["db_override_setting_2"])
		assert.Equal(t, "db_override_value_3", newOverrideMap["db_override_setting_3"])
	})

	t.Run("Database Base Settings Operations", func(t *testing.T) {
		testSetting := "db_test_base_setting"

		// Test creating base setting
		baseSetting := config.GatewayConfigTemplateBaseSetting{
			Setting:      testSetting,
			DefaultValue: "default_db_value",
			Name:         "DB Test Base Setting",
			Description:  "Base setting for database testing",
			Format:       `{"type":"string"}`,
		}

		insertQuery := `
			INSERT INTO {{GatewayConfigTemplateBaseSettings}} 
			(Setting, DefaultValue, Name, Description, Format, IsDeleted, CreatedAt, UpdatedAt)
			VALUES ($1, $2, $3, $4, $5, false, NOW(), NOW())`

		_, err := pg.Exec(insertQuery,
			baseSetting.Setting,
			baseSetting.DefaultValue,
			baseSetting.Name,
			baseSetting.Description,
			baseSetting.Format)
		require.NoError(t, err, "Should be able to create base setting")

		// Test retrieving base setting
		var retrievedSetting config.GatewayConfigTemplateBaseSetting
		getQuery := `
			SELECT Setting, DefaultValue, Name, Description, Format
			FROM {{GatewayConfigTemplateBaseSettings}}
			WHERE Setting = $1 AND IsDeleted = false`

		err = pg.QueryRowStruct(&retrievedSetting, getQuery, testSetting)
		require.NoError(t, err, "Should be able to retrieve base setting")
		assert.Equal(t, baseSetting.Name, retrievedSetting.Name)
		assert.Equal(t, baseSetting.DefaultValue, retrievedSetting.DefaultValue)

		// Test updating base setting
		newDefaultValue := "updated_default_db_value"
		updateQuery := `
			UPDATE {{GatewayConfigTemplateBaseSettings}}
			SET DefaultValue = $1, UpdatedAt = NOW()
			WHERE Setting = $2`

		_, err = pg.Exec(updateQuery, newDefaultValue, testSetting)
		require.NoError(t, err, "Should be able to update base setting")

		// Verify update
		err = pg.QueryRowStruct(&retrievedSetting, getQuery, testSetting)
		require.NoError(t, err, "Should be able to retrieve updated base setting")
		assert.Equal(t, newDefaultValue, retrievedSetting.DefaultValue)

		// Test soft delete
		deleteQuery := `
			UPDATE {{GatewayConfigTemplateBaseSettings}}
			SET IsDeleted = true, UpdatedAt = NOW()
			WHERE Setting = $1`

		_, err = pg.Exec(deleteQuery, testSetting)
		require.NoError(t, err, "Should be able to soft delete base setting")

		// Verify base setting is no longer accessible
		err = pg.QueryRowStruct(&retrievedSetting, getQuery, testSetting)
		assert.Error(t, err, "Should not be able to retrieve deleted base setting")
	})

	t.Run("Database Constraint Validation", func(t *testing.T) {
		// Test foreign key constraints

		// Template must reference valid organization
		invalidOrgID := uuid.New()
		templateID := uuid.New()

		invalidTemplateQuery := `
			INSERT INTO {{GatewayConfigTemplate}} (Id, Name, OrganizationId, Description, IsDeleted, CreatedAt, UpdatedAt)
			VALUES ($1, $2, $3, $4, false, NOW(), NOW())`

		_, err := pg.Exec(invalidTemplateQuery, templateID, "Invalid Template", invalidOrgID, "Should fail")
		assert.Error(t, err, "Should fail to create template with invalid organization ID")

		// Override must reference valid software gateway
		invalidGatewayID := uuid.New()

		// First ensure the base setting exists (to avoid base setting FK error)
		ensureBaseSettingQuery := `
			INSERT INTO {{GatewayConfigTemplateBaseSettings}} (setting, name, description, format, defaultValue, isDeleted)
			VALUES ($1, $2, $3, '{"type": "string"}', 'default', false)
			ON CONFLICT (setting) DO NOTHING`
		_, err = pg.Exec(ensureBaseSettingQuery, "test_setting", "Test Setting", "Test description")
		require.NoError(t, err, "Should be able to ensure base setting exists")

		invalidOverrideQuery := `
			INSERT INTO {{GatewayConfigTemplateSettingOverrides}} (softwareGatewayId, setting, value)
			VALUES ($1, $2, $3)`

		_, err = pg.Exec(invalidOverrideQuery, invalidGatewayID, "test_setting", "test_value")
		assert.Error(t, err, "Should fail to create override with invalid software gateway ID")

		// Template setting must reference valid template
		invalidTemplateID := uuid.New()

		invalidSettingQuery := `
			INSERT INTO {{GatewayConfigTemplateSettings}} (gatewayConfigTemplateId, setting, value)
			VALUES ($1, $2, $3)`

		_, err = pg.Exec(invalidSettingQuery, invalidTemplateID, "test_setting", "test_value")
		assert.Error(t, err, "Should fail to create template setting with invalid template ID")
	})

	t.Run("Database Organization Verification", func(t *testing.T) {
		// Test that software gateway belongs to the correct organization
		// This simulates the verification logic in the handlers

		verifyQuery := `
			SELECT 1 FROM {{SoftwareGateway}}
			WHERE Id = $1 AND OrganizationId = $2 AND IsDeleted = false`

		var existsResult struct {
			Exists int `db:"?column?"`
		}

		// Correct organization should succeed
		err := pg.QueryRowStruct(&existsResult, verifyQuery, testSoftwareGatewayID, testOrg1ID)
		require.NoError(t, err, "Should find gateway in correct organization")
		assert.Equal(t, 1, existsResult.Exists)

		// Wrong organization should fail
		err = pg.QueryRowStruct(&existsResult, verifyQuery, testSoftwareGatewayID, corneliusOrgID)
		assert.Error(t, err, "Should not find gateway in wrong organization")
	})

	t.Run("Database Legacy Organization Lookup", func(t *testing.T) {
		// Test the legacy function that looks up organization by software gateway ID
		lookupQuery := `
			SELECT OrganizationId 
			FROM {{SoftwareGateway}}
			WHERE Id = $1 AND IsDeleted = false`

		var orgResult struct {
			OrganizationId uuid.UUID `db:"organizationid"`
		}
		err := pg.QueryRowStruct(&orgResult, lookupQuery, testSoftwareGatewayID)
		require.NoError(t, err, "Should be able to lookup organization by gateway ID")
		assert.Equal(t, testOrg1ID, orgResult.OrganizationId, "Should return correct organization ID")

		// Test with non-existent gateway
		invalidGatewayID := uuid.New()
		err = pg.QueryRowStruct(&orgResult, lookupQuery, invalidGatewayID)
		assert.Error(t, err, "Should fail for non-existent gateway")
	})
}

// TestSoftwareGatewayConfig_DataIntegrity_Integration tests data integrity across related entities
func TestSoftwareGatewayConfig_DataIntegrity_Integration(t *testing.T) {
	t.Parallel()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	connections := connect.NewConnections(ctx)
	defer connections.Close()
	pg := connections.Postgres

	t.Run("Template and Settings Relationship", func(t *testing.T) {
		// Create a template and verify default settings are created
		testOrg1ID := uuid.MustParse("550e8400-e29b-41d4-a716-************")
		templateID := uuid.New()

		// Create template
		createTemplateQuery := `
			INSERT INTO {{GatewayConfigTemplate}} (Id, Name, OrganizationId, Description, IsDeleted, CreatedAt, UpdatedAt)
			VALUES ($1, $2, $3, $4, false, NOW(), NOW())`

		_, err := pg.Exec(createTemplateQuery, templateID, "Integrity Test Template", testOrg1ID, "Test template")
		require.NoError(t, err, "Should be able to create template")

		// Create default settings for this template from base settings
		createSettingsQuery := `
			INSERT INTO {{GatewayConfigTemplateSettings}} (gatewayConfigTemplateId, setting, value)
			SELECT $1, Setting, DefaultValue
			FROM {{GatewayConfigTemplateBaseSettings}}
			WHERE IsDeleted = false
			LIMIT 5` // Just a few for testing

		_, err = pg.Exec(createSettingsQuery, templateID)
		require.NoError(t, err, "Should be able to create default settings")

		// Verify settings were created
		countQuery := `SELECT COUNT(*) as count FROM {{GatewayConfigTemplateSettings}} WHERE gatewayConfigTemplateId = $1`
		var countResult struct {
			Count int `db:"count"`
		}
		err = pg.QueryRowStruct(&countResult, countQuery, templateID)
		require.NoError(t, err, "Should be able to count settings")
		assert.Greater(t, countResult.Count, 0, "Should have created some settings")

		// Clean up
		deleteSettingsQuery := `DELETE FROM {{GatewayConfigTemplateSettings}} WHERE gatewayConfigTemplateId = $1`
		_, err = pg.Exec(deleteSettingsQuery, templateID)
		require.NoError(t, err, "Should be able to delete settings")

		deleteTemplateQuery := `DELETE FROM {{GatewayConfigTemplate}} WHERE Id = $1`
		_, err = pg.Exec(deleteTemplateQuery, templateID)
		require.NoError(t, err, "Should be able to delete template")
	})

	t.Run("Gateway and Override Relationship", func(t *testing.T) {
		// Verify that overrides exist for the test gateway
		testSoftwareGatewayID := uuid.MustParse("550e8400-e29b-41d4-a716-************") // Test Gateway 1

		// Check if gateway exists
		gatewayQuery := `SELECT 1 FROM {{SoftwareGateway}} WHERE Id = $1 AND IsDeleted = false`
		var gatewayResult struct {
			Exists int `db:"?column?"`
		}
		err := pg.QueryRowStruct(&gatewayResult, gatewayQuery, testSoftwareGatewayID)
		require.NoError(t, err, "Test gateway should exist")

		// Count existing overrides
		countQuery := `SELECT COUNT(*) as count FROM {{GatewayConfigTemplateSettingOverrides}} WHERE softwareGatewayId = $1`
		var countResult struct {
			Count int `db:"count"`
		}
		err = pg.QueryRowStruct(&countResult, countQuery, testSoftwareGatewayID)
		require.NoError(t, err, "Should be able to count overrides")

		// The dev.sql file creates some overrides for this gateway
		assert.GreaterOrEqual(t, countResult.Count, 2, "Should have overrides from dev.sql")

		// Verify override values match what's in dev.sql
		overrideQuery := `
			SELECT value FROM {{GatewayConfigTemplateSettingOverrides}} 
			WHERE softwareGatewayId = $1 AND setting = $2`

		var overrideResult struct {
			Value string `db:"value"`
		}
		err = pg.QueryRowStruct(&overrideResult, overrideQuery, testSoftwareGatewayID, "log_level")
		require.NoError(t, err, "Should find log_level override")
		assert.Equal(t, "debug", overrideResult.Value, "Log level should be set to debug in dev.sql")
	})

	t.Run("Base Settings Coverage", func(t *testing.T) {
		// Verify that all the base settings from data.sql exist and have correct formats
		expectedSettings := []string{
			"log_level",
			"log_filename",
			"device_state_send_frequency_seconds",
			"channel_state_send_frequency_milliseconds",
		}

		for _, setting := range expectedSettings {
			query := `SELECT 1 FROM {{GatewayConfigTemplateBaseSettings}} WHERE Setting = $1 AND IsDeleted = false`
			var existsResult struct {
				Exists int `db:"?column?"`
			}
			err := pg.QueryRowStruct(&existsResult, query, setting)
			require.NoError(t, err, "Base setting %s should exist", setting)
			assert.Equal(t, 1, existsResult.Exists)
		}

		// Verify that base settings have valid JSON format
		formatQuery := `
			SELECT COUNT(*) FROM {{GatewayConfigTemplateBaseSettings}}
			WHERE IsDeleted = false AND NOT (Format::text ~ '^{.*}$')`

		var formatResult struct {
			Count int `db:"count"`
		}
		err := pg.QueryRowStruct(&formatResult, formatQuery)
		require.NoError(t, err, "Should be able to check format validity")
		assert.Equal(t, 0, formatResult.Count, "All base settings should have valid JSON format")
	})
}

// TestSoftwareGatewayConfig_Performance_Integration tests performance characteristics with real data
func TestSoftwareGatewayConfig_Performance_Integration(t *testing.T) {
	t.Parallel()

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	connections := connect.NewConnections(ctx)
	defer connections.Close()
	pg := connections.Postgres

	t.Run("Bulk Operations Performance", func(t *testing.T) {
		testOrg1ID := uuid.MustParse("550e8400-e29b-41d4-a716-************")
		templateID := uuid.New()

		// Create a template for bulk testing
		createTemplateQuery := `
			INSERT INTO {{GatewayConfigTemplate}} (Id, Name, OrganizationId, Description, IsDeleted, CreatedAt, UpdatedAt)
			VALUES ($1, $2, $3, $4, false, NOW(), NOW())`

		_, err := pg.Exec(createTemplateQuery, templateID, "Performance Test Template", testOrg1ID, "Performance test")
		require.NoError(t, err, "Should be able to create template")

		// Test bulk insert performance
		start := time.Now()

		// Insert settings using existing base settings (to avoid foreign key constraint violations)
		// Use DISTINCT to prevent duplicates and limit based on available base settings
		insertQuery := `
			INSERT INTO {{GatewayConfigTemplateSettings}} (gatewayConfigTemplateId, setting, value)
			SELECT DISTINCT $1::uuid, Setting, CONCAT('perf_', Setting, '_', EXTRACT(EPOCH FROM NOW())::bigint)
			FROM {{GatewayConfigTemplateBaseSettings}} 
			WHERE IsDeleted = false 
			ORDER BY Setting
			LIMIT 100` // Insert up to 100 unique settings at once

		_, err = pg.Exec(insertQuery, templateID)
		require.NoError(t, err, "Should be able to insert settings batch")

		insertDuration := time.Since(start)
		t.Logf("Bulk insert took: %v", insertDuration)
		assert.Less(t, insertDuration, 5*time.Second, "Bulk insert should complete within 5 seconds")

		// Test bulk select performance
		start = time.Now()

		var settings []config.GatewayConfigTemplateSetting
		selectQuery := `
			SELECT gatewayConfigTemplateId, setting, value
			FROM {{GatewayConfigTemplateSettings}}
			WHERE gatewayConfigTemplateId = $1
			ORDER BY setting`

		err = pg.QueryGenericSlice(&settings, selectQuery, templateID)
		require.NoError(t, err, "Should be able to select all settings")

		selectDuration := time.Since(start)
		t.Logf("Bulk select of %d settings took: %v", len(settings), selectDuration)
		assert.Less(t, selectDuration, 1*time.Second, "Bulk select should complete within 1 second")
		assert.Greater(t, len(settings), 10, "Should retrieve multiple settings for performance testing")

		// Clean up
		deleteQuery := `DELETE FROM {{GatewayConfigTemplateSettings}} WHERE gatewayConfigTemplateId = $1`
		_, err = pg.Exec(deleteQuery, templateID)
		require.NoError(t, err, "Should be able to delete settings")

		deleteTemplateQuery := `DELETE FROM {{GatewayConfigTemplate}} WHERE Id = $1`
		_, err = pg.Exec(deleteTemplateQuery, templateID)
		require.NoError(t, err, "Should be able to delete template")
	})

	t.Run("Index Performance", func(t *testing.T) {
		// Test that queries use indexes efficiently
		// This is more of a smoke test - in production you'd use EXPLAIN ANALYZE

		testSoftwareGatewayID := uuid.MustParse("550e8400-e29b-41d4-a716-************") // Test Gateway 1

		start := time.Now()

		// This query should use the primary key index
		var overrides []config.GatewayConfigTemplateSettingOverride
		fastQuery := `
			SELECT softwareGatewayId, setting, value
			FROM {{GatewayConfigTemplateSettingOverrides}}
			WHERE softwareGatewayId = $1`

		err := pg.QueryGenericSlice(&overrides, fastQuery, testSoftwareGatewayID)
		require.NoError(t, err, "Should be able to query overrides by gateway ID")

		queryDuration := time.Since(start)
		t.Logf("Query by gateway ID took: %v", queryDuration)
		assert.Less(t, queryDuration, 100*time.Millisecond, "Query should be fast with proper indexing")
	})
}
