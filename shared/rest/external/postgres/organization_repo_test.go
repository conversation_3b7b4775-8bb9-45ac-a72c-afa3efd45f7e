package postgres

import (
	"database/sql"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	mocks "synapse-its.com/shared/mocks"
	"synapse-its.com/shared/rest/domain/organization"
)

// Fake SQL result for testing
type fakeSQLResult struct{}

func (f *fakeSQLResult) LastInsertId() (int64, error) {
	return 1, nil
}

func (f *fakeSQLResult) RowsAffected() (int64, error) {
	return 1, nil
}

func TestNewOrganizationRepository(t *testing.T) {
	t.Parallel()

	// Arrange
	fakeDB := &mocks.FakeDBExecutor{}

	// Act
	repo := NewOrganizationRepository(fakeDB)

	// Assert
	assert.NotNil(t, repo)
	assert.Implements(t, (*organization.Repository)(nil), repo)
}

func TestOrganizationRepository_FindUsersByOrganizationID(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		organizationID uuid.UUID
		setupDB        func(*mocks.FakeDBExecutor)
		expectedResult []*organization.Membership
		expectedError  error
	}{
		{
			name:           "successful retrieval",
			organizationID: uuid.New(),
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					queryResults := []membershipQueryResult{
						{
							ID:             uuid.New(),
							OrganizationID: uuid.New(),
							UserID:         uuid.New(),
							FirstName:      sql.NullString{String: "John", Valid: true},
							LastName:       sql.NullString{String: "Doe", Valid: true},
							UserName:       sql.NullString{String: "johndoe", Valid: true},
							Email:          sql.NullString{String: "<EMAIL>", Valid: true},
							AuthMethod:     "USERNAME_PASSWORD",
							OrgRole:        sql.NullString{String: "Admin", Valid: true},
							LastLogin:      nil,
						},
					}
					if results, ok := dest.(*[]membershipQueryResult); ok {
						*results = queryResults
					}
					return nil
				}
			},
			expectedResult: []*organization.Membership{
				{
					ID:             uuid.UUID{},
					UserID:         uuid.UUID{},
					OrganizationID: uuid.UUID{},
					FirstName:      "John",
					LastName:       "Doe",
					UserName:       "johndoe",
					Email:          "<EMAIL>",
					AuthMethod:     "USERNAME_PASSWORD",
					OrgRole:        "Admin",
					LastLogin:      nil,
				},
			},
			expectedError: nil,
		},
		{
			name:           "database error",
			organizationID: uuid.New(),
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection error")
				}
			},
			expectedResult: nil,
			expectedError:  errors.New("database connection error"),
		},
		{
			name:           "empty result",
			organizationID: uuid.New(),
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					if results, ok := dest.(*[]membershipQueryResult); ok {
						*results = []membershipQueryResult{}
					}
					return nil
				}
			},
			expectedResult: []*organization.Membership{},
			expectedError:  nil,
		},
		{
			name:           "null string fields",
			organizationID: uuid.New(),
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					queryResults := []membershipQueryResult{
						{
							ID:             uuid.New(),
							OrganizationID: uuid.New(),
							UserID:         uuid.New(),
							FirstName:      sql.NullString{String: "", Valid: false},
							LastName:       sql.NullString{String: "", Valid: false},
							UserName:       sql.NullString{String: "", Valid: false},
							Email:          sql.NullString{String: "", Valid: false},
							AuthMethod:     "USERNAME_PASSWORD",
							OrgRole:        sql.NullString{String: "", Valid: false},
							LastLogin:      nil,
						},
					}
					if results, ok := dest.(*[]membershipQueryResult); ok {
						*results = queryResults
					}
					return nil
				}
			},
			expectedResult: []*organization.Membership{
				{
					ID:             uuid.UUID{},
					UserID:         uuid.UUID{},
					OrganizationID: uuid.UUID{},
					FirstName:      "",
					LastName:       "",
					UserName:       "",
					Email:          "",
					AuthMethod:     "USERNAME_PASSWORD",
					OrgRole:        "",
					LastLogin:      nil,
				},
			},
			expectedError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			fakeDB := &mocks.FakeDBExecutor{}
			if tt.setupDB != nil {
				tt.setupDB(fakeDB)
			}

			storage := &organizationRepository{db: fakeDB}

			// Act
			result, err := storage.FindUsersByOrganizationID(tt.organizationID)

			// Assert
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				// Compare content instead of exact objects since UUIDs are generated dynamically
				if tt.expectedResult != nil {
					assert.Len(t, result, len(tt.expectedResult))
					for i, expected := range tt.expectedResult {
						if i < len(result) {
							assert.Equal(t, expected.FirstName, result[i].FirstName)
							assert.Equal(t, expected.LastName, result[i].LastName)
							assert.Equal(t, expected.UserName, result[i].UserName)
							assert.Equal(t, expected.Email, result[i].Email)
							assert.Equal(t, expected.AuthMethod, result[i].AuthMethod)
							assert.Equal(t, expected.OrgRole, result[i].OrgRole)
						}
					}
				} else {
					assert.Nil(t, result)
				}
			}
		})
	}
}

func TestOrganizationRepository_UpdateUserRoleInOrganization(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		userID         uuid.UUID
		organizationID uuid.UUID
		roleID         uuid.UUID
		setupDB        func(*mocks.FakeDBExecutor)
		expectedError  error
	}{
		{
			name:           "successful update",
			userID:         uuid.New(),
			organizationID: uuid.New(),
			roleID:         uuid.New(),
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &fakeSQLResult{}, nil
				}
			},
			expectedError: nil,
		},
		{
			name:           "database error",
			userID:         uuid.New(),
			organizationID: uuid.New(),
			roleID:         uuid.New(),
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("database connection error")
				}
			},
			expectedError: errors.New("database connection error"),
		},
		{
			name:           "rows affected error",
			userID:         uuid.New(),
			organizationID: uuid.New(),
			roleID:         uuid.New(),
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &errorSQLResult{}, nil
				}
			},
			expectedError: errors.New("rows affected error"),
		},
		{
			name:           "no rows affected",
			userID:         uuid.New(),
			organizationID: uuid.New(),
			roleID:         uuid.New(),
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					// Create a custom result that returns 0 rows affected
					return &customSQLResult{rowsAffected: 0}, nil
				}
			},
			expectedError: organization.ErrUserNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			fakeDB := &mocks.FakeDBExecutor{}
			if tt.setupDB != nil {
				tt.setupDB(fakeDB)
			}

			storage := &organizationRepository{db: fakeDB}

			// Act
			err := storage.UpdateUserRoleInOrganization(tt.userID, tt.organizationID, tt.roleID)

			// Assert
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Custom SQL result for testing zero rows affected
type customSQLResult struct {
	rowsAffected int64
}

func (c *customSQLResult) LastInsertId() (int64, error) {
	return 0, nil
}

func (c *customSQLResult) RowsAffected() (int64, error) {
	return c.rowsAffected, nil
}

// Custom SQL result for testing RowsAffected error
type errorSQLResult struct{}

func (e *errorSQLResult) LastInsertId() (int64, error) {
	return 0, nil
}

func (e *errorSQLResult) RowsAffected() (int64, error) {
	return 0, errors.New("rows affected error")
}

func TestOrganizationRepository_DeleteUserFromOrganization(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		userID         uuid.UUID
		organizationID uuid.UUID
		setupDB        func(*mocks.FakeDBExecutor)
		expectedError  error
	}{
		{
			name:           "successful deletion",
			userID:         uuid.New(),
			organizationID: uuid.New(),
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &fakeSQLResult{}, nil
				}
			},
			expectedError: nil,
		},
		{
			name:           "database error",
			userID:         uuid.New(),
			organizationID: uuid.New(),
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("database connection error")
				}
			},
			expectedError: errors.New("database connection error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			fakeDB := &mocks.FakeDBExecutor{}
			if tt.setupDB != nil {
				tt.setupDB(fakeDB)
			}

			storage := &organizationRepository{db: fakeDB}

			// Act
			err := storage.DeleteUserFromOrganization(tt.userID, tt.organizationID)

			// Assert
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
