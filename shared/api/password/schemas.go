package password

const (
	// MinUsernameLength is the minimum length of the user's username.
	MinUsernameLength = 5
	// MaxUsernameLength is the maximum length of the user's username.
	MaxUsernameLength = 128
	// MinUserPasswordLength is the minimum length of the user's password.
	MinUserPasswordLength = 8
	// MaxUserPasswordLength is the maximum length of the user's password.
	MaxUserPasswordLength = 256
)

type Credentials struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type PasswordUpdateRequest struct {
	CurrentPassword string `json:"current_password"`
	NewPassword     string `json:"new_password"`
	ConfirmPassword string `json:"confirm_password"`
}
