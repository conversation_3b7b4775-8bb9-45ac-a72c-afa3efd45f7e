-- ================================================
-- CAT-554: As an Engineer, I need to provide a APS factory reset endpoint
-- ================================================

-- Add permission for APS factory reset
INSERT INTO {{Permission}} (Identifier, PermissionGroupIdentifier, OrgTypeIdentifier, Weight, Name, Description) VALUES
  ('synapse_aps_factory_reset', 'synapse', 'synapse', 6.00, 'Get APS factory reset', 'Grants ability to get factory reset for an APS.'),
  ('org_aps_factory_reset', 'organization', 'municipality', 11.00, 'Get APS factory reset', 'Grants ability to get factory reset for an APS.');

-- Add table to store rate limiting configurations for APS factory reset flood protection
CREATE TABLE {{APSResetConfig}} (
  Id UUID DEFAULT uuid_generate_v4(),
  UserId UUID NOT NULL,
  OrganizationId UUID NOT NULL,
  -- Configuration fields
  ConfigAPSResetSuccessLimit INTEGER NOT NULL DEFAULT 1000,
  ConfigAPSResetSuccessListLength INTEGER NOT NULL DEFAULT 10,
  ConfigAPSResetSuccessListWindowMinutes INTEGER NOT NULL DEFAULT 60,
  -- Metadata fields
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT {{APSResetConfig_PK}} PRIMARY KEY (Id),
  CONSTRAINT {{APSResetConfig_User_FK}} FOREIGN KEY (UserId) REFERENCES {{User}} (Id),
  CONSTRAINT {{APSResetConfig_Organization_FK}} FOREIGN KEY (OrganizationId) REFERENCES {{Organization}} (Id),
  CONSTRAINT {{APSResetConfig_UserOrg_UQ}} UNIQUE (UserId, OrganizationId)
);

-- Add table to store request statistics for APS factory reset flood protection
CREATE TABLE {{APSResetStatistic}} (
  Id UUID DEFAULT uuid_generate_v4(),
  UserId UUID NOT NULL,
  OrganizationId UUID NOT NULL,
  -- Statistics fields
  StatAPSResetCount INTEGER NOT NULL DEFAULT 0,
  StatAPSResetSuccessCount INTEGER NOT NULL DEFAULT 0,
  StatAPSResetSuccessList TIMESTAMP[] NOT NULL DEFAULT '{}',
  -- Metadata fields
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT {{APSResetStatistic_PK}} PRIMARY KEY (Id),
  CONSTRAINT {{APSResetStatistic_User_FK}} FOREIGN KEY (UserId) REFERENCES {{User}} (Id),
  CONSTRAINT {{APSResetStatistic_Organization_FK}} FOREIGN KEY (OrganizationId) REFERENCES {{Organization}} (Id),
  CONSTRAINT {{APSResetStatistic_UserOrg_UQ}} UNIQUE (UserId, OrganizationId)
);

-- Add comments explaining the table purposes
COMMENT ON TABLE {{APSResetConfig}} IS 'Stores rate limiting configurations for APS factory reset flood protection';
COMMENT ON COLUMN {{APSResetConfig}}.ConfigAPSResetSuccessLimit IS 'Lifetime maximum number of successful requests per user (default: 1000)';
COMMENT ON COLUMN {{APSResetConfig}}.ConfigAPSResetSuccessListLength IS 'Maximum number of requests allowed within the time window (default: 10)';
COMMENT ON COLUMN {{APSResetConfig}}.ConfigAPSResetSuccessListWindowMinutes IS 'Time window in minutes for rate limiting (default: 60)';

COMMENT ON TABLE {{APSResetStatistic}} IS 'Stores request statistics for APS factory reset flood protection';
COMMENT ON COLUMN {{APSResetStatistic}}.StatAPSResetCount IS 'Total number of APS reset requests (successful and failed)';
COMMENT ON COLUMN {{APSResetStatistic}}.StatAPSResetSuccessCount IS 'Total number of successful APS reset requests (HTTP 200)';
COMMENT ON COLUMN {{APSResetStatistic}}.StatAPSResetSuccessList IS 'Array of timestamps for successful requests within the time window';