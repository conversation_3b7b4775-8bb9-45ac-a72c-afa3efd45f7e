package softwaregateway

import (
	"net/http"

	"synapse-its.com/shared/logger"
	RestSoftwareGateway "synapse-its.com/shared/rest/onramp/softwaregateway"
	RestSoftwareGatewayConfig "synapse-its.com/shared/rest/onramp/softwaregateway/config"

	"github.com/gorilla/mux"
)

type Handler struct{}

func NewHandler() *Handler {
	return &Handler{}
}

func (h *Handler) RegisterRoutes(router *mux.Router) {
	logger.Info("Registering software gateway routes")
	// Legacy supported endpoints
	// TODO remove these endpoints when GUI is updated to include organizationId in URL
	router.HandleFunc("/softwaregateway/{identifier}/config", RestSoftwareGatewayConfig.GetByIdentifierHandler).Methods(http.MethodGet)
	router.HandleFunc("/softwaregateway/{identifier}/config", RestSoftwareGatewayConfig.BulkUpsertOverridesLegacyHandler).Methods(http.MethodPatch)

	router.HandleFunc("/organization/{organizationId}/softwaregateway", RestSoftwareGateway.CreateHandler).Methods(http.MethodPost)
	router.HandleFunc("/organization/{organizationId}/softwaregateway", RestSoftwareGateway.GetAllHandler).Methods(http.MethodGet)
	router.HandleFunc("/organization/{organizationId}/softwaregateway/{identifier}", RestSoftwareGateway.GetByIdentifierHandler).Methods(http.MethodGet)
	router.HandleFunc("/organization/{organizationId}/softwaregateway/{identifier}", RestSoftwareGateway.UpdateHandler).Methods(http.MethodPatch)
	router.HandleFunc("/organization/{organizationId}/softwaregateway/{identifier}", RestSoftwareGateway.DeleteHandler).Methods(http.MethodDelete)

	// Software Gateway Config (Get with resolved template settings)
	router.HandleFunc("/organization/{organizationId}/softwaregateway/{identifier}/config", RestSoftwareGatewayConfig.GetByIdentifierHandler).Methods(http.MethodGet)

	// TODO: Remove this legacy endpoint when GUI is updated to use the new endpoints
	// Legacy support: Bulk upsert overrides
	router.HandleFunc("/organization/{organizationId}/softwaregateway/{identifier}/config", RestSoftwareGatewayConfig.BulkUpsertOverridesLegacyHandler).Methods(http.MethodPatch)

	// Template Management
	router.HandleFunc("/organization/{organizationId}/softwaregatewayconfig/templates", RestSoftwareGatewayConfig.CreateTemplateHandler).Methods(http.MethodPost)
	router.HandleFunc("/organization/{organizationId}/softwaregatewayconfig/templates", RestSoftwareGatewayConfig.ListTemplatesHandler).Methods(http.MethodGet)
	router.HandleFunc("/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}", RestSoftwareGatewayConfig.GetTemplateHandler).Methods(http.MethodGet)
	router.HandleFunc("/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}", RestSoftwareGatewayConfig.UpdateTemplateHandler).Methods(http.MethodPatch)
	router.HandleFunc("/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}", RestSoftwareGatewayConfig.DeleteTemplateHandler).Methods(http.MethodDelete)

	// Template Settings Management
	router.HandleFunc("/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings", RestSoftwareGatewayConfig.CreateOrUpdateTemplateSettingHandler).Methods(http.MethodPost)
	router.HandleFunc("/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings", RestSoftwareGatewayConfig.GetTemplateSettingsHandler).Methods(http.MethodGet)
	router.HandleFunc("/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings", RestSoftwareGatewayConfig.BulkReplaceTemplateSettingsHandler).Methods(http.MethodPut)
	router.HandleFunc("/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings/{setting}", RestSoftwareGatewayConfig.DeleteTemplateSettingHandler).Methods(http.MethodDelete)

	// Gateway Setting Overrides Management
	router.HandleFunc("/organization/{organizationId}/softwaregateway/{identifier}/config/overrides", RestSoftwareGatewayConfig.CreateOrUpdateOverrideHandler).Methods(http.MethodPost)
	router.HandleFunc("/organization/{organizationId}/softwaregateway/{identifier}/config/overrides", RestSoftwareGatewayConfig.GetOverridesHandler).Methods(http.MethodGet)
	router.HandleFunc("/organization/{organizationId}/softwaregateway/{identifier}/config/overrides", RestSoftwareGatewayConfig.BulkReplaceOverridesHandler).Methods(http.MethodPut)
	router.HandleFunc("/organization/{organizationId}/softwaregateway/{identifier}/config/overrides/bulk", RestSoftwareGatewayConfig.BulkUpsertOverridesHandler).Methods(http.MethodPost)
	router.HandleFunc("/organization/{organizationId}/softwaregateway/{identifier}/config/overrides/{setting}", RestSoftwareGatewayConfig.DeleteOverrideHandler).Methods(http.MethodDelete)

	// Base Settings Management
	// This is synapse only, we only want synapse super admins to be able to use these endpoints
	router.HandleFunc("/softwaregatewayconfig/basesettings", RestSoftwareGatewayConfig.CreateBaseSettingHandler).Methods(http.MethodPost)
	router.HandleFunc("/softwaregatewayconfig/basesettings", RestSoftwareGatewayConfig.ListBaseSettingsHandler).Methods(http.MethodGet)
	router.HandleFunc("/softwaregatewayconfig/basesettings/{setting}", RestSoftwareGatewayConfig.GetBaseSettingHandler).Methods(http.MethodGet)
	router.HandleFunc("/softwaregatewayconfig/basesettings/{setting}", RestSoftwareGatewayConfig.UpdateBaseSettingHandler).Methods(http.MethodPatch)
	router.HandleFunc("/softwaregatewayconfig/basesettings/{setting}", RestSoftwareGatewayConfig.DeleteBaseSettingHandler).Methods(http.MethodDelete)
}
