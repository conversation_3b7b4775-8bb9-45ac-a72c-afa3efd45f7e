package softwaregateway

import (
	"net/http"
	"testing"

	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
)

func TestNewHandler(t *testing.T) {
	handler := NewHandler()

	assert.NotNil(t, handler, "NewHandler should return a non-nil handler")
	assert.IsType(t, &Hand<PERSON>{}, handler, "NewHandler should return a *Handler")
}

func TestHandler_RegisterRoutes(t *testing.T) {
	handler := NewHandler()
	router := mux.NewRouter()

	// Register routes
	handler.RegisterRoutes(router)

	// Test that routes are properly registered by walking through them
	var routes []string
	var methods []string

	err := router.Walk(func(route *mux.Route, router *mux.Router, ancestors []*mux.Route) error {
		template, _ := route.GetPathTemplate()
		methodsSlice, _ := route.GetMethods()

		routes = append(routes, template)
		if len(methodsSlice) > 0 {
			methods = append(methods, methodsSlice[0])
		}
		return nil
	})

	assert.NoError(t, err, "Walking routes should not produce an error")

	// Verify expected routes are registered
	expectedRoutes := []string{
		// Legacy Software Gateway Config
		"/softwaregateway/{identifier}/config",
		// Legacy support: Bulk upsert overrides without organizationId in URL path
		"/softwaregateway/{identifier}/config",
		// Organization-scoped Software Gateway CRUD
		"/organization/{organizationId}/softwaregateway",
		"/organization/{organizationId}/softwaregateway",
		"/organization/{organizationId}/softwaregateway/{identifier}",
		"/organization/{organizationId}/softwaregateway/{identifier}",
		"/organization/{organizationId}/softwaregateway/{identifier}",
		// Organization-scoped Software Gateway Config
		"/organization/{organizationId}/softwaregateway/{identifier}/config",
		"/organization/{organizationId}/softwaregateway/{identifier}/config",
		// Template Management
		"/organization/{organizationId}/softwaregatewayconfig/templates",
		"/organization/{organizationId}/softwaregatewayconfig/templates",
		"/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}",
		"/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}",
		"/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}",
		// Template Settings Management
		"/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings",
		"/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings",
		"/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings",
		"/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings/{setting}",
		// Gateway Setting Overrides Management
		"/organization/{organizationId}/softwaregateway/{identifier}/config/overrides",
		"/organization/{organizationId}/softwaregateway/{identifier}/config/overrides",
		"/organization/{organizationId}/softwaregateway/{identifier}/config/overrides",
		"/organization/{organizationId}/softwaregateway/{identifier}/config/overrides/bulk",
		"/organization/{organizationId}/softwaregateway/{identifier}/config/overrides/{setting}",
		// Base Settings Management
		"/softwaregatewayconfig/basesettings",
		"/softwaregatewayconfig/basesettings",
		"/softwaregatewayconfig/basesettings/{setting}",
		"/softwaregatewayconfig/basesettings/{setting}",
		"/softwaregatewayconfig/basesettings/{setting}",
	}

	expectedMethods := []string{
		// Legacy Software Gateway Config
		http.MethodGet,
		// Legacy support: Bulk upsert overrides without organizationId in URL path
		http.MethodPatch,
		// Organization-scoped Software Gateway CRUD
		http.MethodPost,
		http.MethodGet,
		http.MethodGet,
		http.MethodPatch,
		http.MethodDelete,
		// Organization-scoped Software Gateway Config
		http.MethodGet,
		http.MethodPatch,
		// Template Management
		http.MethodPost,
		http.MethodGet,
		http.MethodGet,
		http.MethodPatch,
		http.MethodDelete,
		// Template Settings Management
		http.MethodPost,
		http.MethodGet,
		http.MethodPut,
		http.MethodDelete,
		// Gateway Setting Overrides Management
		http.MethodPost,
		http.MethodGet,
		http.MethodPut,
		http.MethodPost,
		http.MethodDelete,
		// Base Settings Management
		http.MethodPost,
		http.MethodGet,
		http.MethodGet,
		http.MethodPatch,
		http.MethodDelete,
	}

	assert.Equal(t, len(expectedRoutes), len(routes), "Should register correct number of routes")
	assert.Equal(t, len(expectedMethods), len(methods), "Should register correct number of methods")

	// Check that all expected routes and methods are present
	for i, expectedRoute := range expectedRoutes {
		assert.Contains(t, routes, expectedRoute, "Route %s should be registered", expectedRoute)
		if i < len(methods) {
			assert.Contains(t, methods, expectedMethods[i], "Method %s should be registered", expectedMethods[i])
		}
	}
}
