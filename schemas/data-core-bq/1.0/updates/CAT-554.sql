-- CAT-554: Add APSFactoryResetLogs table for logging APS factory reset requests
-- This script creates a new table to log all APS factory reset requests by users.

CREATE TABLE IF NOT EXISTS {{APSFactoryResetLogs}} (
  user_id            STRING    {% OPTIONS(description="The unique identifier of the user making the request") %},
  organization_id    STRING    {% OPTIONS(description="The unique identifier of the user's organization") %},
  ip_address         STRING    {% OPTIONS(description="The IP address of the user making the request, if available") %},
  challenge_code     STRING    {% OPTIONS(description="The challenge code used for the factory reset request") %},
  success            BOOL      {% OPTIONS(description="Whether the factory reset request was successful") %},
  error_message      STRING    {% OPTIONS(description="Error message if the request failed, NULL if successful") %},
  request_timestamp  TIMESTAMP {% OPTIONS(description="Timestamp when the request was processed") %}
)
{% PARTITION BY DATE(request_timestamp) CLUSTER BY organization_id, user_id %};