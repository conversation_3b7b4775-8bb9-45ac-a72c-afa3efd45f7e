package password

import (
	"encoding/json"
	"io"
	"strings"

	security "synapse-its.com/shared/api/security"
	"synapse-its.com/shared/logger"
)

// =================== //
// PasswordHasher
// =================== //

// PasswordHasher interface for hashing passwords
type PasswordHasher interface {
	HashPassword(password string) string
	ComparePassword(password, hash string) bool
}

func NewPasswordHasher() PasswordHasher {
	return &passwordHasher{}
}

type passwordHasher struct{}

// HashPassword hashes a password using SHA256
func (h *passwordHasher) HashPassword(password string) string {
	return security.CalculateSHA256(password)
}

// ComparePassword compares a password with its hash
func (h *passwordHasher) ComparePassword(password, hash string) bool {
	return security.CalculateSHA256(password) == hash
}

// ====================================================================== //

// ValidateUsername validates username using shared password module
func ValidateUsername(username string) error {
	// Trim whitespace for validation
	trimmed := strings.TrimSpace(username)

	if trimmed == "" {
		return ErrUsernameEmpty
	}

	if len(trimmed) < MinUsernameLength {
		logger.Debugf("username too short. min length (%d), got (%d)", MinUsernameLength, len(trimmed))
		return ErrUsernameTooShort
	}

	if len(trimmed) > MaxUsernameLength {
		logger.Debugf("username too long. max length (%d), got (%d)", MaxUsernameLength, len(trimmed))
		return ErrUsernameTooLong
	}

	return nil
}

// ValidatePassword validates password using shared password module
func ValidatePassword(password string) error {
	// Trim whitespace for validation
	trimmed := strings.TrimSpace(password)

	if trimmed == "" {
		return ErrPasswordEmpty
	}

	if len(trimmed) < MinUserPasswordLength {
		logger.Debugf("password too short. min length (%d), got (%d)", MinUserPasswordLength, len(trimmed))
		return ErrPasswordTooShort
	}

	if len(trimmed) > MaxUserPasswordLength {
		logger.Debugf("password too long. max length (%d), got (%d)", MaxUserPasswordLength, len(trimmed))
		return ErrPasswordTooLong
	}

	return nil
}

// ExtractAndValidateUserNameAndPassword extracts username and password from request body
func ExtractAndValidateUserNameAndPassword(body io.ReadCloser) (*Credentials, error) {
	// Extract username and password from request body
	decoder := json.NewDecoder(body)
	decoder.DisallowUnknownFields()

	var creds Credentials
	if err := decoder.Decode(&creds); err != nil {
		logger.Debugf("error parsing credentials JSON: %v", err)
		return nil, ErrInvalidJSON
	}

	// Trim whitespace for validation
	username := strings.TrimSpace(creds.Username)
	password := strings.TrimSpace(creds.Password)

	// Validate the username and password min/max lengths are satisfied.
	err := ValidateUsername(username)
	if err != nil {
		return nil, err
	}
	err = ValidatePassword(password)
	if err != nil {
		return nil, err
	}

	return &Credentials{
		Username: username,
		Password: password,
	}, nil
}

// ExtractAndValidatePasswordUpdate extracts password from request body
func ExtractAndValidatePasswordUpdate(body io.ReadCloser) (*PasswordUpdateRequest, error) {
	// Extract password from request body
	decoder := json.NewDecoder(body)
	decoder.DisallowUnknownFields()

	var req PasswordUpdateRequest
	if err := decoder.Decode(&req); err != nil {
		logger.Debugf("error parsing password update JSON: %v", err)
		return nil, ErrInvalidJSON
	}

	// Trim whitespace for validation
	currentPassword := strings.TrimSpace(req.CurrentPassword)
	newPassword := strings.TrimSpace(req.NewPassword)
	confirmPassword := strings.TrimSpace(req.ConfirmPassword)

	// Validate CurrentPassword password min/max lengths are satisfied.
	if err := ValidatePassword(currentPassword); err != nil {
		return nil, err
	}

	// Validate new password
	if err := ValidatePassword(newPassword); err != nil {
		return nil, err
	}

	// Validate confirm password
	if err := ValidatePassword(confirmPassword); err != nil {
		return nil, err
	}

	// Validate new password and confirm password match
	if newPassword != confirmPassword {
		return nil, ErrPasswordMismatch
	}
	return &PasswordUpdateRequest{
		CurrentPassword: currentPassword,
		NewPassword:     newPassword,
		ConfirmPassword: confirmPassword,
	}, nil
}
