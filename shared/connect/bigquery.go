package connect

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"os"
	"reflect"
	"regexp"
	"strconv"
	"strings"

	"cloud.google.com/go/bigquery"
	"google.golang.org/api/iterator"
	"google.golang.org/api/option"

	"synapse-its.com/shared/logger"
)

// BigQueryExecutorInterface defines the methods for executing queries in BigQuery.
type BigQueryExecutorInterface interface {
	DatabaseExecutor
	// Get the BigQuery client.
	GetClient() BQClient
	// Get the BigQuery config.
	GetConfig() DatabaseConfig
	// Get the context.
	GetContext() context.Context
}

// BigQueryExecutor provides query execution methods for BigQuery.
type BigQueryExecutor struct {
	Client       BQClient
	Config       DatabaseConfig
	Ctx          context.Context
	InProduction bool
}

// BQRowIterator abstracts the row iterator for testability.
type BQRowIterator interface {
	Next(dst interface{}) error
	Schema() bigquery.Schema
}

// BQRowIteratorAdapter wraps *bigquery.RowIterator to implement BQRowIterator.
type BQRowIteratorAdapter struct {
	*bigquery.RowIterator
}

func (r *BQRowIteratorAdapter) Next(dst interface{}) error {
	return r.RowIterator.Next(dst)
}

func (r *BQRowIteratorAdapter) Schema() bigquery.Schema {
	return r.RowIterator.Schema
}

// BQClient defines the subset of bigquery.Client methods used by BigQueryExecutor.
type BQClient interface {
	Query(q string) BQQuery
	Dataset(id string) *bigquery.Dataset
	Close() error
}

// BQQuery defines the subset of bigquery.Query methods/fields used by BigQueryExecutor.
type BQQuery interface {
	SetParameters(params []bigquery.QueryParameter)
	SetUseStandardSQL(use bool)
	Read(ctx context.Context) (BQRowIterator, error)
}

// BQClientAdapter wraps a *bigquery.Client and implements BQClient.
type BQClientAdapter struct {
	*bigquery.Client
}

func NewBQClientAdapter(client *bigquery.Client) *BQClientAdapter {
	return &BQClientAdapter{Client: client}
}

func (r *BQClientAdapter) Query(q string) BQQuery {
	return &BQQueryAdapter{Query: r.Client.Query(q)}
}

// BQQueryAdapter wraps a *bigquery.Query and implements BQQuery.
type BQQueryAdapter struct {
	*bigquery.Query
}

func (q *BQQueryAdapter) SetParameters(params []bigquery.QueryParameter) {
	q.Query.Parameters = params
}

func (q *BQQueryAdapter) SetUseStandardSQL(use bool) {
	q.Query.UseStandardSQL = use
}

func (q *BQQueryAdapter) Read(ctx context.Context) (BQRowIterator, error) {
	it, err := q.Query.Read(ctx)
	return &BQRowIteratorAdapter{RowIterator: it}, err
}

// BQClientFactory is a function type for creating BQClient instances.
type BQClientFactory func(ctx context.Context, projectID string, opts ...option.ClientOption) (BQClient, error)

// DefaultBQClientFactory creates a real BigQuery client and wraps it in a BQClientAdapter.
func DefaultBQClientFactory(ctx context.Context, projectID string, opts ...option.ClientOption) (BQClient, error) {
	client, err := bigquery.NewClient(ctx, projectID, opts...)
	if err != nil {
		return nil, err
	}
	return NewBQClientAdapter(client), nil
}

// translatePlaceholders converts PostgreSQL-style positional placeholders ($1, $2, ...) into
// BigQuery named parameters (@p0, @p1, ...).
// TODO: Throw an error if '$1'... appears inside a string.
func translatePlaceholders(query string) string {
	// Regular expression to match PostgreSQL-style placeholders.
	// The regex guarantees that numStr is a valid integer string, removing the error check is safe and simplifies the code.
	re := regexp.MustCompile(`\$(\d+)`)
	translated := re.ReplaceAllStringFunc(query, func(match string) string {
		// Remove the '$' and convert the number to an integer.
		numStr := match[1:]
		num, _ := strconv.Atoi(numStr) // Ignore error since numStr is guaranteed to be digits.
		// BigQuery parameter names: @p0 corresponds to $1.
		return fmt.Sprintf("@p%d", num-1)
	})
	return translated
}

// bigqueryResult is a dummy result type that implements sql.Result.
type bigqueryResult struct {
	rowsAffected int64
}

// LastInsertId is not supported in BigQuery.
func (r bigqueryResult) LastInsertId() (int64, error) {
	return 0, fmt.Errorf("LastInsertId is not supported by BigQuery")
}

// RowsAffected returns the number of rows affected.
func (r bigqueryResult) RowsAffected() (int64, error) {
	return r.rowsAffected, nil
}

// This function initializes a BigQuery connection using the provided context
// and an optional config. If config is nil, values are read from environment variables.
var BigQuery = func(ctx context.Context, config *DatabaseConfig, factory BQClientFactory) (*BigQueryExecutor, error) {
	projectID := os.Getenv("GCP_PROJECT_ID")
	if projectID == "" {
		return nil, fmt.Errorf("GCP_PROJECT_ID environment variable not set")
	}

	var clientOptions []option.ClientOption
	var inProduction bool = false

	// Check for BigQuery emulator.
	emulatorEndpoint := os.Getenv("BIGQUERY_EMULATOR_HOST")
	if emulatorEndpoint != "" {
		if !strings.HasPrefix(emulatorEndpoint, "http://") && !strings.HasPrefix(emulatorEndpoint, "https://") {
			emulatorEndpoint = "http://" + emulatorEndpoint
		}
		clientOptions = append(clientOptions, option.WithEndpoint(emulatorEndpoint), option.WithoutAuthentication())

		logger.Debugf("BigQuery: Using emulator at %s (InProduction: %t)", emulatorEndpoint, inProduction)
	} else {

		logger.Debugf("BigQuery: Using production mode (InProduction: %t)", true)
		inProduction = true

		// Check for service account credentials.
		credFile := os.Getenv("GOOGLE_APPLICATION_CREDENTIALS")
		if credFile != "" {
			clientOptions = append(clientOptions, option.WithCredentialsFile(credFile))
			logger.Debugf("Using credentials from: %s", credFile)
		} else {
			logger.Debugf("No GOOGLE_APPLICATION_CREDENTIALS set; using ADC.")
		}
	}

	if factory == nil {
		factory = DefaultBQClientFactory
	}
	client, err := factory(ctx, projectID, clientOptions...)
	if err != nil {
		return nil, fmt.Errorf("failed to create BigQuery client: %w", err)
	}

	// Default config values.
	cfg := DatabaseConfig{
		DBName:      os.Getenv("BIGQUERY_DATASET"),
		Environment: os.Getenv("ENVIRONMENT"),
		Namespace:   os.Getenv("BIGQUERY_NAMESPACE"),
	}

	// Apply overrides from the provided config (if any).
	if config != nil {
		if config.DBName != "" {
			cfg.DBName = config.DBName
		}
		if config.Environment != "" {
			cfg.Environment = config.Environment
		}
		if config.Namespace != "" {
			cfg.Namespace = config.Namespace
		}
	}

	return &BigQueryExecutor{Client: client, Config: cfg, Ctx: ctx, InProduction: inProduction}, nil
}

// Exec executes a query in BigQuery with parameter support.
// This version supports queries written with PostgreSQL-style placeholders ($1, $2, ...)
// by translating them into BigQuery's named parameters (@p0, @p1, ...).
func (bq *BigQueryExecutor) Exec(query string, args ...interface{}) (sql.Result, error) {
	// Do all the replacements in the query.
	query = bq.doReplacements(query)

	q := bq.Client.Query(query)

	// Build query parameters.
	if len(args) > 0 {
		var params []bigquery.QueryParameter
		for i, arg := range args {
			params = append(params, bigquery.QueryParameter{
				Name:  fmt.Sprintf("p%d", i),
				Value: arg,
			})
		}
		q.SetParameters(params)
	}

	// Execute the query.
	_, err := q.Read(bq.Ctx)
	if err != nil {
		// BigQuery returns errors for "table is already created" if using "CREATE TABLE IF NOT EXISTS"
		// Schema-migrations tests will hit this multiple times and function should not return with an error
		// If a duplicate "CREATE TABLE IF NOT EXISTS" is run outside of testing, log error.
		if strings.Contains(err.Error(), "table is already created") {
			logger.Errorf("repeated run of `create table if not exists` %v", query)
		} else {
			return nil, err
		}
	}

	// Since BigQuery doesn't provide rows affected, we return 0.
	return bigqueryResult{rowsAffected: 0}, nil
}

// ExecMultiple will break the `query` string into multiple individual queries.
// Notice that this function does not support parameters in the queries.
func (bq *BigQueryExecutor) ExecMultiple(queries string) error {
	// Split the SQL file/string into individual statements.
	// SplitSQLStatements already trims and skips empty/whitespace statements, so no further check is needed here.
	statements := SplitSQLStatements(queries)
	for _, stmt := range statements {
		// Use the existing Exec function to run each statement.
		if _, err := bq.Exec(stmt); err != nil {
			return fmt.Errorf("error executing statement %q: %w", stmt, err)
		}
	}
	return nil
}

// QueryGeneric executes a query in BigQuery with support for parameters
// and returns the results as a slice of maps.
// This version supports queries written with PostgreSQL-style placeholders ($1, $2, ...)
// by translating them into BigQuery's named parameters (@p0, @p1, ...).
func (bq *BigQueryExecutor) QueryGeneric(query string, args ...interface{}) ([]map[string]interface{}, error) {
	// Translate placeholders and substitute namespace.
	query = bq.doReplacements(query)
	q := bq.Client.Query(query)
	q.SetUseStandardSQL(true) // Force Standard SQL mode

	// Convert provided args to BigQuery query parameters.
	if len(args) > 0 {
		var params []bigquery.QueryParameter
		for i, arg := range args {
			params = append(params, bigquery.QueryParameter{
				Name:  fmt.Sprintf("p%d", i),
				Value: arg,
			})
		}
		q.SetParameters(params)
	}

	it, err := q.Read(bq.Ctx)
	if err != nil {
		return nil, err
	}

	// The Results array.
	var results []map[string]interface{}

	// Check if schema is empty.
	if len(it.Schema()) == 0 {
		// Fallback: read rows directly into a map.
		for {
			rowMap := make(map[string]bigquery.Value)
			err := it.Next(&rowMap)
			if err == iterator.Done {
				break
			}
			if err != nil {
				return nil, err
			}
			// Convert map[string]bigquery.Value to map[string]interface{}
			converted := make(map[string]interface{})
			for k, v := range rowMap {
				converted[k] = v
			}
			results = append(results, converted)
		}
	} else {
		// Use the schema-based approach.
		schema := it.Schema()
		for {
			var row []bigquery.Value
			err := it.Next(&row)
			if err == iterator.Done {
				break
			}
			if err != nil {
				return nil, err
			}
			if len(row) != len(schema) {
				return nil, fmt.Errorf("row length mismatch with schema")
			}
			rowMap := make(map[string]interface{})
			for i, fieldSchema := range schema {
				rowMap[fieldSchema.Name] = row[i]
			}
			results = append(results, rowMap)
		}
	}

	// If no rows were returned, signal that as an error.
	if len(results) == 0 {
		return nil, sql.ErrNoRows
	}
	return results, nil
}

// QueryRow executes a query in BigQuery with support for parameters
// and returns a single row as a map.
// This version supports queries written with PostgreSQL-style placeholders ($1, $2, ...)
// by translating them into BigQuery's named parameters (@p0, @p1, ...).
func (bq *BigQueryExecutor) QueryRow(query string, args ...interface{}) (map[string]interface{}, error) {
	// Translate positional placeholders and substitute namespace.
	query = bq.doReplacements(query)
	q := bq.Client.Query(query)
	q.SetUseStandardSQL(true) // Force Standard SQL mode

	// Convert provided args to BigQuery query parameters.
	if len(args) > 0 {
		var params []bigquery.QueryParameter
		for i, arg := range args {
			params = append(params, bigquery.QueryParameter{
				Name:  fmt.Sprintf("p%d", i),
				Value: arg,
			})
		}
		q.SetParameters(params)
	}

	it, err := q.Read(bq.Ctx)
	if err != nil {
		return nil, err
	}

	// If the schema is empty, fall back to reading a row directly into a map.
	if len(it.Schema()) == 0 {
		rowMap := make(map[string]bigquery.Value)
		err = it.Next(&rowMap)
		if err == iterator.Done {
			return nil, sql.ErrNoRows
		}
		if err != nil {
			return nil, err
		}
		converted := make(map[string]interface{})
		for k, v := range rowMap {
			converted[k] = v
		}
		return converted, nil
	}

	// Otherwise, use the schema-based approach.
	schema := it.Schema()
	var row []bigquery.Value
	err = it.Next(&row)
	if err == iterator.Done {
		return nil, sql.ErrNoRows
	}
	if err != nil {
		return nil, err
	}
	if len(row) != len(schema) {
		return nil, fmt.Errorf("row length mismatch with schema")
	}
	rowMap := make(map[string]interface{})
	for i, fieldSchema := range schema {
		rowMap[fieldSchema.Name] = row[i]
	}
	return rowMap, nil
}

// QueryRowStruct executes a query and scans the first row into dest.
// dest must be a pointer to a struct. The first row is scanned into the struct,
// mapping column names to struct fields using the "db" tag if present (or the field name otherwise).
// This version supports queries written with PostgreSQL-style placeholders ($1, $2, ...)
// by translating them into BigQuery's named parameters (@p0, @p1, ...).
func (bq *BigQueryExecutor) QueryRowStruct(dest interface{}, query string, args ...interface{}) error {
	// Ensure dest is a non-nil pointer.
	destVal := reflect.ValueOf(dest)
	if destVal.Kind() != reflect.Ptr || destVal.IsNil() {
		return errors.New("dest must be a non-nil pointer")
	}

	// Translate placeholders and substitute namespace in the query.
	query = bq.doReplacements(query)

	// Set the query.
	q := bq.Client.Query(query)
	q.SetUseStandardSQL(true)

	// Format arguments.
	if len(args) > 0 {
		var params []bigquery.QueryParameter
		for i, arg := range args {
			params = append(params, bigquery.QueryParameter{
				Name:  fmt.Sprintf("p%d", i),
				Value: arg,
			})
		}
		q.SetParameters(params)
	}

	// Execute the query.
	it, err := q.Read(bq.Ctx)
	if err != nil {
		return err
	}

	// Get column names.
	// TODO: This should be removed, but some of the tests expect it.
	_ = it.Schema()

	// Check if any rows exist.
	if err = it.Next(dest); err == iterator.Done {
		return sql.ErrNoRows
	}
	return err
}

// QueryGenericSlice executes a query and scans all returned rows into dest.
// dest must be a pointer to a slice of structs. Each row is scanned into a new struct,
// mapping column names to struct fields using the "db" tag if present (or the field name otherwise).
// This version supports queries written with PostgreSQL-style placeholders ($1, $2, ...)
// by translating them into BigQuery's named parameters (@p0, @p1, ...).
func (bq *BigQueryExecutor) QueryGenericSlice(dest interface{}, query string, args ...interface{}) error {
	// Ensure dest is a non-nil pointer.
	destVal := reflect.ValueOf(dest)
	if destVal.Kind() != reflect.Ptr || destVal.IsNil() {
		return errors.New("dest must be a non-nil pointer")
	}

	// Ensure dest points to a slice.
	sliceVal := destVal.Elem()
	if sliceVal.Kind() != reflect.Slice {
		return errors.New("dest must point to a slice")
	}

	// Ensure the slice element type is a struct.
	elemType := sliceVal.Type().Elem()
	if elemType.Kind() != reflect.Struct {
		return errors.New("dest slice element must be a struct")
	}

	// Translate placeholders and substitute namespace in the query.
	query = bq.doReplacements(query)

	// Set the query.
	q := bq.Client.Query(query)
	q.SetUseStandardSQL(true)

	// Format arguments.
	if len(args) > 0 {
		var params []bigquery.QueryParameter
		for i, arg := range args {
			params = append(params, bigquery.QueryParameter{
				Name:  fmt.Sprintf("p%d", i),
				Value: arg,
			})
		}
		q.SetParameters(params)
	}

	// Execute the query.
	it, err := q.Read(bq.Ctx)
	if err != nil {
		return err
	}

	// Get column names.
	// TODO: This should be removed, but some of the tests expect it.
	_ = it.Schema()

	// Preallocate 10 to cut down on early resizing at the cost of memory
	results := reflect.MakeSlice(sliceVal.Type(), 0, 10)

	// For each row, scan into a new struct and append struct to results
	for {
		// Allocate a pointer to the element type
		elemPtr := reflect.New(elemType).Interface()

		// Have the iterator to scan the *whole row* into the nested struct
		// (including nested structs, if any)
		err := it.Next(elemPtr)
		if err == iterator.Done {
			break
		}
		if err != nil {
			return err
		}

		// Append the dereferenced struct to the results slice
		results = reflect.Append(results, reflect.ValueOf(elemPtr).Elem())
	}

	sliceVal.Set(results)
	return nil
}

// EscapeIdentifier safely escapes identifiers for BigQuery.
func (bq *BigQueryExecutor) EscapeIdentifier(identifier string) string {
	return fmt.Sprintf("`%s`", strings.ReplaceAll(identifier, "`", "``"))
}

// ReplaceNamespace scans the query for double-curly-brace table markers (e.g., {{results_table}})
// and replaces them with a fully qualified, escaped table name that incorporates the namespace.
// For example, if Config.DBName is "test_dataset" and Config.Namespace is "DEV",
// then "{{results_table}}" becomes "`test_dataset.DEVresults_table`".
func (bq *BigQueryExecutor) ReplaceNamespace(query string) string {
	// Regular expression to match {{table_name}}.
	re := regexp.MustCompile(`\{\{(.*?)\}\}`)
	return re.ReplaceAllStringFunc(query, func(match string) string {
		// Extract the table name inside the braces.
		tableName := strings.TrimSpace(match[2 : len(match)-2])
		// Prepend the namespace if set.
		fullTableName := tableName
		if bq.Config.Namespace != "" {
			fullTableName = CombineTableNamespace(bq.Config.Namespace, tableName)
		}
		// Use EscapeIdentifier to properly quote the table name.
		escapedTable := bq.EscapeIdentifier(fullTableName) // e.g., `DEVresults_table`
		// If a dataset is configured, fully qualify the table name.
		if bq.Config.DBName != "" {
			// Remove the backticks from escapedTable and wrap the full name with backticks.
			return fmt.Sprintf("`%s.%s`", bq.Config.DBName, strings.Trim(escapedTable, "`"))
		}
		return escapedTable
	})
}

// replacePartitionCluster scans the query for a pair of markers (`{%` and
// `%}`) and does the following:
//  1. If in production, it removes the markers but leaves the content
//     inside them.
//  2. If not in production, it removes the markers as well as the content
//     inside them.
//  3. If the markers are not found, it returns the original query.
//
// This is necessary because the BigQuery emulator does not support the
// partitioning and clustering syntax.
func (bq *BigQueryExecutor) replacePartitionCluster(query string) string {
	// Regular expression to match `{%` and `%}`.
	// The (?s) flag makes . match newlines for multiline content
	re := regexp.MustCompile(`(?s)\{\%\s*(.*?)\s*\%\}`)

	if bq.InProduction {
		return re.ReplaceAllString(query, "$1")
	}
	return re.ReplaceAllString(query, "")
}

func (bq *BigQueryExecutor) doReplacements(query string) string {
	query = translatePlaceholders(query)
	query = bq.ReplaceNamespace(query)
	query = bq.replacePartitionCluster(query)
	return query
}

// Close the DB connection.
func (b *BigQueryExecutor) Close() error {
	if b.Client == nil {
		return nil
	}
	return b.Client.Close()
}

// Get the BigQuery client (interface).
func (bq *BigQueryExecutor) GetClient() BQClient {
	return bq.Client
}

// Get the BigQuery config.
func (bq *BigQueryExecutor) GetConfig() DatabaseConfig {
	return bq.Config
}

// Get the context.
func (bq *BigQueryExecutor) GetContext() context.Context {
	return bq.Ctx
}
