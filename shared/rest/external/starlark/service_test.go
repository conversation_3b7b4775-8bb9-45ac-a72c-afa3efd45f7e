package starlark

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"go.starlark.net/starlark"
	"go.starlark.net/syntax"
)

func TestNewFromEnv(t *testing.T) {
	tests := []struct {
		name        string
		envValue    string
		expectError bool
		errorType   error
	}{
		{
			name:        "missing environment variable",
			envValue:    "",
			expectError: true,
			errorType:   ErrMissingFunctionSource,
		},
		{
			name:        "valid base64 encoded environment variable",
			envValue:    "ZGVmIG1haW4oaW5wdXQpOiByZXR1cm4gaW5wdXRbOjotMV0=", // base64 for: def main(input): return input[::-1]
			expectError: false,
			errorType:   nil,
		},
		{
			name:        "malformed base64 environment variable",
			envValue:    "invalid-base64-string!@#",
			expectError: true,
			errorType:   ErrBase64Decode,
		},
		{
			name:        "base64 string with padding issues",
			envValue:    "ZGVmIHByb2Nlc3MoaW5wdXQpOiByZXR1cm4gaW5wdXRbOjotMV0", // missing padding
			expectError: true,
			errorType:   ErrBase64Decode,
		},
		{
			name:        "valid base64 but invalid Starlark code",
			envValue:    "ZGVmIG1haW4oaW5wdXQpOiByZXR1cm4gdW5kZWZpbmVkX3ZhcmlhYmxl", // base64 for: def main(input): return undefined_variable
			expectError: true,
			errorType:   nil, // This will fail later in the process, not at base64 decode
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.envValue != "" {
				t.Setenv("STARLARK_FUNCTION", tt.envValue)
			} else {
				t.Setenv("STARLARK_FUNCTION", "")
			}

			service, err := NewFromEnv()

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, service)
				if tt.errorType != nil {
					assert.ErrorIs(t, err, tt.errorType)
				} else {
					// For the last test case, we expect an error but not a base64 decode error
					assert.NotErrorIs(t, err, ErrBase64Decode)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, service)
			}
		})
	}
}

func TestNewFromSource(t *testing.T) {
	tests := []struct {
		name        string
		source      string
		expectError bool
		errorType   error
	}{
		{
			name:        "valid function",
			source:      `def main(input): return input[::-1]`,
			expectError: false,
		},
		{
			name:        "invalid syntax",
			source:      `def main(input: return input`, // Missing closing parenthesis
			expectError: true,
		},
		{
			name:        "execution error in Starlark code",
			source:      `def main(input): return undefined_variable`,
			expectError: true,
		},
		{
			name:        "no callable functions",
			source:      `x = 42`,
			expectError: true,
		},
		{
			name:        "main function exists but is not callable",
			source:      `main = "not a function"`,
			expectError: true,
			errorType:   ErrInvalidSignature,
		},
		{
			name:        "function with wrong signature",
			source:      `def main(): return "test"`,
			expectError: true,
		},
		{
			name:        "function with multiple parameters",
			source:      `def main(input, extra): return input`,
			expectError: true,
		},
		{
			name:        "function returns non-string during validation",
			source:      `def main(input): return 42`,
			expectError: true,
		},
		{
			name:        "function raises exception during validation",
			source:      `def main(input): return input[999]`,
			expectError: true,
		},
		{
			name:        "valid function with generic name",
			source:      `def my_func(input): return input[::-1]`,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, err := NewFromSource(tt.source)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, service)
				if tt.errorType != nil {
					assert.ErrorIs(t, err, tt.errorType)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, service)
			}
		})
	}
}

func TestRuntimeService_ExchangeCode(t *testing.T) {
	tests := []struct {
		name        string
		source      string
		input       string
		expected    string
		expectError bool
	}{
		{
			name:        "basic transformation",
			source:      `def main(input): return input[::-1]`,
			input:       "hello",
			expected:    "olleh",
			expectError: false,
		},
		{
			name:        "runtime error with empty input",
			source:      `def main(input): return input[1]`,
			input:       "",
			expectError: true,
		},
		{
			name:        "wrong return type during execution",
			source:      `def main(input): return 42 if input == "number" else input`,
			input:       "number",
			expectError: true,
		},
		{
			name:        "function returns list during execution",
			source:      `def main(input): return [input] if input == "list" else input`,
			input:       "list",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, err := NewFromSource(tt.source)
			assert.NoError(t, err)

			result, err := service.ExchangeCode(tt.input)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestValidateCallable(t *testing.T) {
	tests := []struct {
		name        string
		source      string
		expectError bool
	}{
		{
			name:        "valid function",
			source:      `def main(input): return input[::-1]`,
			expectError: false,
		},
		{
			name:        "wrong return type",
			source:      `def main(input): return 42`,
			expectError: true,
		},
		{
			name:        "no parameters",
			source:      `def main(): return "test"`,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			thread := &starlark.Thread{Name: "test_validation"}
			module, err := starlark.ExecFileOptions(&syntax.FileOptions{}, thread, "test.star", tt.source, nil)
			assert.NoError(t, err)

			var callable starlark.Callable
			for _, v := range module {
				if fn, ok := v.(starlark.Callable); ok {
					callable = fn
					break
				}
			}
			assert.NotNil(t, callable, "No callable function found in source")

			err = validateCallable(callable)

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestRuntimeService_ConcurrentExecution(t *testing.T) {
	source := `def main(input): return input + "_suffix"`
	service, err := NewFromSource(source)
	assert.NoError(t, err)

	const numGoroutines = 5
	const input = "hello"
	expected := "hello_suffix"

	results := make(chan string, numGoroutines)
	errors := make(chan error, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func() {
			result, err := service.ExchangeCode(input)
			if err != nil {
				errors <- err
				return
			}
			results <- result
		}()
	}

	successCount := 0
	for i := 0; i < numGoroutines; i++ {
		select {
		case result := <-results:
			if result == expected {
				successCount++
			}
		case err := <-errors:
			t.Errorf("Unexpected error in goroutine: %v", err)
		}
	}

	assert.Equal(t, numGoroutines, successCount)
}
