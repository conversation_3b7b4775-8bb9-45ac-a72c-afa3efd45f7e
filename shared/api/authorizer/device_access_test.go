package authorizer

import (
	"database/sql"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"synapse-its.com/shared/mocks/dbexecutor"
)

func TestUserPermissions_GetAuthorizedDevices(t *testing.T) {
	tests := []struct {
		name                string
		userPermissions     *UserPermissions
		mockDeviceQueries   map[string][]string // query -> device IDs to return
		requiredPermissions []string
		expectedDevices     []string
		expectError         bool
	}{
		{
			name: "organization-level permissions return all org devices",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			mockDeviceQueries: map[string][]string{
				"org_devices": {"device-1", "device-2", "device-3"},
			},
			requiredPermissions: []string{"org_view_devices"},
			expectedDevices:     []string{"device-1", "device-2", "device-3"},
			expectError:         false,
		},
		{
			name: "device group permissions return group devices",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "device_group",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440020",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"},
					},
				},
			},
			mockDeviceQueries: map[string][]string{
				"device_group_devices": {"device-4", "device-5"},
			},
			requiredPermissions: []string{"device_group_view_devices"},
			expectedDevices:     []string{"device-4", "device-5"},
			expectError:         false,
		},
		{
			name: "mixed permissions return combined devices with deduplication",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
					{
						Scope:          "device_group",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440020",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"},
					},
				},
			},
			mockDeviceQueries: map[string][]string{
				"org_devices":          {"device-1", "device-2", "device-3"},
				"device_group_devices": {"device-2", "device-3", "device-4"}, // device-2 and device-3 overlap
			},
			requiredPermissions: []string{"org_view_devices", "device_group_view_devices"},
			expectedDevices:     []string{"device-1", "device-2", "device-3", "device-4"},
			expectError:         false,
		},
		{
			name: "no permissions return empty list",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_users"}, // Wrong permission
					},
				},
			},
			mockDeviceQueries:   map[string][]string{},
			requiredPermissions: []string{"org_view_devices"},
			expectedDevices:     []string{},
			expectError:         false,
		},
		{
			name: "default permissions used when none provided",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"}, // Has default permission
					},
					{
						Scope:          "device_group",
						ScopeID:        "group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"}, // Has default permission
					},
				},
			},
			mockDeviceQueries: map[string][]string{
				"org_devices":          {"device-1", "device-2"},
				"device_group_devices": {"device-3", "device-4"},
			},
			requiredPermissions: []string{}, // Empty - should use defaults: ["org_view_devices", "device_group_view_devices"]
			expectedDevices:     []string{"device-1", "device-2", "device-3", "device-4"},
			expectError:         false,
		},
		{
			name: "database error returns error",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			mockDeviceQueries:   map[string][]string{}, // Will cause error
			requiredPermissions: []string{"org_view_devices"},
			expectedDevices:     []string{},
			expectError:         true,
		},

		{
			name: "mixed permissions including location groups return combined devices",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
					{
						Scope:          "device_group",
						ScopeID:        "group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"},
					},
					{
						Scope:          "location_group",
						ScopeID:        "location-group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"}, // Location groups use device_group permissions
					},
				},
			},
			mockDeviceQueries: map[string][]string{
				"org_devices":            {"device-1", "device-2"},
				"device_group_devices":   {"device-3", "device-4"},
				"location_group_devices": {"device-5", "device-6"},
			},
			requiredPermissions: []string{"org_view_devices", "device_group_view_devices"},
			expectedDevices:     []string{"device-1", "device-2", "device-3", "device-4", "device-5", "device-6"},
			expectError:         false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					// If this is the error test case, return an error
					if tc.name == "database error returns error" {
						return fmt.Errorf("database connection failed")
					}

					// The actual implementation uses a slice of structs with DeviceID field
					deviceIds := dest.(*[]struct {
						DeviceID string `db:"device_id"`
					})

					// JOIN-based query - combine results from all query types
					var combinedDevices []string
					if orgDevices, exists := tc.mockDeviceQueries["org_devices"]; exists {
						combinedDevices = append(combinedDevices, orgDevices...)
					}
					if dgDevices, exists := tc.mockDeviceQueries["device_group_devices"]; exists {
						combinedDevices = append(combinedDevices, dgDevices...)
					}
					if lgDevices, exists := tc.mockDeviceQueries["location_group_devices"]; exists {
						combinedDevices = append(combinedDevices, lgDevices...)
					}
					// Remove duplicates (since query has DISTINCT)
					seen := make(map[string]bool)
					var uniqueDevices []struct {
						DeviceID string `db:"device_id"`
					}
					for _, device := range combinedDevices {
						if !seen[device] {
							seen[device] = true
							uniqueDevices = append(uniqueDevices, struct {
								DeviceID string `db:"device_id"`
							}{DeviceID: device})
						}
					}
					*deviceIds = uniqueDevices

					return nil
				},
			}

			// Execute the function
			devices, err := tc.userPermissions.GetAuthorizedDevices(mockDB, tc.requiredPermissions...)

			// Verify results
			if tc.expectError {
				assert.Error(t, err)
				// Verify the error message format for database errors
				if tc.name == "database error returns error" {
					assert.Contains(t, err.Error(), "failed to get devices for permission scopes")
					assert.Contains(t, err.Error(), "database connection failed")
				}
			} else {
				require.NoError(t, err)
				assert.ElementsMatch(t, tc.expectedDevices, devices)
			}
		})
	}
}

func TestUserPermissions_CanAccessDevice(t *testing.T) {
	tests := []struct {
		name            string
		userPermissions *UserPermissions
		deviceId        string
		mockDeviceInfo  *DeviceInfo
		requiredPerms   []string
		expectedAccess  bool
		expectError     bool
	}{
		{
			name: "org-level permission grants access to org device",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			deviceId: "device-1",
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-1",
				OrganizationID:   "550e8400-e29b-41d4-a716-446655440010",
				DeviceGroupIDs:   []string{"group-1"},
				LocationGroupIDs: []string{},
			},
			requiredPerms:  []string{"org_view_devices"},
			expectedAccess: true,
			expectError:    false,
		},
		{
			name: "device group permission grants access to group device",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "device_group",
						ScopeID:        "group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"},
					},
				},
			},
			deviceId: "device-1",
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-1",
				OrganizationID:   "550e8400-e29b-41d4-a716-446655440010",
				DeviceGroupIDs:   []string{"group-1", "group-2"},
				LocationGroupIDs: []string{},
			},
			requiredPerms:  []string{"device_group_view_devices"},
			expectedAccess: true,
			expectError:    false,
		},
		{
			name: "no matching permissions deny access",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "device_group",
						ScopeID:        "group-2",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"},
					},
				},
			},
			deviceId: "device-1",
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-1",
				OrganizationID:   "550e8400-e29b-41d4-a716-446655440010",
				DeviceGroupIDs:   []string{"group-1"}, // User has access to group-2, not group-1
				LocationGroupIDs: []string{},
			},
			requiredPerms:  []string{"device_group_view_devices"},
			expectedAccess: false,
			expectError:    false,
		},

		{
			name: "default permissions used when required permissions is empty",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"}, // Has default permission
					},
				},
			},
			deviceId: "device-1",
			mockDeviceInfo: &DeviceInfo{
				DeviceID:         "device-1",
				OrganizationID:   "550e8400-e29b-41d4-a716-446655440010",
				DeviceGroupIDs:   []string{"group-1"},
				LocationGroupIDs: []string{},
			},
			requiredPerms:  []string{}, // Empty - should use defaults: ["org_view_devices", "device_group_view_devices"]
			expectedAccess: true,       // Should pass because user has org_view_devices which is in defaults
			expectError:    false,
		},
		{
			name: "getDeviceInfo error returns error",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			deviceId:       "device-1",
			mockDeviceInfo: nil, // Will be ignored since we're returning an error
			requiredPerms:  []string{"org_view_devices"},
			expectedAccess: false,
			expectError:    true,
		},
		{
			name: "device not found returns false without error",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			deviceId:       "non-existent-device",
			mockDeviceInfo: nil, // Will be ignored since we're returning sql.ErrNoRows
			requiredPerms:  []string{"org_view_devices"},
			expectedAccess: false,
			expectError:    false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					// If this is the error test case, return an error
					if tc.name == "getDeviceInfo error returns error" {
						return fmt.Errorf("database connection failed")
					}

					// If this is the device not found test case, return sql.ErrNoRows
					if tc.name == "device not found returns false without error" {
						return sql.ErrNoRows
					}

					deviceInfo := dest.(*DeviceInfo)
					*deviceInfo = *tc.mockDeviceInfo
					return nil
				},
			}

			// Execute the function
			canAccess, err := tc.userPermissions.CanAccessDevice(mockDB, tc.deviceId, tc.requiredPerms...)

			// Verify results
			if tc.expectError {
				assert.Error(t, err)
				// Verify the error message format for getDeviceInfo errors
				if tc.name == "getDeviceInfo error returns error" {
					assert.Contains(t, err.Error(), "failed to get device info")
					assert.Contains(t, err.Error(), "database connection failed")
				}
			} else {
				require.NoError(t, err)
				assert.Equal(t, tc.expectedAccess, canAccess)
			}
		})
	}
}

func TestUserPermissions_GetAccessibleOrganizations(t *testing.T) {
	userPermissions := &UserPermissions{
		UserID: "550e8400-e29b-41d4-a716-446655440001",
		Permissions: []Permission{
			{
				Scope:          "org",
				ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
				OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
				Permissions:    []string{"org_view_devices"},
			},
			{
				Scope:          "device_group",
				ScopeID:        "group-1",
				OrganizationID: "550e8400-e29b-41d4-a716-446655440020",
				Permissions:    []string{"device_group_view_devices"},
			},
			{
				Scope:          "device_group",
				ScopeID:        "group-2",
				OrganizationID: "550e8400-e29b-41d4-a716-446655440010", // Same org as first permission
				Permissions:    []string{"device_group_manage_devices"},
			},
		},
	}

	organizations := userPermissions.GetAccessibleOrganizations()

	expectedOrgs := []string{
		"550e8400-e29b-41d4-a716-446655440010",
		"550e8400-e29b-41d4-a716-446655440020",
	}

	assert.ElementsMatch(t, expectedOrgs, organizations)
}

func TestUserPermissions_GetAccessibleDeviceGroups(t *testing.T) {
	userPermissions := &UserPermissions{
		UserID: "550e8400-e29b-41d4-a716-446655440001",
		Permissions: []Permission{
			{
				Scope:          "org",
				ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
				OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
				Permissions:    []string{"org_view_devices"},
			},
			{
				Scope:          "device_group",
				ScopeID:        "group-1",
				OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
				Permissions:    []string{"device_group_view_devices"},
			},
			{
				Scope:          "device_group",
				ScopeID:        "group-2",
				OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
				Permissions:    []string{"device_group_manage_devices"},
			},
			{
				Scope:          "device_group",
				ScopeID:        "group-3",
				OrganizationID: "550e8400-e29b-41d4-a716-446655440020", // Different org
				Permissions:    []string{"device_group_view_devices"},
			},
		},
	}

	// Test getting device groups for first organization
	deviceGroups := userPermissions.GetAccessibleDeviceGroups("550e8400-e29b-41d4-a716-446655440010")
	expectedGroups := []string{"group-1", "group-2"}
	assert.ElementsMatch(t, expectedGroups, deviceGroups)

	// Test getting device groups for second organization
	deviceGroups2 := userPermissions.GetAccessibleDeviceGroups("550e8400-e29b-41d4-a716-446655440020")
	expectedGroups2 := []string{"group-3"}
	assert.ElementsMatch(t, expectedGroups2, deviceGroups2)

	// Test getting device groups for non-existent organization
	deviceGroups3 := userPermissions.GetAccessibleDeviceGroups("550e8400-e29b-41d4-a716-446655440999")
	assert.Empty(t, deviceGroups3)
}

func TestUserPermissions_GetAccessibleLocationGroups(t *testing.T) {
	userPermissions := &UserPermissions{
		UserID: "550e8400-e29b-41d4-a716-446655440001",
		Permissions: []Permission{
			{
				Scope:          "org",
				ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
				OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
				Permissions:    []string{"org_view_devices"},
			},
			{
				Scope:          "location_group",
				ScopeID:        "location-group-1",
				OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
				Permissions:    []string{"device_group_view_devices"},
			},
			{
				Scope:          "location_group",
				ScopeID:        "location-group-2",
				OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
				Permissions:    []string{"device_group_manage_devices"},
			},
			{
				Scope:          "location_group",
				ScopeID:        "location-group-3",
				OrganizationID: "550e8400-e29b-41d4-a716-446655440020", // Different org
				Permissions:    []string{"device_group_view_devices"},
			},
			{
				Scope:          "location_group",
				ScopeID:        "location-group-4",
				OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
				Permissions:    []string{"org_view_devices"}, // Included, but not used for location group access
			},
		},
	}

	// Test getting location groups for first organization
	locationGroups := userPermissions.GetAccessibleLocationGroups("550e8400-e29b-41d4-a716-446655440010")
	expectedGroups := []string{"location-group-1", "location-group-2", "location-group-4"}
	assert.ElementsMatch(t, expectedGroups, locationGroups)

	// Test getting location groups for second organization
	locationGroups2 := userPermissions.GetAccessibleLocationGroups("550e8400-e29b-41d4-a716-446655440020")
	expectedGroups2 := []string{"location-group-3"}
	assert.ElementsMatch(t, expectedGroups2, locationGroups2)

	// Test getting location groups for non-existent organization
	locationGroups3 := userPermissions.GetAccessibleLocationGroups("550e8400-e29b-41d4-a716-446655440999")
	assert.Empty(t, locationGroups3)
}

func TestUserPermissions_HasPermission(t *testing.T) {
	tests := []struct {
		name            string
		userPermissions *UserPermissions
		permission      string
		expected        bool
	}{
		{
			name: "user has permission in org scope",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices", "org_manage_devices"},
					},
				},
			},
			permission: "org_view_devices",
			expected:   true,
		},
		{
			name: "user has permission in device group scope",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "device_group",
						ScopeID:        "group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices", "device_group_manage_devices"},
					},
				},
			},
			permission: "device_group_manage_devices",
			expected:   true,
		},
		{
			name: "user has permission in multiple scopes",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
					{
						Scope:          "device_group",
						ScopeID:        "group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_manage_devices"},
					},
				},
			},
			permission: "device_group_manage_devices",
			expected:   true,
		},
		{
			name: "user does not have permission",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			permission: "org_manage_users",
			expected:   false,
		},
		{
			name: "empty permissions",
			userPermissions: &UserPermissions{
				UserID:      "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{},
			},
			permission: "org_view_devices",
			expected:   false,
		},
		{
			name: "permission exists in scope but not the one we're looking for",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_users", "org_manage_users"},
					},
				},
			},
			permission: "org_view_devices",
			expected:   false,
		},
		{
			name: "user has permission in location group scope",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "location_group",
						ScopeID:        "location-group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices", "device_group_manage_devices"}, // Location groups use device_group permissions
					},
				},
			},
			permission: "device_group_view_devices",
			expected:   true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.userPermissions.HasPermission(tc.permission)
			assert.Equal(t, tc.expected, result)
		})
	}
}

func TestUserPermissions_HasPermissionInScope(t *testing.T) {
	tests := []struct {
		name            string
		userPermissions *UserPermissions
		permission      string
		scope           string
		scopeID         string
		expected        bool
	}{
		{
			name: "user has permission in specified org scope",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices", "org_manage_devices"},
					},
				},
			},
			permission: "org_view_devices",
			scope:      "org",
			scopeID:    "550e8400-e29b-41d4-a716-446655440010",
			expected:   true,
		},
		{
			name: "user has permission in specified device group scope",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "device_group",
						ScopeID:        "group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices", "device_group_manage_devices"},
					},
				},
			},
			permission: "device_group_manage_devices",
			scope:      "device_group",
			scopeID:    "group-1",
			expected:   true,
		},
		{
			name: "user has permission but in different scope",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
					{
						Scope:          "device_group",
						ScopeID:        "group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_manage_devices"},
					},
				},
			},
			permission: "device_group_manage_devices",
			scope:      "org",
			scopeID:    "550e8400-e29b-41d4-a716-446655440010",
			expected:   false,
		},
		{
			name: "user has permission but in different scope ID",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "device_group",
						ScopeID:        "group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"},
					},
				},
			},
			permission: "device_group_view_devices",
			scope:      "device_group",
			scopeID:    "group-2",
			expected:   false,
		},
		{
			name: "user does not have the permission in the specified scope",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			permission: "org_manage_users",
			scope:      "org",
			scopeID:    "550e8400-e29b-41d4-a716-446655440010",
			expected:   false,
		},
		{
			name: "empty permissions",
			userPermissions: &UserPermissions{
				UserID:      "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{},
			},
			permission: "org_view_devices",
			scope:      "org",
			scopeID:    "550e8400-e29b-41d4-a716-446655440010",
			expected:   false,
		},
		{
			name: "multiple scopes with same permission, checking specific scope",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "device_group",
						ScopeID:        "group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"},
					},
					{
						Scope:          "device_group",
						ScopeID:        "group-2",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"},
					},
				},
			},
			permission: "device_group_view_devices",
			scope:      "device_group",
			scopeID:    "group-2",
			expected:   true,
		},
		{
			name: "user has permission in specified location group scope",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "location_group",
						ScopeID:        "location-group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices", "device_group_manage_devices"}, // Location groups use device_group permissions
					},
				},
			},
			permission: "device_group_manage_devices",
			scope:      "location_group",
			scopeID:    "location-group-1",
			expected:   true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.userPermissions.HasPermissionInScope(tc.permission, tc.scope, tc.scopeID)
			assert.Equal(t, tc.expected, result)
		})
	}
}

func TestUserPermissions_GetPermissionsForScope(t *testing.T) {
	tests := []struct {
		name            string
		userPermissions *UserPermissions
		scope           string
		scopeID         string
		expected        []string
	}{
		{
			name: "get permissions for org scope",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices", "org_manage_devices", "org_delete_devices"},
					},
				},
			},
			scope:    "org",
			scopeID:  "550e8400-e29b-41d4-a716-446655440010",
			expected: []string{"org_view_devices", "org_manage_devices", "org_delete_devices"},
		},
		{
			name: "get permissions for device group scope",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "device_group",
						ScopeID:        "group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices", "device_group_manage_devices"},
					},
				},
			},
			scope:    "device_group",
			scopeID:  "group-1",
			expected: []string{"device_group_view_devices", "device_group_manage_devices"},
		},
		{
			name: "get permissions for specific scope among multiple scopes",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
					{
						Scope:          "device_group",
						ScopeID:        "group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices", "device_group_manage_devices"},
					},
					{
						Scope:          "device_group",
						ScopeID:        "group-2",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"},
					},
				},
			},
			scope:    "device_group",
			scopeID:  "group-1",
			expected: []string{"device_group_view_devices", "device_group_manage_devices"},
		},
		{
			name: "get permissions for non-existent scope",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			scope:    "device_group",
			scopeID:  "group-1",
			expected: []string{},
		},
		{
			name: "get permissions for non-existent scope ID",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			scope:    "org",
			scopeID:  "550e8400-e29b-41d4-a716-446655440999",
			expected: []string{},
		},
		{
			name: "empty permissions",
			userPermissions: &UserPermissions{
				UserID:      "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{},
			},
			scope:    "org",
			scopeID:  "550e8400-e29b-41d4-a716-446655440010",
			expected: []string{},
		},
		{
			name: "single permission in scope",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "location_group",
						ScopeID:        "location-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"}, // Location groups use device_group permissions
					},
				},
			},
			scope:    "location_group",
			scopeID:  "location-1",
			expected: []string{"device_group_view_devices"},
		},
		{
			name: "get permissions for location group scope",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "location_group",
						ScopeID:        "location-group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices", "device_group_manage_devices", "device_group_delete_devices"}, // Location groups use device_group permissions
					},
				},
			},
			scope:    "location_group",
			scopeID:  "location-group-1",
			expected: []string{"device_group_view_devices", "device_group_manage_devices", "device_group_delete_devices"},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.userPermissions.GetPermissionsForScope(tc.scope, tc.scopeID)
			assert.ElementsMatch(t, tc.expected, result)
		})
	}
}

func TestUserPermissions_GetAuthorizedDevicesByOrganization(t *testing.T) {
	tests := []struct {
		name                string
		userPermissions     *UserPermissions
		organizationID      string
		mockDeviceQueries   map[string][]string // query -> device IDs to return
		requiredPermissions []string
		expectedDevices     []string
		expectError         bool
	}{
		{
			name: "user has org permissions in specified organization",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			organizationID: "550e8400-e29b-41d4-a716-446655440010",
			mockDeviceQueries: map[string][]string{
				"org_devices": {"device-1", "device-2", "device-3"},
			},
			requiredPermissions: []string{"org_view_devices"},
			expectedDevices:     []string{"device-1", "device-2", "device-3"},
			expectError:         false,
		},
		{
			name: "user has device group permissions in specified organization",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "device_group",
						ScopeID:        "group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"},
					},
				},
			},
			organizationID: "550e8400-e29b-41d4-a716-446655440010",
			mockDeviceQueries: map[string][]string{
				"device_group_devices": {"device-4", "device-5"},
			},
			requiredPermissions: []string{"device_group_view_devices"},
			expectedDevices:     []string{"device-4", "device-5"},
			expectError:         false,
		},
		{
			name: "user has mixed permissions in specified organization",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
					{
						Scope:          "device_group",
						ScopeID:        "group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"},
					},
				},
			},
			organizationID: "550e8400-e29b-41d4-a716-446655440010",
			mockDeviceQueries: map[string][]string{
				"org_devices":          {"device-1", "device-2"},
				"device_group_devices": {"device-3", "device-4"},
			},
			requiredPermissions: []string{"org_view_devices", "device_group_view_devices"},
			expectedDevices:     []string{"device-1", "device-2", "device-3", "device-4"},
			expectError:         false,
		},
		{
			name: "user has permissions but in different organization",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440020",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440020", // Different org
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			organizationID:      "550e8400-e29b-41d4-a716-446655440010", // Looking for devices in this org
			mockDeviceQueries:   map[string][]string{},
			requiredPermissions: []string{"org_view_devices"},
			expectedDevices:     []string{}, // Should be empty since user has no permissions in this org
			expectError:         false,
		},
		{
			name: "user has permissions across multiple organizations, only return devices from specified org",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010", // Target org
						Permissions:    []string{"org_view_devices"},
					},
					{
						Scope:          "device_group",
						ScopeID:        "group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440020", // Different org - should be filtered out
						Permissions:    []string{"device_group_view_devices"},
					},
					{
						Scope:          "device_group",
						ScopeID:        "group-2",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010", // Target org
						Permissions:    []string{"device_group_view_devices"},
					},
				},
			},
			organizationID: "550e8400-e29b-41d4-a716-446655440010",
			mockDeviceQueries: map[string][]string{
				"org_devices":          {"device-1", "device-2"},
				"device_group_devices": {"device-3", "device-4"}, // Only from target org
			},
			requiredPermissions: []string{"org_view_devices", "device_group_view_devices"},
			expectedDevices:     []string{"device-1", "device-2", "device-3", "device-4"},
			expectError:         false,
		},
		{
			name: "user has no permissions in any organization",
			userPermissions: &UserPermissions{
				UserID:      "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{},
			},
			organizationID:      "550e8400-e29b-41d4-a716-446655440010",
			mockDeviceQueries:   map[string][]string{},
			requiredPermissions: []string{"org_view_devices"},
			expectedDevices:     []string{},
			expectError:         false,
		},
		{
			name: "user has permissions but not the required ones",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_manage_users"}, // Wrong permission
					},
				},
			},
			organizationID:      "550e8400-e29b-41d4-a716-446655440010",
			mockDeviceQueries:   map[string][]string{},
			requiredPermissions: []string{"org_view_devices"},
			expectedDevices:     []string{},
			expectError:         false,
		},
		{
			name: "default permissions used when none provided",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"}, // Has default permission
					},
					{
						Scope:          "device_group",
						ScopeID:        "group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"}, // Has default permission
					},
				},
			},
			organizationID: "550e8400-e29b-41d4-a716-446655440010",
			mockDeviceQueries: map[string][]string{
				"org_devices":          {"device-1", "device-2"},
				"device_group_devices": {"device-3", "device-4"},
			},
			requiredPermissions: []string{}, // Empty - should use defaults: ["org_view_devices", "device_group_view_devices"]
			expectedDevices:     []string{"device-1", "device-2", "device-3", "device-4"},
			expectError:         false,
		},
		{
			name: "database error returns error",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			organizationID:      "550e8400-e29b-41d4-a716-446655440010",
			mockDeviceQueries:   map[string][]string{}, // Will cause error
			requiredPermissions: []string{"org_view_devices"},
			expectedDevices:     []string{},
			expectError:         true,
		},

		{
			name: "user has comprehensive mixed permissions including location groups",
			userPermissions: &UserPermissions{
				UserID: "550e8400-e29b-41d4-a716-446655440001",
				Permissions: []Permission{
					{
						Scope:          "org",
						ScopeID:        "550e8400-e29b-41d4-a716-446655440010",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"org_view_devices"},
					},
					{
						Scope:          "device_group",
						ScopeID:        "group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"},
					},
					{
						Scope:          "location_group",
						ScopeID:        "location-group-1",
						OrganizationID: "550e8400-e29b-41d4-a716-446655440010",
						Permissions:    []string{"device_group_view_devices"}, // Location groups use device_group permissions
					},
				},
			},
			organizationID: "550e8400-e29b-41d4-a716-446655440010",
			mockDeviceQueries: map[string][]string{
				"org_devices":            {"device-1", "device-2"},
				"device_group_devices":   {"device-3", "device-4"},
				"location_group_devices": {"device-5", "device-6"},
			},
			requiredPermissions: []string{"org_view_devices", "device_group_view_devices"},
			expectedDevices:     []string{"device-1", "device-2", "device-3", "device-4", "device-5", "device-6"},
			expectError:         false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					// If this is the error test case, return an error
					if tc.name == "database error returns error" {
						return fmt.Errorf("database connection failed")
					}

					// The actual implementation uses a slice of structs with DeviceID field
					deviceIds := dest.(*[]struct {
						DeviceID string `db:"device_id"`
					})

					// JOIN-based query - combine results from all query types
					var combinedDevices []string
					if orgDevices, exists := tc.mockDeviceQueries["org_devices"]; exists {
						combinedDevices = append(combinedDevices, orgDevices...)
					}
					if dgDevices, exists := tc.mockDeviceQueries["device_group_devices"]; exists {
						combinedDevices = append(combinedDevices, dgDevices...)
					}
					if lgDevices, exists := tc.mockDeviceQueries["location_group_devices"]; exists {
						combinedDevices = append(combinedDevices, lgDevices...)
					}
					// Remove duplicates (since query has DISTINCT)
					seen := make(map[string]bool)
					var uniqueDevices []struct {
						DeviceID string `db:"device_id"`
					}
					for _, device := range combinedDevices {
						if !seen[device] {
							seen[device] = true
							uniqueDevices = append(uniqueDevices, struct {
								DeviceID string `db:"device_id"`
							}{DeviceID: device})
						}
					}
					*deviceIds = uniqueDevices

					return nil
				},
			}

			// Execute the function
			devices, err := tc.userPermissions.GetAuthorizedDevicesByOrganization(mockDB, tc.organizationID, tc.requiredPermissions...)

			// Verify results
			if tc.expectError {
				assert.Error(t, err)
				// Verify the error message format for database errors
				if tc.name == "database error returns error" {
					assert.Contains(t, err.Error(), "failed to get devices for permission scopes in org")
					assert.Contains(t, err.Error(), tc.organizationID)
					assert.Contains(t, err.Error(), "database connection failed")
				}
			} else {
				require.NoError(t, err)
				assert.ElementsMatch(t, tc.expectedDevices, devices)
			}
		})
	}
}

// TestGetDeviceInfoSqlErrNoRows tests that getDeviceInfo properly handles sql.ErrNoRows
func TestGetDeviceInfoSqlErrNoRows(t *testing.T) {
	t.Run("device not found", func(t *testing.T) {
		// Create mock database executor that returns sql.ErrNoRows
		mockDB := &dbexecutor.FakeDBExecutor{
			QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
				return sql.ErrNoRows
			},
		}

		// Test the function with a device ID that doesn't exist
		deviceInfo, err := getDeviceInfo(mockDB, "non-existent-device-id")
		assert.NoError(t, err, "Should not return error when device not found")
		assert.Nil(t, deviceInfo, "Device info should be nil when device not found")
	})

	t.Run("database error", func(t *testing.T) {
		// Create mock database executor that returns a database error
		mockDB := &dbexecutor.FakeDBExecutor{
			QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
				return fmt.Errorf("database connection failed")
			},
		}

		// Test the function with a database error
		deviceInfo, err := getDeviceInfo(mockDB, "device-123")
		assert.Error(t, err, "Should return error when database fails")
		assert.Contains(t, err.Error(), "database connection failed", "Error should contain the original error message")
		assert.Nil(t, deviceInfo, "Device info should be nil when database fails")
	})
}

// TestGetDeviceInfoByOrigIDSqlErrNoRows tests that getDeviceInfoByOrigID properly handles sql.ErrNoRows
func TestGetDeviceInfoByOrigIDSqlErrNoRows(t *testing.T) {
	t.Run("device not found", func(t *testing.T) {
		// Create mock database executor that returns sql.ErrNoRows
		mockDB := &dbexecutor.FakeDBExecutor{
			QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
				return sql.ErrNoRows
			},
		}

		// Test the function with an orig ID that doesn't exist
		deviceInfo, err := getDeviceInfoByOrigID(mockDB, 999999)
		assert.NoError(t, err, "Should not return error when device not found")
		assert.Nil(t, deviceInfo, "Device info should be nil when device not found")
	})

	t.Run("database error", func(t *testing.T) {
		// Create mock database executor that returns a database error
		mockDB := &dbexecutor.FakeDBExecutor{
			QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
				return fmt.Errorf("database connection failed")
			},
		}

		// Test the function with a database error
		deviceInfo, err := getDeviceInfoByOrigID(mockDB, 12345)
		assert.Error(t, err, "Should return error when database fails")
		assert.Contains(t, err.Error(), "database connection failed", "Error should contain the original error message")
		assert.Nil(t, deviceInfo, "Device info should be nil when database fails")
	})
}
