package middlewares

import (
	"net/http"

	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/logger"
)

func PanicMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("Panic recovered: %v", r) // Errors include stack trace by default
				response.CreateInternalErrorResponse(w)
			}
		}()
		next.ServeHTTP(w, r)
	})
}
