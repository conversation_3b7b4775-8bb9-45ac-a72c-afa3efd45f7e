package socketio

import (
	"fmt"

	"bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/wrappers"
	rushhourv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/rushhour/v1"
	"google.golang.org/protobuf/proto"

	"synapse-its.com/rushhour/domain"
	"synapse-its.com/rushhour/permissions"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// BinaryMessageHandler handles multi-layer binary protobuf messages for rushhour relay functionality.
//
// ARCHITECTURE: rushhour operates as an intelligent relay with permission-based filtering, not a pass-through.
//
// Message Structure:
//
//	rushhour envelope {
//	  metadata: type, request_id, user_id, device_id, organization_id, origin
//	  payload: monf-protobuf-devices envelope (WrapperCommand/WrapperResponse) {
//	    metadata: version, request_id, command/response type
//	    payload: actual device message (config, logs, realtime data, etc.)
//	  }
//	}
//
// FILTERING POINTS:
//  1. Envelope-level filtering (validateEnvelopePermissions): device access, org membership
//  2. Command-level filtering (validateCommandPermissions): specific command permissions
//  3. Response-level filtering (validateResponsePermissions): data visibility permissions
type BinaryMessageHandler struct {
	// Registry of protobuf message types for dynamic decoding
	protoRegistry map[domain.EnvelopeType]func() proto.Message
}

// NewBinaryMessageHandler creates a new multi-layer binary message handler
func NewBinaryMessageHandler() *BinaryMessageHandler {
	handler := &BinaryMessageHandler{
		protoRegistry: make(map[domain.EnvelopeType]func() proto.Message),
	}

	// Register protobuf message types
	handler.registerProtoTypes()

	return handler
}

// registerProtoTypes registers all known protobuf message types for both layers
func (bmh *BinaryMessageHandler) registerProtoTypes() {
	// Register the rushhour envelope types (outer layer)
	bmh.protoRegistry[domain.EnvelopeTypeWrapperCommand] = func() proto.Message {
		return &rushhourv1.SocketEnvelope{}
	}
	bmh.protoRegistry[domain.EnvelopeTypeWrapperResponse] = func() proto.Message {
		return &rushhourv1.SocketEnvelope{}
	}

	// NOTE: The inner layer (WrapperCommand/WrapperResponse from monf-protobufs-messages)
	// is handled separately in ProcessDeviceEnvelope() for permission filtering.
	// The payload is only deserialized when inspection/filtering is required.

	logger.Info("Multi-layer protobuf message types registered for binary handler")
}

// ProcessBinaryMessage processes incoming binary protobuf messages with multi-layer support
// FILTERING POINT 1: This is where envelope-level permission filtering occurs
func (bmh *BinaryMessageHandler) ProcessBinaryMessage(
	data []byte,
	userPermissions *authorizer.UserPermissions,
	pg connect.DatabaseExecutor,
	orgID string,
) (*domain.SocketEnvelope, error) {
	if len(data) == 0 {
		return nil, ErrCommandPayloadEmpty
	}

	// Validate the binary message
	envelope, err := bmh.ValidateBinaryMessage(data, userPermissions, pg, orgID)
	if err != nil {
		return nil, err
	}

	// For responses from gateways, optionally filter response data based on permissions
	if envelope.Type == domain.EnvelopeTypeWrapperResponse && envelope.Origin == domain.OriginTypeGateway {
		if err := bmh.validateDeviceResponse(envelope, userPermissions); err != nil {
			logger.Errorf("Device response validation failed: %v", err)
			return nil, ErrCommandNotPermitted
		}
	}

	return envelope, nil
}

// ValidateBinaryMessage validates a binary protobuf message
func (bmh *BinaryMessageHandler) ValidateBinaryMessage(
	data []byte,
	userPermissions *authorizer.UserPermissions,
	pg connect.DatabaseExecutor,
	orgID string,
) (*domain.SocketEnvelope, error) {
	if len(data) == 0 {
		return nil, ErrCommandPayloadEmpty
	}

	// Parse the rushhour envelope (outer layer)
	var envelope rushhourv1.SocketEnvelope
	if err := proto.Unmarshal(data, &envelope); err != nil {
		return nil, ErrFailedToUnmarshalWrapperCommand
	}

	logger.Debugf("Parsed rushhour envelope: type=%v, origin=%v, device=%s, org=%s",
		envelope.Type, envelope.Origin, envelope.DeviceId, envelope.OrganizationId)

	// FILTERING POINT 1: Validate envelope-level permissions
	if err := bmh.validateEnvelopePermissions(&envelope, userPermissions, orgID); err != nil {
		logger.Errorf("Envelope permission check failed: %v", err)
		return nil, ErrDeviceAccessDenied
	}

	// For commands from FSA, inspect and validate the inner device envelope
	if envelope.Type == domain.EnvelopeTypeWrapperCommand && envelope.Origin == domain.OriginTypeFSA {
		if err := bmh.validateDeviceCommand(&envelope, userPermissions, pg); err != nil {
			logger.Errorf("Device command validation failed: %v", err)
			return nil, ErrCommandNotPermitted
		}
	}

	// Return the validated envelope
	logger.Debugf("Validated rushhour envelope: type=%v, origin=%v, device=%s, org=%s",
		envelope.Type, envelope.Origin, envelope.DeviceId, envelope.OrganizationId)
	return &envelope, nil
}

// validateEnvelopePermissions validates rushhour envelope-level permissions
// FILTERING POINT 1: Organization membership, device access, basic envelope validation
func (bmh *BinaryMessageHandler) validateEnvelopePermissions(envelope *domain.SocketEnvelope, userPermissions interface{}, orgID string) error {
	if envelope == nil {
		return fmt.Errorf("envelope cannot be nil")
	}

	// Validate required fields
	if envelope.DeviceId == "" {
		return fmt.Errorf("device_id is required")
	}

	if envelope.OrganizationId == "" {
		return fmt.Errorf("organization_id is required")
	}

	// Validate organization membership
	if envelope.OrganizationId != orgID {
		return fmt.Errorf("organization mismatch: envelope=%s, user=%s", envelope.OrganizationId, orgID)
	}

	// Validate envelope type
	if envelope.Type != domain.EnvelopeTypeWrapperCommand && envelope.Type != domain.EnvelopeTypeWrapperResponse {
		return fmt.Errorf("unsupported envelope type: %v", envelope.Type)
	}

	// TODO: Add device access validation using userPermissions
	// This should integrate with the permission checker to validate device access
	// Similar to: permissionChecker.CheckDeviceAccess(ctx, envelope.DeviceId, operation)

	return nil
}

// validateDeviceCommand validates device command permissions by inspecting the inner envelope
// FILTERING POINT 2: Command-specific permissions (config read/write, log access, etc.)
func (bmh *BinaryMessageHandler) validateDeviceCommand(
	envelope *domain.SocketEnvelope,
	userPermissions *authorizer.UserPermissions,
	pg connect.DatabaseExecutor,
) error {
	if len(envelope.Payload) == 0 {
		return ErrCommandPayloadEmpty
	}
	// Deserialize WrapperCommand from monf-protobufs-messages
	var wrapperCmd wrappers.WrapperCommand
	if err := proto.Unmarshal(envelope.Payload, &wrapperCmd); err != nil {
		logger.Errorf("Failed to unmarshal wrapper command: %v", err)
		return ErrFailedToUnmarshalWrapperCommand
	}

	// Extract command type using the command permission mapper
	mapper := permissions.NewCommandPermissionMapper()
	commandType := mapper.ExtractCommandType(&wrapperCmd)
	if commandType == "" {
		logger.Errorf("Unable to determine command type for device %s", envelope.DeviceId)
		return ErrUnableToDetermineCommandType
	}

	// Validate command permissions using the command permission mapper
	if err := mapper.ValidateCommandPermissions(userPermissions, envelope.DeviceId, commandType, pg); err != nil {
		logger.Errorf("Command permission check failed: %v", err)
		return ErrCommandNotPermitted
	}

	logger.Debugf("Device command validation passed for device %s (validation implementation pending)", envelope.DeviceId)
	return nil
}

// validateDeviceResponse validates and optionally filters device response data
// FILTERING POINT 3: Response data filtering based on user permissions and sensitivity
func (bmh *BinaryMessageHandler) validateDeviceResponse(envelope *domain.SocketEnvelope, userPermissions interface{}) error {
	if len(envelope.Payload) == 0 {
		return fmt.Errorf("response payload cannot be empty")
	}

	// TODO: Deserialize WrapperResponse from monf-protobufs-messages
	// Parse the inner device envelope to inspect response type and filter sensitive data
	//
	// Example structure:
	//   var wrapperResp wrappers.WrapperResponse
	//   if err := proto.Unmarshal(envelope.Payload, &wrapperResp); err != nil {
	//       return fmt.Errorf("failed to unmarshal wrapper response: %w", err)
	//   }
	//
	// FILTERING LOGIC:
	//   - Remove sensitive configuration data for users without admin permissions
	//   - Filter log entries based on user's log access level
	//   - Redact system statistics for users without full device access
	//   - Remove diagnostic data for users without troubleshooting permissions
	//
	// This may require modifying the envelope.Payload with filtered data:
	//   filteredPayload, err := bmh.filterResponseData(&wrapperResp, userPermissions)
	//   if err != nil {
	//       return err
	//   }
	//   envelope.Payload = filteredPayload

	logger.Debugf("Device response validation passed for device %s (filtering implementation pending)", envelope.DeviceId)
	return nil
}

// CreateBinaryMessage creates a binary protobuf message from a rushhour envelope
func (bmh *BinaryMessageHandler) CreateBinaryMessage(envelope *domain.SocketEnvelope) ([]byte, error) {
	// Validate the envelope
	if envelope == nil {
		return nil, fmt.Errorf("envelope cannot be nil")
	}

	// Marshal the envelope to binary
	data, err := proto.Marshal(envelope)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal envelope: %w", err)
	}

	logger.Debugf("Created binary message: %d bytes, type=%v", len(data), envelope.Type)
	return data, nil
}

// RelayCommand creates a binary message for relaying FSA commands to gateways
// This wraps the device command in a rushhour envelope for routing
func (bmh *BinaryMessageHandler) RelayCommand(deviceID, orgID, userID string, requestID uint32, deviceCommandPayload []byte) ([]byte, error) {
	envelope := &domain.SocketEnvelope{
		Type:           domain.EnvelopeTypeWrapperCommand,
		RequestId:      requestID,
		UserId:         userID,
		DeviceId:       deviceID,
		OrganizationId: orgID,
		Origin:         domain.OriginTypeFSA,
		Payload:        deviceCommandPayload, // This contains the serialized WrapperCommand
	}

	logger.Debugf("Relaying device command: device=%s, user=%s, requestID=%d, payload_size=%d",
		deviceID, userID, requestID, len(deviceCommandPayload))
	return bmh.CreateBinaryMessage(envelope)
}

// RelayResponse creates a binary message for relaying device responses to FSA apps
// This wraps the device response in a rushhour envelope for routing
func (bmh *BinaryMessageHandler) RelayResponse(deviceID, orgID string, requestID uint32, deviceResponsePayload []byte) ([]byte, error) {
	envelope := &domain.SocketEnvelope{
		Type:           domain.EnvelopeTypeWrapperResponse,
		RequestId:      requestID,
		UserId:         "", // Response doesn't typically have a user ID
		DeviceId:       deviceID,
		OrganizationId: orgID,
		Origin:         domain.OriginTypeGateway,
		Payload:        deviceResponsePayload, // This contains the serialized WrapperResponse
	}

	logger.Debugf("Relaying device response: device=%s, requestID=%d, payload_size=%d",
		deviceID, requestID, len(deviceResponsePayload))
	return bmh.CreateBinaryMessage(envelope)
}

// ValidateEnvelope performs basic validation on a rushhour envelope
func (bmh *BinaryMessageHandler) ValidateEnvelope(envelope *domain.SocketEnvelope) error {
	if envelope == nil {
		return fmt.Errorf("envelope cannot be nil")
	}

	if envelope.DeviceId == "" {
		return fmt.Errorf("device_id is required")
	}

	if envelope.OrganizationId == "" {
		return fmt.Errorf("organization_id is required")
	}

	// Validate envelope type - support both protobuf wrapper types and new JSON/protobuf command types
	switch envelope.Type {
	case domain.EnvelopeTypeWrapperCommand,
		domain.EnvelopeTypeWrapperResponse,
		domain.EnvelopeTypeCommandJSON,
		domain.EnvelopeTypeCommandProtobuf:
		// Valid envelope types
	default:
		return fmt.Errorf("unsupported envelope type: %v", envelope.Type)
	}

	return nil
}

// ExtractRequestID safely extracts the request ID from an envelope
func (bmh *BinaryMessageHandler) ExtractRequestID(envelope *domain.SocketEnvelope) uint32 {
	if envelope == nil {
		return 0
	}
	return envelope.RequestId
}

// ExtractDeviceID safely extracts the device ID from an envelope
func (bmh *BinaryMessageHandler) ExtractDeviceID(envelope *domain.SocketEnvelope) string {
	if envelope == nil {
		return ""
	}
	return envelope.DeviceId
}

// ExtractSessionID safely extracts the session ID from an envelope
func (bmh *BinaryMessageHandler) ExtractSessionID(envelope *domain.SocketEnvelope) string {
	if envelope == nil {
		return ""
	}
	return envelope.SessionId
}

// ExtractOrganizationID safely extracts the organization ID from an envelope
func (bmh *BinaryMessageHandler) ExtractOrganizationID(envelope *domain.SocketEnvelope) string {
	if envelope == nil {
		return ""
	}
	return envelope.OrganizationId
}

// ExtractInnerRequestID extracts the request ID from the inner device envelope (WrapperCommand/WrapperResponse)
// This is useful for correlating requests and responses at the device protocol level
func (bmh *BinaryMessageHandler) ExtractInnerRequestID(envelope *domain.SocketEnvelope) (uint32, error) {
	if envelope == nil || len(envelope.Payload) == 0 {
		return 0, fmt.Errorf("envelope or payload is empty")
	}

	// TODO: Implement based on WrapperCommand/WrapperResponse structure
	//
	// Example implementation:
	//   if envelope.Type == domain.EnvelopeTypeWrapperCommand {
	//       var wrapperCmd wrappers.WrapperCommand
	//       if err := proto.Unmarshal(envelope.Payload, &wrapperCmd); err != nil {
	//           return 0, err
	//       }
	//       return wrapperCmd.RequestId, nil
	//   } else if envelope.Type == domain.EnvelopeTypeWrapperResponse {
	//       var wrapperResp wrappers.WrapperResponse
	//       if err := proto.Unmarshal(envelope.Payload, &wrapperResp); err != nil {
	//           return 0, err
	//       }
	//       return wrapperResp.RequestId, nil
	//   }

	logger.Debugf("Inner request ID extraction not yet implemented for envelope type %v", envelope.Type)
	return 0, fmt.Errorf("inner request ID extraction not implemented")
}

// TODO: IMPLEMENTATION ROADMAP for filtering functionality:
//
// 1. Add monf-protobufs-messages dependency and import wrappers.proto generated types
// 2. Implement command-specific permission validation in validateDeviceCommand():
//    - Configuration commands: device_manage permissions
//    - Log commands: device_view_logs permissions
//    - Statistics: device_view_stats permissions
//    - DFU: device_firmware_update permissions
//    - Realtime: device_view_realtime permissions
// 3. Implement response data filtering in validateDeviceResponse():
//    - Remove sensitive config data for non-admin users
//    - Filter log entries by access level
//    - Redact system diagnostics for limited users
// 4. Integrate with the existing PermissionChecker for device access validation
// 5. Add metrics and logging for filtered/blocked messages
// 6. Implement caching for frequently accessed permission checks
//
// INTEGRATION POINTS:
// - Service.ProcessBinaryMessage() should call ProcessBinaryMessage() with user permissions
// - Permission filtering should use the same logic as the existing CheckDeviceAccess()
// - Filtered messages should be logged for audit purposes
// - Error responses should not reveal what was filtered (avoid information leakage)
