# REST Endpoint for User to Location Group CRUD Operations

## Overview

As an Engineer, I need a REST endpoint for user to location group CRUD operations. This document outlines the step-by-step implementation details for creating, reading, updating, and deleting **direct user-to-location group assignments** (not role-based assignments).

## Current System Analysis

### Existing Infrastructure
- **Location Groups**: Already implemented in `/shared/rest/onramp/organization/locationgroups/`
- **User Permissions**: Existing system with `UserPermissions` struct and role-based access control
- **Database Schema**: `LocationGroupRoleAssignments` table exists for role-based access
- **Authentication**: Session-based authentication with middleware already in place

### Current Database Relationships
```
User → AuthMethod → Membership → LocationGroupRoleAssignments → LocationGroups (Role-based)
```

**Note**: The current system uses role-based assignments through memberships. The new requirement is for **direct user-to-location group assignments**.

### System Comparison

**Current Role-Based System** (LocationGroupRoleAssignments):
- Users → AuthMethod → Membership → Role → LocationGroup
- Complex permission management through roles
- Flexible but complex for simple assignments

**Enhanced Direct Assignment System** (using existing LocationGroupRoleAssignments):
- Users → AuthMethod → Membership → LocationGroup (with default role)
- Leverages existing infrastructure
- Simple assignment with automatic role assignment
- No duplicate tables or complexity

### Existing Endpoints
- `GET /api/organizations/{organizationId}/locationgroups` - List location groups
- `POST /api/organizations/{organizationId}/locationgroups` - Create location group
- `PATCH /api/organizations/{organizationId}/locationgroups/{locationgroupId}` - Update location group
- `DELETE /api/organizations/{organizationId}/locationgroups/{locationgroupId}` - Delete location group

## Implementation Plan

### Step 1: Database Schema Analysis and Enhancement

**Reason**: Use existing `LocationGroupRoleAssignments` table by creating direct user memberships, avoiding the need for a new table

**Current Schema**:
```sql
-- LocationGroupRoleAssignments table already exists
CREATE TABLE {{LocationGroupRoleAssignments}} (
  MembershipId UUID NOT NULL,
  LocationGroupId UUID NOT NULL,
  RoleId UUID NOT NULL,
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT {{LocationGroupRoleAssignments_PK}} PRIMARY KEY (MembershipId, LocationGroupId, RoleId)
);
```

**Approach**: Create a **direct user membership** in the `Memberships` table and assign it to location groups through `LocationGroupRoleAssignments`. This leverages existing infrastructure without duplication.

**Implementation Strategy**:
1. Create a direct `Membership` record linking user's `AuthMethod` to the organization
2. Use existing `LocationGroupRoleAssignments` table for the assignment
3. Assign a default role (e.g., "LOCATION_GROUP_MEMBER") for basic access

### Step 2: Create User Location Groups Package Structure

**Reason**: Follow existing project patterns for consistency and maintainability

**Directory Structure**:
```
shared/rest/onramp/user/locationgroups/
├── schemas.go          # Request/response data structures
├── locationgroups.go   # Main handler functions
├── errors.go           # Domain-specific error definitions
└── locationgroups_test.go # Comprehensive tests
```

### Step 3: Define Data Schemas

**Reason**: Need consistent data structures for API requests and responses

**Schemas to Create**:
```go
// UserLocationGroupRequest - for creating/updating user-location group assignments
type UserLocationGroupRequest struct {
    LocationGroupId uuid.UUID `json:"locationGroupId" validate:"required"`
}

// UserLocationGroupResponse - for API responses
type UserLocationGroupResponse struct {
    MembershipId    uuid.UUID `json:"membershipId"`
    UserId          uuid.UUID `json:"userId"`
    LocationGroupId uuid.UUID `json:"locationGroupId"`
    OrganizationId  uuid.UUID `json:"organizationId"`
    RoleId          uuid.UUID `json:"roleId"`
    CreatedAt       time.Time `json:"createdAt"`
    UpdatedAt       time.Time `json:"updatedAt"`
}

// UserLocationGroupsResponse - for listing multiple assignments
type UserLocationGroupsResponse struct {
    UserLocationGroups []UserLocationGroupResponse `json:"userLocationGroups"`
}
```

### Step 4: Implement CRUD Operations

**Reason**: Core functionality needed for user-location group management

**Operations to Implement**:

#### 4.1 Create User-Location Group Assignment
- **Endpoint**: `POST /api/user/{userId}/locationgroups`
- **Purpose**: Assign a user to a location group
- **Validation**: 
  - User exists and is authenticated
  - Location group exists and belongs to user's organization
  - User has permission to manage location group assignments
  - No duplicate assignment exists

#### 4.2 Read User-Location Group Assignments
- **Endpoint**: `GET /api/user/{userId}/locationgroups`
- **Purpose**: List all location groups a user is assigned to
- **Filters**: 
  - By organization (optional)
  - Include deleted assignments (optional)

#### 4.3 Update User-Location Group Assignment
- **Endpoint**: `PATCH /api/user/{userId}/locationgroups/{locationGroupId}`
- **Purpose**: Update organization for existing assignment (if needed)
- **Validation**: 
  - Assignment exists
  - User has permission to modify assignments

#### 4.4 Delete User-Location Group Assignment
- **Endpoint**: `DELETE /api/user/{userId}/locationgroups/{locationGroupId}`
- **Purpose**: Remove user's assignment to specific location group
- **Validation**: 
  - Assignment exists
  - User has permission to remove assignments

### Step 5: Implement Business Logic Layer

**Reason**: Separate business logic from HTTP handlers for testability and reusability

**Functions to Implement**:
```go
type HandlerDeps struct {
    GetConnections                    func(context.Context, ...bool) (*connect.Connections, error)
    CreateUserLocationGroup           func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, *UserLocationGroupRequest) (*UserLocationGroupResponse, error)
    GetUserLocationGroups             func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) (*UserLocationGroupsResponse, error)
    UpdateUserLocationGroup           func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, *UserLocationGroupRequest) (*UserLocationGroupResponse, error)
    DeleteUserLocationGroup           func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
    ValidateUserLocationGroupAccess   func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
}
```

### Step 6: Database Operations Implementation

**Reason**: Need efficient database queries for CRUD operations

**Queries to Implement**:

#### 6.1 Create Assignment
```sql
-- First, ensure user has a membership to the organization
INSERT INTO {{Memberships}} (Id, AuthMethodId, OrganizationId, IsDeleted, CreatedAt, UpdatedAt)
SELECT 
    uuid_generate_v4(), am.Id, lg.OrganizationId, false, NOW(), NOW()
FROM {{AuthMethod}} am
JOIN {{LocationGroups}} lg ON lg.Id = $2
WHERE am.UserId = $1 AND lg.IsDeleted = false
ON CONFLICT (AuthMethodId, OrganizationId) DO NOTHING
RETURNING Id;

-- Then, assign the location group with a default role
INSERT INTO {{LocationGroupRoleAssignments}} (
    MembershipId, LocationGroupId, RoleId, IsDeleted, CreatedAt, UpdatedAt
)
SELECT 
    m.Id, $2, (SELECT Id FROM {{CustomRole}} WHERE Identifier = 'LOCATION_GROUP_MEMBER' LIMIT 1), 
    false, NOW(), NOW()
FROM {{Memberships}} m
JOIN {{LocationGroups}} lg ON lg.Id = $2
WHERE m.AuthMethodId = (SELECT am.Id FROM {{AuthMethod}} am WHERE am.UserId = $1)
AND lg.OrganizationId = m.OrganizationId
RETURNING MembershipId, LocationGroupId, RoleId, CreatedAt, UpdatedAt;
```

#### 6.2 Read Assignments
```sql
SELECT 
    lgra.MembershipId,
    u.Id as UserId,
    lg.Id as LocationGroupId,
    lg.OrganizationId,
    lgra.RoleId,
    lgra.CreatedAt,
    lgra.UpdatedAt
FROM {{User}} u
JOIN {{AuthMethod}} am ON am.UserId = u.Id
JOIN {{Memberships}} m ON m.AuthMethodId = am.Id
JOIN {{LocationGroupRoleAssignments}} lgra ON lgra.MembershipId = m.Id
JOIN {{LocationGroups}} lg ON lg.Id = lgra.LocationGroupId
WHERE u.Id = $1 AND lgra.IsDeleted = false
```

### Step 7: Permission Validation

**Reason**: Ensure users can only manage location groups they have access to

**Validation Rules**:
1. **User Authentication**: User must be authenticated via session
2. **Organization Access**: User must have membership in the organization
3. **Location Group Access**: User must have permission to manage the specific location group
4. **Duplicate Prevention**: Prevent duplicate user-location group assignments (through unique constraint)
5. **Data Integrity**: Ensure location group belongs to user's organization
6. **Default Role**: Ensure a default role exists for location group assignments

### Step 8: Error Handling

**Reason**: Consistent error responses for better API usability

**Error Types**:
```go
var (
    ErrUserNotFound              = errors.New("user not found")
    ErrLocationGroupNotFound     = errors.New("location group not found")
    ErrInvalidAssignment         = errors.New("invalid user-location group assignment")
    ErrPermissionDenied          = errors.New("permission denied")
    ErrDuplicateAssignment       = errors.New("user already assigned to this location group")
    ErrOrganizationMismatch      = errors.New("location group does not belong to user's organization")
    ErrDefaultRoleNotFound       = errors.New("default location group member role not found")
)
```

### Step 9: Integration with Existing User Module

**Reason**: Follow existing patterns and maintain consistency

**Integration Points**:
1. **Route Registration**: Add new routes to existing user handler
2. **Middleware**: Use existing session and authorization middleware
3. **Error Handling**: Follow existing error response patterns
4. **Testing**: Integrate with existing test infrastructure

**Route Updates**:
```go
// In microservices/onramp/modules/user/handler.go
userSpecificRouter.HandleFunc("/locationgroups", RestUserLocationGroups.ListHandler).Methods(http.MethodGet)
userSpecificRouter.HandleFunc("/locationgroups", RestUserLocationGroups.CreateHandler).Methods(http.MethodPost)
userSpecificRouter.HandleFunc("/locationgroups/{locationGroupId}", RestUserLocationGroups.UpdateHandler).Methods(http.MethodPatch)
userSpecificRouter.HandleFunc("/locationgroups/{locationGroupId}", RestUserLocationGroups.DeleteHandler).Methods(http.MethodDelete)
```

### Step 10: Testing Strategy

**Reason**: Ensure reliability and maintainability

**Test Coverage**:
1. **Unit Tests**: Test individual functions and business logic
2. **Integration Tests**: Test database operations and API endpoints
3. **Permission Tests**: Test various permission scenarios
4. **Error Tests**: Test error handling and edge cases
5. **Performance Tests**: Test with large datasets

## Implementation Order

### Phase 1: Core Infrastructure
1. Create package structure and schemas
2. Implement database operations
3. Add basic error handling

### Phase 2: Business Logic
1. Implement CRUD operations
2. Add permission validation
3. Implement business rules

### Phase 3: API Layer
1. Create HTTP handlers
2. Add route registration
3. Implement middleware integration

### Phase 4: Testing and Validation
1. Write comprehensive tests
2. Perform integration testing
3. Validate permission scenarios

## Technical Considerations

### Performance
- **Indexing**: Ensure proper database indexes on frequently queried fields
- **Caching**: Consider caching user permissions for frequently accessed data
- **Pagination**: Implement pagination for large result sets

### Security
- **Input Validation**: Validate all input parameters
- **SQL Injection**: Use parameterized queries
- **Permission Checks**: Implement comprehensive permission validation
- **Audit Logging**: Log all CRUD operations for audit purposes

### Scalability
- **Database Design**: Optimize queries for large datasets
- **Connection Pooling**: Use existing connection management
- **Async Operations**: Consider async processing for bulk operations

## Dependencies

### Internal Dependencies
- `shared/connect` - Database connections
- `shared/api/response` - Standardized HTTP responses
- `shared/logger` - Logging functionality
- `shared/rest/onramp/helper` - Common validation functions

### External Dependencies
- `github.com/google/uuid` - UUID generation and validation
- `github.com/gorilla/mux` - HTTP routing
- `database/sql` - Database operations

## Success Criteria

1. **Functional Requirements**:
   - All CRUD operations work correctly
   - Permission validation is comprehensive
   - Error handling is consistent

2. **Non-Functional Requirements**:
   - API response time < 200ms for standard operations
   - 100% test coverage for business logic
   - Follows existing code patterns and standards

3. **Integration Requirements**:
   - Seamlessly integrates with existing user module
   - Maintains backward compatibility
   - Follows established API patterns

## Risk Assessment

### High Risk
- **Permission Complexity**: Complex permission validation logic
- **Data Consistency**: Maintaining referential integrity across multiple tables

### Medium Risk
- **Performance**: Database queries with multiple joins
- **Testing Complexity**: Comprehensive testing of permission scenarios

### Low Risk
- **API Design**: Following established patterns
- **Integration**: Using existing middleware and infrastructure

## Conclusion

This implementation provides a comprehensive solution for **direct user-to-location group assignments** while maintaining consistency with existing system architecture. The approach leverages the existing `LocationGroupRoleAssignments` infrastructure by creating direct user memberships, providing engineers with a simple way to assign users to location groups:

1. **Simple Direct Assignments** - Using existing infrastructure with default roles
2. **Complex Role-Based Access** - For advanced permission management (existing)

The implementation will be delivered in phases, allowing for iterative development and testing. Each phase builds upon the previous one, ensuring a solid foundation for the complete feature.

### Key Benefits of Enhanced Direct Assignment System:
- **Leverages Existing Infrastructure**: No new tables needed
- **Simpler Management**: Direct user-to-location group mapping through memberships
- **Easier API**: Cleaner REST endpoints for basic operations
- **Flexible Architecture**: Can be used alongside existing role-based system
- **Better Performance**: Uses existing optimized table structure
- **Easier Testing**: Simpler business logic and validation
- **No Duplication**: Reuses existing permission and role systems
