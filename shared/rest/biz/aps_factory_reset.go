package biz

import (
	"database/sql"
	"errors"

	"github.com/google/uuid"
	"synapse-its.com/shared/logger"
	apsReset "synapse-its.com/shared/rest/domain/aps_reset"
)

// starlarkService defines the interface for executing Starlark functions
type starlarkService interface {
	ExchangeCode(challengeCode string) (string, error)
}

type apsResetAuditor interface {
	Audit(userID, orgID uuid.UUID, challengeCode string, err error) error
}

// ApsFactoryResetBiz handles the business logic for APS factory reset requests
type apsFactoryResetBiz struct {
	starlarkService starlarkService
	apsResetRepo    apsReset.Repository
	apsResetAuditor apsResetAuditor
}

// NewApsFactoryResetBiz creates a new APS factory reset business logic handler
func NewApsFactoryResetBiz(
	starlarkService starlarkService,
	apsResetRepo apsReset.Repository,
	apsResetAuditor apsResetAuditor,
) *apsFactoryResetBiz {
	return &apsFactoryResetBiz{
		starlarkService: starlarkService,
		apsResetRepo:    apsResetRepo,
		apsResetAuditor: apsResetAuditor,
	}
}

func (b *apsFactoryResetBiz) GetAPSFactorResetCode(userID, orgID uuid.UUID, challengeCode string) (code string, err error) {
	defer func() {
		if auditErr := b.apsResetAuditor.Audit(userID, orgID, challengeCode, err); auditErr != nil {
			logger.Warnf("Failed to audit APS reset request: %v", auditErr)
		}
	}()

	config, err := b.apsResetRepo.FindConfigByUserIDAndOrganizationID(userID, orgID)
	if err != nil && errors.Is(err, sql.ErrNoRows) {
		config, err = b.apsResetRepo.CreateConfig(userID, orgID)
	}
	if err != nil {
		return "", err
	}

	statistic, err := b.apsResetRepo.FindStatisticByUserIDAndOrganizationID(userID, orgID)
	if err != nil && errors.Is(err, sql.ErrNoRows) {
		statistic, err = b.apsResetRepo.CreateStatistic(userID, orgID)
	}
	if err != nil {
		return "", err
	}

	if statistic.IsLimitReached(config) {
		err = apsReset.ErrLimitReached
		return "", err
	}

	if !statistic.CheckAndPruneSuccessList(config) {
		err = apsReset.ErrSuccessListWindowNotExpired
		return "", err
	}

	code, err = b.starlarkService.ExchangeCode(challengeCode)
	if err != nil {
		return "", err
	}

	statistic.RecordSuccess()
	logger.Infof("Updating APS reset statistic: %+v", statistic)
	err = b.apsResetRepo.UpdateStatistic(statistic)
	if err != nil {
		return "", err
	}

	return code, nil
}
