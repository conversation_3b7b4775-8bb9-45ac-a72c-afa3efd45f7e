package mailgun

import (
	"context"
	"fmt"
	"os"

	mg "github.com/mailgun/mailgun-go/v5"
	"synapse-its.com/shared/logger"
)

// MailgunSender defines the interface for sending emails (real or stub)
type MailgunSender interface {
	Send(ctx context.Context, m mg.Message) (string, error)
}

// Client wraps the Mailgun client and config
type Client struct {
	domain      string
	sender      MailgunSender
	senderEmail string
}

// NewClient initializes the Mailgun client based on environment variables
func NewClient(ctx context.Context) (*Client, error) {
	// Read all required environment variables
	domain := os.Getenv("MG_DOMAIN")
	apiKey := os.Getenv("MG_API_KEY")
	sender := os.Getenv("MG_SENDER")

	// Validate required environment variables
	if domain == "" {
		return nil, ErrMissingDomain
	}
	if apiKey == "" {
		return nil, ErrMissingAPIKey
	}
	if sender == "" {
		return nil, ErrMissingSender
	}

	// Create the appropriate sender based on environment
	var mailgunSender MailgunSender

	if os.Getenv("MG_STUB") != "" {
		// Use stub client for local development (currently being used for local development)
		logger.Infof("Mailgun: Using stub client (MG_STUB set)")
		mailgunSender = &StubMailgunClient{}
	} else if host := os.Getenv("MG_HOST"); host != "" {
		// Redirect to custom host for local development (not being used in any environment yet...)
		logger.Infof("Mailgun: Redirecting API base to %s (MG_HOST set)", host)
		client := mg.NewMailgun(apiKey)
		client.SetAPIBase(fmt.Sprintf("http://%s", host))
		mailgunSender = &RealMailgunClient{client: client}
	} else {
		// Use production Mailgun API
		logger.Infof("Mailgun: Using production API")
		client := mg.NewMailgun(apiKey)
		mailgunSender = &RealMailgunClient{client: client}
	}

	return &Client{
		domain:      domain,
		sender:      mailgunSender,
		senderEmail: sender,
	}, nil
}

// SendEmail sends an email using the configured Mailgun client
func (c *Client) SendEmail(ctx context.Context, to, subject, body string) error {
	m := mg.NewMessage(c.senderEmail, subject, body, to)
	_, err := c.sender.Send(ctx, m)
	if err != nil {
		return fmt.Errorf("mailgun send error: %w", err)
	}
	return nil
}

// RealMailgunClient wraps the real SDK client
type RealMailgunClient struct {
	client mg.Mailgun
}

func (r *RealMailgunClient) Send(ctx context.Context, m mg.Message) (string, error) {
	resp, err := r.client.Send(ctx, m)
	if err != nil {
		return "", err
	}
	return resp.ID, nil
}

// StubMailgunClient is a no-op client for local/dev
// It implements the MailgunSender interface
type StubMailgunClient struct{}

func (s *StubMailgunClient) Send(ctx context.Context, m mg.Message) (string, error) {
	logger.Infof("[STUB] Would send email: to=%v", m.To())
	return "stub-id", nil
}

// Store the Mailgun client in context
const mailgunClientKey string = "mailgun_client"

// WithClient stores the *Client in a context under a private key
func WithClient(ctx context.Context, client *Client) context.Context {
	return context.WithValue(ctx, mailgunClientKey, client)
}

// FromContext retrieves the *Client from the context
func FromContext(ctx context.Context) *Client {
	if client, ok := ctx.Value(mailgunClientKey).(*Client); ok {
		return client
	}
	return nil
}
