package middlewares

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"golang.org/x/oauth2"
	"synapse-its.com/onramp/domain"
	onrampMocks "synapse-its.com/onramp/mock"
)

func TestAuthMiddleware(t *testing.T) {
	// Create a test handler that we'll protect
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		claims, ok := r.Context().Value(domain.UserKey).(map[string]any)
		if !ok {
			http.Error(w, "no claims in context", http.StatusInternalServerError)
			return
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(claims)
	})

	// Create a mock session store
	mockSessionStore := &onrampMocks.MockSessionStore{}

	// Create a valid token for testing - this will fail verification since it's not a real JWT
	validToken := &oauth2.Token{
		AccessToken: "test-access-token",
	}
	// Mock the ID token
	validToken = validToken.WithExtra(map[string]any{
		"id_token": "invalid-jwt-token", // This will cause verification to fail
	})

	// Set up mock expectations for valid session
	mockSessionStore.On("GetSession", "valid-session").Return(&domain.Session{
		UserID:          "test-user",
		OAuthToken:      domain.FromOAuth2Token(validToken),
		UserPermissions: nil,
	}, true)

	// Set up mock expectations for invalid session
	mockSessionStore.On("GetSession", "invalid-session").Return((*domain.Session)(nil), false)

	protectedHandler := AuthMiddleware(mockSessionStore)(testHandler)

	tests := []struct {
		name          string
		host          string
		sessionCookie *http.Cookie
		wantCode      int
		wantBody      string
	}{
		{
			name:          "no session cookie",
			host:          "localhost:4200",
			sessionCookie: nil,
			wantCode:      http.StatusUnauthorized,
		},
		{
			name:          "invalid session id",
			host:          "localhost:4200",
			sessionCookie: &http.Cookie{Name: "session_id", Value: "invalid-session"},
			wantCode:      http.StatusUnauthorized,
		},
		{
			name:          "valid session but invalid token",
			host:          "localhost:4200",
			sessionCookie: &http.Cookie{Name: "session_id", Value: "valid-session"},
			wantCode:      http.StatusUnauthorized, // Token verification will fail
		},
		{
			name:          "production host with invalid session",
			host:          "example.com",
			sessionCookie: &http.Cookie{Name: "session_id", Value: "invalid-session"},
			wantCode:      http.StatusUnauthorized,
		},
		{
			name:          "production host with invalid token",
			host:          "example.com",
			sessionCookie: &http.Cookie{Name: "session_id", Value: "valid-session"},
			wantCode:      http.StatusUnauthorized, // Token verification will fail in production mode too
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/protected/test", nil)
			req.Host = tc.host
			if tc.sessionCookie != nil {
				req.AddCookie(tc.sessionCookie)
			}
			rr := httptest.NewRecorder()

			protectedHandler.ServeHTTP(rr, req)

			assert.Equal(t, tc.wantCode, rr.Code)
			if tc.wantBody != "" {
				assert.Contains(t, rr.Body.String(), tc.wantBody)
			}
		})
	}

	// Verify that all expected mock calls were made
	mockSessionStore.AssertExpectations(t)
}

func TestAuthMiddleware_ComprehensiveCoverage(t *testing.T) {
	mockSessionStore := &onrampMocks.MockSessionStore{}

	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		claims, ok := r.Context().Value(domain.UserKey).(map[string]any)
		if !ok {
			http.Error(w, "no claims in context", http.StatusInternalServerError)
			return
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(claims)
	})

	protectedHandler := AuthMiddleware(mockSessionStore)(testHandler)

	t.Run("middleware_creation", func(t *testing.T) {
		// Test AuthMiddleware function creation
		middleware := AuthMiddleware(mockSessionStore)
		assert.NotNil(t, middleware, "AuthMiddleware should return a middleware function")

		handler := middleware(testHandler)
		assert.NotNil(t, handler, "Middleware should wrap the handler")
	})

	t.Run("localhost_host_variants", func(t *testing.T) {
		// Test various localhost host formats (isDev = true)
		localhostHosts := []string{
			"localhost:4200",
			"localhost:3000",
			"localhost:8080",
			"localhost",
		}

		for _, host := range localhostHosts {
			req := httptest.NewRequest("GET", "/test", nil)
			req.Host = host
			rr := httptest.NewRecorder()

			protectedHandler.ServeHTTP(rr, req)
			assert.Equal(t, http.StatusUnauthorized, rr.Code,
				"Host %s should fail at cookie stage", host)
		}
	})

	t.Run("production_host_variants", func(t *testing.T) {
		// Test various production host formats (isDev = false)
		prodHosts := []string{
			"example.com",
			"api.example.com",
			"sub.domain.com",
			"production.com:8080",
		}

		for _, host := range prodHosts {
			req := httptest.NewRequest("GET", "/test", nil)
			req.Host = host
			rr := httptest.NewRecorder()

			protectedHandler.ServeHTTP(rr, req)
			assert.Equal(t, http.StatusUnauthorized, rr.Code,
				"Host %s should fail at cookie stage", host)
		}
	})

	t.Run("context_propagation", func(t *testing.T) {
		// Test that existing context values are preserved
		originalCtx := context.WithValue(context.Background(), "test", "value")

		testHandlerWithContext := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Verify original context is preserved
			value := r.Context().Value("test")
			assert.Equal(t, "value", value, "Original context should be preserved")
			w.WriteHeader(http.StatusOK)
		})

		protectedHandlerWithContext := AuthMiddleware(mockSessionStore)(testHandlerWithContext)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(originalCtx)
		req.Host = "localhost:4200"
		rr := httptest.NewRecorder()

		// Will fail at cookie stage but context should be preserved
		protectedHandlerWithContext.ServeHTTP(rr, req)
		assert.Equal(t, http.StatusUnauthorized, rr.Code)
	})

	t.Run("malformed_id_tokens", func(t *testing.T) {
		// Test various malformed ID token scenarios
		malformedTokens := []struct {
			name    string
			idToken any
		}{
			{"empty_string", ""},
			{"malformed_jwt", "not.a.jwt.token"},
			{"incomplete_jwt", "header.payload"},
			{"random_string", "random-string-not-jwt"},
		}

		for _, tokenTest := range malformedTokens {
			t.Run(tokenTest.name, func(t *testing.T) {
				token := &oauth2.Token{AccessToken: "test"}
				token = token.WithExtra(map[string]any{
					"id_token": tokenTest.idToken,
				})

				sessionID := "malformed-" + tokenTest.name
				mockSessionStore.On("GetSession", sessionID).Return(&domain.Session{
					UserID:     "user123",
					OAuthToken: domain.FromOAuth2Token(token),
				}, true)

				req := httptest.NewRequest("GET", "/test", nil)
				req.Host = "localhost:4200"
				req.AddCookie(&http.Cookie{Name: "session_id", Value: sessionID})
				rr := httptest.NewRecorder()

				protectedHandler.ServeHTTP(rr, req)
				assert.Equal(t, http.StatusUnauthorized, rr.Code)
				assert.Contains(t, rr.Body.String(), "Unauthorized")
			})
		}
	})
}

func TestAuthMiddleware_EdgeCases(t *testing.T) {
	mockSessionStore := &onrampMocks.MockSessionStore{}

	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	protectedHandler := AuthMiddleware(mockSessionStore)(testHandler)

	t.Run("session_cookie_edge_cases", func(t *testing.T) {
		edgeCases := []struct {
			name       string
			cookieName string
			wantCode   int
		}{
			{"correct_cookie_name", "session_id", http.StatusUnauthorized},  // Will fail at session lookup
			{"wrong_cookie_name", "wrong_session", http.StatusUnauthorized}, // Will fail at cookie lookup
		}

		for _, ec := range edgeCases {
			t.Run(ec.name, func(t *testing.T) {
				// Set up mock expectation for the session lookup
				if ec.cookieName == "session_id" {
					mockSessionStore.On("GetSession", "some-value").Return((*domain.Session)(nil), false)
				}

				req := httptest.NewRequest("GET", "/test", nil)
				req.Host = "localhost:4200"
				req.AddCookie(&http.Cookie{Name: ec.cookieName, Value: "some-value"})
				rr := httptest.NewRecorder()

				protectedHandler.ServeHTTP(rr, req)
				assert.Equal(t, ec.wantCode, rr.Code)
			})
		}
	})

	t.Run("oidc_config_selection_logic", func(t *testing.T) {
		// Test the map[bool]*auth.OIDCConfig selection logic
		testCases := []struct {
			host  string
			isDev bool
		}{
			{"localhost:4200", true},
			{"localhost:3000", true},
			{"localhost", true},
			{"example.com", false},
			{"api.example.com", false},
		}

		for _, tc := range testCases {
			req := httptest.NewRequest("GET", "/test", nil)
			req.Host = tc.host
			rr := httptest.NewRecorder()

			// This exercises the config selection map logic even though it fails at cookie stage
			protectedHandler.ServeHTTP(rr, req)
			assert.Equal(t, http.StatusUnauthorized, rr.Code,
				"Host %s should fail at cookie stage", tc.host)
		}
	})
}

func TestAuthMiddleware_CoverageImprovement(t *testing.T) {
	mockSessionStore := &onrampMocks.MockSessionStore{}

	// Test middleware creation function coverage
	t.Run("middleware_function_calls", func(t *testing.T) {
		// Test the function creation and execution paths
		middleware := AuthMiddleware(mockSessionStore)
		assert.NotNil(t, middleware, "AuthMiddleware should return a function")

		testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		})

		wrappedHandler := middleware(testHandler)
		assert.NotNil(t, wrappedHandler, "Middleware should wrap the handler")

		// Test that the wrapped handler executes the middleware logic
		req := httptest.NewRequest("GET", "/test", nil)
		req.Host = "localhost:4200"
		rr := httptest.NewRecorder()

		wrappedHandler.ServeHTTP(rr, req)
		// Should fail at cookie stage
		assert.Equal(t, http.StatusUnauthorized, rr.Code)
	})

	t.Run("token_extra_method_coverage", func(t *testing.T) {
		// Test the .Extra() method call on OAuth token
		protectedHandler := AuthMiddleware(mockSessionStore)(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		}))

		// Create a token with Extra data
		tokenWithExtra := &oauth2.Token{AccessToken: "test"}
		tokenWithExtra = tokenWithExtra.WithExtra(map[string]any{
			"id_token":    "some.fake.token",
			"other_field": "other_value",
		})

		mockSessionStore.On("GetSession", "token-extra-test").Return(&domain.Session{
			UserID:     "user123",
			OAuthToken: domain.FromOAuth2Token(tokenWithExtra),
		}, true)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Host = "localhost:4200"
		req.AddCookie(&http.Cookie{Name: "session_id", Value: "token-extra-test"})
		rr := httptest.NewRecorder()

		protectedHandler.ServeHTTP(rr, req)
		// Should fail at token verification but exercises the .Extra() method
		assert.Equal(t, http.StatusUnauthorized, rr.Code)
		assert.Contains(t, rr.Body.String(), "Unauthorized")
	})

	t.Run("context_client_context_usage", func(t *testing.T) {
		// Test that oidc.ClientContext is called with proper context
		protectedHandler := AuthMiddleware(mockSessionStore)(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		}))

		// Test with both localhost and production to exercise context creation
		hosts := []string{"localhost:4200", "example.com"}
		for _, host := range hosts {
			req := httptest.NewRequest("GET", "/test", nil)
			req.Host = host
			rr := httptest.NewRecorder()

			// This exercises the oidc.ClientContext call
			protectedHandler.ServeHTTP(rr, req)
			assert.Equal(t, http.StatusUnauthorized, rr.Code)
		}
	})

	t.Run("verifier_access_coverage", func(t *testing.T) {
		// Test that verifier is accessed from oidcConfig
		protectedHandler := AuthMiddleware(mockSessionStore)(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		}))

		// Create session to get past session check
		invalidToken := &oauth2.Token{AccessToken: "test"}
		invalidToken = invalidToken.WithExtra(map[string]any{
			"id_token": "invalid.token.here",
		})

		mockSessionStore.On("GetSession", "verifier-test").Return(&domain.Session{
			UserID:     "user123",
			OAuthToken: domain.FromOAuth2Token(invalidToken),
		}, true)

		// Test both localhost and production to exercise verifier access
		hosts := []string{"localhost:4200", "production.example.com"}
		for _, host := range hosts {
			req := httptest.NewRequest("GET", "/test", nil)
			req.Host = host
			req.AddCookie(&http.Cookie{Name: "session_id", Value: "verifier-test"})
			rr := httptest.NewRecorder()

			protectedHandler.ServeHTTP(rr, req)
			// Should fail at verification but exercises verifier access
			assert.Equal(t, http.StatusUnauthorized, rr.Code)
			assert.Contains(t, rr.Body.String(), "Unauthorized")
		}
	})
}

// TestAuthMiddleware_UsernamePasswordAuth tests the new username/password authentication path
func TestAuthMiddleware_UsernamePasswordAuth(t *testing.T) {
	mockSessionStore := &onrampMocks.MockSessionStore{}

	// Create a test handler that verifies claims are properly set
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		claims, ok := r.Context().Value(domain.UserKey).(map[string]any)
		if !ok {
			http.Error(w, "no claims in context", http.StatusInternalServerError)
			return
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(claims)
	})

	protectedHandler := AuthMiddleware(mockSessionStore)(testHandler)

	t.Run("successful_username_password_auth", func(t *testing.T) {
		// Test successful username/password authentication (OAuthToken is nil)
		userID := "123e4567-e89b-12d3-a456-426614174000"
		sessionData := &domain.Session{
			UserID:          userID,
			OAuthToken:      nil, // This is the key difference for username/password auth
			UserPermissions: nil,
			Claims: map[string]any{
				"sub":   userID,
				"name":  "Test User",
				"email": "<EMAIL>",
			},
		}

		mockSessionStore.On("GetSession", "username-password-session").Return(sessionData, true)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Host = "localhost:4200"
		req.AddCookie(&http.Cookie{Name: "session_id", Value: "username-password-session"})
		rr := httptest.NewRecorder()

		protectedHandler.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusOK, rr.Code)

		// Verify the response contains the expected claims
		var claims map[string]any
		err := json.NewDecoder(rr.Body).Decode(&claims)
		assert.NoError(t, err)
		assert.Equal(t, userID, claims["sub"])
	})

	t.Run("username_password_auth_production_host", func(t *testing.T) {
		// Test username/password auth with production host
		userID := "987fcdeb-51a2-43d1-b654-321987654321"
		sessionData := &domain.Session{
			UserID:          userID,
			OAuthToken:      nil,
			UserPermissions: nil,
			Claims: map[string]any{
				"sub":   userID,
				"name":  "Prod User",
				"email": "<EMAIL>",
			},
		}

		mockSessionStore.On("GetSession", "prod-username-password-session").Return(sessionData, true)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Host = "api.example.com"
		req.AddCookie(&http.Cookie{Name: "session_id", Value: "prod-username-password-session"})
		rr := httptest.NewRecorder()

		protectedHandler.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusOK, rr.Code)

		// Verify the response contains the expected claims
		var claims map[string]any
		err := json.NewDecoder(rr.Body).Decode(&claims)
		assert.NoError(t, err)
		assert.Equal(t, userID, claims["sub"])
	})

	t.Run("username_password_auth_with_permissions", func(t *testing.T) {
		// Test username/password auth with user permissions
		userID := "456e7890-e89b-12d3-a456-426614174001"
		userPermissions := &domain.UserPermissions{
			// Add any permission fields as needed
		}
		sessionData := &domain.Session{
			UserID:          userID,
			OAuthToken:      nil,
			UserPermissions: userPermissions,
			Claims: map[string]any{
				"sub":   userID,
				"name":  "User With Permissions",
				"email": "<EMAIL>",
			},
		}

		mockSessionStore.On("GetSession", "username-password-with-perms").Return(sessionData, true)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Host = "localhost:4200"
		req.AddCookie(&http.Cookie{Name: "session_id", Value: "username-password-with-perms"})
		rr := httptest.NewRecorder()

		protectedHandler.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusOK, rr.Code)

		// Verify the response contains the expected claims
		var claims map[string]any
		err := json.NewDecoder(rr.Body).Decode(&claims)
		assert.NoError(t, err)
		assert.Equal(t, userID, claims["sub"])
	})

	t.Run("empty_user_id_username_password", func(t *testing.T) {
		// Test username/password auth with empty user ID
		sessionData := &domain.Session{
			UserID:          "",
			OAuthToken:      nil,
			UserPermissions: nil,
			Claims: map[string]any{
				"sub":   "",
				"name":  "Empty User",
				"email": "<EMAIL>",
			},
		}

		mockSessionStore.On("GetSession", "empty-userid-session").Return(sessionData, true)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Host = "localhost:4200"
		req.AddCookie(&http.Cookie{Name: "session_id", Value: "empty-userid-session"})
		rr := httptest.NewRecorder()

		protectedHandler.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusOK, rr.Code)

		// Verify the response contains claims with empty sub
		var claims map[string]any
		err := json.NewDecoder(rr.Body).Decode(&claims)
		assert.NoError(t, err)
		assert.Equal(t, "", claims["sub"])
	})

	// Verify all mock expectations were met
	mockSessionStore.AssertExpectations(t)
}

// TestAuthMiddleware_MixedAuthenticationMethods tests both OAuth and username/password paths
func TestAuthMiddleware_MixedAuthenticationMethods(t *testing.T) {
	mockSessionStore := &onrampMocks.MockSessionStore{}

	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		claims, ok := r.Context().Value(domain.UserKey).(map[string]any)
		if !ok {
			http.Error(w, "no claims in context", http.StatusInternalServerError)
			return
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]any{
			"claims": claims,
			"auth_type": func() string {
				if _, hasIss := claims["iss"]; hasIss {
					return "oauth"
				}
				return "username_password"
			}(),
		})
	})

	protectedHandler := AuthMiddleware(mockSessionStore)(testHandler)

	t.Run("oauth_vs_username_password_comparison", func(t *testing.T) {
		// Test OAuth session (with token)
		oauthToken := &oauth2.Token{AccessToken: "oauth-token"}
		oauthToken = oauthToken.WithExtra(map[string]any{
			"id_token": "invalid.jwt.for.test", // Will fail verification but tests the path
		})

		oauthSession := &domain.Session{
			UserID:     "oauth-user-123",
			OAuthToken: domain.FromOAuth2Token(oauthToken),
			Claims: map[string]any{
				"sub":   "oauth-user-123",
				"name":  "OAuth User",
				"email": "<EMAIL>",
			},
		}

		// Test username/password session (without token)
		usernamePasswordSession := &domain.Session{
			UserID:     "username-user-456",
			OAuthToken: nil,
			Claims: map[string]any{
				"sub":   "username-user-456",
				"name":  "Username Password User",
				"email": "<EMAIL>",
			},
		}

		mockSessionStore.On("GetSession", "oauth-session").Return(oauthSession, true)
		mockSessionStore.On("GetSession", "username-password-session").Return(usernamePasswordSession, true)

		// Test OAuth path (should succeed with pre-stored claims)
		req1 := httptest.NewRequest("GET", "/test", nil)
		req1.Host = "localhost:4200"
		req1.AddCookie(&http.Cookie{Name: "session_id", Value: "oauth-session"})
		rr1 := httptest.NewRecorder()

		protectedHandler.ServeHTTP(rr1, req1)
		assert.Equal(t, http.StatusOK, rr1.Code, "OAuth path should succeed with pre-stored claims")

		// Test username/password path (should succeed)
		req2 := httptest.NewRequest("GET", "/test", nil)
		req2.Host = "localhost:4200"
		req2.AddCookie(&http.Cookie{Name: "session_id", Value: "username-password-session"})
		rr2 := httptest.NewRecorder()

		protectedHandler.ServeHTTP(rr2, req2)
		assert.Equal(t, http.StatusOK, rr2.Code, "Username/password path should succeed")

		// Verify username/password response
		var response map[string]any
		err := json.NewDecoder(rr2.Body).Decode(&response)
		assert.NoError(t, err)
		assert.Equal(t, "username_password", response["auth_type"])

		claims := response["claims"].(map[string]any)
		assert.Equal(t, "username-user-456", claims["sub"])
	})

	mockSessionStore.AssertExpectations(t)
}

// TestAuthMiddleware_EdgeCasesUsernamePassword tests edge cases specific to username/password auth
func TestAuthMiddleware_EdgeCasesUsernamePassword(t *testing.T) {
	mockSessionStore := &onrampMocks.MockSessionStore{}

	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		claims, ok := r.Context().Value(domain.UserKey).(map[string]any)
		if !ok {
			http.Error(w, "no claims in context", http.StatusInternalServerError)
			return
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(claims)
	})

	protectedHandler := AuthMiddleware(mockSessionStore)(testHandler)

	t.Run("nil_session_oauth_token_field", func(t *testing.T) {
		// Explicitly test that nil OAuthToken is handled correctly with pre-stored claims
		sessionData := &domain.Session{
			UserID:          "test-user-nil-token",
			OAuthToken:      nil, // Explicitly nil
			UserPermissions: nil,
			Claims: map[string]any{
				"sub":   "test-user-nil-token",
				"name":  "Test User",
				"email": "<EMAIL>",
			},
		}

		mockSessionStore.On("GetSession", "nil-oauth-token").Return(sessionData, true)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Host = "localhost:4200"
		req.AddCookie(&http.Cookie{Name: "session_id", Value: "nil-oauth-token"})
		rr := httptest.NewRecorder()

		protectedHandler.ServeHTTP(rr, req)

		assert.Equal(t, http.StatusOK, rr.Code)

		var claims map[string]any
		err := json.NewDecoder(rr.Body).Decode(&claims)
		assert.NoError(t, err)
		assert.Equal(t, "test-user-nil-token", claims["sub"])

		// Verify expected claims are present
		assert.Contains(t, claims, "sub")
		assert.Contains(t, claims, "name")
		assert.Contains(t, claims, "email")
		assert.Equal(t, "Test User", claims["name"])
		assert.Equal(t, "<EMAIL>", claims["email"])
	})

	t.Run("context_value_type_assertion", func(t *testing.T) {
		// Test that the context value is properly set as map[string]any
		contextTestHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Test type assertion
			claims, ok := r.Context().Value(domain.UserKey).(map[string]any)
			assert.True(t, ok, "Claims should be of type map[string]any")
			assert.NotNil(t, claims, "Claims should not be nil")

			// Test that sub exists and is a string
			sub, subExists := claims["sub"]
			assert.True(t, subExists, "Claims should contain 'sub' field")
			assert.IsType(t, "", sub, "Sub should be a string")

			w.WriteHeader(http.StatusOK)
		})

		protectedHandlerContext := AuthMiddleware(mockSessionStore)(contextTestHandler)

		sessionData := &domain.Session{
			UserID:     "context-test-user",
			OAuthToken: nil,
			Claims: map[string]any{
				"sub":   "context-test-user",
				"name":  "Context Test User",
				"email": "<EMAIL>",
			},
		}

		mockSessionStore.On("GetSession", "context-test").Return(sessionData, true)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Host = "localhost:4200"
		req.AddCookie(&http.Cookie{Name: "session_id", Value: "context-test"})
		rr := httptest.NewRecorder()

		protectedHandlerContext.ServeHTTP(rr, req)
		assert.Equal(t, http.StatusOK, rr.Code)
	})

	mockSessionStore.AssertExpectations(t)
}
