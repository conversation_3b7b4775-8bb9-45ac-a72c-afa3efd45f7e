package authmethods

import "time"

// A single authentication method record
type AuthMethodR<PERSON>ord struct {
	ID            string     `json:"id" db:"id"`
	Type          string     `json:"type" db:"type"`
	UserName      string     `json:"userName" db:"username"`
	Email         string     `json:"email" db:"email"`
	FirstName     string     `json:"first_name" db:"firstname"`
	LastName      string     `json:"last_name" db:"lastname"`
	Organizations []string   `json:"organizations" db:"organizations"`
	IsEnabled     bool       `json:"is_enabled" db:"isenabled"`
	LastLogin     *time.Time `json:"last_login,omitempty" db:"lastlogin"`
}

// Struct for validating the auth method
type ValidateAuthMethodRecord struct {
	AuthMethodID   string `db:"id"`
	AuthMethodType string `db:"type"`
}

// Struct for validating the current password
type ValidateCurrentPasswordRecord struct {
	PasswordHash string `db:"passwordhash"`
}

// PasswordUpdateRequest is the request body for updating a password
type PasswordUpdateRequest struct {
	CurrentPassword string `json:"current_password"`
	NewPassword     string `json:"new_password"`
	ConfirmPassword string `json:"confirm_password"`
}
