package socketio

import "errors"

var (
	ErrFailedToUnmarshalWrapperCommand = errors.New("failed to unmarshal wrapper command")
	ErrUnableToDetermineCommandType    = errors.New("unable to determine command type")
	ErrCommandNotPermitted             = errors.New("command not permitted")
	ErrCommandPayloadEmpty             = errors.New("command payload cannot be empty")
	ErrUnauthorized                    = errors.New("unauthorized")
	ErrDeviceAccessDenied              = errors.New("device access denied")
)
