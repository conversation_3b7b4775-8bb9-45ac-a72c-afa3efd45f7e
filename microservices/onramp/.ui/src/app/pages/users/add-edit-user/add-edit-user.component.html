<nz-modal class="modal-create-invite" [(nzVisible)]="isVisible" [nzTitle]="modalTitle" (nzOnCancel)="handleCancel()"
  [nzOkText]="'Save'" [nzFooter]="modalFooter" nzWidth="440px">
  <ng-template #modalTitle>
    <div class="content-header">
      <h2 style="margin-bottom: 4px;">{{ isEditMode ? 'Edit User' : 'Invite User' }}</h2>
      <span>
        You are inviting a user to the <b>{{ nameOrg ? nameOrg : 'Synapse' }}</b> Organization.
        They will be assigned the role that you specify here.
      </span>
    </div>
  </ng-template>
  <ng-container *nzModalContent>
    <nz-form [formGroup]="form" class="modal-add-invite">
      <nz-form-item class="form-item">
        <nz-form-control nzErrorTip="Please Input E-mail.">
          <label>E-mail</label>
          <nz-input-group>
            <input class="h-4 br-8" nz-input id="input-user-email" formControlName="email" placeholder="Enter E-mail"
              [disabled]="isEditMode" />
          </nz-input-group>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item class="form-item">
        <nz-form-control nzErrorTip="Please select an Organization Role!">
          <label>Organization Role</label>
          <nz-select class="select-template" formControlName="organizationrole"
            nzPlaceHolder="Select an Organization Role">
            <nz-option *ngFor="let role of listOrgRole" [nzValue]="role.value" [nzLabel]="role.label"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item class="form-item">
        <nz-form-control>
          <label>Message (Optional)</label>
          <nz-textarea-count [nzMaxCharacterCount]="2000">
            <textarea class="br-8" id="input-user-message" formControlName="message" nz-input rows="4"
              placeholder="Enter Role Message" [disabled]="isEditMode"></textarea>
          </nz-textarea-count>
        </nz-form-control>
      </nz-form-item>
    </nz-form>
  </ng-container>
  <ng-template #modalFooter>
    <button id="btn-create-invite" class="btn-submit-invite h-4 br-8" nz-button nzType="primary" (click)="handleSave()">
      Send Invitation
    </button>
  </ng-template>
</nz-modal>