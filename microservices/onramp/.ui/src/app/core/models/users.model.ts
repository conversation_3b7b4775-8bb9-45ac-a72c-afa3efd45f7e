export interface Users {
  id: string;
  firstName: string;
  lastName: string;
  userName: string;
  email: string;
  authMethod: string;
  orgRole: string;
  lastLogin: string;
  userId: string;
}
export interface Invitations {
  id: string;
  email: string;
  created: string;
  timesNotified: string;
  lastNotification: string;
  accepted: string;
  status: string;
  expired: string;
  updated: string;
}