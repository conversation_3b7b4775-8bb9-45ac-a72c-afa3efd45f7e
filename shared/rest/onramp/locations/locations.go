package locations

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/rest/onramp/helper"
)

// HandlerDeps enables dependency injection
type HandlerDeps struct {
	GetConnections             func(context.Context, ...bool) (*connect.Connections, error)
	GetLocationsByOrganization func(connect.DatabaseExecutor, uuid.UUID) (*[]Location, error)
	CreateLocation             func(connect.DatabaseExecutor, uuid.UUID, *CreateAndUpdateLocationRequest) (*Location, error)
	DeleteLocation             func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID) error
	UpdateLocation             func(connect.DatabaseExecutor, uuid.UUID, uuid.UUID, *CreateAndUpdateLocationRequest) error
}

// GetLocationsHandlerWithDeps
func GetLocationsHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		orgId, err := helper.ValidateOrganizationID(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		locations, err := deps.GetLocationsByOrganization(pg, orgId)
		if err != nil {
			logger.Errorf("failed to get locations for organization %s: %v", orgId, err)
			response.CreateInternalErrorResponse(w)
			return
		}
		response.CreateSuccessResponse(locations, w)
	}
}

// CreateLocationHandlerWithDeps
func CreateLocationHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		orgId, err := helper.ValidateOrganizationID(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		req, err := parseCreateAndUpdateLocationRequest(r)
		if err != nil {
			logger.Errorf("Error parsing request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		loc, err := deps.CreateLocation(pg, orgId, req)
		if err != nil {
			if err == ErrOrganizationNotFound {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("failed to create location for organization %s: %v", orgId, err)
			response.CreateInternalErrorResponse(w)
			return
		}
		response.CreateSuccessResponse(loc, w)
	}
}

// UpdateLocationHandlerWithDeps
func UpdateLocationHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		orgId, err := helper.ValidateOrganizationID(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}
		locationId, err := helper.ParseUUIDFromRequest(r, "locationId")
		if err != nil {
			logger.Errorf("invalid location ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		req, err := parseCreateAndUpdateLocationRequest(r)
		if err != nil {
			logger.Errorf("Error parsing request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		if err := deps.UpdateLocation(pg, orgId, locationId, req); err != nil {
			if err == ErrLocationNotFound {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("failed to update location %s for organization %s: %v", locationId, orgId, err)
			response.CreateInternalErrorResponse(w)
			return
		}
		response.CreateSuccessResponse(nil, w)
	}
}

// DeleteLocationHandlerWithDeps
func DeleteLocationHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		orgId, err := helper.ValidateOrganizationID(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}
		locationId, err := helper.ParseUUIDFromRequest(r, "locationId")
		if err != nil {
			logger.Errorf("invalid location ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		if err := deps.DeleteLocation(pg, orgId, locationId); err != nil {
			if err == ErrLocationNotFound {
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("failed to delete location %s for organization %s: %v", locationId, orgId, err)
			response.CreateInternalErrorResponse(w)
			return
		}
		response.CreateSuccessResponse(nil, w)
	}
}

// Query fns and parsers
var getLocationsByOrganization = func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*[]Location, error) {
	query := `
		SELECT Id, OrganizationId, Name, Description, Latitude, Longitude, IsDeleted, CreatedAt, UpdatedAt
		FROM {{Location}}
		WHERE OrganizationId = $1 AND NOT IsDeleted
		ORDER BY CreatedAt DESC`
	var locations []Location
	if err := pg.QueryGenericSlice(&locations, query, orgId); err != nil {
		logger.Errorf("Failed to get locations for organization %s: %v", orgId, err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}
	return &locations, nil
}

func parseCreateAndUpdateLocationRequest(r *http.Request) (*CreateAndUpdateLocationRequest, error) {
	var req CreateAndUpdateLocationRequest
	dec := json.NewDecoder(r.Body)
	dec.DisallowUnknownFields()
	if err := dec.Decode(&req); err != nil {
		logger.Infof("failed to parse create/update location request: %v", err)
		if strings.Contains(err.Error(), "unknown field") {
			return &req, ErrUnexpectedFields
		}
		return &req, ErrInvalidRequestBody
	}
	if strings.TrimSpace(req.Name) == "" {
		return &req, ErrInvalidLocationName
	}
	if strings.TrimSpace(req.Description) == "" {
		return &req, ErrInvalidLocationDesc
	}
	if req.Latitude < -90 || req.Latitude > 90 {
		return &req, ErrInvalidLatitude
	}
	if req.Longitude < -180 || req.Longitude > 180 {
		return &req, ErrInvalidLongitude
	}
	return &req, nil
}

var createLocation = func(pg connect.DatabaseExecutor, orgId uuid.UUID, req *CreateAndUpdateLocationRequest) (*Location, error) {
	checkQuery := `
		SELECT EXISTS(SELECT 1 FROM {{Organization}} WHERE Id = $1 AND NOT IsDeleted) AS exists`
	row, err := pg.QueryRow(checkQuery, orgId)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}
	existsVal, ok := row["exists"]
	if !ok {
		return nil, fmt.Errorf("%w: missing exists column in query result", ErrDatabaseOperation)
	}
	exists, ok := existsVal.(bool)
	if !ok {
		return nil, fmt.Errorf("%w: invalid type for exists column", ErrDatabaseOperation)
	}
	if !exists {
		return nil, ErrOrganizationNotFound
	}
	now := time.Now().UTC()
	insert := `
		INSERT INTO {{Location}} (OrganizationId, Name, Description, Latitude, Longitude, CreatedAt, UpdatedAt, IsDeleted)
		VALUES ($1, $2, $3, $4, $5, $6, $7, false)
		RETURNING Id, OrganizationId, Name, Description, Latitude, Longitude, IsDeleted, CreatedAt, UpdatedAt`
	var loc Location
	if err := pg.QueryRowStruct(&loc, insert, orgId, req.Name, req.Description, req.Latitude, req.Longitude, now, now); err != nil {
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}
	return &loc, nil
}

var deleteLocation = func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID) error {
	query := `
		UPDATE {{Location}}
		SET IsDeleted = true, UpdatedAt = NOW()
		WHERE Id = $1 AND OrganizationId = $2 AND NOT IsDeleted`
	res, err := pg.Exec(query, locationId, orgId)
	if err != nil {
		logger.Errorf("Failed to delete location %s for organization %s: %v", locationId, orgId, err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}
	rows, err := res.RowsAffected()
	if err != nil {
		logger.Errorf("Failed to get rows affected for location deletion %s: %v", locationId, err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}
	if rows == 0 {
		return ErrLocationNotFound
	}
	return nil
}

var updateLocation = func(pg connect.DatabaseExecutor, orgId uuid.UUID, locationId uuid.UUID, req *CreateAndUpdateLocationRequest) error {
	query := `
		UPDATE {{Location}}
		SET Name = $1, Description = $2, Latitude = $3, Longitude = $4, UpdatedAt = NOW()
		WHERE Id = $5 AND OrganizationId = $6 AND NOT IsDeleted`
	res, err := pg.Exec(query, req.Name, req.Description, req.Latitude, req.Longitude, locationId, orgId)
	if err != nil {
		logger.Errorf("Failed to update location %s for organization %s: %v", locationId, orgId, err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}
	rows, err := res.RowsAffected()
	if err != nil {
		logger.Errorf("Failed to get rows affected for location update %s: %v", locationId, err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}
	if rows == 0 {
		return ErrLocationNotFound
	}
	return nil
}

var (
	GetLocationsHandler   = GetLocationsHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, GetLocationsByOrganization: getLocationsByOrganization})
	CreateLocationHandler = CreateLocationHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, CreateLocation: createLocation})
	DeleteLocationHandler = DeleteLocationHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, DeleteLocation: deleteLocation})
	UpdateLocationHandler = UpdateLocationHandlerWithDeps(HandlerDeps{GetConnections: connect.GetConnections, UpdateLocation: updateLocation})
)
