package authmethods

import "errors"

var (
	ErrAuthMethodNotFound     = errors.New("auth method not found")
	ErrInvalidAuthMethodID    = errors.New("invalid auth method ID")
	ErrInvalidAuthMethodType  = errors.New("invalid auth method type")
	ErrDatabaseOperation      = errors.New("database operation failed")
	ErrUnexpectedFields       = errors.New("unexpected fields")
	ErrInvalidRequestBody     = errors.New("invalid request body")
	ErrInvalidCurrentPassword = errors.New("invalid current password")
	ErrInvalidNewPassword     = errors.New("invalid new password")
	ErrInvalidConfirmPassword = errors.New("invalid confirm password")
	ErrPasswordMismatch       = errors.New("password mismatch")
)
