<div class="login-container">
  <h2>Login</h2>
  <p class="login-subtitle">
    Login with either your local username and password
    or choose a Single Sign On (SSO) provider.
  </p>

  <form id="form-login" nz-form class="form-login" [formGroup]="loginForm" (ngSubmit)="onSubmit()">
    <nz-form-item>
      <nz-form-control [nzErrorTip]="getUsernameErrorTip()">
        <span>Username</span>
        <nz-input-group nzPrefixIcon="user">
          <input id="username" nz-input formControlName="username" placeholder="Username" />
        </nz-input-group>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-control nzErrorTip="Please enter your Password!">
        <span>Password</span>
        <nz-input-group [nzPrefixIcon]="passwordVisible ? 'eye' : 'lock'" [nzSuffix]="suffixTemplate">
          <input id="password" formControlName="password" nz-input placeholder="Password"
            [type]="passwordVisible ? 'text' : 'password'" autocomplete="current-password" />
        </nz-input-group>
        <ng-template #suffixTemplate>
          <nz-icon *ngIf="isClearValue()" (click)="handleClear()" class="ant-input-clear-icon mr-5" nzTheme="fill"
            nzType="close-circle" />
          <span nz-icon [nzType]="passwordVisible ? 'eye-invisible' : 'eye'" style="cursor: pointer;"
            (click)="togglePasswordVisibility()"></span>
        </ng-template>
      </nz-form-control>
    </nz-form-item>
    <div class="button-group">
      <button nz-button nzType="default" type="button" (click)="forgotPassword()">Forgot Password</button>
      <button id="kc-login" nz-button nzType="primary" type="submit" [nzLoading]="isLoading"
        [disabled]="!loginForm.valid">Login</button>
    </div>
  </form>

  <!-- Single Sign-On -->
  <div class="sso-section">
    <h3>Single Sign On</h3>
    <p class="sso-subtitle">
      Sign in with your company’s SSO. If you do not already have an account on this system,
      then one will be created for you automatically.
    </p>
    <button id="btn-keycloak" nz-button nzType="default" nzBlock (click)="loginWithSSO()">Synapse SSO</button>
  </div>

  <!-- Create New Account -->
  <div class="create-account-section">
    <h3>Create New Account</h3>
    <p class="create-account-subtitle">
      Create a new Username/Password based account.
      Use this if there is no SSO option from your company.
    </p>
    <button id="register-btn" nz-button nzType="default" nzBlock (click)="createNewAccount()">
      Create New Account</button>
  </div>
</div>