Title:
As an Engineer, I need a REST endpoint for user to location group CRUD

Description:
When a user is added to an organization, a membership record is added to the database that connects the user (via their auth method) to the organization with a particular role.  This is for general membership within the organization, but it is not what this ticket is addressing.

This ticket addresses a different circumstance.

Devices can be added to one or more device groups, as determined by the organization.

The organization admins can then add a user to a device group with a particular role.  Note, the permissions associated with this role (see the device_group permissions) are only granted to the user in relation to the devices in that group.  This ticket, then, is to provide the REST endpoints to add/update/delete members (and their role/s) in relation to these device groups.  Note: a user may have more than one role, simply by adding multiple records to the table.

See the LocationGroupRoleAssignments postgres table definition:



CREATE TABLE {{LocationGroupRoleAssignments}} (
  MembershipId UUID NOT NULL,
  LocationGroupId UUID NOT NULL,
  RoleId UUID NOT NULL,
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT {{LocationGroupRoleAssignments_PK}} PRIMARY KEY (MembershipId, LocationGroupId, RoleId),
  CONSTRAINT {{LocationGroupRoleAssignments_Membership_FK}} FOREIGN KEY (MembershipId) REFERENCES {{Memberships}} (Id),
  CONSTRAINT {{LocationGroupRoleAssignments_LocationGroup_FK}} FOREIGN KEY (LocationGroupId) REFERENCES {{LocationGroups}} (Id),
  CONSTRAINT {{LocationGroupRoleAssignments_CustomRole_FK}} FOREIGN KEY (RoleId) REFERENCES {{CustomRole}} (Id)
);

CREATE INDEX {{LocationGroupRoleAssignments_MembershipId_IDX}} ON {{LocationGroupRoleAssignments}} (MembershipId);
CREATE INDEX {{LocationGroupRoleAssignments_LocationGroupId_IDX}} ON {{LocationGroupRoleAssignments}} (LocationGroupId);
CREATE INDEX {{LocationGroupRoleAssignments_RoleId_IDX}} ON {{LocationGroupRoleAssignments}} (RoleId);

Notice that the primary key is a complex key.
