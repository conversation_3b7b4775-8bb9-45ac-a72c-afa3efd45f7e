package authorizer

import "time"

type dbUserInfo struct {
	UserID        string    `db:"id"`
	ExpirationUTC time.Time `db:"expirationutc"`
}

// DeviceInfo represents basic information about a device for authorization purposes
type DeviceInfo struct {
	DeviceID         string   `db:"device_id"`
	OrganizationID   string   `db:"organization_id"`
	DeviceGroupIDs   []string `db:"device_group_ids"`
	LocationGroupIDs []string `db:"location_group_ids"`
}

// UserPermissions represents all permissions for a user across different scopes
type UserPermissions struct {
	UserID      string       `json:"user_id"`
	Permissions []Permission `json:"permissions"`
}

// Permission represents permissions within a specific scope (organization, device group)
type Permission struct {
	Scope          string   `json:"scope" db:"scope"`                     // "organization", "device_group", "location_group"
	ScopeID        string   `json:"scope_id" db:"scope_id"`               // UUID representing the group (organizationid, devicegroupid, locationgroupid)
	OrganizationID string   `json:"organization_id" db:"organization_id"` // Always present - the organization this permission belongs to
	Permissions    []string `json:"permissions" db:"permissions"`         // List of resolved permission identifiers for this scope
}

// User represents a user eligible for notifications
type User struct {
	ID           string `json:"user_id" db:"id"`
	Mobile       string `json:"mobile" db:"mobile"`
	IanaTimezone string `json:"iana_timezone" db:"ianatimezone"`
}
