package bigquery

import (
	"database/sql"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"synapse-its.com/shared/mocks"
)

// Mock sql.Result for testing
type mockSQLResult struct {
	mock.Mock
}

func (m *mockSQLResult) LastInsertId() (int64, error) {
	args := m.Called()
	return args.Get(0).(int64), args.Error(1)
}

func (m *mockSQLResult) RowsAffected() (int64, error) {
	args := m.Called()
	return args.Get(0).(int64), args.Error(1)
}

func TestNewApsResetAuditor(t *testing.T) {
	t.Parallel()

	// Arrange
	mockBQ := &mocks.FakeBigQueryExecutor{}

	// Act
	auditor := NewApsResetAuditor(mockBQ)

	// Assert
	assert.NotNil(t, auditor)
	assert.Equal(t, mockBQ, auditor.bq)
}

func TestApsResetAuditor_Audit(t *testing.T) {
	tests := []struct {
		name          string
		userID        uuid.UUID
		orgID         uuid.UUID
		challengeCode string
		err           error
		setupMock     func(*mocks.FakeBigQueryExecutor)
		expectedError string
		checkQuery    bool
		checkArgs     bool
	}{
		{
			name:          "Success - no error",
			userID:        uuid.MustParse("12345678-1234-1234-1234-123456789abc"),
			orgID:         uuid.MustParse("*************-4321-4321-cba987654321"),
			challengeCode: "challenge123",
			err:           nil,
			setupMock: func(mockBQ *mocks.FakeBigQueryExecutor) {
				mockResult := &mockSQLResult{}
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)

				mockBQ.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					// Verify the query contains expected elements
					assert.Contains(t, query, "INSERT INTO")
					assert.Contains(t, query, "APSFactoryResetLogs")
					assert.Contains(t, query, "user_id")
					assert.Contains(t, query, "organization_id")
					assert.Contains(t, query, "challenge_code")
					assert.Contains(t, query, "success")
					assert.Contains(t, query, "error_message")
					assert.Contains(t, query, "request_timestamp")

					// Verify the arguments
					assert.Len(t, args, 6)
					assert.Equal(t, "12345678-1234-1234-1234-123456789abc", args[0]) // user_id
					assert.Equal(t, "*************-4321-4321-cba987654321", args[1]) // organization_id
					assert.Equal(t, "challenge123", args[2])                         // challenge_code
					assert.Equal(t, true, args[3])                                   // success
					assert.Equal(t, "", args[4])                                     // error_message (empty for success)

					return mockResult, nil
				}
			},
			checkQuery: true,
			checkArgs:  true,
		},
		{
			name:          "Success - with error",
			userID:        uuid.MustParse("11111111-1111-1111-1111-111111111111"),
			orgID:         uuid.MustParse("*************-2222-2222-************"),
			challengeCode: "challenge456",
			err:           errors.New("some error occurred"),
			setupMock: func(mockBQ *mocks.FakeBigQueryExecutor) {
				mockResult := &mockSQLResult{}
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)

				mockBQ.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					// Verify the query contains expected elements
					assert.Contains(t, query, "INSERT INTO")
					assert.Contains(t, query, "APSFactoryResetLogs")

					// Verify the arguments
					assert.Len(t, args, 6)
					assert.Equal(t, "11111111-1111-1111-1111-111111111111", args[0]) // user_id
					assert.Equal(t, "*************-2222-2222-************", args[1]) // organization_id
					assert.Equal(t, "challenge456", args[2])                         // challenge_code
					assert.Equal(t, false, args[3])                                  // success
					assert.Equal(t, "some error occurred", args[4])                  // error_message

					return mockResult, nil
				}
			},
			checkQuery: true,
			checkArgs:  true,
		},
		{
			name:          "Success - empty challenge code",
			userID:        uuid.MustParse("*************-3333-3333-************"),
			orgID:         uuid.MustParse("*************-4444-4444-************"),
			challengeCode: "",
			err:           nil,
			setupMock: func(mockBQ *mocks.FakeBigQueryExecutor) {
				mockResult := &mockSQLResult{}
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)

				mockBQ.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					// Verify the arguments
					assert.Len(t, args, 6)
					assert.Equal(t, "", args[2])   // challenge_code (empty)
					assert.Equal(t, true, args[3]) // success

					return mockResult, nil
				}
			},
			checkArgs: true,
		},
		{
			name:          "Success - nil UUIDs",
			userID:        uuid.Nil,
			orgID:         uuid.Nil,
			challengeCode: "challenge789",
			err:           nil,
			setupMock: func(mockBQ *mocks.FakeBigQueryExecutor) {
				mockResult := &mockSQLResult{}
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)

				mockBQ.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					// Verify the arguments
					assert.Len(t, args, 6)
					assert.Equal(t, "00000000-0000-0000-0000-000000000000", args[0]) // user_id (nil UUID)
					assert.Equal(t, "00000000-0000-0000-0000-000000000000", args[1]) // organization_id (nil UUID)

					return mockResult, nil
				}
			},
			checkArgs: true,
		},
		{
			name:          "Error - database connection failed",
			userID:        uuid.MustParse("*************-5555-5555-************"),
			orgID:         uuid.MustParse("*************-6666-6666-************"),
			challengeCode: "challenge_error",
			err:           nil,
			setupMock: func(mockBQ *mocks.FakeBigQueryExecutor) {
				mockBQ.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("database connection failed")
				}
			},
			expectedError: "database connection failed",
		},
		{
			name:          "Error - bigquery timeout with existing error",
			userID:        uuid.MustParse("*************-7777-7777-************"),
			orgID:         uuid.MustParse("*************-8888-8888-************"),
			challengeCode: "challenge_error2",
			err:           errors.New("business logic error"),
			setupMock: func(mockBQ *mocks.FakeBigQueryExecutor) {
				mockBQ.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("bigquery timeout")
				}
			},
			expectedError: "bigquery timeout",
		},
		{
			name:          "Success - nil error should set success true and empty error message",
			userID:        uuid.MustParse("*************-9999-9999-************"),
			orgID:         uuid.MustParse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"),
			challengeCode: "challenge_nil_error",
			err:           nil,
			setupMock: func(mockBQ *mocks.FakeBigQueryExecutor) {
				mockResult := &mockSQLResult{}
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)

				mockBQ.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					assert.Equal(t, true, args[3]) // success should be true
					assert.Equal(t, "", args[4])   // error_message should be empty
					return mockResult, nil
				}
			},
		},
		{
			name:          "Success - non-nil error should set success false and error message",
			userID:        uuid.MustParse("bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb"),
			orgID:         uuid.MustParse("cccccccc-cccc-cccc-cccc-cccccccccccc"),
			challengeCode: "challenge_with_error",
			err:           errors.New("validation failed"),
			setupMock: func(mockBQ *mocks.FakeBigQueryExecutor) {
				mockResult := &mockSQLResult{}
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)

				mockBQ.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					assert.Equal(t, false, args[3])               // success should be false
					assert.Equal(t, "validation failed", args[4]) // error_message should contain error
					return mockResult, nil
				}
			},
		},
		{
			name:          "Success - empty string error should handle gracefully",
			userID:        uuid.MustParse("dddddddd-dddd-dddd-dddd-dddddddddddd"),
			orgID:         uuid.MustParse("eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee"),
			challengeCode: "challenge_empty_error",
			err:           errors.New(""),
			setupMock: func(mockBQ *mocks.FakeBigQueryExecutor) {
				mockResult := &mockSQLResult{}
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)

				mockBQ.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					assert.Equal(t, false, args[3]) // success should be false
					assert.Equal(t, "", args[4])    // error_message should be empty string
					return mockResult, nil
				}
			},
		},
		{
			name:          "Success - query structure verification",
			userID:        uuid.MustParse("ffffffff-ffff-ffff-ffff-ffffffffffff"),
			orgID:         uuid.MustParse("eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee"),
			challengeCode: "test_challenge",
			err:           nil,
			setupMock: func(mockBQ *mocks.FakeBigQueryExecutor) {
				mockResult := &mockSQLResult{}
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)

				mockBQ.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					// Verify query structure
					assert.Contains(t, query, "INSERT INTO")
					assert.Contains(t, query, "APSFactoryResetLogs")
					assert.Contains(t, query, "user_id")
					assert.Contains(t, query, "organization_id")
					assert.Contains(t, query, "challenge_code")
					assert.Contains(t, query, "success")
					assert.Contains(t, query, "error_message")
					assert.Contains(t, query, "request_timestamp")
					assert.Contains(t, query, "VALUES ($1, $2, $3, $4, $5, $6)")

					// Verify argument types and values
					assert.Len(t, args, 6)
					assert.Equal(t, "ffffffff-ffff-ffff-ffff-ffffffffffff", args[0])
					assert.Equal(t, "eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee", args[1])
					assert.Equal(t, "test_challenge", args[2])
					assert.Equal(t, true, args[3]) // success
					assert.Equal(t, "", args[4])   // error_message (empty for success)

					// Verify timestamp is recent (within 1 second)
					timestamp, ok := args[5].(time.Time)
					assert.True(t, ok, "Expected timestamp to be time.Time")
					assert.WithinDuration(t, time.Now(), timestamp, time.Second)

					return mockResult, nil
				}
			},
			checkQuery: true,
			checkArgs:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			mockBQ := &mocks.FakeBigQueryExecutor{}
			tt.setupMock(mockBQ)

			auditor := &apsResetAuditor{
				bq: mockBQ,
			}

			// Act
			err := auditor.Audit(tt.userID, tt.orgID, tt.challengeCode, tt.err)

			// Assert
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err.Error())
			} else {
				assert.NoError(t, err)
			}

			// Verify that Exec was called exactly once
			assert.Equal(t, 1, mockBQ.ExecCallCount)
		})
	}
}

func TestApsResetAuditor_InterfaceCompliance(t *testing.T) {
	t.Parallel()

	// This test ensures that apsResetAuditor implements the expected interface
	// The interface is defined in the business logic layer as:
	// type apsResetAuditor interface {
	//     Audit(userID, orgID uuid.UUID, challengeCode string, err error) error
	// }

	// Create a mock that satisfies the interface
	var auditor interface {
		Audit(userID, orgID uuid.UUID, challengeCode string, err error) error
	} = &apsResetAuditor{}

	// Test that the interface method can be called
	userID := uuid.New()
	orgID := uuid.New()

	// This should compile and run without errors
	assert.NotNil(t, auditor)

	// Test that the method signature is correct
	// This is a compile-time check, but we can also verify it works at runtime
	mockBQ := &mocks.FakeBigQueryExecutor{}
	mockResult := &mockSQLResult{}
	mockResult.On("LastInsertId").Return(int64(1), nil)
	mockResult.On("RowsAffected").Return(int64(1), nil)

	mockBQ.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
		return mockResult, nil
	}

	// Cast to concrete type to set the mock
	concreteAuditor := auditor.(*apsResetAuditor)
	concreteAuditor.bq = mockBQ

	// Test the method call
	err := auditor.Audit(userID, orgID, "test", nil)
	assert.NoError(t, err)
}
