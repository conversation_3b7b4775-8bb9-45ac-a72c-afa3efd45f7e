package domain

// Session holds information for a user session in the system.
type Session struct {
	UserID          string
	OAuthToken      *Token
	UserPermissions *UserPermissions
	Claims          map[string]any
}

// SessionStore defines the interface for session management.
type SessionStore interface {
	GetSession(sessionID string) (*Session, bool)
	SetSession(sessionID string, session *Session)
	ClearSession(sessionID string)
}
