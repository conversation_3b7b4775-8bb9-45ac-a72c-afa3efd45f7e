<nz-sider (scroll)="onSiderScroll($event)">
  <div class="form-menu ml-10">
    <!-- Synapse Section -->
    <div class="form-synapse mt-20 mb-20" *ngIf="synapseMenu.length > 0">
      <h3>Synapse</h3>
      <ul class="item-menu" nz-menu nzMode="inline">
        <li nz-menu-item [nzSelected]="isActive('/protected')" routerLink="/protected">
          <nz-icon nzType="fund-view" nzTheme="outline" />
          <span>Overview</span>
        </li>
        <li *ngIf="hasSynapseViewAllUsers" nz-menu-item [nzSelected]="isActive('/all-users')" routerLink="/all-users">
          <nz-icon nzType="user" nzTheme="outline" />
          <span>All Users</span>
        </li>
        <li *ngIf="hasSynapseViewAllOrganizations" id="select-organizations" nz-menu-item
          [nzSelected]="isActive('/organizations')" routerLink="/organizations">
          <nz-icon nzType="team" nzTheme="outline" />
          <span>All Organizations</span>
        </li>
        <li class="submenu-item" nz-submenu [(nzOpen)]="openMap['sub1']" (nzOpenChange)="openHandler('sub1')"
          nzTitle="Admin" nzIcon="setting">
          <ul>
            <li nz-menu-item [nzSelected]="isActive('/permissions')" routerLink="/permissions">
              Permissions
            </li>
            <li nz-menu-item [nzSelected]="isActive('/users')" routerLink="/users">
              Users
            </li>
          </ul>
        </li>
      </ul>
    </div>

    <!-- Organizations Section -->
    <div class="form-organizations mt-20" *ngFor="let org of organizationsMenu; let i = index">
      <h3>{{ org.name }}</h3>
      <ul class="item-menu" nz-menu nzMode="inline">
        <li nz-menu-item [nzSelected]="isActive('/overview')" routerLink="/overview">
          <nz-icon nzType="fund-view" nzTheme="outline" />
          <span>Overview</span>
        </li>
        <li nz-menu-item [nzSelected]="isActive('/software-gateway')" routerLink="/software-gateway">
          <nz-icon nzType="cluster" nzTheme="outline" />
          <span>Gateways</span>
        </li>
        <li nz-menu-item *ngIf="org.hasOrgManageDevices" [nzSelected]="isActive('/devices')" routerLink="/devices">
          <nz-icon nzType="desktop" nzTheme="outline" />
          <span>Devices</span>
        </li>
        <li class="submenu-item" nz-submenu [(nzOpen)]="openMap['sub' + (i + 2)]"
          (nzOpenChange)="openHandler('sub' + (i + 2))" nzTitle="Admin" nzIcon="setting">
          <ul>
            <li nz-menu-item *ngIf="org.hasDeviceGroupScope" [nzSelected]="isActive('/DeviceGroups')"
              routerLink="/DeviceGroups">
              Device Groups
            </li>
            <li nz-menu-item *ngIf="org.hasLocationGroupScope" [nzSelected]="isActive('/Locations')"
              routerLink="/Locations">
              Locations
            </li>
          </ul>
        </li>
        <li nz-menu-item [nzSelected]="isActive('/Notifications')" routerLink="/Notifications">
          <nz-icon nzType="notification" nzTheme="outline" />
          <span>My Notifications</span>
        </li>
      </ul>
    </div>

    <!-- Account Section -->
    <div class="form-account mt-20 mb-20">
      <h3>My Account</h3>
      <ul class="item-menu" nz-menu nzMode="inline">
        <li nz-menu-item [nzSelected]="isActive('/Info')" routerLink="/Info">
          <nz-icon nzType="info-circle" nzTheme="outline" />
          <span>Info</span>
        </li>
        <li nz-menu-item [nzSelected]="isActive('/authentication')" routerLink="/authentication">
          <nz-icon nzType="safety-certificate" nzTheme="outline" />
          <span>Authentication Methods</span>
        </li>
        <li nz-menu-item [nzSelected]="isActive('/invitations-pending')" routerLink="/invitations-pending">
          <nz-icon nzType="send" nzTheme="outline" />
          <span>Invitations Pending</span>
        </li>
        <li nz-menu-item [nzSelected]="isActive('/Preferences')" routerLink="/Preferences">
          <nz-icon nzType="block" nzTheme="outline" />
          <span>Preferences</span>
        </li>
      </ul>
    </div>
  </div>
</nz-sider>