-- Test Organizations for permission testing
INSERT INTO {{Organization}} (Id, Name, Description, OrgTypeIdentifier) VALUES
  ('550e8400-e29b-41d4-a716-************', 'Test Organization 1', 'First test organization for permissions', 'municipality'),
  ('550e8400-e29b-41d4-a716-************', 'Test Organization 2', 'Second test organization for permissions', 'municipality');

-- Test SoftwareGateways for integration testing
INSERT INTO {{SoftwareGateway}} (Id, MachineKey, OrganizationId, APIKey, Token, Name, Description, Config, IsEnabled) VALUES
  -- Test gateways for Test Organization 1
  ('550e8400-e29b-41d4-a716-************', 'TESTGATEWAY1', '550e8400-e29b-41d4-a716-************', 'testapi1_qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd', '', 'Test Gateway 1', 'Integration test gateway for Test Organization 1', '{"log_level": "info", "log_filename": "log/gateway-app.log", "log_max_backups": 5, "application_version": "deprecated", "log_max_age_in_days": 7, "log_compress_backups": true, "log_file_max_size_mb": 5, "rest_api_device_endpoint": "http://localhost:8080/api/v3/gateway/ingest", "send_gateway_logs_to_cloud": false, "edi_device_persist_connection": true, "edi_device_processing_retries": 3, "record_http_requests_to_folder": false, "device_state_send_frequency_seconds": 30, "config_change_check_frequency_seconds": 15, "send_gateway_performance_stats_to_cloud": false, "software_update_check_frequency_seconds": 43200, "channel_state_send_frequency_milliseconds": 1000, "gateway_performance_stats_output_frequency_seconds": 60, "ws_active": true, "ws_port": "8079", "ws_endpoint": "/gateway", "ws_max_connections": 1, "ws_send_frequency_milliseconds": 3000, "ws_heartbeat_send_frequency_milliseconds": 30000, "threshold_device_error_seconds": 45}', true),
  -- Test gateways for Test Organization 2  
  ('550e8400-e29b-41d4-a716-************', 'TESTGATEWAY2', '550e8400-e29b-41d4-a716-************', 'testapi2_qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd', '', 'Test Gateway 2', 'Integration test gateway for Test Organization 2', '{"log_level": "debug", "log_filename": "log/gateway-app.log", "log_max_backups": 3, "application_version": "deprecated", "log_max_age_in_days": 3, "log_compress_backups": false, "log_file_max_size_mb": 2, "rest_api_device_endpoint": "http://localhost:8080/api/v3/gateway/ingest", "send_gateway_logs_to_cloud": true, "edi_device_persist_connection": false, "edi_device_processing_retries": 10, "record_http_requests_to_folder": true, "device_state_send_frequency_seconds": 120, "config_change_check_frequency_seconds": 60, "send_gateway_performance_stats_to_cloud": true, "software_update_check_frequency_seconds": 43200, "channel_state_send_frequency_milliseconds": 250, "gateway_performance_stats_output_frequency_seconds": 30, "ws_active": false, "ws_port": "8080", "ws_endpoint": "/test", "ws_max_connections": 5, "ws_send_frequency_milliseconds": 1000, "ws_heartbeat_send_frequency_milliseconds": 15000, "threshold_device_error_seconds": 180}', true),
  -- Additional test gateways for specific testing scenarios
  ('550e8400-e29b-41d4-a716-************', 'TESTGATEWAY1_DISABLED', '550e8400-e29b-41d4-a716-************', 'testapi1_disabled_qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd', '', 'Test Gateway 1 (Disabled)', 'Disabled integration test gateway for Test Organization 1', '{"log_level": "error"}', false),
  ('550e8400-e29b-41d4-a716-************', 'TESTGATEWAY2_MINIMAL', '550e8400-e29b-41d4-a716-************', 'testapi2_minimal_qiLkND6fYR2zVIQbgifvR2VbyOST6qt6OgzPZTd', '', 'Test Gateway 2 (Minimal Config)', 'Minimal config test gateway for Test Organization 2', '{"log_level": "info", "rest_api_device_endpoint": "http://localhost:8080/api/v3/gateway/ingest"}', true);


WITH orgs AS (
  SELECT
   	Id   AS organizationId,
    Name AS organizationName
  FROM {{Organization}}
)
INSERT INTO {{GatewayConfigTemplate}}
  (Id, Name, OrganizationId, Description)
SELECT
  -- deterministic UUID per org
  uuid_generate_v5(
    uuid_nil(),
    format('SYNAPSE_softwaregatewayconfigtemplate_%s', organizationId::text)
  )                                                       AS I,
  format('Default Software Gateway Template for %s', organizationName) AS Name,
  organizationId,
  'Default configuration for software gateways'           AS Description
FROM orgs
WHERE NOT EXISTS (
  SELECT 1
  FROM {{GatewayConfigTemplate}} t
  WHERE t.OrganizationId = orgs.organizationId
);

-- Only populate settings for templates created by this organization setup  
WITH org_templates AS (
  SELECT Id AS templateId
  FROM {{GatewayConfigTemplate}}
  WHERE OrganizationId IN (
    '550e8400-e29b-41d4-a716-************',  -- Test Organization 1
    '550e8400-e29b-41d4-a716-************'  -- Test Organization 2
  )
)
INSERT INTO {{GatewayConfigTemplateSettings}}
  (gatewayConfigTemplateId, setting, value)
SELECT
  t.templateId,
  b.Setting,
  b.DefaultValue
FROM org_templates t
CROSS JOIN {{GatewayConfigTemplateBaseSettings}} b
WHERE NOT EXISTS (
  SELECT 1
  FROM {{GatewayConfigTemplateSettings}} s
  WHERE s.gatewayConfigTemplateId = t.templateId
    AND s.setting               = b.Setting
);

-- Update test gateways with their organization's default template IDs
UPDATE {{SoftwareGateway}} 
SET TemplateId = uuid_generate_v5(
  uuid_nil(), 
  format('SYNAPSE_softwaregatewayconfigtemplate_%s', OrganizationId::text)
)
WHERE Id IN (
  '550e8400-e29b-41d4-a716-************',  -- Test Gateway 1
  '550e8400-e29b-41d4-a716-************',  -- Test Gateway 2
  '550e8400-e29b-41d4-a716-************',  -- Test Gateway 1 Disabled
  '550e8400-e29b-41d4-a716-************'   -- Test Gateway 2 Minimal
);


-- Create overrides for Cornelius gateway (used by integration tests)
WITH test_gateway AS (
  SELECT Id AS softwareGatewayId
  FROM {{SoftwareGateway}}
  WHERE Id = '550e8400-e29b-41d4-a716-************'
  LIMIT 1
)
INSERT INTO {{GatewayConfigTemplateSettingOverrides}}
  (softwareGatewayId, setting, value)
SELECT
  tg.softwareGatewayId,
  s.setting,
  s.value
FROM
  test_gateway tg
CROSS JOIN LATERAL
  (VALUES
    ('log_level',                              'debug'),
    ('device_state_send_frequency_seconds',    '120')
  ) AS s(setting, value);

INSERT INTO {{GatewayConfigTemplateBaseSettings}} (Setting, DefaultValue, Name, Description, Format)
VALUES
('rest_api_device_endpoint',
   'https://api.dev.synapse-its.app/api/v3/gateway/ingest',
   'Rest API Device Endpoint',
   'The cloud endpoint for ingesting device data.',
   '{"type": "string", "versions": ["v3"]}'
  );
