package password

import (
	"bytes"
	"encoding/json"
	"io"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/api/security"
)

// Test_NewPasswordHasher tests the NewPasswordHasher function
func Test_NewPasswordHasher(t *testing.T) {
	t.<PERSON>()

	// Test that NewPasswordHasher returns a non-nil hasher
	hasher := NewPasswordHasher()
	assert.NotNil(t, hasher, "NewPasswordHasher should return a non-nil hasher")
	assert.Implements(t, (*PasswordHasher)(nil), hasher, "Should implement PasswordHasher interface")

	// Test that it returns a *passwordHasher instance
	_, ok := hasher.(*passwordHasher)
	assert.True(t, ok, "Should return a *passwordHasher instance")
}

// Test_passwordHasher_HashPassword tests the HashPassword method
func Test_passwordHasher_HashPassword(t *testing.T) {
	t.<PERSON>()

	hasher := NewPasswordHasher()

	tests := []struct {
		name     string
		password string
		expected string
	}{
		{
			name:     "simple_password",
			password: "test123",
			expected: security.CalculateSHA256("test123"),
		},
		{
			name:     "empty_password",
			password: "",
			expected: security.CalculateSHA256(""),
		},
		{
			name:     "password_with_spaces",
			password: "  test123  ",
			expected: security.CalculateSHA256("  test123  "),
		},
		{
			name:     "special_characters",
			password: "test@#$%^&*()",
			expected: security.CalculateSHA256("test@#$%^&*()"),
		},
		{
			name:     "unicode_password",
			password: "测试密码123",
			expected: security.CalculateSHA256("测试密码123"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result := hasher.HashPassword(tt.password)
			assert.Equal(t, tt.expected, result, "HashPassword should return expected hash")
		})
	}
}

// Test_passwordHasher_ComparePassword tests the ComparePassword method
func Test_passwordHasher_ComparePassword(t *testing.T) {
	t.Parallel()

	hasher := NewPasswordHasher()

	tests := []struct {
		name     string
		password string
		hash     string
		expected bool
	}{
		{
			name:     "matching_password",
			password: "test123",
			hash:     security.CalculateSHA256("test123"),
			expected: true,
		},
		{
			name:     "non_matching_password",
			password: "test123",
			hash:     security.CalculateSHA256("different123"),
			expected: false,
		},
		{
			name:     "empty_password",
			password: "",
			hash:     security.CalculateSHA256(""),
			expected: true,
		},
		{
			name:     "empty_password_wrong_hash",
			password: "",
			hash:     security.CalculateSHA256("notempty"),
			expected: false,
		},
		{
			name:     "case_sensitive",
			password: "Test123",
			hash:     security.CalculateSHA256("test123"),
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result := hasher.ComparePassword(tt.password, tt.hash)
			assert.Equal(t, tt.expected, result, "ComparePassword should return expected result")
		})
	}
}

// Test_ValidateUsername tests the ValidateUsername function
func Test_ValidateUsername(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		username    string
		expectedErr error
		wantErr     bool
	}{
		{
			name:        "valid_username",
			username:    "validuser",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "minimum_length",
			username:    "12345",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "exactly_max_length",
			username:    strings.Repeat("a", MaxUsernameLength),
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "empty_username",
			username:    "",
			expectedErr: ErrUsernameEmpty,
			wantErr:     true,
		},
		{
			name:        "whitespace_only_username",
			username:    "   ",
			expectedErr: ErrUsernameEmpty,
			wantErr:     true,
		},
		{
			name:        "too_short",
			username:    "1234",
			expectedErr: ErrUsernameTooShort,
			wantErr:     true,
		},
		{
			name:        "too_long",
			username:    strings.Repeat("a", MaxUsernameLength+1),
			expectedErr: ErrUsernameTooLong,
			wantErr:     true,
		},
		{
			name:        "username_with_spaces",
			username:    "  validuser  ",
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := ValidateUsername(tt.username)

			if tt.wantErr {
				assert.Error(t, err, "ValidateUsername should return an error")
				assert.Equal(t, tt.expectedErr, err, "Error should match expected error")
			} else {
				assert.NoError(t, err, "ValidateUsername should not return an error")
			}
		})
	}
}

// Test_ValidatePassword tests the ValidatePassword function
func Test_ValidatePassword(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		password    string
		expectedErr error
		wantErr     bool
	}{
		{
			name:        "valid_password",
			password:    "validpassword123",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "minimum_length",
			password:    "12345678",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "exactly_max_length",
			password:    strings.Repeat("a", MaxUserPasswordLength),
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "empty_password",
			password:    "",
			expectedErr: ErrPasswordEmpty,
			wantErr:     true,
		},
		{
			name:        "whitespace_only_password",
			password:    "   ",
			expectedErr: ErrPasswordEmpty,
			wantErr:     true,
		},
		{
			name:        "too_short",
			password:    "1234567",
			expectedErr: ErrPasswordTooShort,
			wantErr:     true,
		},
		{
			name:        "too_long",
			password:    strings.Repeat("a", MaxUserPasswordLength+1),
			expectedErr: ErrPasswordTooLong,
			wantErr:     true,
		},
		{
			name:        "password_with_spaces",
			password:    "  validpass123  ",
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := ValidatePassword(tt.password)

			if tt.wantErr {
				assert.Error(t, err, "ValidatePassword should return an error")
				assert.Equal(t, tt.expectedErr, err, "Error should match expected error")
			} else {
				assert.NoError(t, err, "ValidatePassword should not return an error")
			}
		})
	}
}

// Test_ExtractAndValidateUserNameAndPassword tests the ExtractAndValidateUserNameAndPassword function
func Test_ExtractAndValidateUserNameAndPassword(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name         string
		requestBody  interface{}
		expectedUser string
		expectedPass string
		expectedErr  error
		wantErr      bool
	}{
		{
			name: "valid_credentials",
			requestBody: Credentials{
				Username: "validuser",
				Password: "validpassword123",
			},
			expectedUser: "validuser",
			expectedPass: "validpassword123",
			expectedErr:  nil,
			wantErr:      false,
		},
		{
			name: "credentials_with_spaces",
			requestBody: Credentials{
				Username: "  validuser  ",
				Password: "  validpass123  ",
			},
			expectedUser: "validuser",
			expectedPass: "validpass123",
			expectedErr:  nil,
			wantErr:      false,
		},
		{
			name:         "invalid_json",
			requestBody:  "invalid json string",
			expectedUser: "",
			expectedPass: "",
			expectedErr:  ErrInvalidJSON,
			wantErr:      true,
		},
		{
			name: "invalid_username",
			requestBody: Credentials{
				Username: "1234",
				Password: "validpassword123",
			},
			expectedUser: "",
			expectedPass: "",
			expectedErr:  ErrUsernameTooShort,
			wantErr:      true,
		},
		{
			name: "invalid_password",
			requestBody: Credentials{
				Username: "validuser",
				Password: "1234567",
			},
			expectedUser: "",
			expectedPass: "",
			expectedErr:  ErrPasswordTooShort,
			wantErr:      true,
		},
		{
			name: "empty_username",
			requestBody: Credentials{
				Username: "",
				Password: "validpassword123",
			},
			expectedUser: "",
			expectedPass: "",
			expectedErr:  ErrUsernameEmpty,
			wantErr:      true,
		},
		{
			name: "empty_password",
			requestBody: Credentials{
				Username: "validuser",
				Password: "",
			},
			expectedUser: "",
			expectedPass: "",
			expectedErr:  ErrPasswordEmpty,
			wantErr:      true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create request body
			var body []byte
			var err error

			if str, ok := tt.requestBody.(string); ok {
				// For invalid JSON test case
				body = []byte(str)
			} else {
				body, err = json.Marshal(tt.requestBody)
				assert.NoError(t, err, "Should be able to marshal request body")
			}

			// Create io.ReadCloser
			reader := io.NopCloser(bytes.NewBuffer(body))

			// Execute the function under test
			creds, err := ExtractAndValidateUserNameAndPassword(reader)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "ExtractAndValidateUserNameAndPassword should return an error")
				assert.Equal(t, tt.expectedErr, err, "Error should match expected error")
				assert.Nil(t, creds, "Credentials should be nil on error")
			} else {
				assert.NoError(t, err, "ExtractAndValidateUserNameAndPassword should not return an error")
				assert.NotNil(t, creds, "Credentials should not be nil on success")
				assert.Equal(t, tt.expectedUser, creds.Username, "Username should match expected")
				assert.Equal(t, tt.expectedPass, creds.Password, "Password should match expected")
			}
		})
	}
}

// Test_ExtractAndValidatePasswordUpdate tests the ExtractAndValidatePasswordUpdate function
func Test_ExtractAndValidatePasswordUpdate(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name            string
		requestBody     interface{}
		expectedCurrent string
		expectedNew     string
		expectedConfirm string
		expectedErr     error
		wantErr         bool
	}{
		{
			name: "valid_password_update",
			requestBody: PasswordUpdateRequest{
				CurrentPassword: "current123",
				NewPassword:     "newpassword123",
				ConfirmPassword: "newpassword123",
			},
			expectedCurrent: "current123",
			expectedNew:     "newpassword123",
			expectedConfirm: "newpassword123",
			expectedErr:     nil,
			wantErr:         false,
		},
		{
			name: "passwords_with_spaces",
			requestBody: PasswordUpdateRequest{
				CurrentPassword: "  current123  ",
				NewPassword:     "  newpassword123  ",
				ConfirmPassword: "  newpassword123  ",
			},
			expectedCurrent: "current123",
			expectedNew:     "newpassword123",
			expectedConfirm: "newpassword123",
			expectedErr:     nil,
			wantErr:         false,
		},
		{
			name:            "invalid_json",
			requestBody:     "invalid json string",
			expectedCurrent: "",
			expectedNew:     "",
			expectedConfirm: "",
			expectedErr:     ErrInvalidJSON,
			wantErr:         true,
		},
		{
			name: "empty_current_password",
			requestBody: PasswordUpdateRequest{
				CurrentPassword: "",
				NewPassword:     "new123",
				ConfirmPassword: "new123",
			},
			expectedCurrent: "",
			expectedNew:     "",
			expectedConfirm: "",
			expectedErr:     ErrPasswordEmpty,
			wantErr:         true,
		},
		{
			name: "empty_new_password",
			requestBody: PasswordUpdateRequest{
				CurrentPassword: "current123",
				NewPassword:     "",
				ConfirmPassword: "newpassword123",
			},
			expectedCurrent: "",
			expectedNew:     "",
			expectedConfirm: "",
			expectedErr:     ErrPasswordEmpty,
			wantErr:         true,
		},
		{
			name: "empty_confirm_password",
			requestBody: PasswordUpdateRequest{
				CurrentPassword: "current123",
				NewPassword:     "newpassword123",
				ConfirmPassword: "",
			},
			expectedCurrent: "",
			expectedNew:     "",
			expectedConfirm: "",
			expectedErr:     ErrPasswordEmpty,
			wantErr:         true,
		},
		{
			name: "password_mismatch",
			requestBody: PasswordUpdateRequest{
				CurrentPassword: "current123",
				NewPassword:     "newpassword123",
				ConfirmPassword: "differentpassword123",
			},
			expectedCurrent: "",
			expectedNew:     "",
			expectedConfirm: "",
			expectedErr:     ErrPasswordMismatch,
			wantErr:         true,
		},
		{
			name: "current_password_too_short",
			requestBody: PasswordUpdateRequest{
				CurrentPassword: "1234567",
				NewPassword:     "new123456",
				ConfirmPassword: "new123456",
			},
			expectedCurrent: "",
			expectedNew:     "",
			expectedConfirm: "",
			expectedErr:     ErrPasswordTooShort,
			wantErr:         true,
		},
		{
			name: "new_password_too_short",
			requestBody: PasswordUpdateRequest{
				CurrentPassword: "current123456",
				NewPassword:     "1234567",
				ConfirmPassword: "1234567",
			},
			expectedCurrent: "",
			expectedNew:     "",
			expectedConfirm: "",
			expectedErr:     ErrPasswordTooShort,
			wantErr:         true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create request body
			var body []byte
			var err error

			if str, ok := tt.requestBody.(string); ok {
				// For invalid JSON test case
				body = []byte(str)
			} else {
				body, err = json.Marshal(tt.requestBody)
				assert.NoError(t, err, "Should be able to marshal request body")
			}

			// Create io.ReadCloser
			reader := io.NopCloser(bytes.NewBuffer(body))

			// Execute the function under test
			req, err := ExtractAndValidatePasswordUpdate(reader)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "ExtractAndValidatePasswordUpdate should return an error")
				assert.Equal(t, tt.expectedErr, err, "Error should match expected error")
				assert.Nil(t, req, "PasswordUpdateRequest should be nil on error")
			} else {
				assert.NoError(t, err, "ExtractAndValidatePasswordUpdate should not return an error")
				assert.NotNil(t, req, "PasswordUpdateRequest should not be nil on success")
				assert.Equal(t, tt.expectedCurrent, req.CurrentPassword, "Current password should match expected")
				assert.Equal(t, tt.expectedNew, req.NewPassword, "New password should match expected")
				assert.Equal(t, tt.expectedConfirm, req.ConfirmPassword, "Confirm password should match expected")
			}
		})
	}
}

// Test_passwordHasher_Interface_Compliance tests that passwordHasher implements PasswordHasher interface
func Test_passwordHasher_Interface_Compliance(t *testing.T) {
	t.Parallel()

	// Verify that passwordHasher implements PasswordHasher interface
	var _ PasswordHasher = (*passwordHasher)(nil)

	// Test that the struct can be used as the interface
	hasher := &passwordHasher{}
	var interfaceHasher PasswordHasher = hasher

	// Test that interface methods work
	password := "test123"
	hash := interfaceHasher.HashPassword(password)
	assert.Equal(t, security.CalculateSHA256(password), hash, "Interface HashPassword should work correctly")

	result := interfaceHasher.ComparePassword(password, hash)
	assert.True(t, result, "Interface ComparePassword should work correctly")
}

// Test_passwordHasher_EdgeCases tests edge cases for password hashing
func Test_passwordHasher_EdgeCases(t *testing.T) {
	t.Parallel()

	hasher := NewPasswordHasher()

	tests := []struct {
		name     string
		password string
	}{
		{
			name:     "very_long_password",
			password: strings.Repeat("a", 1000),
		},
		{
			name:     "unicode_password",
			password: "测试密码123!@#$%",
		},
		{
			name:     "special_characters",
			password: "!@#$%^&*()_+-=[]{}|;:,.<>?",
		},
		{
			name:     "numbers_only",
			password: "1234567890",
		},
		{
			name:     "mixed_case",
			password: "TestPassword123",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Test that hashing works for edge cases
			hash := hasher.HashPassword(tt.password)
			assert.NotEmpty(t, hash, "Hash should not be empty")
			assert.Len(t, hash, 64, "SHA256 hash should be 64 characters long")

			// Test that comparison works
			result := hasher.ComparePassword(tt.password, hash)
			assert.True(t, result, "Password comparison should work for edge cases")
		})
	}
}
