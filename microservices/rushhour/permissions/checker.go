package permissions

import (
	"fmt"

	"synapse-its.com/rushhour/domain"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/logger"
)

// Permission<PERSON><PERSON><PERSON> handles permission validation for various operations
type Permission<PERSON>he<PERSON> struct {
	// No internal state needed - permissions come from database via authorizer
}

// NewPermission<PERSON>hecker creates a new permission checker
func NewPermissionChecker() *PermissionChecker {
	return &PermissionChecker{}
}

// CheckDeviceAccess checks if a user/gateway has access to a specific device
func (pc *PermissionChecker) CheckDeviceAccess(ctx *domain.ConnectionContext, deviceID string, operation string) error {
	if ctx == nil {
		return fmt.Errorf("no connection context")
	}

	var userID, gatewayID string
	if ctx.AuthInfo != nil {
		userID = ctx.AuthInfo.GetUserID()
		if ctx.AuthInfo.ClientType == domain.ClientTypeGateway {
			gatewayID = ctx.AuthInfo.GatewayID
		}
	}

	logger.Debugf("Checking device access: user=%s, gateway=%s, device=%s, operation=%s",
		userID, gatewayID, deviceID, operation)

	// Basic validation
	if deviceID == "" {
		return fmt.Errorf("device ID cannot be empty")
	}

	// Gateway connections are allowed to access devices they manage
	if ctx.AuthInfo != nil && ctx.AuthInfo.ClientType == domain.ClientTypeGateway && ctx.AuthInfo.GatewayID != "" {
		// For gateways, we assume they can access devices they're responsible for
		// In production, this should check the gateway-device relationship in the database
		logger.Debugf("Gateway %s granted access to device %s for operation %s",
			ctx.AuthInfo.GatewayID, deviceID, operation)
		return nil
	}

	// For FSA and Onramp clients, check database permissions using authorizer
	if ctx.AuthInfo != nil &&
		(ctx.AuthInfo.ClientType == domain.ClientTypeFSA || ctx.AuthInfo.ClientType == domain.ClientTypeOnramp) &&
		ctx.AuthInfo.Permissions != nil {
		return pc.checkUserDeviceAccess(ctx.AuthInfo.Permissions, deviceID, operation)
	}

	return fmt.Errorf("no valid authentication context for device access")
}

// checkUserDeviceAccess validates user access to a device based on database permissions
func (pc *PermissionChecker) checkUserDeviceAccess(userPermissions *authorizer.UserPermissions, deviceID string, operation string) error {
	// Use the same permission logic as the broker
	requiredPermissions := []string{"org_view_devices", "device_group_view_devices"}

	// For control operations, require management permissions
	if operation == "control" || operation == "configure" {
		requiredPermissions = []string{"org_manage_devices", "device_group_manage_devices"}
	}

	// Check if user has any of the required permissions (same logic as broker)
	if pc.hasAnyPermission(userPermissions, requiredPermissions) {
		logger.Debugf("User has required permissions for device %s operation %s", deviceID, operation)
		return nil
	}

	return fmt.Errorf("user does not have permission to access device %s for operation %s", deviceID, operation)
}

// hasAnyPermission checks if the user has any of the required permissions across all scopes (same as broker)
func (pc *PermissionChecker) hasAnyPermission(userPermissions *authorizer.UserPermissions, requiredPermissions []string) bool {
	// Create a set of all user permissions for efficient lookup
	userPerms := make(map[string]bool)
	for _, permission := range userPermissions.Permissions {
		for _, perm := range permission.Permissions {
			userPerms[perm] = true
		}
	}

	// Check if user has any of the required permissions
	for _, requiredPerm := range requiredPermissions {
		if userPerms[requiredPerm] {
			return true
		}
	}

	return false
}

// CheckRoomAccess checks if a user/gateway can join a specific room/channel
func (pc *PermissionChecker) CheckRoomAccess(ctx *domain.ConnectionContext, roomName string) error {
	if ctx == nil {
		return fmt.Errorf("no connection context")
	}

	var userID, gatewayID string
	if ctx.AuthInfo != nil {
		userID = ctx.AuthInfo.GetUserID()
		if ctx.AuthInfo.ClientType == domain.ClientTypeGateway {
			gatewayID = ctx.AuthInfo.GatewayID
		}
	}

	logger.Debugf("Checking room access: user=%s, gateway=%s, room=%s",
		userID, gatewayID, roomName)

	// Parse the room to understand its type and requirements
	roomInfo := domain.ParseChannelInfo(roomName)

	// Handle device stream rooms (EntityType: "device", Subtype: stream type)
	// Only streaming uses rooms now - device commands use direct socket messaging
	if roomInfo.EntityType == "device" {
		// Stream channels require device access permission
		return pc.CheckDeviceAccess(ctx, roomInfo.EntityID, "stream")
	}

	return fmt.Errorf("unknown room type: %s", roomInfo.Type)
}

// CheckMessageSending checks if a user/gateway can send a specific type of message
func (pc *PermissionChecker) CheckMessageSending(ctx *domain.ConnectionContext, msgType string, targetRoom string) error {
	if ctx == nil {
		return fmt.Errorf("no connection context")
	}

	var userID, gatewayID string
	if ctx.AuthInfo != nil {
		userID = ctx.AuthInfo.GetUserID()
		if ctx.AuthInfo.ClientType == domain.ClientTypeGateway {
			gatewayID = ctx.AuthInfo.GatewayID
		}
	}

	logger.Debugf("Checking message sending: user=%s, gateway=%s, msgType=%s, room=%s",
		userID, gatewayID, msgType, targetRoom)

	// First check if they can access the target room
	if err := pc.CheckRoomAccess(ctx, targetRoom); err != nil {
		return fmt.Errorf("cannot send message to room: %w", err)
	}

	switch msgType {
	case "device_message":
		// Only gateways can send device messages (replaces stream_frame and device_status)
		if ctx.AuthInfo == nil || ctx.AuthInfo.ClientType != domain.ClientTypeGateway {
			return fmt.Errorf("message type %s only allowed from gateways", msgType)
		}
		return nil

	case "device_request":
		// FSA and Onramp apps can send device requests to control devices
		if ctx.AuthInfo == nil ||
			(ctx.AuthInfo.ClientType != domain.ClientTypeFSA && ctx.AuthInfo.ClientType != domain.ClientTypeOnramp) {
			return fmt.Errorf("message type %s only allowed from FSA and Onramp apps", msgType)
		}
		return nil

	case "stream_control":
		// Internal system messages for gateway stream control
		// These are sent by the system to gateways, not by clients
		return nil

	default:
		// For unknown message types, require explicit permission check
		logger.Warnf("Unknown message type: %s", msgType)
		return fmt.Errorf("unknown message type: %s", msgType)
	}
}

// GetPermissionsForUser returns the permissions for a specific user from database (not used in JWT approach)
func (pc *PermissionChecker) GetPermissionsForUser(userID string, orgID string) ([]string, error) {
	// This method is not used in the JWT-based approach since permissions come from database via authorizer
	// Keeping for interface compatibility
	logger.Debugf("GetPermissionsForUser called for user %s, org %s", userID, orgID)
	return []string{}, fmt.Errorf("permissions should be loaded via authorizer.GetUserPermissions")
}

// GetPermissionsForGateway returns the permissions for a specific gateway
func (pc *PermissionChecker) GetPermissionsForGateway(gatewayID string, orgID string) ([]string, error) {
	// Gateways have implicit permissions for their associated devices
	// In production, this would query the database for gateway-device relationships
	logger.Debugf("GetPermissionsForGateway called for gateway %s, org %s", gatewayID, orgID)
	return []string{"device_access", "stream_send"}, nil
}
