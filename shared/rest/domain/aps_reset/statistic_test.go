package apsreset

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestAPSResetStatistic_IsLimitReached(t *testing.T) {
	tests := []struct {
		name           string
		statistic      *APSResetStatistic
		config         *APSResetConfig
		expectedResult bool
	}{
		{
			name: "Limit not reached - count below limit",
			statistic: &APSResetStatistic{
				StatAPSResetCount: 3,
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessLimit: 5,
			},
			expectedResult: false,
		},
		{
			name: "Limit reached - count equals limit",
			statistic: &APSResetStatistic{
				StatAPSResetCount: 5,
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessLimit: 5,
			},
			expectedResult: true,
		},
		{
			name: "Limit exceeded - count above limit",
			statistic: &APSResetStatistic{
				StatAPSResetCount: 7,
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessLimit: 5,
			},
			expectedResult: true,
		},
		{
			name: "Limit not reached - count is zero",
			statistic: &APSResetStatistic{
				StatAPSResetCount: 0,
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessLimit: 5,
			},
			expectedResult: false,
		},
		{
			name: "Limit not reached - count is one",
			statistic: &APSResetStatistic{
				StatAPSResetCount: 1,
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessLimit: 5,
			},
			expectedResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Act
			result := tt.statistic.IsLimitReached(tt.config)

			// Assert
			assert.Equal(t, tt.expectedResult, result)
		})
	}
}

func TestAPSResetStatistic_RecordSuccess(t *testing.T) {
	tests := []struct {
		name          string
		statistic     *APSResetStatistic
		expectedCount int
		expectedList  int
	}{
		{
			name: "Record success - empty success list",
			statistic: &APSResetStatistic{
				StatAPSResetCount:       0,
				StatAPSResetSuccessList: []time.Time{},
			},
			expectedCount: 1,
			expectedList:  1,
		},
		{
			name: "Record success - existing success list",
			statistic: &APSResetStatistic{
				StatAPSResetCount:       3,
				StatAPSResetSuccessList: []time.Time{time.Now().Add(-10 * time.Minute)},
			},
			expectedCount: 4,
			expectedList:  2,
		},
		{
			name: "Record success - multiple existing entries",
			statistic: &APSResetStatistic{
				StatAPSResetCount:       5,
				StatAPSResetSuccessList: []time.Time{time.Now().Add(-10 * time.Minute), time.Now().Add(-5 * time.Minute)},
			},
			expectedCount: 6,
			expectedList:  3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			originalCount := tt.statistic.StatAPSResetCount
			originalListLength := len(tt.statistic.StatAPSResetSuccessList)

			// Act
			tt.statistic.RecordSuccess()

			// Assert
			assert.Equal(t, originalCount+1, tt.statistic.StatAPSResetCount)
			assert.Equal(t, originalListLength+1, len(tt.statistic.StatAPSResetSuccessList))

			// Verify the new timestamp was added (should be very recent)
			newTimestamp := tt.statistic.StatAPSResetSuccessList[len(tt.statistic.StatAPSResetSuccessList)-1]
			assert.WithinDuration(t, time.Now(), newTimestamp, 2*time.Second)
		})
	}
}

func TestAPSResetStatistic_CheckAndPruneSuccessList(t *testing.T) {
	tests := []struct {
		name           string
		statistic      *APSResetStatistic
		config         *APSResetConfig
		expectedResult bool
		expectedList   int
	}{
		{
			name: "Allow request - list not full",
			statistic: &APSResetStatistic{
				StatAPSResetSuccessList: []time.Time{time.Now().Add(-10 * time.Minute)},
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessListLength:        3,
				ConfigAPSResetSuccessListWindowMinutes: 60,
			},
			expectedResult: true,
			expectedList:   1,
		},
		{
			name: "Allow request - empty list",
			statistic: &APSResetStatistic{
				StatAPSResetSuccessList: []time.Time{},
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessListLength:        3,
				ConfigAPSResetSuccessListWindowMinutes: 60,
			},
			expectedResult: true,
			expectedList:   0,
		},
		{
			name: "Allow request - list full but oldest entry expired",
			statistic: &APSResetStatistic{
				StatAPSResetSuccessList: []time.Time{
					time.Now().Add(-120 * time.Minute), // 2 hours ago - expired
					time.Now().Add(-10 * time.Minute),  // 10 minutes ago
					time.Now().Add(-5 * time.Minute),   // 5 minutes ago
				},
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessListLength:        3,
				ConfigAPSResetSuccessListWindowMinutes: 60,
			},
			expectedResult: true,
			expectedList:   2, // Should prune the expired entry
		},
		{
			name: "Allow request - list full but multiple expired entries",
			statistic: &APSResetStatistic{
				StatAPSResetSuccessList: []time.Time{
					time.Now().Add(-120 * time.Minute), // 2 hours ago - expired
					time.Now().Add(-90 * time.Minute),  // 1.5 hours ago - expired
					time.Now().Add(-10 * time.Minute),  // 10 minutes ago
				},
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessListLength:        3,
				ConfigAPSResetSuccessListWindowMinutes: 60,
			},
			expectedResult: true,
			expectedList:   2, // Should prune only the first expired entry, leaving 2 entries
		},
		{
			name: "Deny request - list full and oldest entry not expired",
			statistic: &APSResetStatistic{
				StatAPSResetSuccessList: []time.Time{
					time.Now().Add(-30 * time.Minute), // 30 minutes ago - not expired
					time.Now().Add(-10 * time.Minute), // 10 minutes ago
					time.Now().Add(-5 * time.Minute),  // 5 minutes ago
				},
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessListLength:        3,
				ConfigAPSResetSuccessListWindowMinutes: 60,
			},
			expectedResult: false,
			expectedList:   3, // Should not prune anything
		},
		{
			name: "Allow request - list full, mixed expired and non-expired entries",
			statistic: &APSResetStatistic{
				StatAPSResetSuccessList: []time.Time{
					time.Now().Add(-120 * time.Minute), // 2 hours ago - expired
					time.Now().Add(-30 * time.Minute),  // 30 minutes ago - not expired
					time.Now().Add(-10 * time.Minute),  // 10 minutes ago
				},
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessListLength:        3,
				ConfigAPSResetSuccessListWindowMinutes: 60,
			},
			expectedResult: true,
			expectedList:   2, // Should prune only the expired entry
		},
		{
			name: "Allow request - list full, all entries expired",
			statistic: &APSResetStatistic{
				StatAPSResetSuccessList: []time.Time{
					time.Now().Add(-120 * time.Minute), // 2 hours ago - expired
					time.Now().Add(-90 * time.Minute),  // 1.5 hours ago - expired
					time.Now().Add(-70 * time.Minute),  // 1.17 hours ago - expired
				},
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessListLength:        3,
				ConfigAPSResetSuccessListWindowMinutes: 60,
			},
			expectedResult: true,
			expectedList:   2, // Should prune only the first expired entry, leaving 2 entries
		},
		{
			name: "Allow request - list not full, some expired entries",
			statistic: &APSResetStatistic{
				StatAPSResetSuccessList: []time.Time{
					time.Now().Add(-120 * time.Minute), // 2 hours ago - expired
					time.Now().Add(-10 * time.Minute),  // 10 minutes ago
				},
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessListLength:        3,
				ConfigAPSResetSuccessListWindowMinutes: 60,
			},
			expectedResult: true,
			expectedList:   2, // Should not prune anything since list is not full
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Act
			result := tt.statistic.CheckAndPruneSuccessList(tt.config)

			// Assert
			assert.Equal(t, tt.expectedResult, result)
			assert.Equal(t, tt.expectedList, len(tt.statistic.StatAPSResetSuccessList))

						// Additional verification: if result is true, the list should not be full
			// Note: The function only prunes one expired entry at a time, so some expired entries may remain
			if result {
				assert.Less(t, len(tt.statistic.StatAPSResetSuccessList), tt.config.ConfigAPSResetSuccessListLength,
					"List should not be full after pruning")
			}
		})
	}
}

func TestAPSResetStatistic_CheckAndPruneSuccessList_EdgeCases(t *testing.T) {
	tests := []struct {
		name           string
		statistic      *APSResetStatistic
		config         *APSResetConfig
		expectedResult bool
		expectedList   int
	}{
		{
			name: "Edge case - window is 0 minutes",
			statistic: &APSResetStatistic{
				StatAPSResetSuccessList: []time.Time{
					time.Now().Add(-1 * time.Second), // 1 second ago
				},
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessListLength:        1,
				ConfigAPSResetSuccessListWindowMinutes: 0,
			},
			expectedResult: true, // Should prune the expired entry and allow request
			expectedList:   0,    // Should prune the entry
		},
		{
			name: "Edge case - window is 1 minute, entry just expired",
			statistic: &APSResetStatistic{
				StatAPSResetSuccessList: []time.Time{
					time.Now().Add(-61 * time.Second), // Just over 1 minute ago
				},
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessListLength:        1,
				ConfigAPSResetSuccessListWindowMinutes: 1,
			},
			expectedResult: true, // Entry should be pruned
			expectedList:   0,
		},
		{
			name: "Edge case - window is 1 minute, entry just within limit",
			statistic: &APSResetStatistic{
				StatAPSResetSuccessList: []time.Time{
					time.Now().Add(-59 * time.Second), // Just under 1 minute ago
				},
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessListLength:        1,
				ConfigAPSResetSuccessListWindowMinutes: 1,
			},
			expectedResult: false, // Entry is still within window
			expectedList:   1,
		},
		{
			name: "Edge case - very large window",
			statistic: &APSResetStatistic{
				StatAPSResetSuccessList: []time.Time{
					time.Now().Add(-24 * time.Hour), // 1 day ago
				},
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessListLength:        1,
				ConfigAPSResetSuccessListWindowMinutes: 1440, // 24 hours
			},
			expectedResult: true, // Entry is expired (older than 24 hours), so it gets pruned
			expectedList:   0,    // Should prune the expired entry
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Act
			result := tt.statistic.CheckAndPruneSuccessList(tt.config)

			// Assert
			assert.Equal(t, tt.expectedResult, result)
			assert.Equal(t, tt.expectedList, len(tt.statistic.StatAPSResetSuccessList))
		})
	}
}

func TestAPSResetStatistic_CheckAndPruneSuccessList_ComplexScenarios(t *testing.T) {
	tests := []struct {
		name           string
		statistic      *APSResetStatistic
		config         *APSResetConfig
		expectedResult bool
		expectedList   int
	}{
		{
			name: "Complex scenario - multiple pruning cycles needed",
			statistic: &APSResetStatistic{
				StatAPSResetSuccessList: []time.Time{
					time.Now().Add(-120 * time.Minute), // 2 hours ago - expired
					time.Now().Add(-90 * time.Minute),  // 1.5 hours ago - expired
					time.Now().Add(-60 * time.Minute),  // 1 hour ago - expired
					time.Now().Add(-30 * time.Minute),  // 30 minutes ago - not expired
					time.Now().Add(-10 * time.Minute),  // 10 minutes ago - not expired
				},
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessListLength:        5,
				ConfigAPSResetSuccessListWindowMinutes: 60,
			},
			expectedResult: true,
			expectedList:   4, // Should prune only the first expired entry, leaving 4 entries
		},
		{
			name: "Complex scenario - list becomes empty after pruning",
			statistic: &APSResetStatistic{
				StatAPSResetSuccessList: []time.Time{
					time.Now().Add(-120 * time.Minute), // 2 hours ago - expired
					time.Now().Add(-90 * time.Minute),  // 1.5 hours ago - expired
				},
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessListLength:        2,
				ConfigAPSResetSuccessListWindowMinutes: 60,
			},
			expectedResult: true,
			expectedList:   1, // Should prune only the first expired entry, leaving 1 entry
		},
		{
			name: "Complex scenario - list remains full after pruning expired entries",
			statistic: &APSResetStatistic{
				StatAPSResetSuccessList: []time.Time{
					time.Now().Add(-120 * time.Minute), // 2 hours ago - expired
					time.Now().Add(-30 * time.Minute),  // 30 minutes ago - not expired
					time.Now().Add(-10 * time.Minute),  // 10 minutes ago - not expired
				},
			},
			config: &APSResetConfig{
				ConfigAPSResetSuccessListLength:        3,
				ConfigAPSResetSuccessListWindowMinutes: 60,
			},
			expectedResult: true,
			expectedList:   2, // Should prune the expired entry, list becomes not full
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Act
			result := tt.statistic.CheckAndPruneSuccessList(tt.config)

			// Assert
			assert.Equal(t, tt.expectedResult, result)
			assert.Equal(t, tt.expectedList, len(tt.statistic.StatAPSResetSuccessList))

			// Verify the remaining entries are in chronological order (oldest first)
			for i := 1; i < len(tt.statistic.StatAPSResetSuccessList); i++ {
				assert.True(t, tt.statistic.StatAPSResetSuccessList[i-1].Before(tt.statistic.StatAPSResetSuccessList[i]) ||
					tt.statistic.StatAPSResetSuccessList[i-1].Equal(tt.statistic.StatAPSResetSuccessList[i]),
					"Entries should be in chronological order")
			}
		})
	}
}
