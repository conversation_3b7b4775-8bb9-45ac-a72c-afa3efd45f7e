package devicegroups

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
	"synapse-its.com/shared/rest/onramp/helper"
)

// Test helper to create a mock request with JSON body
func createMockRequest(method, url string, body interface{}) *http.Request {
	var reqBody *bytes.Buffer
	if body != nil {
		jsonBody, _ := json.Marshal(body)
		reqBody = bytes.NewBuffer(jsonBody)
	} else {
		reqBody = bytes.NewBuffer([]byte{})
	}

	req := httptest.NewRequest(method, url, reqBody)
	req.Header.Set("Content-Type", "application/json")
	return req
}

// Mock sql.Result for testing
type mockSQLResult struct {
	rowsAffected int64
	lastInsertId int64
	err          error
}

func (m *mockSQLResult) LastInsertId() (int64, error) {
	return m.lastInsertId, m.err
}

func (m *mockSQLResult) RowsAffected() (int64, error) {
	return m.rowsAffected, m.err
}

// Test validateOrganizationId function
func Test_validateOrganizationId(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		orgId       string
		expectedErr error
		wantErr     bool
	}{
		{
			name:        "valid organization ID",
			orgId:       uuid.New().String(),
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "invalid organization ID",
			orgId:       "invalid-uuid",
			expectedErr: helper.ErrInvalidOrganizationID,
			wantErr:     true,
		},
		{
			name:        "empty organization ID",
			orgId:       "",
			expectedErr: helper.ErrInvalidOrganizationID,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock request with URL vars
			req := httptest.NewRequest("GET", "/organizations/"+tt.orgId+"/devicegroups", nil)
			req = mux.SetURLVars(req, map[string]string{"organizationId": tt.orgId})

			// Execute the function under test
			result, err := helper.ValidateOrganizationID(req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				if tt.expectedErr != nil {
					assert.Equal(t, tt.expectedErr, err)
				}
				assert.Equal(t, uuid.Nil, result)
			} else {
				assert.NoError(t, err)
				assert.NotEqual(t, uuid.Nil, result)
			}
		})
	}
}

// Test validateDeviceGroupId function
func Test_validateDeviceGroupId(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		deviceGroupId string
		expectedErr   error
		wantErr       bool
	}{
		{
			name:          "valid device group ID",
			deviceGroupId: uuid.New().String(),
			expectedErr:   nil,
			wantErr:       false,
		},
		{
			name:          "invalid device group ID",
			deviceGroupId: "invalid-uuid",
			expectedErr:   nil, // Will be a uuid.invalidLengthError, not helper.ErrInvalidUUID
			wantErr:       true,
		},
		{
			name:          "empty device group ID",
			deviceGroupId: "",
			expectedErr:   helper.ErrInvalidUUID,
			wantErr:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock request with URL vars
			req := httptest.NewRequest("GET", "/organizations/org-id/devicegroups/"+tt.deviceGroupId, nil)
			req = mux.SetURLVars(req, map[string]string{"devicegroupId": tt.deviceGroupId})

			// Execute the function under test
			result, err := helper.ParseUUIDFromRequest(req, "devicegroupId")

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				if tt.expectedErr != nil {
					assert.Equal(t, tt.expectedErr, err)
				}
				assert.Equal(t, uuid.Nil, result)
			} else {
				assert.NoError(t, err)
				assert.NotEqual(t, uuid.Nil, result)
			}
		})
	}
}

// Test parseCreateAndUpdateDeviceGroupsRequest function
func Test_parseCreateAndUpdateDeviceGroupsRequest(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		requestBody interface{}
		expectedErr error
		wantErr     bool
	}{
		{
			name: "valid request",
			requestBody: CreateAndUpdateDeviceGroupsRequest{
				Name: "Test Device Group",
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name: "empty name",
			requestBody: CreateAndUpdateDeviceGroupsRequest{
				Name: "",
			},
			expectedErr: ErrInvalidDeviceGroupsName,
			wantErr:     true,
		},
		{
			name: "whitespace only name",
			requestBody: CreateAndUpdateDeviceGroupsRequest{
				Name: "   ",
			},
			expectedErr: ErrInvalidDeviceGroupsName,
			wantErr:     true,
		},
		{
			name:        "invalid JSON",
			requestBody: "invalid json",
			expectedErr: ErrInvalidRequestBody,
			wantErr:     true,
		},
		{
			name: "unexpected fields",
			requestBody: map[string]interface{}{
				"name":       "Test Device Group",
				"unexpected": "field",
			},
			expectedErr: ErrUnexpectedFields,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock request with test body
			req := createMockRequest("POST", "/organizations/org-id/devicegroups", tt.requestBody)

			// Execute the function under test
			result, err := parseCreateAndUpdateDeviceGroupsRequest(req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.requestBody.(CreateAndUpdateDeviceGroupsRequest).Name, result.Name)
			}
		})
	}
}

// Test getDeviceGroupsByOrganization function
func Test_getDeviceGroupsByOrganization(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		orgId       uuid.UUID
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
		expectedLen int
	}{
		{
			name:  "successful retrieval with device groups",
			orgId: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryGenericSlice call
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful device groups retrieval
					devicegroups := dest.(*[]DeviceGroups)
					*devicegroups = []DeviceGroups{
						{
							Id:             uuid.New(),
							OrganizationId: uuid.New(),
							Name:           "Device Group 1",
							CreatedAt:      time.Now().UTC(),
							UpdatedAt:      time.Now().UTC(),
							IsDeleted:      false,
						},
						{
							Id:             uuid.New(),
							OrganizationId: uuid.New(),
							Name:           "Device Group 2",
							CreatedAt:      time.Now().UTC(),
							UpdatedAt:      time.Now().UTC(),
							IsDeleted:      false,
						},
					}
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
			expectedLen: 2,
		},
		{
			name:  "successful retrieval with no device groups",
			orgId: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryGenericSlice call with empty result
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate empty device groups list
					devicegroups := dest.(*[]DeviceGroups)
					*devicegroups = []DeviceGroups{}
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
			expectedLen: 0,
		},
		{
			name:  "database error",
			orgId: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
			expectedLen: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			result, err := getDeviceGroupsByOrganization(mockDB, tt.orgId)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, *result, tt.expectedLen)
			}
		})
	}
}

// Test createDeviceGroups function
func Test_createDeviceGroups(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		orgId       uuid.UUID
		request     *CreateAndUpdateDeviceGroupsRequest
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
	}{
		{
			name:  "successful creation",
			orgId: uuid.New(),
			request: &CreateAndUpdateDeviceGroupsRequest{
				Name: "Test Device Group",
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryRow call for organization check
				mockDB.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"exists": true}, nil
				}
				// Mock successful QueryRowStruct call for insertion
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful device group creation
					devicegroup := dest.(*DeviceGroups)
					devicegroup.Id = uuid.New()
					devicegroup.OrganizationId = uuid.New()
					devicegroup.Name = "Test Device Group"
					devicegroup.CreatedAt = time.Now().UTC()
					devicegroup.UpdatedAt = time.Now().UTC()
					devicegroup.IsDeleted = false
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:  "organization not found",
			orgId: uuid.New(),
			request: &CreateAndUpdateDeviceGroupsRequest{
				Name: "Test Device Group",
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryRow call for organization check with false
				mockDB.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"exists": false}, nil
				}
			},
			expectedErr: ErrOrganizationNotFound,
			wantErr:     true,
		},
		{
			name:  "database error on organization check",
			orgId: uuid.New(),
			request: &CreateAndUpdateDeviceGroupsRequest{
				Name: "Test Device Group",
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error on QueryRow
				mockDB.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return nil, errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:  "missing exists column",
			orgId: uuid.New(),
			request: &CreateAndUpdateDeviceGroupsRequest{
				Name: "Test Device Group",
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock QueryRow call with missing exists column
				mockDB.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"other_column": "value"}, nil
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:  "invalid exists column type",
			orgId: uuid.New(),
			request: &CreateAndUpdateDeviceGroupsRequest{
				Name: "Test Device Group",
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock QueryRow call with invalid exists column type
				mockDB.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"exists": "not_a_bool"}, nil
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:  "database error on insertion",
			orgId: uuid.New(),
			request: &CreateAndUpdateDeviceGroupsRequest{
				Name: "Test Device Group",
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryRow call for organization check
				mockDB.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"exists": true}, nil
				}
				// Mock database error on QueryRowStruct
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("insertion failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			result, err := createDeviceGroups(mockDB, tt.orgId, tt.request)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.request.Name, result.Name)
				assert.NotEmpty(t, result.Id)
			}
		})
	}
}

// Test deleteDeviceGroups function
func Test_deleteDeviceGroups(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		orgId         uuid.UUID
		deviceGroupId uuid.UUID
		setupMock     func(*dbexecutor.FakeDBExecutor)
		expectedErr   error
		wantErr       bool
	}{
		{
			name:          "successful deletion",
			orgId:         uuid.New(),
			deviceGroupId: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful Exec call with 1 row affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 1}, nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:          "device group not found (0 rows affected)",
			orgId:         uuid.New(),
			deviceGroupId: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful Exec call with 0 rows affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 0}, nil
				}
			},
			expectedErr: ErrDeviceGroupNotFound,
			wantErr:     true,
		},
		{
			name:          "database error on exec",
			orgId:         uuid.New(),
			deviceGroupId: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error on Exec
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:          "error getting rows affected",
			orgId:         uuid.New(),
			deviceGroupId: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock error when getting rows affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 1, err: errors.New("rows affected error")}, nil
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			err := deleteDeviceGroups(mockDB, tt.orgId, tt.deviceGroupId)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test updateDeviceGroups function
func Test_updateDeviceGroups(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		orgId         uuid.UUID
		deviceGroupId uuid.UUID
		newName       string
		setupMock     func(*dbexecutor.FakeDBExecutor)
		expectedErr   error
		wantErr       bool
	}{
		{
			name:          "successful update",
			orgId:         uuid.New(),
			deviceGroupId: uuid.New(),
			newName:       "Updated Device Group",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful Exec call with 1 row affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 1}, nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:          "device group not found (0 rows affected)",
			orgId:         uuid.New(),
			deviceGroupId: uuid.New(),
			newName:       "Updated Device Group",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful Exec call with 0 rows affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 0}, nil
				}
			},
			expectedErr: ErrDeviceGroupNotFound,
			wantErr:     true,
		},
		{
			name:          "database error on exec",
			orgId:         uuid.New(),
			deviceGroupId: uuid.New(),
			newName:       "Updated Device Group",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error on Exec
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:          "error getting rows affected",
			orgId:         uuid.New(),
			deviceGroupId: uuid.New(),
			newName:       "Updated Device Group",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock error when getting rows affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 1, err: errors.New("rows affected error")}, nil
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			err := updateDeviceGroups(mockDB, tt.orgId, tt.deviceGroupId, tt.newName)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test hasDevices function
func Test_hasDevices(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		deviceGroupId  uuid.UUID
		setupMock      func(*dbexecutor.FakeDBExecutor)
		expectedErr    error
		wantErr        bool
		expectedResult bool
	}{
		{
			name:          "has devices",
			deviceGroupId: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryRowStruct call with count > 0
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					countResult := dest.(*struct {
						Exists bool `db:"exists"`
					})
					countResult.Exists = true
					return nil
				}
			},
			expectedErr:    nil,
			wantErr:        false,
			expectedResult: true,
		},
		{
			name:          "no devices",
			deviceGroupId: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryRowStruct call with count = 0
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					countResult := dest.(*struct {
						Exists bool `db:"exists"`
					})
					countResult.Exists = false
					return nil
				}
			},
			expectedErr:    nil,
			wantErr:        false,
			expectedResult: false,
		},
		{
			name:          "database error",
			deviceGroupId: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedErr:    ErrDatabaseOperation,
			wantErr:        true,
			expectedResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			result, err := hasDevices(mockDB, tt.deviceGroupId)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResult, result)
			}
		})
	}
}

// Test GetDeviceGroupsHandlerWithDeps function
func Test_GetDeviceGroupsHandlerWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		orgId          string
		setupDeps      func() HandlerDeps
		expectedStatus int
		wantErr        bool
	}{
		{
			name:  "successful_retrieval",
			orgId: uuid.New().String(),
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					GetDeviceGroupsByOrganization: func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*[]DeviceGroups, error) {
						// Return mock device groups
						devicegroups := []DeviceGroups{
							{
								Id:             uuid.New(),
								OrganizationId: orgId,
								Name:           "Device Group 1",
								CreatedAt:      time.Now().UTC(),
								UpdatedAt:      time.Now().UTC(),
								IsDeleted:      false,
							},
						}
						return &devicegroups, nil
					},
				}
			},
			expectedStatus: http.StatusOK,
			wantErr:        false,
		},
		{
			name:  "connection_error",
			orgId: uuid.New().String(),
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return nil, errors.New("connection failed")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name:  "invalid_organization_id",
			orgId: "invalid-uuid",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
				}
			},
			expectedStatus: http.StatusBadRequest,
			wantErr:        true,
		},
		{
			name:  "get_device_groups_error",
			orgId: uuid.New().String(),
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					GetDeviceGroupsByOrganization: func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*[]DeviceGroups, error) {
						return nil, errors.New("database error")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with test dependencies
			handler := GetDeviceGroupsHandlerWithDeps(tt.setupDeps())

			// Create mock request with URL vars
			req := httptest.NewRequest("GET", "/organizations/"+tt.orgId+"/devicegroups", nil)
			req = req.WithContext(context.Background())
			req = mux.SetURLVars(req, map[string]string{"organizationId": tt.orgId})

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, rr.Code)
		})
	}
}

// Test CreateDeviceGroupsHandlerWithDeps function
func Test_CreateDeviceGroupsHandlerWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		orgId          string
		requestBody    interface{}
		setupDeps      func() HandlerDeps
		expectedStatus int
		wantErr        bool
	}{
		{
			name:  "successful_creation",
			orgId: uuid.New().String(),
			requestBody: CreateAndUpdateDeviceGroupsRequest{
				Name: "Test Device Group",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					CreateDeviceGroups: func(pg connect.DatabaseExecutor, orgId uuid.UUID, req *CreateAndUpdateDeviceGroupsRequest) (*DeviceGroups, error) {
						// Return mock device group
						return &DeviceGroups{
							Id:             uuid.New(),
							OrganizationId: orgId,
							Name:           req.Name,
							CreatedAt:      time.Now().UTC(),
							UpdatedAt:      time.Now().UTC(),
							IsDeleted:      false,
						}, nil
					},
				}
			},
			expectedStatus: http.StatusOK,
			wantErr:        false,
		},
		{
			name:  "connection_error",
			orgId: uuid.New().String(),
			requestBody: CreateAndUpdateDeviceGroupsRequest{
				Name: "Test Device Group",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return nil, errors.New("connection failed")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name:  "invalid_organization_id",
			orgId: "invalid-uuid",
			requestBody: CreateAndUpdateDeviceGroupsRequest{
				Name: "Test Device Group",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
				}
			},
			expectedStatus: http.StatusBadRequest,
			wantErr:        true,
		},
		{
			name:        "invalid_request_body",
			orgId:       uuid.New().String(),
			requestBody: "invalid json",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
				}
			},
			expectedStatus: http.StatusBadRequest,
			wantErr:        true,
		},
		{
			name:  "create_device_groups_error",
			orgId: uuid.New().String(),
			requestBody: CreateAndUpdateDeviceGroupsRequest{
				Name: "Test Device Group",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					CreateDeviceGroups: func(pg connect.DatabaseExecutor, orgId uuid.UUID, req *CreateAndUpdateDeviceGroupsRequest) (*DeviceGroups, error) {
						return nil, errors.New("database error")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name:  "organization_not_found_error",
			orgId: uuid.New().String(),
			requestBody: CreateAndUpdateDeviceGroupsRequest{
				Name: "Test Device Group",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					CreateDeviceGroups: func(pg connect.DatabaseExecutor, orgId uuid.UUID, req *CreateAndUpdateDeviceGroupsRequest) (*DeviceGroups, error) {
						return nil, ErrOrganizationNotFound
					},
				}
			},
			expectedStatus: http.StatusNotFound,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with test dependencies
			handler := CreateDeviceGroupsHandlerWithDeps(tt.setupDeps())

			// Create mock request with URL vars
			req := createMockRequest("POST", "/organizations/"+tt.orgId+"/devicegroups", tt.requestBody)
			req = req.WithContext(context.Background())
			req = mux.SetURLVars(req, map[string]string{"organizationId": tt.orgId})

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, rr.Code)
		})
	}
}

// Test DeleteDeviceGroupsHandlerWithDeps function
func Test_DeleteDeviceGroupsHandlerWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		orgId          string
		deviceGroupId  string
		setupDeps      func() HandlerDeps
		expectedStatus int
		wantErr        bool
	}{
		{
			name:          "successful_deletion",
			orgId:         uuid.New().String(),
			deviceGroupId: uuid.New().String(),
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					HasDevices: func(pg connect.DatabaseExecutor, deviceGroupId uuid.UUID) (bool, error) {
						return false, nil
					},
					DeleteDevieGroups: func(pg connect.DatabaseExecutor, orgId uuid.UUID, deviceGroupId uuid.UUID) error {
						// Return success
						return nil
					},
				}
			},
			expectedStatus: http.StatusOK,
			wantErr:        false,
		},
		{
			name:          "connection_error",
			orgId:         uuid.New().String(),
			deviceGroupId: uuid.New().String(),
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return nil, errors.New("connection failed")
					},
					HasDevices: func(pg connect.DatabaseExecutor, deviceGroupId uuid.UUID) (bool, error) {
						return false, nil
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name:          "invalid_organization_id",
			orgId:         "invalid-uuid",
			deviceGroupId: uuid.New().String(),
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					HasDevices: func(pg connect.DatabaseExecutor, deviceGroupId uuid.UUID) (bool, error) {
						return false, nil
					},
				}
			},
			expectedStatus: http.StatusBadRequest,
			wantErr:        true,
		},
		{
			name:          "invalid_device_group_id",
			orgId:         uuid.New().String(),
			deviceGroupId: "invalid-uuid",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					HasDevices: func(pg connect.DatabaseExecutor, deviceGroupId uuid.UUID) (bool, error) {
						return false, nil
					},
				}
			},
			expectedStatus: http.StatusBadRequest,
			wantErr:        true,
		},
		{
			name:          "has_devices_error",
			orgId:         uuid.New().String(),
			deviceGroupId: uuid.New().String(),
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					HasDevices: func(pg connect.DatabaseExecutor, deviceGroupId uuid.UUID) (bool, error) {
						return false, errors.New("database error")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name:          "has_devices_true",
			orgId:         uuid.New().String(),
			deviceGroupId: uuid.New().String(),
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					HasDevices: func(pg connect.DatabaseExecutor, deviceGroupId uuid.UUID) (bool, error) {
						return true, nil
					},
				}
			},
			expectedStatus: http.StatusBadRequest,
			wantErr:        true,
		},
		{
			name:          "delete_device_groups_error",
			orgId:         uuid.New().String(),
			deviceGroupId: uuid.New().String(),
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					HasDevices: func(pg connect.DatabaseExecutor, deviceGroupId uuid.UUID) (bool, error) {
						return false, nil
					},
					DeleteDevieGroups: func(pg connect.DatabaseExecutor, orgId uuid.UUID, deviceGroupId uuid.UUID) error {
						return errors.New("database error")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name:          "delete_device_groups_not_found",
			orgId:         uuid.New().String(),
			deviceGroupId: uuid.New().String(),
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					HasDevices: func(pg connect.DatabaseExecutor, deviceGroupId uuid.UUID) (bool, error) {
						return false, nil
					},
					DeleteDevieGroups: func(pg connect.DatabaseExecutor, orgId uuid.UUID, deviceGroupId uuid.UUID) error {
						return ErrDeviceGroupNotFound
					},
				}
			},
			expectedStatus: http.StatusNotFound,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with test dependencies
			handler := DeleteDeviceGroupsHandlerWithDeps(tt.setupDeps())

			// Create mock request with URL vars
			req := httptest.NewRequest("DELETE", "/organizations/"+tt.orgId+"/devicegroups/"+tt.deviceGroupId, nil)
			req = req.WithContext(context.Background())
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.orgId,
				"devicegroupId":  tt.deviceGroupId,
			})

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, rr.Code)
		})
	}
}

// Test UpdateDeviceGroupsHandlerWithDeps function
func Test_UpdateDeviceGroupsHandlerWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		orgId          string
		deviceGroupId  string
		requestBody    interface{}
		setupDeps      func() HandlerDeps
		expectedStatus int
		wantErr        bool
	}{
		{
			name:          "successful_update",
			orgId:         uuid.New().String(),
			deviceGroupId: uuid.New().String(),
			requestBody: CreateAndUpdateDeviceGroupsRequest{
				Name: "Updated Device Group",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					UpdateDevieGroups: func(pg connect.DatabaseExecutor, orgId uuid.UUID, deviceGroupId uuid.UUID, newName string) error {
						// Return success
						return nil
					},
				}
			},
			expectedStatus: http.StatusOK,
			wantErr:        false,
		},
		{
			name:          "connection_error",
			orgId:         uuid.New().String(),
			deviceGroupId: uuid.New().String(),
			requestBody: CreateAndUpdateDeviceGroupsRequest{
				Name: "Updated Device Group",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return nil, errors.New("connection failed")
					},
					UpdateDevieGroups: func(pg connect.DatabaseExecutor, orgId uuid.UUID, deviceGroupId uuid.UUID, newName string) error {
						return nil
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name:          "invalid_organization_id",
			orgId:         "invalid-uuid",
			deviceGroupId: uuid.New().String(),
			requestBody: CreateAndUpdateDeviceGroupsRequest{
				Name: "Updated Device Group",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					UpdateDevieGroups: func(pg connect.DatabaseExecutor, orgId uuid.UUID, deviceGroupId uuid.UUID, newName string) error {
						return nil
					},
				}
			},
			expectedStatus: http.StatusBadRequest,
			wantErr:        true,
		},
		{
			name:          "invalid_device_group_id",
			orgId:         uuid.New().String(),
			deviceGroupId: "invalid-uuid",
			requestBody: CreateAndUpdateDeviceGroupsRequest{
				Name: "Updated Device Group",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					UpdateDevieGroups: func(pg connect.DatabaseExecutor, orgId uuid.UUID, deviceGroupId uuid.UUID, newName string) error {
						return nil
					},
				}
			},
			expectedStatus: http.StatusBadRequest,
			wantErr:        true,
		},
		{
			name:          "invalid_request_body",
			orgId:         uuid.New().String(),
			deviceGroupId: uuid.New().String(),
			requestBody:   "invalid json",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					UpdateDevieGroups: func(pg connect.DatabaseExecutor, orgId uuid.UUID, deviceGroupId uuid.UUID, newName string) error {
						return nil
					},
				}
			},
			expectedStatus: http.StatusBadRequest,
			wantErr:        true,
		},
		{
			name:          "update_device_groups_error",
			orgId:         uuid.New().String(),
			deviceGroupId: uuid.New().String(),
			requestBody: CreateAndUpdateDeviceGroupsRequest{
				Name: "Updated Device Group",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					UpdateDevieGroups: func(pg connect.DatabaseExecutor, orgId uuid.UUID, deviceGroupId uuid.UUID, newName string) error {
						return errors.New("database error")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name:          "update_device_groups_not_found",
			orgId:         uuid.New().String(),
			deviceGroupId: uuid.New().String(),
			requestBody: CreateAndUpdateDeviceGroupsRequest{
				Name: "Updated Device Group",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					UpdateDevieGroups: func(pg connect.DatabaseExecutor, orgId uuid.UUID, deviceGroupId uuid.UUID, newName string) error {
						return ErrDeviceGroupNotFound
					},
				}
			},
			expectedStatus: http.StatusNotFound,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with test dependencies
			handler := UpdateDeviceGroupsHandlerWithDeps(tt.setupDeps())

			// Create mock request with URL vars
			req := createMockRequest("PUT", "/organizations/"+tt.orgId+"/devicegroups/"+tt.deviceGroupId, tt.requestBody)
			req = req.WithContext(context.Background())
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.orgId,
				"devicegroupId":  tt.deviceGroupId,
			})

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, rr.Code)
		})
	}
}
