-- Delete location_group related entries from CustomRolePermission
DELETE FROM {{CustomRolePermission}} WHERE PermissionIdentifier IN (
  'location_group_view_devices',
  'location_group_manage_devices', 
  'location_group_delete_devices'
);

-- Delete location_group related entries from TemplateRolePermission
DELETE FROM {{TemplateRolePermission}} WHERE PermissionIdentifier IN (
  'location_group_view_devices',
  'location_group_manage_devices', 
  'location_group_delete_devices',
  'location_group_ng_all_access',
  'location_group_ng_read_stats',
  'location_group_ng_set_time',
  'location_group_ng_read_config',
  'location_group_ng_clear_logs',
  'location_group_ng_reset_stats',
  'location_group_ng_write_datakey',
  'location_group_ng_write_user_settings',
  'location_group_ng_dfu'
);

-- Delete location_group related permissions from Permission table
DELETE FROM {{Permission}} WHERE Identifier IN (
  'location_group_view_devices',
  'location_group_manage_devices', 
  'location_group_delete_devices',
  'location_group_ng_all_access',
  'location_group_ng_read_stats',
  'location_group_ng_set_time',
  'location_group_ng_read_config',
  'location_group_ng_clear_logs',
  'location_group_ng_reset_stats',
  'location_group_ng_write_datakey',
  'location_group_ng_write_user_settings',
  'location_group_ng_dfu'
);



-- Delete location_group from PermissionGroup table
DELETE FROM {{PermissionGroup}} WHERE Identifier = 'location_group';
