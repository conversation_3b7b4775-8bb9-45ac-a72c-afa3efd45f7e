package devicegroups

import (
	"time"

	"github.com/google/uuid"
)

// DeviceGroup represents a device group for an organization
type DeviceGroups struct {
	Id             uuid.UUID `json:"id" db:"id"`
	OrganizationId uuid.UUID `json:"organizationid" db:"organizationid"`
	Name           string    `json:"name" db:"name"`
	IsDeleted      bool      `json:"-" db:"isdeleted"`
	CreatedAt      time.Time `json:"createdat" db:"createdat"`
	UpdatedAt      time.Time `json:"updatedat" db:"updatedat"`
}

// CreateAndUpdateDeviceGroupsRequest represents the request body for creating a new device groups
type CreateAndUpdateDeviceGroupsRequest struct {
	Name string `json:"name"`
}
