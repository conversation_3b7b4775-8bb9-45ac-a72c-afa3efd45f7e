# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Compiled Python bytecode
*.py[cod]
__pycache__

# Python testing artifacts
.pytest_cache

# Log files
*.log

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Test coverage reports
coverage.out
microservices/testing/coverage/*_coverage.out
**/*.out

# Keycloak realm export temporary file
external/keycloak/realm-export.tmp.json

# Local environmental variables
.env.local
.env.*.local

# devcontainer .settings file
.devcontainer/.settings

# Debugging files (created when using the dev container)
__debug_bin*

# Node.js dependencies
node_modules
.angular

# Personal credential file
creds.json

# Cucumber/Selenium screencaps
microservices/onramp/.e2e/screenshots/*

# Swagger UI
microservices/broker/docs
microservices/onramp/docs
