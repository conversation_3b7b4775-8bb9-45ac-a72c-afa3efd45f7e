# Multiple Roles Per User Implementation Plan

## Overview

This document outlines the implementation plan for updating the onramp service to support multiple roles per user. The current system assumes one user can have one role, but the database schema already supports multiple roles through the role assignment tables. This implementation will update the API endpoints to handle multiple roles.

## Current Architecture Analysis

### Database Schema
The database already supports multiple roles per user through the following structure:

1. **Memberships Table**: Links users (via AuthMethod) to organizations
2. **Role Assignment Tables**:
   - `OrgRoleAssignments`: Organization-wide roles
   - `DeviceGroupRoleAssignments`: Device group-scoped roles  
   - `LocationGroupRoleAssignments`: Location group-scoped roles

3. **Key Constraint**: `CONSTRAINT {{OrgRoleAssignments_PK}} PRIMARY KEY (MembershipId, RoleId)`
   - This composite primary key allows multiple roles per membership

### Current Implementation Issues

1. **Invite Schema**: `CreateInviteRequest` has single `OrganizationRole` field
2. **Role Assignment Logic**: Current code assumes single role assignment
3. **API Endpoints**: Not designed to handle multiple role assignments

## Implementation Requirements

### 1. Update Invite Schema and Logic

**Current Schema**:
```go
type CreateInviteRequest struct {
    Email            string    `json:"email" validate:"required,email"`
    InviterID        uuid.UUID `json:"inviterid" validate:"required"`
    Message          *string   `json:"message"`
    OrganizationRole uuid.UUID `json:"organizationrole" validate:"required"` // Single role
    ExpiredDays      *int      `json:"expireddays"`
}
```

**New Schema**:
```go
type CreateInviteRequest struct {
    Email            string      `json:"email" validate:"required,email"`
    InviterID        uuid.UUID   `json:"inviterid" validate:"required"`
    Message          *string     `json:"message"`
    OrganizationRoles []uuid.UUID `json:"organizationroles" validate:"required,min=1"` // Multiple roles
    ExpiredDays      *int        `json:"expireddays"`
}
```

### 2. Update User Endpoints

**New Endpoints Needed**:
- `GET /api/v3/user/{userId}/roles` - Get all roles for a user
- `POST /api/v3/user/{userId}/roles` - Assign multiple roles to user
- `PUT /api/v3/user/{userId}/roles` - Update user's role assignments
- `DELETE /api/v3/user/{userId}/roles/{roleId}` - Remove specific role from user

**Updated Endpoints**:
- `GET /api/v3/user/{userId}/profile` - Include all role information

### 3. Update Invite Endpoints

**Updated Endpoints**:
- `POST /api/v3/organization/{orgId}/invites` - Accept multiple roles
- `GET /api/v3/organization/{orgId}/invites` - Return role information
- `PUT /api/v3/organization/{orgId}/invites/{inviteId}` - Update role assignments

## Implementation Steps

### Phase 1: Schema and Data Model Updates

#### 1.1 Update Invite Schemas
- [ ] Modify `CreateInviteRequest` to use `OrganizationRoles` array
- [ ] Update `UserInvite` model to handle multiple roles
- [ ] Create new response schemas for multiple roles
- [ ] Update validation rules

#### 1.2 Create New User Role Schemas
- [ ] Create `UserRoleAssignment` struct
- [ ] Create `UserRolesResponse` struct
- [ ] Create `AssignUserRolesRequest` struct
- [ ] Create `UpdateUserRolesRequest` struct

### Phase 2: Database Function Updates

#### 2.1 Update Invite Functions
- [ ] Modify `CreateInvite` to handle multiple roles
- [ ] Update `CreateMembershipForUser` to assign multiple roles
- [ ] Add role assignment validation functions
- [ ] Update role verification logic

#### 2.2 Create New User Role Functions
- [ ] `GetUserRoles(pg, userId, orgId)` - Get all roles for user in organization
- [ ] `AssignUserRoles(pg, userId, orgId, roleIds)` - Assign multiple roles
- [ ] `UpdateUserRoles(pg, userId, orgId, roleIds)` - Update role assignments
- [ ] `RemoveUserRole(pg, userId, orgId, roleId)` - Remove specific role
- [ ] `ValidateRoleAssignments(pg, orgId, roleIds)` - Validate role assignments

### Phase 3: Handler Updates

#### 3.1 Update Invite Handlers
- [ ] Modify `CreateInviteHandler` to accept multiple roles
- [ ] Update invite creation logic to handle role arrays
- [ ] Add role assignment validation
- [ ] Update response schemas

#### 3.2 Create New User Role Handlers
- [ ] `GetUserRolesHandler` - Retrieve user's roles
- [ ] `AssignUserRolesHandler` - Assign roles to user
- [ ] `UpdateUserRolesHandler` - Update user's role assignments
- [ ] `RemoveUserRoleHandler` - Remove role from user

### Phase 4: API Endpoint Updates

#### 4.1 Update Router Configuration
- [ ] Add new user role endpoints to router
- [ ] Update existing invite endpoints
- [ ] Ensure proper middleware and authentication

#### 4.2 Update Response Handling
- [ ] Modify response functions to handle role arrays
- [ ] Update error handling for multiple role scenarios
- [ ] Ensure backward compatibility where possible

### Phase 5: Testing and Validation

#### 5.1 Unit Tests
- [ ] Update existing invite tests for multiple roles
- [ ] Create tests for new user role functions
- [ ] Test role assignment validation logic
- [ ] Test error scenarios

#### 5.2 Integration Tests
- [ ] Test complete invite flow with multiple roles
- [ ] Test user role management endpoints
- [ ] Validate database constraints and relationships

## Technical Implementation Details

### Database Queries

#### Multiple Role Assignment
```sql
-- Insert multiple role assignments
INSERT INTO {{OrgRoleAssignments}} (MembershipId, RoleId, IsDeleted, CreatedAt, UpdatedAt)
SELECT $1, unnest($2::uuid[]), false, NOW(), NOW()
ON CONFLICT (MembershipId, RoleId) DO UPDATE SET
    IsDeleted = false,
    UpdatedAt = NOW();
```

#### Get User Roles
```sql
-- Get all roles for a user in an organization
SELECT 
    cr.Id,
    cr.Name,
    cr.Description,
    cr.TemplateRoleIdentifier,
    ora.CreatedAt as AssignedAt
FROM {{OrgRoleAssignments}} ora
JOIN {{CustomRole}} cr ON ora.RoleId = cr.Id
JOIN {{Memberships}} m ON ora.MembershipId = m.Id
WHERE m.AuthMethodId = $1 
    AND m.OrganizationId = $2 
    AND ora.IsDeleted = false
    AND cr.IsDeleted = false;
```

### Error Handling

#### New Error Types
```go
var (
    ErrMultipleRolesRequired = errors.New("at least one role must be specified")
    ErrInvalidRoleAssignment = errors.New("invalid role assignment for organization")
    ErrRoleAlreadyAssigned  = errors.New("role is already assigned to user")
    ErrRoleNotAssigned      = errors.New("role is not assigned to user")
)
```

#### Validation Functions
```go
// Validate role assignments for organization
func ValidateRoleAssignments(pg connect.DatabaseExecutor, orgId uuid.UUID, roleIds []uuid.UUID) error {
    if len(roleIds) == 0 {
        return ErrMultipleRolesRequired
    }
    
    // Check if all roles belong to the organization
    // Check if roles are valid and not deleted
    // Return appropriate errors
}
```

### Backward Compatibility

#### API Versioning Strategy
- **Current**: `/api/v3/` endpoints support multiple roles
- **Legacy**: Maintain existing single-role behavior for older clients
- **Migration**: Provide migration path for existing single-role assignments

#### Data Migration
```sql
-- Ensure existing single-role assignments remain valid
-- No data migration required as schema already supports multiple roles
-- Update existing single-role assignments to use new endpoint structure
```

## Security Considerations

### Role Assignment Validation
- [ ] Verify user has permission to assign roles
- [ ] Validate role hierarchy and permissions
- [ ] Prevent privilege escalation
- [ ] Audit role assignment changes

### Access Control
- [ ] Ensure users can only manage roles within their scope
- [ ] Validate organization membership
- [ ] Check role assignment permissions

## Performance Considerations

### Database Optimization
- [ ] Add composite indexes for role queries
- [ ] Optimize role assignment queries
- [ ] Consider caching for frequently accessed role data

### API Performance
- [ ] Implement pagination for large role lists
- [ ] Use efficient database queries
- [ ] Consider response caching strategies

## Testing Strategy

### Test Coverage Requirements
- [ ] 100% coverage for new functions
- [ ] Test multiple role assignment scenarios
- [ ] Test error conditions and edge cases
- [ ] Test backward compatibility

### Test Scenarios
1. **Happy Path**: Assign multiple roles successfully
2. **Validation**: Invalid role assignments
3. **Permissions**: Insufficient permissions for role assignment
4. **Edge Cases**: Empty role arrays, duplicate roles
5. **Integration**: Complete invite and role assignment flow

## Deployment Plan

### Phase 1: Development and Testing
- [ ] Implement schema changes
- [ ] Update business logic
- [ ] Comprehensive testing
- [ ] Code review and approval

### Phase 2: Staging Deployment
- [ ] Deploy to staging environment
- [ ] Integration testing
- [ ] Performance testing
- [ ] Security validation

### Phase 3: Production Deployment
- [ ] Deploy to production
- [ ] Monitor system performance
- [ ] Validate functionality
- [ ] Monitor error rates

## Rollback Plan

### Rollback Triggers
- [ ] High error rates in new endpoints
- [ ] Performance degradation
- [ ] Data integrity issues
- [ ] Security vulnerabilities

### Rollback Procedure
- [ ] Revert to previous version
- [ ] Restore database schema if needed
- [ ] Notify stakeholders
- [ ] Investigate and fix issues

## Success Metrics

### Functional Metrics
- [ ] All new endpoints return correct responses
- [ ] Multiple role assignments work correctly
- [ ] Backward compatibility maintained
- [ ] No data loss or corruption

### Performance Metrics
- [ ] Response times within acceptable limits
- [ ] Database query performance maintained
- [ ] No memory leaks or resource issues

### Quality Metrics
- [ ] 100% test coverage for new code
- [ ] All linting rules passed
- [ ] Security scan passed
- [ ] Documentation updated

## Conclusion

This implementation plan provides a comprehensive approach to supporting multiple roles per user in the onramp service. The database schema already supports this functionality, so the main work involves updating the API layer and business logic. The phased approach ensures minimal disruption while providing a robust, scalable solution for role management.

The implementation maintains backward compatibility while adding new functionality, ensuring existing systems continue to work while enabling the new multiple-role capabilities.
