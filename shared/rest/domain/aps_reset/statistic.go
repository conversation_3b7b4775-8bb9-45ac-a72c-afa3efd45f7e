package apsreset

import (
	"time"

	"github.com/google/uuid"
)

// Entity for APS reset statistic
type APSResetStatistic struct {
	ID                       uuid.UUID
	UserID                   uuid.UUID
	OrganizationID           uuid.UUID
	StatAPSResetCount        int
	StatAPSResetSuccessCount int
	StatAPSResetSuccessList  []time.Time
}

func (s *APSResetStatistic) IsLimitReached(config *APSResetConfig) bool {
	return s.StatAPSResetCount >= config.ConfigAPSResetSuccessLimit
}

func (s *APSResetStatistic) RecordSuccess() {
	s.StatAPSResetCount++
	s.StatAPSResetSuccessList = append(s.StatAPSResetSuccessList, time.Now())
}

// CheckAndPruneSuccessList checks if the success list has reached its limit and prunes old timestamps as needed.
// Returns true if the request can proceed, false if it should be rejected (rate limit window not expired).
// TODO: On deny (false), caller will send an alert and log to bigquery/postgres.
func (s *APSResetStatistic) CheckAndPruneSuccessList(config *APSResetConfig) bool {
	window := time.Duration(config.ConfigAPSResetSuccessListWindowMinutes) * time.Minute
	for {
		if len(s.StatAPSResetSuccessList) >= config.ConfigAPSResetSuccessListLength {
			oldest := s.StatAPSResetSuccessList[0]
			if time.Since(oldest) > window {
				// Oldest timestamp is outside the window, remove it and check again
				s.StatAPSResetSuccessList = s.StatAPSResetSuccessList[1:]
				continue
			} else {
				// Oldest timestamp is still within the window, reject the request
				return false
			}
		}
		// List is not full, allow the request
		break
	}
	return true
}
