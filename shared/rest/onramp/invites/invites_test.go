package invites

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/bqbatcher"
	"synapse-its.com/shared/mocks/dbexecutor"
	"synapse-its.com/shared/mocks/pubsub"
	"synapse-its.com/shared/pubsubdata"
)

// Test_validateCreateInviteRequest tests the validateCreateInviteRequest function
func Test_validateCreateInviteRequest(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		req         CreateInviteRequest
		expectedErr error
	}{
		{
			name: "valid_request",
			req: CreateInviteRequest{
				Email:            "<EMAIL>",
				InviterID:        uuid.New(),
				OrganizationRole: uuid.New(),
			},
			expectedErr: nil,
		},
		{
			name: "empty_email",
			req: CreateInviteRequest{
				Email:            "",
				InviterID:        uuid.New(),
				OrganizationRole: uuid.New(),
			},
			expectedErr: ErrInvalidEmail,
		},
		{
			name: "invalid_email_format",
			req: CreateInviteRequest{
				Email:            "invalid-email",
				InviterID:        uuid.New(),
				OrganizationRole: uuid.New(),
			},
			expectedErr: ErrInvalidEmail,
		},
		{
			name: "nil_inviter_id",
			req: CreateInviteRequest{
				Email:            "<EMAIL>",
				InviterID:        uuid.Nil,
				OrganizationRole: uuid.New(),
			},
			expectedErr: ErrInvalidUserID,
		},
		{
			name: "nil_organization_role",
			req: CreateInviteRequest{
				Email:            "<EMAIL>",
				InviterID:        uuid.New(),
				OrganizationRole: uuid.Nil,
			},
			expectedErr: ErrInvalidRequestBody,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute test
			err := validateCreateInviteRequest(&tt.req)

			// Assert results
			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test_validateTokenFormat tests the validateTokenFormat function
func Test_validateTokenFormat(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		token    string
		expected bool
	}{
		{
			name:     "valid_token_64_hex_chars",
			token:    "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
			expected: true,
		},
		{
			name:     "valid_token_uppercase_hex",
			token:    "ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890",
			expected: true,
		},
		{
			name:     "valid_token_mixed_case_hex",
			token:    "AbCdEf1234567890aBcDeF1234567890AbCdEf1234567890aBcDeF1234567890",
			expected: true,
		},
		{
			name:     "invalid_token_too_short",
			token:    "abcdef123456789",
			expected: false,
		},
		{
			name:     "invalid_token_too_long",
			token:    "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890extra",
			expected: false,
		},
		{
			name:     "invalid_token_non_hex_chars",
			token:    "abcdef1234567890abcdef1234567890abcdef1234567890abcdef123456789g",
			expected: false,
		},
		{
			name:     "invalid_token_special_chars",
			token:    "abcdef1234567890abcdef1234567890abcdef1234567890abcdef123456789!",
			expected: false,
		},
		{
			name:     "empty_token",
			token:    "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute test
			result := validateTokenFormat(tt.token)

			// Assert results
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test_buildInviteLink tests the buildInviteLink function
func Test_buildInviteLink(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		orgID    string
		token    string
		expected string
	}{
		{
			name:     "valid_org_and_token",
			orgID:    "123e4567-e89b-12d3-a456-************",
			token:    "abcdef1234567890",
			expected: "/api/organizations/123e4567-e89b-12d3-a456-************/invites/validate?token=abcdef1234567890",
		},
		{
			name:     "different_org_and_token",
			orgID:    "987fcdeb-51a2-43d1-b654-321987654321",
			token:    "fedcba0987654321",
			expected: "/api/organizations/987fcdeb-51a2-43d1-b654-321987654321/invites/validate?token=fedcba0987654321",
		},
		{
			name:     "empty_org_id",
			orgID:    "",
			token:    "abcdef1234567890",
			expected: "/api/organizations//invites/validate?token=abcdef1234567890",
		},
		{
			name:     "empty_token",
			orgID:    "123e4567-e89b-12d3-a456-************",
			token:    "",
			expected: "/api/organizations/123e4567-e89b-12d3-a456-************/invites/validate?token=",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute test
			result := buildInviteLink(tt.orgID, tt.token)

			// Assert results
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test_toInviteResponse tests the toInviteResponse function
func Test_toInviteResponse(t *testing.T) {
	t.Parallel()

	// Setup test data
	testID := uuid.New()
	testOrgID := uuid.New()
	testInviterID := uuid.New()
	testCustomRoleID := uuid.New()
	testMessage := "Test message"
	testTime := time.Now().UTC()
	testRetried := time.Now().UTC().Add(-1 * time.Hour)
	testExpired := time.Now().UTC().Add(24 * time.Hour)
	testSent := time.Now().UTC().Add(-30 * time.Minute)

	tests := []struct {
		name     string
		invite   *UserInvite
		expected *InviteResponse
	}{
		{
			name: "complete_invite_with_all_fields",
			invite: &UserInvite{
				ID:                     testID,
				OrganizationIdentifier: testOrgID,
				TokenHash:              "test-hash",
				Email:                  "<EMAIL>",
				InviterID:              testInviterID,
				CustomRoleID:           testCustomRoleID,
				Status:                 StatusPending,
				Message:                &testMessage,
				RequireSSO:             true,
				RetryCount:             2,
				Retried:                &testRetried,
				Expired:                &testExpired,
				Created:                testTime,
				Sent:                   &testSent,
				Updated:                testTime,
			},
			expected: &InviteResponse{
				ID:                     testID,
				OrganizationIdentifier: testOrgID,
				Email:                  "<EMAIL>",
				InviterID:              testInviterID,
				CustomRoleID:           testCustomRoleID,
				Status:                 StatusPending,
				Message:                &testMessage,
				RequireSSO:             true,
				RetryCount:             2,
				Retried:                &testRetried,
				Expired:                &testExpired,
				Created:                testTime,
				Sent:                   &testSent,
				Updated:                testTime,
			},
		},
		{
			name: "invite_with_nil_optional_fields",
			invite: &UserInvite{
				ID:                     testID,
				OrganizationIdentifier: testOrgID,
				TokenHash:              "test-hash",
				Email:                  "<EMAIL>",
				InviterID:              testInviterID,
				CustomRoleID:           testCustomRoleID,
				Status:                 StatusRedeemed,
				Message:                nil,
				RequireSSO:             false,
				RetryCount:             0,
				Retried:                nil,
				Expired:                nil,
				Created:                testTime,
				Sent:                   nil,
				Updated:                testTime,
			},
			expected: &InviteResponse{
				ID:                     testID,
				OrganizationIdentifier: testOrgID,
				Email:                  "<EMAIL>",
				InviterID:              testInviterID,
				CustomRoleID:           testCustomRoleID,
				Status:                 StatusRedeemed,
				Message:                nil,
				RequireSSO:             false,
				RetryCount:             0,
				Retried:                nil,
				Expired:                nil,
				Created:                testTime,
				Sent:                   nil,
				Updated:                testTime,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute test
			result := toInviteResponse(tt.invite)

			// Assert results
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test_toInviteResponseSlice tests the toInviteResponseSlice function
func Test_toInviteResponseSlice(t *testing.T) {
	t.Parallel()

	testID := uuid.New()
	testTime := time.Now().UTC()

	invites := &[]UserInvite{
		{
			ID:      testID,
			Email:   "<EMAIL>",
			Status:  StatusPending,
			Created: testTime,
			Updated: testTime,
		},
	}

	result := toInviteResponseSlice(invites)

	assert.Len(t, *result, 1)
	assert.Equal(t, testID, (*result)[0].ID)
	assert.Equal(t, "<EMAIL>", (*result)[0].Email)
}

// Test_checkCooldownPeriod tests the checkCooldownPeriod function
func Test_checkCooldownPeriod(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		invite      *UserInvite
		expectedErr error
	}{
		{
			name: "no_retry_yet",
			invite: &UserInvite{
				Retried: nil,
			},
			expectedErr: nil,
		},
		{
			name: "cooldown_passed",
			invite: &UserInvite{
				Retried: func() *time.Time { t := time.Now().UTC().Add(-2 * time.Minute); return &t }(),
			},
			expectedErr: nil,
		},
		{
			name: "cooldown_not_passed",
			invite: &UserInvite{
				Retried: func() *time.Time { t := time.Now().UTC().Add(-30 * time.Second); return &t }(),
			},
			expectedErr: ErrResendCooldown,
		},
		{
			name:        "invite_is_nil",
			invite:      nil,
			expectedErr: ErrInvalidInviteID,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := checkCooldownPeriod(tt.invite)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test_createInvite tests the createInvite function
func Test_createInvite(t *testing.T) {
	t.Parallel()

	orgID := uuid.New()
	tokenHash := "test-hash"

	tests := []struct {
		name        string
		req         CreateInviteRequest
		mockDB      *dbexecutor.FakeDBExecutor
		expectedErr error
	}{
		{
			name: "success_basic",
			req: CreateInviteRequest{
				Email:            "<EMAIL>",
				InviterID:        uuid.New(),
				OrganizationRole: uuid.New(),
			},
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"id": uuid.New().String(),
					}, nil
				},
			},
			expectedErr: nil,
		},
		{
			name: "success_with_expired_days_and_message",
			req: CreateInviteRequest{
				Email:            "<EMAIL>",
				InviterID:        uuid.New(),
				OrganizationRole: uuid.New(),
				ExpiredDays:      func() *int { days := 7; return &days }(),
				Message:          func() *string { msg := "Custom invite message"; return &msg }(),
			},
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"id": uuid.New().String(),
					}, nil
				},
			},
			expectedErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result, err := createInvite(tt.mockDB, orgID, tokenHash, tt.req)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.req.Email, result.Email)
				assert.Equal(t, StatusPending, result.Status)

				// For the case with expired days and message, verify the fields are set correctly
				if tt.req.ExpiredDays != nil && tt.req.Message != nil {
					assert.NotNil(t, result.Expired)
					assert.Equal(t, *tt.req.Message, *result.Message)
				}
			}
		})
	}
}

// Test_createInvite_error tests the createInvite function error cases
func Test_createInvite_error(t *testing.T) {
	t.Parallel()

	orgID := uuid.New()
	tokenHash := "test-hash"
	req := CreateInviteRequest{
		Email:            "<EMAIL>",
		InviterID:        uuid.New(),
		OrganizationRole: uuid.New(),
	}

	tests := []struct {
		name        string
		mockDB      *dbexecutor.FakeDBExecutor
		expectedErr error
	}{
		{
			name: "database_error",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return nil, errors.New("database error")
				},
			},
			expectedErr: ErrDatabaseOperation,
		},
		{
			name: "missing_id_in_result",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{}, nil
				},
			},
			expectedErr: ErrInvalidInviteID,
		},
		{
			name: "invalid_id_type",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"id": 123, // not a string
					}, nil
				},
			},
			expectedErr: fmt.Errorf("ID column is not a string: %T", 123),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			_, err := createInvite(tt.mockDB, orgID, tokenHash, req)

			assert.Error(t, err)
			if tt.expectedErr == ErrDatabaseOperation || tt.expectedErr == ErrInvalidInviteID {
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.Contains(t, err.Error(), "ID column is not a string")
			}
		})
	}
}

// Test_getInviteByID tests the getInviteByID function
func Test_getInviteByID(t *testing.T) {
	t.Parallel()

	inviteID := uuid.New()

	tests := []struct {
		name        string
		mockDB      *dbexecutor.FakeDBExecutor
		expectedErr error
	}{
		{
			name: "success",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					invite := dest.(*UserInvite)
					invite.ID = inviteID
					invite.Email = "<EMAIL>"
					invite.Status = StatusPending
					return nil
				},
			},
			expectedErr: nil,
		},
		{
			name: "not_found",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				},
			},
			expectedErr: ErrInviteNotFound,
		},
		{
			name: "database_error",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database error")
				},
			},
			expectedErr: ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result, err := getInviteByID(tt.mockDB, inviteID)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, inviteID, result.ID)
			}
		})
	}
}

// Test_updateInviteStatus tests the updateInviteStatus function
func Test_updateInviteStatus(t *testing.T) {
	t.Parallel()

	inviteID := uuid.New()
	now := time.Now().UTC()

	tests := []struct {
		name        string
		mockDB      *dbexecutor.FakeDBExecutor
		expectedErr error
	}{
		{
			name: "success",
			mockDB: &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					return &mockResult{rowsAffected: 1}, nil
				},
			},
			expectedErr: nil,
		},
		{
			name: "not_found",
			mockDB: &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					return &mockResult{rowsAffected: 0}, nil
				},
			},
			expectedErr: ErrInviteNotFound,
		},
		{
			name: "database_error",
			mockDB: &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("database error")
				},
			},
			expectedErr: ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := updateInviteStatus(tt.mockDB, inviteID, StatusRedeemed, &now)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// mockResult implements sql.Result for testing
type mockResult struct {
	rowsAffected int64
	lastInsertID int64
}

func (m *mockResult) LastInsertId() (int64, error) {
	return m.lastInsertID, nil
}

func (m *mockResult) RowsAffected() (int64, error) {
	return m.rowsAffected, nil
}

// Test_validateInviteToken tests the validateInviteToken function
func Test_validateInviteToken(t *testing.T) {
	t.Parallel()

	validToken := "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
	invalidToken := "invalid"

	tests := []struct {
		name        string
		token       string
		mockDB      *dbexecutor.FakeDBExecutor
		expectedErr error
	}{
		{
			name:  "success",
			token: validToken,
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					invite := dest.(*UserInvite)
					invite.ID = uuid.New()
					invite.Email = "<EMAIL>"
					invite.Status = StatusPending
					return nil
				},
			},
			expectedErr: nil,
		},
		{
			name:        "invalid_token_format",
			token:       invalidToken,
			mockDB:      &dbexecutor.FakeDBExecutor{},
			expectedErr: ErrInvalidInviteToken,
		},
		{
			name:  "token_not_found",
			token: validToken,
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				},
			},
			expectedErr: ErrInviteNotFound,
		},
		{
			name:  "database_error",
			token: validToken,
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database error")
				},
			},
			expectedErr: ErrInviteNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result, err := validateInviteToken(tt.mockDB, tt.token)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

// Test_parseRevokeInviteRequest tests the parseRevokeInviteRequest function
func Test_parseRevokeInviteRequest(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		body        string
		expectedErr bool
	}{
		{
			name:        "valid_request",
			body:        `{"actor": "test-actor"}`,
			expectedErr: false,
		},
		{
			name:        "missing_actor",
			body:        `{}`,
			expectedErr: true,
		},
		{
			name:        "empty_actor",
			body:        `{"actor": ""}`,
			expectedErr: true,
		},
		{
			name:        "invalid_json",
			body:        `{invalid json}`,
			expectedErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest("POST", "/test", strings.NewReader(tt.body))
			result, err := parseRevokeInviteRequest(req)

			if tt.expectedErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, "test-actor", result.Actor)
			}
		})
	}
}

// Test_parseResendInviteRequest tests the parseResendInviteRequest function
func Test_parseResendInviteRequest(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		body        string
		expectedErr bool
	}{
		{
			name:        "valid_request",
			body:        `{"actor": "test-actor", "body": "test message"}`,
			expectedErr: false,
		},
		{
			name:        "missing_actor",
			body:        `{"body": "test message"}`,
			expectedErr: true,
		},
		{
			name:        "empty_actor",
			body:        `{"actor": ""}`,
			expectedErr: true,
		},
		{
			name:        "invalid_json",
			body:        `{invalid json}`,
			expectedErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest("POST", "/test", strings.NewReader(tt.body))
			result, err := parseResendInviteRequest(req)

			if tt.expectedErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, "test-actor", result.Actor)
			}
		})
	}
}

// Test_getOrganizationName tests the getOrganizationName function
func Test_getOrganizationName(t *testing.T) {
	t.Parallel()

	orgID := uuid.New()

	tests := []struct {
		name         string
		mockDB       *dbexecutor.FakeDBExecutor
		expectedErr  error
		expectedName string
	}{
		{
			name: "success",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"name": "Test Organization",
					}, nil
				},
			},
			expectedErr:  nil,
			expectedName: "Test Organization",
		},
		{
			name: "not_found",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return nil, sql.ErrNoRows
				},
			},
			expectedErr: ErrOrganizationNotFound,
		},
		{
			name: "database_error",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return nil, errors.New("database error")
				},
			},
			expectedErr: ErrDatabaseOperation,
		},
		{
			name: "missing_name_field",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{}, nil
				},
			},
			expectedErr: ErrInvalidRowData,
		},
		{
			name: "invalid_name_type",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"name": 123, // not a string
					}, nil
				},
			},
			expectedErr: ErrInvalidRowData,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result, err := getOrganizationName(tt.mockDB, orgID)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				if tt.expectedErr == ErrDatabaseOperation {
					assert.Contains(t, err.Error(), "database operation failed")
				} else {
					assert.Equal(t, tt.expectedErr, err)
				}
				assert.Empty(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedName, result)
			}
		})
	}
}

// Test_updateInviteToken tests the updateInviteToken function
func Test_updateInviteToken(t *testing.T) {
	t.Parallel()

	inviteID := uuid.New()
	tokenHash := "test-hash"
	retryCount := 1
	req := &ResendInviteRequest{
		Actor: "test-actor",
	}

	tests := []struct {
		name        string
		mockDB      *dbexecutor.FakeDBExecutor
		expectedErr error
	}{
		{
			name: "success",
			mockDB: &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					return &mockResult{rowsAffected: 1}, nil
				},
			},
			expectedErr: nil,
		},
		{
			name: "not_found",
			mockDB: &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					return &mockResult{rowsAffected: 0}, nil
				},
			},
			expectedErr: ErrInviteNotFound,
		},
		{
			name: "database_error",
			mockDB: &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("database error")
				},
			},
			expectedErr: ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := updateInviteToken(tt.mockDB, inviteID, tokenHash, retryCount, req)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test_getInvitesForUser tests the getInvitesForUser function
func Test_getInvitesForUser(t *testing.T) {
	t.Parallel()

	userID := uuid.New()

	tests := []struct {
		name        string
		mockDB      *dbexecutor.FakeDBExecutor
		expectedErr error
	}{
		{
			name: "success",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					// Check if this is the email query or invite query by type
					switch dest.(type) {
					case *[]EmailResult:
						// Email query - populate with test email
						emails := dest.(*[]EmailResult)
						*emails = []EmailResult{{Email: "<EMAIL>"}}
						return nil
					case *[]UserInvite:
						// Invite query - return empty success
						return nil
					default:
						return errors.New("unexpected type")
					}
				},
			},
			expectedErr: nil,
		},
		{
			name: "no_email_found",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					// Check if this is the email query
					switch dest.(type) {
					case *[]EmailResult:
						return sql.ErrNoRows
					default:
						return errors.New("unexpected call")
					}
				},
			},
			expectedErr: ErrNoUserEmail,
		},
		{
			name: "database_error_email",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					// Check if this is the email query
					switch dest.(type) {
					case *[]EmailResult:
						return errors.New("database error")
					default:
						return errors.New("unexpected call")
					}
				},
			},
			expectedErr: ErrDatabaseOperation,
		},
		{
			name: "no_invites_found_for_email",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					// Check if this is the email query or invite query
					switch dest.(type) {
					case *[]EmailResult:
						// Email query - populate with test email
						emails := dest.(*[]EmailResult)
						*emails = []EmailResult{{Email: "<EMAIL>"}}
						return nil
					case *[]UserInvite:
						// Invite query - return no rows (empty result, not error)
						return sql.ErrNoRows
					default:
						return errors.New("unexpected type")
					}
				},
			},
			expectedErr: nil, // Should return empty slice, not error
		},
		{
			name: "multiple_emails_success",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					// Check if this is the email query or invite query
					switch dest.(type) {
					case *[]EmailResult:
						// Email query - populate with multiple emails to test the i > 0 branch
						emails := dest.(*[]EmailResult)
						*emails = []EmailResult{
							{Email: "<EMAIL>"},
							{Email: "<EMAIL>"},
							{Email: "<EMAIL>"},
						}
						return nil
					case *[]UserInvite:
						// Invite query - return empty success
						return nil
					default:
						return errors.New("unexpected type")
					}
				},
			},
			expectedErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result, err := getInvitesForUser(tt.mockDB, userID)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

// Test_getInvitesForOrganization tests the getInvitesForOrganization function
func Test_getInvitesForOrganization(t *testing.T) {
	t.Parallel()

	orgID := uuid.New()

	tests := []struct {
		name        string
		mockDB      *dbexecutor.FakeDBExecutor
		expectedErr error
	}{
		{
			name: "success",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					return nil
				},
			},
			expectedErr: nil,
		},
		{
			name: "no_invites_found",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				},
			},
			expectedErr: ErrNoInvitesFoundForOrganization,
		},
		{
			name: "database_error",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database error")
				},
			},
			expectedErr: ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result, err := getInvitesForOrganization(tt.mockDB, orgID)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

// Test_logInviteEvent tests the logInviteEvent function
func Test_logInviteEvent(t *testing.T) {
	t.Parallel()

	invite := &UserInvite{
		ID:                     uuid.New(),
		OrganizationIdentifier: uuid.New(),
		TokenHash:              "test-hash",
		Email:                  "<EMAIL>",
		InviterID:              uuid.New(),
		CustomRoleID:           uuid.New(),
		Status:                 StatusPending,
		Created:                time.Now().UTC(),
		Updated:                time.Now().UTC(),
	}

	tests := []struct {
		name        string
		batcher     bqbatch.Batcher
		expectedErr error
	}{
		{
			name:        "success",
			batcher:     bqbatcher.FakeBatcherWithOptions(),
			expectedErr: nil,
		},
		{
			name: "batcher_error",
			batcher: bqbatcher.FakeBatcherWithOptions(func(f *bqbatcher.FakeBatcher) {
				f.AddFn = func(row interface{}) error {
					return errors.New("batcher error")
				}
			}),
			expectedErr: ErrBigQueryOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := logInviteEvent(tt.batcher, EventTypeCreate, invite, "test-actor")

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test_publishEmailNotification tests the publishEmailNotification function
func Test_publishEmailNotification(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	message := pubsubdata.NotificationRequest{
		Type: "email",
		Payload: map[string]interface{}{
			"to":   "<EMAIL>",
			"body": "test message",
		},
		Metadata: map[string]interface{}{
			"source": "onramp",
		},
	}

	tests := []struct {
		name         string
		pubsubClient connect.PsClient
		expectedErr  bool
	}{
		{
			name:         "success",
			pubsubClient: pubsub.NewFakePubsubClient(),
			expectedErr:  false,
		},
		{
			name: "publish_error",
			pubsubClient: func() connect.PsClient {
				client := pubsub.NewFakePubsubClient()
				client.PublishError = errors.New("publish error")
				return client
			}(),
			expectedErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := publishEmailNotification(ctx, tt.pubsubClient, pubSubTopic, message)

			if tt.expectedErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test_renderEmailTemplate tests the renderEmailTemplate function from email.go
func Test_renderEmailTemplate(t *testing.T) {
	// Don't run in parallel to avoid template conflicts
	// t.Parallel()

	// Save original template and restore it after test
	originalTemplate := emailTemplate
	defer func() {
		emailTemplate = originalTemplate
	}()

	data := EmailTemplateData{
		AppName:          "Test App",
		Message:          "Test message",
		InviteLink:       "http://example.com/invite",
		OrganizationName: "Test Org",
	}

	result, err := renderEmailTemplate(data)

	assert.NoError(t, err)
	assert.NotEmpty(t, result)
	assert.Contains(t, result, "Test App")
	assert.Contains(t, result, "Test message")
	assert.Contains(t, result, "http://example.com/invite")
	assert.Contains(t, result, "Test Org")
}

// Test_renderEmailTemplate_template_parsing_error tests the template parsing error case
func Test_renderEmailTemplate_template_parsing_error(t *testing.T) {
	// Don't run in parallel to avoid template conflicts
	// t.Parallel()

	// Save original template
	originalTemplate := emailTemplate
	defer func() {
		emailTemplate = originalTemplate
	}()

	// Set invalid template to trigger parsing error
	emailTemplate = `{{.InvalidTemplate`

	data := EmailTemplateData{
		AppName:          "Test App",
		Message:          "Test message",
		InviteLink:       "http://example.com/invite",
		OrganizationName: "Test Org",
	}

	result, err := renderEmailTemplate(data)

	assert.Error(t, err)
	assert.Equal(t, ErrTemplateParsing, err)
	assert.Empty(t, result)
}

// Test_renderEmailTemplate_template_execution_error tests the template execution error case
func Test_renderEmailTemplate_template_execution_error(t *testing.T) {
	// Don't run in parallel to avoid template conflicts
	// t.Parallel()

	// Save original template
	originalTemplate := emailTemplate
	defer func() {
		emailTemplate = originalTemplate
	}()

	// Set template with invalid field reference to trigger execution error
	emailTemplate = `<!DOCTYPE html>
<html>
<body>
  <h1>{{.NonExistentField}}</h1>
</body>
</html>`

	data := EmailTemplateData{
		AppName:          "Test App",
		Message:          "Test message",
		InviteLink:       "http://example.com/invite",
		OrganizationName: "Test Org",
	}

	result, err := renderEmailTemplate(data)

	assert.Error(t, err)
	assert.Equal(t, ErrTemplateExecution, err)
	assert.Empty(t, result)
}

// Test_ExportedHandlers tests that exported handlers are not nil
func Test_ExportedHandlers(t *testing.T) {
	t.Parallel()

	// Test that all exported handlers are properly initialized
	assert.NotNil(t, CreateInviteHandler)
	assert.NotNil(t, ListUserInvitesForUserHandler)
	assert.NotNil(t, ListUserInvitesForOrganizationHandler)
	assert.NotNil(t, RejectInviteHandler)
	assert.NotNil(t, RevokeInviteHandler)
	assert.NotNil(t, ResendInviteHandler)
	assert.NotNil(t, ValidateInviteHandler)
	assert.NotNil(t, RedeemInviteHandler)
}

// Handler tests have been moved to separate files

// Test_validateCreateInviteRequest_edge_cases tests edge cases for validateCreateInviteRequest
func Test_validateCreateInviteRequest_edge_cases(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		req         *CreateInviteRequest
		expectedErr error
	}{
		{
			name: "empty_email",
			req: &CreateInviteRequest{
				Email:            "",
				InviterID:        uuid.New(),
				OrganizationRole: uuid.New(),
			},
			expectedErr: ErrInvalidEmail,
		},
		{
			name: "email_missing_at_symbol",
			req: &CreateInviteRequest{
				Email:            "testexample.com",
				InviterID:        uuid.New(),
				OrganizationRole: uuid.New(),
			},
			expectedErr: ErrInvalidEmail,
		},
		{
			name: "email_missing_domain",
			req: &CreateInviteRequest{
				Email:            "test@",
				InviterID:        uuid.New(),
				OrganizationRole: uuid.New(),
			},
			expectedErr: ErrInvalidEmail,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := validateCreateInviteRequest(tt.req)

			assert.Error(t, err)
			assert.Equal(t, tt.expectedErr, err)
		})
	}
}

// Test_createInvite_uuid_parsing tests UUID parsing in createInvite
func Test_createInvite_uuid_parsing(t *testing.T) {
	t.Parallel()

	orgID := uuid.New()
	tokenHash := "test-hash"
	req := CreateInviteRequest{
		Email:            "<EMAIL>",
		InviterID:        uuid.New(),
		OrganizationRole: uuid.New(),
	}

	// Test invalid UUID string
	mockDB := &dbexecutor.FakeDBExecutor{
		QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
			return map[string]interface{}{
				"id": "invalid-uuid-string",
			}, nil
		},
	}

	_, err := createInvite(mockDB, orgID, tokenHash, req)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid UUID")
}

// Test_getInvitesForUser_email_parsing tests email parsing in getInvitesForUser
func Test_getInvitesForUser_email_parsing(t *testing.T) {
	t.Parallel()

	userID := uuid.New()

	tests := []struct {
		name        string
		mockDB      *dbexecutor.FakeDBExecutor
		expectedErr error
	}{
		{
			name: "missing_email_field",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					// Check if this is the email query
					switch dest.(type) {
					case *[]EmailResult:
						// Return empty slice but no error - this simulates no emails found
						emails := dest.(*[]EmailResult)
						*emails = []EmailResult{}
						return nil
					default:
						return errors.New("unexpected call")
					}
				},
			},
			expectedErr: ErrNoUserEmail, // Changed from ErrInvalidRowData since empty results now return ErrNoUserEmail
		},
		{
			name: "no_rows_found",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					// Check if this is the email query
					switch dest.(type) {
					case *[]EmailResult:
						return sql.ErrNoRows
					default:
						return errors.New("unexpected call")
					}
				},
			},
			expectedErr: ErrNoUserEmail,
		},
		{
			name: "invites_query_error",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					// Check if this is the email query or invite query
					switch dest.(type) {
					case *[]EmailResult:
						// Email query - populate with test email
						emails := dest.(*[]EmailResult)
						*emails = []EmailResult{{Email: "<EMAIL>"}}
						return nil
					case *[]UserInvite:
						// Invite query - return error
						return errors.New("query error")
					default:
						return errors.New("unexpected type")
					}
				},
			},
			expectedErr: ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			_, err := getInvitesForUser(tt.mockDB, userID)

			assert.Error(t, err)
			assert.Equal(t, tt.expectedErr, err)
		})
	}
}

// Test_updateInviteToken_edge_cases tests edge cases for updateInviteToken
func Test_updateInviteToken_edge_cases(t *testing.T) {
	t.Parallel()

	inviteID := uuid.New()
	tokenHash := "test-hash"
	retryCount := 1

	tests := []struct {
		name        string
		req         *ResendInviteRequest
		mockDB      *dbexecutor.FakeDBExecutor
		expectedErr error
	}{
		{
			name: "request_with_expired_days",
			req:  &ResendInviteRequest{Actor: "test-actor", ExpiredDays: func() *int { i := 30; return &i }()},
			mockDB: &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					return &mockResult{rowsAffected: 1}, nil
				},
			},
			expectedErr: nil,
		},
		{
			name: "rows_affected_error",
			req:  &ResendInviteRequest{Actor: "test-actor"},
			mockDB: &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					return &mockResultWithError{}, nil
				},
			},
			expectedErr: ErrDatabaseOperation,
		},
		{
			name: "request_with_message",
			req: &ResendInviteRequest{
				Actor:   "test-actor",
				Message: func() *string { msg := "Custom resend message"; return &msg }(),
			},
			mockDB: &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					return &mockResult{rowsAffected: 1}, nil
				},
			},
			expectedErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := updateInviteToken(tt.mockDB, inviteID, tokenHash, retryCount, tt.req)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// mockResultWithError implements sql.Result but returns error on RowsAffected
type mockResultWithError struct{}

func (m *mockResultWithError) LastInsertId() (int64, error) {
	return 0, nil
}

func (m *mockResultWithError) RowsAffected() (int64, error) {
	return 0, errors.New("rows affected error")
}

// Test_publishEmailNotification_edge_cases tests edge cases for publishEmailNotification
func Test_publishEmailNotification_edge_cases(t *testing.T) {
	t.Parallel()

	ctx := context.Background()

	tests := []struct {
		name         string
		message      pubsubdata.NotificationRequest
		pubsubClient connect.PsClient
		expectedErr  bool
	}{
		{
			name: "empty_message",
			message: pubsubdata.NotificationRequest{
				Type: "",
				Payload: map[string]interface{}{
					"to":   "",
					"body": "",
				},
				Metadata: map[string]interface{}{
					"source": "onramp",
				},
			},
			pubsubClient: pubsub.NewFakePubsubClient(),
			expectedErr:  false,
		},
		{
			name: "large_message",
			message: pubsubdata.NotificationRequest{
				Type: "email",
				Payload: map[string]interface{}{
					"to":   "<EMAIL>",
					"body": strings.Repeat("a", 10000), // Large message
				},
				Metadata: map[string]interface{}{
					"source": "onramp",
				},
			},
			pubsubClient: pubsub.NewFakePubsubClient(),
			expectedErr:  false,
		},
		{
			name: "special_characters_in_message",
			message: pubsubdata.NotificationRequest{
				Type: "email",
				Payload: map[string]interface{}{
					"to":   "<EMAIL>",
					"body": "test with special chars: !@#$%^&*()",
				},
				Metadata: map[string]interface{}{
					"source": "onramp",
				},
			},
			pubsubClient: pubsub.NewFakePubsubClient(),
			expectedErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := publishEmailNotification(ctx, tt.pubsubClient, pubSubTopic, tt.message)

			if tt.expectedErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test_logInviteEvent_edge_cases tests edge cases for logInviteEvent
func Test_logInviteEvent_edge_cases(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		eventType   string
		invite      *UserInvite
		actor       string
		batcher     bqbatch.Batcher
		expectedErr error
	}{
		{
			name:      "invite_with_nil_message",
			eventType: EventTypeCreate,
			invite: &UserInvite{
				ID:      uuid.New(),
				Email:   "<EMAIL>",
				Message: nil,
			},
			actor:       "test-actor",
			batcher:     bqbatcher.FakeBatcherWithOptions(),
			expectedErr: nil,
		},
		{
			name:      "empty_event_type",
			eventType: "",
			invite: &UserInvite{
				ID:    uuid.New(),
				Email: "<EMAIL>",
			},
			actor:       "test-actor",
			batcher:     bqbatcher.FakeBatcherWithOptions(),
			expectedErr: nil,
		},
		{
			name:      "empty_actor",
			eventType: EventTypeCreate,
			invite: &UserInvite{
				ID:    uuid.New(),
				Email: "<EMAIL>",
			},
			actor:       "",
			batcher:     bqbatcher.FakeBatcherWithOptions(),
			expectedErr: nil,
		},
		{
			name:      "invite_with_message",
			eventType: EventTypeCreate,
			invite: &UserInvite{
				ID:      uuid.New(),
				Email:   "<EMAIL>",
				Message: func() *string { s := "test message"; return &s }(),
			},
			actor:       "test-actor",
			batcher:     bqbatcher.FakeBatcherWithOptions(),
			expectedErr: nil,
		},
		{
			name:      "invite_with_non_nil_time_fields",
			eventType: EventTypeCreate,
			invite: &UserInvite{
				ID:                     uuid.New(),
				Email:                  "<EMAIL>",
				OrganizationIdentifier: uuid.New(),
				TokenHash:              "test-hash",
				InviterID:              uuid.New(),
				CustomRoleID:           uuid.New(),
				Status:                 StatusPending,
				Created:                time.Now().UTC(),
				Updated:                time.Now().UTC(),
				Retried:                func() *time.Time { t := time.Now().UTC().Add(-1 * time.Hour); return &t }(),
				Expired:                func() *time.Time { t := time.Now().UTC().Add(24 * time.Hour); return &t }(),
				Sent:                   func() *time.Time { t := time.Now().UTC().Add(-30 * time.Minute); return &t }(),
			},
			actor:       "test-actor",
			batcher:     bqbatcher.FakeBatcherWithOptions(),
			expectedErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := logInviteEvent(tt.batcher, tt.eventType, tt.invite, tt.actor)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test_checkCooldownPeriod_edge_cases tests edge cases for checkCooldownPeriod
func Test_checkCooldownPeriod_edge_cases(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		invite      *UserInvite
		expectedErr error
	}{
		{
			name: "exactly_at_cooldown_boundary",
			invite: &UserInvite{
				Retried: func() *time.Time { t := time.Now().UTC().Add(-1 * time.Minute); return &t }(),
			},
			expectedErr: nil, // Should be exactly at the boundary
		},
		{
			name: "just_under_cooldown",
			invite: &UserInvite{
				Retried: func() *time.Time { t := time.Now().UTC().Add(-59 * time.Second); return &t }(),
			},
			expectedErr: ErrResendCooldown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := checkCooldownPeriod(tt.invite)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test_buildInviteLink_edge_cases tests edge cases for buildInviteLink
func Test_buildInviteLink_edge_cases(t *testing.T) {
	t.Parallel()

	orgID := "123e4567-e89b-12d3-a456-************"

	tests := []struct {
		name     string
		orgID    string
		token    string
		expected string
	}{
		{
			name:     "empty_token",
			orgID:    orgID,
			token:    "",
			expected: "/api/organizations/123e4567-e89b-12d3-a456-************/invites/validate?token=",
		},
		{
			name:     "very_long_token",
			orgID:    orgID,
			token:    strings.Repeat("a", 100),
			expected: "/api/organizations/123e4567-e89b-12d3-a456-************/invites/validate?token=" + strings.Repeat("a", 100),
		},
		{
			name:     "token_with_special_chars",
			orgID:    orgID,
			token:    "abc123",
			expected: "/api/organizations/123e4567-e89b-12d3-a456-************/invites/validate?token=abc123",
		},
		{
			name:     "empty_org_id",
			orgID:    "",
			token:    "abc123",
			expected: "/api/organizations//invites/validate?token=abc123",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result := buildInviteLink(tt.orgID, tt.token)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test_validateTokenFormat_edge_cases tests edge cases for validateTokenFormat
func Test_validateTokenFormat_edge_cases(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		token    string
		expected bool
	}{
		{
			name:     "token_too_short",
			token:    "abc123",
			expected: false,
		},
		{
			name:     "token_too_long",
			token:    strings.Repeat("a", 100),
			expected: false,
		},
		{
			name:     "token_with_uppercase",
			token:    "ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890",
			expected: true,
		},
		{
			name:     "token_with_special_chars",
			token:    "abcdef1234567890abcdef1234567890abcdef1234567890abcdef123456789!",
			expected: false,
		},
		{
			name:     "valid_token",
			token:    "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result := validateTokenFormat(tt.token)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test_toInviteResponse_edge_cases tests edge cases for toInviteResponse
func Test_toInviteResponse_edge_cases(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name   string
		invite *UserInvite
	}{
		{
			name: "invite_with_nil_message",
			invite: &UserInvite{
				ID:                     uuid.New(),
				OrganizationIdentifier: uuid.New(),
				Email:                  "<EMAIL>",
				InviterID:              uuid.New(),
				CustomRoleID:           uuid.New(),
				Status:                 StatusPending,
				Message:                nil,
				Created:                time.Now().UTC(),
				Updated:                time.Now().UTC(),
			},
		},
		{
			name: "invite_with_empty_message",
			invite: &UserInvite{
				ID:                     uuid.New(),
				OrganizationIdentifier: uuid.New(),
				Email:                  "<EMAIL>",
				InviterID:              uuid.New(),
				CustomRoleID:           uuid.New(),
				Status:                 StatusPending,
				Message:                func() *string { s := ""; return &s }(),
				Created:                time.Now().UTC(),
				Updated:                time.Now().UTC(),
			},
		},
		{
			name: "invite_with_nil_expired",
			invite: &UserInvite{
				ID:                     uuid.New(),
				OrganizationIdentifier: uuid.New(),
				Email:                  "<EMAIL>",
				InviterID:              uuid.New(),
				CustomRoleID:           uuid.New(),
				Status:                 StatusPending,
				Expired:                nil,
				Created:                time.Now().UTC(),
				Updated:                time.Now().UTC(),
			},
		},
		{
			name: "invite_with_nil_retried",
			invite: &UserInvite{
				ID:                     uuid.New(),
				OrganizationIdentifier: uuid.New(),
				Email:                  "<EMAIL>",
				InviterID:              uuid.New(),
				CustomRoleID:           uuid.New(),
				Status:                 StatusPending,
				Retried:                nil,
				Created:                time.Now().UTC(),
				Updated:                time.Now().UTC(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result := toInviteResponse(tt.invite)

			assert.NotNil(t, result)
			assert.Equal(t, tt.invite.ID, result.ID)
			assert.Equal(t, tt.invite.Email, result.Email)
			assert.Equal(t, tt.invite.Status, result.Status)
		})
	}
}

// Test_checkCooldownPeriod_boundary_cases tests boundary cases for checkCooldownPeriod
func Test_checkCooldownPeriod_boundary_cases(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		invite      *UserInvite
		expectedErr error
	}{
		{
			name: "nil_retried_field",
			invite: &UserInvite{
				Retried: nil,
			},
			expectedErr: nil,
		},
		{
			name: "exactly_one_minute_ago",
			invite: &UserInvite{
				Retried: func() *time.Time { t := time.Now().UTC().Add(-1 * time.Minute); return &t }(),
			},
			expectedErr: nil,
		},
		{
			name: "just_under_one_minute",
			invite: &UserInvite{
				Retried: func() *time.Time { t := time.Now().UTC().Add(-59 * time.Second); return &t }(),
			},
			expectedErr: ErrResendCooldown,
		},
		{
			name: "way_in_the_past",
			invite: &UserInvite{
				Retried: func() *time.Time { t := time.Now().UTC().Add(-24 * time.Hour); return &t }(),
			},
			expectedErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := checkCooldownPeriod(tt.invite)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test_validateInviteToken_expired tests expired invite token validation
func Test_validateInviteToken_expired(t *testing.T) {
	t.Parallel()

	token := "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
	expiredTime := time.Now().UTC().Add(-1 * time.Hour)

	mockDB := &dbexecutor.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			invite := dest.(*UserInvite)
			invite.ID = uuid.New()
			invite.Email = "<EMAIL>"
			invite.Status = StatusPending
			invite.Expired = &expiredTime
			return nil
		},
	}

	_, err := validateInviteToken(mockDB, token)

	assert.Error(t, err)
	assert.Equal(t, ErrInviteExpired, err)
}

// Test_getInvitesForOrganization_error tests error scenarios for getInvitesForOrganization
func Test_getInvitesForOrganization_error(t *testing.T) {
	t.Parallel()

	orgID := uuid.New()

	mockDB := &dbexecutor.FakeDBExecutor{
		QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
			return errors.New("database error")
		},
	}

	_, err := getInvitesForOrganization(mockDB, orgID)

	assert.Error(t, err)
	assert.Equal(t, ErrDatabaseOperation, err)
}

// Test_getOrganizationName_error tests error scenarios for getOrganizationName
func Test_getOrganizationName_error(t *testing.T) {
	t.Parallel()

	orgID := uuid.New()

	tests := []struct {
		name        string
		mockDB      *dbexecutor.FakeDBExecutor
		expectedErr error
	}{
		{
			name: "database_error",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return nil, errors.New("database error")
				},
			},
			expectedErr: ErrDatabaseOperation,
		},
		{
			name: "no_rows_found",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return nil, sql.ErrNoRows
				},
			},
			expectedErr: ErrOrganizationNotFound,
		},
		{
			name: "missing_name_field",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowFunc: func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"id": orgID.String(),
					}, nil
				},
			},
			expectedErr: ErrInvalidRowData,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			_, err := getOrganizationName(tt.mockDB, orgID)

			assert.Error(t, err)
			assert.ErrorIs(t, err, tt.expectedErr)
		})
	}
}

// Test_validateInviteToken_comprehensive tests comprehensive scenarios for validateInviteToken
func Test_validateInviteToken_comprehensive(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		token       string
		mockDB      *dbexecutor.FakeDBExecutor
		expectedErr error
	}{
		{
			name:  "token_not_found",
			token: "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				},
			},
			expectedErr: ErrInviteNotFound,
		},
		{
			name:  "database_error",
			token: "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database error")
				},
			},
			expectedErr: ErrInviteNotFound,
		},
		{
			name:        "invalid_token_format",
			token:       "invalid-token",
			mockDB:      &dbexecutor.FakeDBExecutor{},
			expectedErr: ErrInvalidInviteToken,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			_, err := validateInviteToken(tt.mockDB, tt.token)

			assert.Error(t, err)
			assert.Equal(t, tt.expectedErr, err)
		})
	}
}

// Test_updateInviteStatus_comprehensive tests comprehensive scenarios for updateInviteStatus
func Test_updateInviteStatus_comprehensive(t *testing.T) {
	t.Parallel()

	inviteID := uuid.New()
	status := StatusRedeemed
	now := time.Now().UTC()

	tests := []struct {
		name        string
		mockDB      *dbexecutor.FakeDBExecutor
		expectedErr error
	}{
		{
			name: "zero_rows_affected",
			mockDB: &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					return &mockResult{rowsAffected: 0}, nil
				},
			},
			expectedErr: ErrInviteNotFound,
		},
		{
			name: "rows_affected_error",
			mockDB: &dbexecutor.FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					return &mockResultWithError{}, nil
				},
			},
			expectedErr: ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := updateInviteStatus(tt.mockDB, inviteID, status, &now)

			assert.Error(t, err)
			assert.Equal(t, tt.expectedErr, err)
		})
	}
}

// Test_getInvitesForUser_comprehensive tests comprehensive scenarios for getInvitesForUser
func Test_getInvitesForUser_comprehensive(t *testing.T) {
	t.Parallel()

	userID := uuid.New()

	tests := []struct {
		name        string
		mockDB      *dbexecutor.FakeDBExecutor
		expectedErr error
	}{
		{
			name: "user_query_error",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					// Check if this is the email query
					switch dest.(type) {
					case *[]EmailResult:
						return errors.New("user query error")
					default:
						return errors.New("unexpected call")
					}
				},
			},
			expectedErr: ErrDatabaseOperation,
		},
		{
			name: "empty_email_string",
			mockDB: &dbexecutor.FakeDBExecutor{
				QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
					// Check if this is the email query or invite query
					switch dest.(type) {
					case *[]EmailResult:
						// Email query - populate with empty email (which should still work)
						emails := dest.(*[]EmailResult)
						*emails = []EmailResult{{Email: ""}}
						return nil
					case *[]UserInvite:
						// Invite query - return empty results
						invites := dest.(*[]UserInvite)
						*invites = []UserInvite{}
						return nil
					default:
						return errors.New("unexpected type")
					}
				},
			},
			expectedErr: nil, // Empty email is allowed, just returns empty results
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			_, err := getInvitesForUser(tt.mockDB, userID)

			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test_logInviteEvent_success tests successful logInviteEvent
func Test_logInviteEvent_success(t *testing.T) {
	t.Parallel()

	batcher := bqbatcher.FakeBatcherWithOptions()
	eventType := "created"
	invite := &UserInvite{
		ID:                     uuid.New(),
		OrganizationIdentifier: uuid.New(),
		Email:                  "<EMAIL>",
		InviterID:              uuid.New(),
		CustomRoleID:           uuid.New(),
		Status:                 StatusPending,
		Created:                time.Now().UTC(),
		Updated:                time.Now().UTC(),
	}
	actor := "test-actor"

	err := logInviteEvent(batcher, eventType, invite, actor)

	assert.NoError(t, err)
}

// Test_publishEmailNotification_success tests successful publishEmailNotification
func Test_publishEmailNotification_success(t *testing.T) {
	t.Parallel()
	// Save original template and restore it after test
	originalTemplate := emailTemplate
	defer func() {
		emailTemplate = originalTemplate
	}()

	ctx := context.Background()
	pubsubClient := pubsub.NewFakePubsubClient()
	topicName := "test-topic"
	message := pubsubdata.NotificationRequest{
		Type: "email",
		Payload: map[string]interface{}{
			"to":   "<EMAIL>",
			"body": "Test email content",
		},
		Metadata: map[string]interface{}{
			"source": "onramp",
		},
	}

	err := publishEmailNotification(ctx, pubsubClient, topicName, message)

	assert.NoError(t, err)
}

// Test_renderEmailTemplate_success tests successful renderEmailTemplate
func Test_renderEmailTemplate_success(t *testing.T) {
	t.Parallel()
	// Save original template and restore it after test
	originalTemplate := emailTemplate
	defer func() {
		emailTemplate = originalTemplate
	}()

	data := EmailTemplateData{
		InviteLink:       "https://example.com/invite?token=abc123",
		OrganizationName: "Test Organization",
		Message:          "Welcome to our organization!",
		AppName:          "TestApp",
	}

	result, err := renderEmailTemplate(data)

	assert.NoError(t, err)
	assert.NotEmpty(t, result)
	assert.Contains(t, result, "Test Organization")
	assert.Contains(t, result, "Welcome to our organization!")
	assert.Contains(t, result, "TestApp")
	assert.Contains(t, result, "https://example.com/invite?token=abc123")
}

// Test_parseRevokeInviteRequest_success tests successful parseRevokeInviteRequest
func Test_parseRevokeInviteRequest_success(t *testing.T) {
	t.Parallel()

	body := `{"actor": "test-actor"}`
	req := httptest.NewRequest("PUT", "/test", strings.NewReader(body))
	req.Header.Set("Content-Type", "application/json")

	result, err := parseRevokeInviteRequest(req)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "test-actor", result.Actor)
}

// Test_parseResendInviteRequest_success tests successful parseResendInviteRequest
func Test_parseResendInviteRequest_success(t *testing.T) {
	t.Parallel()

	body := `{"actor": "test-actor", "expireddays": 30}`
	req := httptest.NewRequest("PUT", "/test", strings.NewReader(body))
	req.Header.Set("Content-Type", "application/json")

	result, err := parseResendInviteRequest(req)

	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "test-actor", result.Actor)
	assert.Equal(t, 30, *result.ExpiredDays)
}

// Test_validateCreateInviteRequest_success tests successful validateCreateInviteRequest
func Test_validateCreateInviteRequest_success(t *testing.T) {
	t.Parallel()

	req := &CreateInviteRequest{
		Email:            "<EMAIL>",
		InviterID:        uuid.New(),
		OrganizationRole: uuid.New(),
		Message:          func() *string { s := "Welcome!"; return &s }(),
	}

	err := validateCreateInviteRequest(req)

	assert.NoError(t, err)
}

// Test_validateTokenFormat_success tests successful validateTokenFormat
func Test_validateTokenFormat_success(t *testing.T) {
	t.Parallel()

	token := "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"

	result := validateTokenFormat(token)

	assert.True(t, result)
}

// Test_buildInviteLink_success tests successful buildInviteLink
func Test_buildInviteLink_success(t *testing.T) {
	t.Parallel()

	orgID := "123e4567-e89b-12d3-a456-************"
	token := "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"

	result := buildInviteLink(orgID, token)

	expected := "/api/organizations/123e4567-e89b-12d3-a456-************/invites/validate?token=abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
	assert.Equal(t, expected, result)
}

// Test_toInviteResponse_success tests successful toInviteResponse
func Test_toInviteResponse_success(t *testing.T) {
	t.Parallel()

	invite := &UserInvite{
		ID:                     uuid.New(),
		OrganizationIdentifier: uuid.New(),
		Email:                  "<EMAIL>",
		InviterID:              uuid.New(),
		CustomRoleID:           uuid.New(),
		Status:                 StatusPending,
		Message:                func() *string { s := "Welcome!"; return &s }(),
		Created:                time.Now().UTC(),
		Updated:                time.Now().UTC(),
	}

	result := toInviteResponse(invite)

	assert.NotNil(t, result)
	assert.Equal(t, invite.ID, result.ID)
	assert.Equal(t, invite.Email, result.Email)
	assert.Equal(t, invite.Status, result.Status)
	assert.Equal(t, invite.Message, result.Message)
}

// Test_checkCooldownPeriod_success tests successful checkCooldownPeriod
func Test_checkCooldownPeriod_success(t *testing.T) {
	t.Parallel()

	invite := &UserInvite{
		Retried: func() *time.Time { t := time.Now().UTC().Add(-2 * time.Minute); return &t }(),
	}

	err := checkCooldownPeriod(invite)

	assert.NoError(t, err)
}

// Test_publishEmailNotification_marshal_error tests the marshal error case in publishEmailNotification
func Test_publishEmailNotification_marshal_error(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	message := pubsubdata.NotificationRequest{
		Type: "email",
		Payload: map[string]interface{}{
			"to":   "<EMAIL>",
			"body": "test message",
		},
		Metadata: map[string]interface{}{
			"source": "onramp",
		},
	}

	// Save original function
	originalJsonMarshal := jsonMarshal

	// Override jsonMarshal to return an error
	jsonMarshal = func(v interface{}) ([]byte, error) {
		return nil, errors.New("marshal error")
	}

	// Restore original function after test
	defer func() {
		jsonMarshal = originalJsonMarshal
	}()

	pubsubClient := pubsub.NewFakePubsubClient()

	err := publishEmailNotification(ctx, pubsubClient, pubSubTopic, message)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to marshal email message")
	assert.Contains(t, err.Error(), "marshal error")
}

// Test_renderEmailSubject tests the renderEmailSubject function from email.go
func Test_renderEmailSubject(t *testing.T) {
	// Don't run in parallel to avoid template conflicts
	// t.Parallel()

	// Save original template and restore it after test
	originalTemplate := emailSubjectTemplate
	defer func() {
		emailSubjectTemplate = originalTemplate
	}()

	data := EmailTemplateData{
		AppName:          "Test App",
		Message:          "Test message",
		InviteLink:       "http://example.com/invite",
		OrganizationName: "Test Org",
	}

	result, err := renderEmailSubject(data)

	assert.NoError(t, err)
	assert.NotEmpty(t, result)
	assert.Equal(t, "You're Invited to Test App!", result)
}

// Test_renderEmailSubject_template_parsing_error tests the template parsing error case
func Test_renderEmailSubject_template_parsing_error(t *testing.T) {
	// Don't run in parallel to avoid template conflicts
	// t.Parallel()

	// Save original template
	originalTemplate := emailSubjectTemplate
	defer func() {
		emailSubjectTemplate = originalTemplate
	}()

	// Set invalid template to trigger parsing error
	emailSubjectTemplate = `{{.InvalidTemplate`

	data := EmailTemplateData{
		AppName:          "Test App",
		Message:          "Test message",
		InviteLink:       "http://example.com/invite",
		OrganizationName: "Test Org",
	}

	result, err := renderEmailSubject(data)

	assert.Error(t, err)
	assert.Equal(t, ErrTemplateParsing, err)
	assert.Empty(t, result)
}

// Test_renderEmailSubject_template_execution_error tests the template execution error case
func Test_renderEmailSubject_template_execution_error(t *testing.T) {
	// Don't run in parallel to avoid template conflicts
	// t.Parallel()

	// Save original template
	originalTemplate := emailSubjectTemplate
	defer func() {
		emailSubjectTemplate = originalTemplate
	}()

	// Set template with invalid field reference to trigger execution error
	emailSubjectTemplate = `{{.NonExistentField}}`

	data := EmailTemplateData{
		AppName:          "Test App",
		Message:          "Test message",
		InviteLink:       "http://example.com/invite",
		OrganizationName: "Test Org",
	}

	result, err := renderEmailSubject(data)

	assert.Error(t, err)
	assert.Equal(t, ErrTemplateExecution, err)
	assert.Empty(t, result)
}

// Test_renderEmailSubject_success tests successful renderEmailSubject
func Test_renderEmailSubject_success(t *testing.T) {
	t.Parallel()
	// Save original template and restore it after test
	originalTemplate := emailSubjectTemplate
	defer func() {
		emailSubjectTemplate = originalTemplate
	}()

	data := EmailTemplateData{
		InviteLink:       "https://example.com/invite?token=abc123",
		OrganizationName: "Test Organization",
		Message:          "Welcome to our organization!",
		AppName:          "TestApp",
	}

	result, err := renderEmailSubject(data)

	assert.NoError(t, err)
	assert.NotEmpty(t, result)
	assert.Equal(t, "You're Invited to TestApp!", result)
}

// Test_renderEmailSubject_edge_cases tests edge cases for renderEmailSubject
func Test_renderEmailSubject_edge_cases(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		data     EmailTemplateData
		expected string
	}{
		{
			name: "empty_app_name",
			data: EmailTemplateData{
				AppName:          "",
				Message:          "Test message",
				InviteLink:       "http://example.com/invite",
				OrganizationName: "Test Org",
			},
			expected: "You're Invited to !",
		},
		{
			name: "app_name_with_spaces",
			data: EmailTemplateData{
				AppName:          "My Awesome App",
				Message:          "Test message",
				InviteLink:       "http://example.com/invite",
				OrganizationName: "Test Org",
			},
			expected: "You're Invited to My Awesome App!",
		},
		{
			name: "app_name_with_special_characters",
			data: EmailTemplateData{
				AppName:          "App & Co. (Beta)",
				Message:          "Test message",
				InviteLink:       "http://example.com/invite",
				OrganizationName: "Test Org",
			},
			expected: "You're Invited to App & Co. (Beta)!",
		},
		{
			name: "long_app_name",
			data: EmailTemplateData{
				AppName:          "This Is A Very Long Application Name That Should Still Work Properly",
				Message:          "Test message",
				InviteLink:       "http://example.com/invite",
				OrganizationName: "Test Org",
			},
			expected: "You're Invited to This Is A Very Long Application Name That Should Still Work Properly!",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result, err := renderEmailSubject(tt.data)

			assert.NoError(t, err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test_renderEmailSubject_custom_template tests renderEmailSubject with custom template
func Test_renderEmailSubject_custom_template(t *testing.T) {
	// Don't run in parallel to avoid template conflicts
	// t.Parallel()

	// Save original template
	originalTemplate := emailSubjectTemplate
	defer func() {
		emailSubjectTemplate = originalTemplate
	}()

	// Set custom template
	emailSubjectTemplate = `Welcome to {{.AppName}} - {{.OrganizationName}}`

	data := EmailTemplateData{
		AppName:          "TestApp",
		Message:          "Test message",
		InviteLink:       "http://example.com/invite",
		OrganizationName: "Test Organization",
	}

	result, err := renderEmailSubject(data)

	assert.NoError(t, err)
	assert.Equal(t, "Welcome to TestApp - Test Organization", result)
}
