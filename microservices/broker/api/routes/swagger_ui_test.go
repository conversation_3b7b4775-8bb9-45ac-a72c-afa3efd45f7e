package routes

import (
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"testing"

	"github.com/gorilla/mux"
)

// helper to build router with only swagger routes
func newSwaggerRouterOnly() *mux.Router {
	r := mux.NewRouter()
	setupSwaggerUI(r)
	return r
}

func TestDocsRedirectAddsSecurityHeaders(t *testing.T) {
	r := newSwaggerRouterOnly()
	req := httptest.NewRequest(http.MethodGet, "/docs/broker", nil)
	w := httptest.NewRecorder()

	r.ServeHTTP(w, req)

	// Expect a 301 redirect to trailing slash
	if w.Code != http.StatusMovedPermanently {
		t.Fatalf("status = %d, want %d", w.Code, http.StatusMovedPermanently)
	}
	if loc := w.Header().Get("Location"); loc != "/docs/broker/" {
		t.Fatalf("Location = %q, want %q", loc, "/docs/broker/")
	}

	// Security headers should be present even on redirect
	assertSecurityHeaders(t, w.<PERSON><PERSON>())
}

func TestSwaggerUIHandlerServesUnderDocsPath(t *testing.T) {
	r := newSwaggerRouterOnly()
	req := httptest.NewRequest(http.MethodGet, "/docs/broker/", nil)
	w := httptest.NewRecorder()

	r.ServeHTTP(w, req)

	// Handler comes from swaggo; verify not 404 and has security headers
	if w.Code == http.StatusNotFound {
		t.Fatalf("unexpected 404 for swagger UI")
	}
	assertSecurityHeaders(t, w.Header())
}

func TestOpenAPISpecServedWithSecurityHeaders(t *testing.T) {
	r := newSwaggerRouterOnly()
	ensureWorkingDirHasOpenAPISpec(t)
	req := httptest.NewRequest(http.MethodGet, "/openapi/broker.yaml", nil)
	w := httptest.NewRecorder()

	r.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Fatalf("status = %d, want %d", w.Code, http.StatusOK)
	}
	// Minimal sanity check that this looks like YAML
	ct := w.Header().Get("Content-Type")
	if ct == "" {
		// net/http may not always set a type for ServeFile; tolerate empty
	}
	assertSecurityHeaders(t, w.Header())
	if len(w.Body.Bytes()) == 0 {
		t.Fatalf("expected non-empty spec body")
	}
}

func assertSecurityHeaders(t *testing.T, h http.Header) {
	t.Helper()
	checks := map[string]string{
		"X-Content-Type-Options": "nosniff",
		"X-Frame-Options":        "DENY",
		"Referrer-Policy":        "no-referrer",
		"X-Robots-Tag":           "noindex",
	}
	for k, v := range checks {
		if got := h.Get(k); got != v {
			t.Fatalf("header %s = %q, want %q", k, got, v)
		}
	}
	if csp := h.Get("Content-Security-Policy"); csp == "" {
		t.Fatalf("expected Content-Security-Policy header to be set")
	}
}

// ensureWorkingDirHasOpenAPISpec attempts to change the working directory so that
// the relative path ./openapi/broker.yaml resolves (as used by http.ServeFile).
func ensureWorkingDirHasOpenAPISpec(t *testing.T) {
	t.Helper()
	const specRel = "openapi/broker.yaml"
	// Check current directory and a few parents for the spec file
	wd, _ := os.Getwd()
	orig := wd
	for i := 0; i < 5; i++ {
		candidate := filepath.Join(wd, specRel)
		if _, err := os.Stat(candidate); err == nil {
			// If we had to walk up, chdir to wd so relative path works
			if wd != orig {
				if err := os.Chdir(wd); err != nil {
					t.Fatalf("failed to chdir to %s: %v", wd, err)
				}
				t.Cleanup(func() { _ = os.Chdir(orig) })
			}
			return
		}
		parent := filepath.Dir(wd)
		if parent == wd {
			break
		}
		wd = parent
	}
	// Fallback: move three levels up from routes package to repo root
	repoRoot := filepath.Clean(filepath.Join("..", "..", ".."))
	if err := os.Chdir(repoRoot); err != nil {
		t.Fatalf("failed to chdir to repo root: %v", err)
	}
	if _, err := os.Stat(specRel); err != nil {
		t.Fatalf("could not locate %s after chdir: %v", specRel, err)
	}
	t.Cleanup(func() { _ = os.Chdir(orig) })
}
