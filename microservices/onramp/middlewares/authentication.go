package middlewares

import (
	"context"
	"net/http"

	"synapse-its.com/onramp/domain"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/logger"
)

func AuthMiddleware(sessionStore domain.SessionStore) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// 1) Grab session cookie
			c, err := r.<PERSON>("session_id")
			if err != nil {
				logger.Debugf("error - AuthMiddleware: %v", err.Error())
				response.CreateUnauthorizedResponse(w)
				return
			}

			// 2) Get session data from the injected session store
			sessionData, ok := sessionStore.GetSession(c.Value)
			if !ok {
				logger.Warnf("error - AuthMiddleware: invalid session for cookie value: %s", c.Value)
				response.CreateUnauthorizedResponse(w)
				return
			}

			// Debug log session retrieval
			logger.Debugf("AuthMiddleware: Retrieved session for cookie %s: UserID=%s, HasOAuthToken=%t",
				c.Value, sessionData.UserID, sessionData.OAuthToken != nil)

			// 3) Validate that claims exist in session
			claims := sessionData.Claims
			if claims == nil {
				// No claims stored - session is corrupted or login failed to populate claims
				// Force user to re-login to get proper claims
				logger.Warnf("error - AuthMiddleware: no claims found in session %s, forcing re-login", c.Value)
				response.CreateUnauthorizedResponse(w)
				return
			}

			// 4) Next handler with claims in context
			next.ServeHTTP(w, r.WithContext(context.WithValue(r.Context(), domain.UserKey, claims)))
		})
	}
}
