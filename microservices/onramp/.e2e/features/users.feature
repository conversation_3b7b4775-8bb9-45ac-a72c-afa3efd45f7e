Feature: Users Management
  As a user,
  I want to manage roles and Users,
  So that I can effectively control access within an organization.
  
  Scenario: Access Users page from Organizations
    Given I am on the home page
    Then I should see a modal with the title "Login required"
    When I click the "OK" button with class "custom-login-modal-required"
    Then I should see the login page with the title "Login" in an h2 tag
    When I click the "Synapse SSO" button with id "btn-keycloak"
    And I complete the Keycloak login form with username "<EMAIL>" and password "puppies1234"
    Then I should be redirected back to the application
    When I am on the organizations page
    Then I see the page title "All Organizations"
    When I find the row with name "Test Organization 1" and click the Users button
    Then I am redirected to the "Users" page
    And I see the page title "Users" with id "title-users"
    And The Users In Organization table contains at least one record
    And I see the page title "Invitations" with id "title-invitations"
    When I click the button with id "btn-create-invite-user"
    Then I see a modal with class "modal-create-invite"
    And I see the modal title "Invite User"
    When I fill in the email field with "<EMAIL>"
    When I fill in the message field with "test add Message invite"
    And I click the button with id "btn-create-invite"
    Then I see the message "Invitation created successfully!"
    And the table with id "invitations-list" contains "<EMAIL>"
    And I click the "Edit" button with id "btn-edit-user" in that row
    Then I see a modal with class "modal-create-invite"
    And I see the modal title "Edit User"
    And I click the button with id "btn-create-invite"
    Then I see the message "User updated successfully!"
    And the table with id "users-list" contains "<EMAIL>"
    And I click the "Delete" button with id "btn-delete-user" in that row
    Then I see the delete confirmation modal
    When I confirm the deletion
    Then The user should be deleted
    And I click the "Resend" button with id "btn-resend-invite" in that row
    Then I see the message invitation "Invitation resent successfully!"
  