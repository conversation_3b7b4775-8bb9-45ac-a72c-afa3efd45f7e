package invites

import "errors"

var (
	ErrInvalidEmail                  = errors.New("invalid email")
	ErrInvalidUserID                 = errors.New("invalid user ID")
	ErrInvalidRequestBody            = errors.New("invalid request body")
	ErrInvalidResponse               = errors.New("invalid response")
	ErrDatabaseOperation             = errors.New("database operation failed")
	ErrInvalidInviteID               = errors.New("invalid invite ID")
	ErrOrganizationNotFound          = errors.New("organization not found")
	ErrBigQueryOperation             = errors.New("bigquery operation failed")
	ErrInvalidRowData                = errors.New("invalid row data")
	ErrTemplateParsing               = errors.New("template parsing failed")
	ErrTemplateExecution             = errors.New("template execution failed")
	ErrNoUserEmail                   = errors.New("no user email found")
	ErrNoInvitesFoundForOrganization = errors.New("no invites found for organization")
	ErrInviteNotFound                = errors.New("invite not found")
	ErrResendCooldown                = errors.New("resend cooldown period not passed")
	ErrInviteExpired                 = errors.New("invite expired")
	ErrInvalidInviteToken            = errors.New("invalid invite token")
	ErrEmailNotInAuthMethod          = errors.New("email not found in auth method for user")
	ErrInvalidCustomRole             = errors.New("custom role not valid for organization")
	ErrUserAlreadyMember             = errors.New("user is already a member of the organization")
	ErrSSORequiredButNotOIDC         = errors.New("SSO is required but auth method is not OIDC")
)
