<nz-modal class="modal-create-organization" [(nzVisible)]="isVisible" [nzTitle]="modalTitle"
  (nzOnCancel)="handleCancel()" [nzOkText]="'Save'" [nzFooter]="modalFooter" nzWidth="490px">
  <ng-template #modalTitle>
    <div class="content-header">
      <h2 style="margin-bottom: 10px;">{{isDetail ? 'Organization Detail' : isEditMode ? 'Edit Organization'
        : 'Create Organization' }}</h2>
      <span class="description-header">{{isDetail ? '' : isEditMode ? 'You are editing the information for this
        Organization.'
        : 'This will initiate a setup process that creates
        standard roles and groups.' }}</span>
    </div>
  </ng-template>
  <ng-container *nzModalContent>
    <nz-form *ngIf="!isDetail" [formGroup]="form" class="modal-add-organization">
      <nz-form-item>
        <nz-form-control nzErrorTip="Please Input Organization Name.">
          <label>Organization Name</label>
          <nz-input-group nzErrorTip="Please Input Name.">
            <input nz-input id="new-organization-name" class="h-4 br-8" formControlName="name"
              placeholder="Enter Name" />
          </nz-input-group>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-control nzErrorTip="Please Input Description (e.g., Location).">
          <label>Description (e.g., Location)</label>
          <nz-input-group nzErrorTip="Please Input Description.">
            <textarea id="new-organization-description" [nzAutosize]="{ minRows: 3, maxRows: 5 }" nz-input
              class="h-4 br-8" formControlName="description" placeholder="Enter Description"></textarea>
          </nz-input-group>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item *ngIf="!isEditMode">
        <nz-form-control>
          <label>Organization Type</label>
          <nz-form-control nzErrorTip="Please Select Organization Type.">
            <nz-select id="new-organization-type" class="select-template" formControlName="orgtypeidentifier"
              nzPlaceHolder="Select Organization Type">
              <nz-option nzValue="municipality" nzLabel="Municipality"></nz-option>
              <nz-option nzValue="oem" nzLabel="OEM"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-control>
      </nz-form-item>
    </nz-form>
    <nz-form *ngIf="isDetail" [formGroup]="form">
      <nz-form-item>
        <nz-form-label [nzSpan]="5">Name</nz-form-label>
        <nz-form-control [nzSpan]="18">
          <input nz-input formControlName="name" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="5">Description</nz-form-label>
        <nz-form-control [nzSpan]="18">
          <input nz-input formControlName="description" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="5">Organization Type</nz-form-label>
        <nz-form-control [nzSpan]="18">
          <input nz-input formControlName="orgtypeidentifier" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="5">Date Created</nz-form-label>
        <nz-form-control [nzSpan]="18">
          <input nz-input formControlName="createdat" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="5">Date Updated</nz-form-label>
        <nz-form-control [nzSpan]="18">
          <input nz-input formControlName="updatedat" />
        </nz-form-control>
      </nz-form-item>
    </nz-form>
  </ng-container>
  <ng-template #modalFooter>
    <div class="button-action">
      <button *ngIf="!isEditMode && !isDetail" class="btn-submit br-8 h-4" nz-button nzType="primary"
        (click)="handleSave()">
        Create Organization</button>
      <button *ngIf="isEditMode" class="btn-submit br-8 h-4" nz-button nzType="primary" (click)="handleSave()">
        Update Organization</button>
      <button *ngIf="isEditMode" class="btn-delete br-8 h-4" style="margin-left: 0;" nzDanger nz-button nzType="primary"
        (click)="handleDelete()">
        Delete Organization</button>
    </div>
  </ng-template>
</nz-modal>