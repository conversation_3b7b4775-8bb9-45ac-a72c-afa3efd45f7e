package permissions

import (
	"bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/wrappers"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// CommandPermissionMapper maps device commands to required permissions
type CommandPermissionMapper struct {
	// Maps command types to permission requirements
	commandPermissions map[string][]string
}

// NewCommandPermissionMapper creates a new command permission mapper
func NewCommandPermissionMapper() *CommandPermissionMapper {
	mapper := &CommandPermissionMapper{
		commandPermissions: make(map[string][]string),
	}
	mapper.initializeCommandPermissions()
	return mapper
}

func (cpm *CommandPermissionMapper) initializeCommandPermissions() {
	// Group 0: All Access
	cpm.commandPermissions["start_realtime"] = []string{"org_ng_all_access", "device_group_ng_all_access"}
	cpm.commandPermissions["stop_realtime"] = []string{"org_ng_all_access", "device_group_ng_all_access"}
	cpm.commandPermissions["request_log_counts"] = []string{"org_ng_all_access", "device_group_ng_all_access"}
	cpm.commandPermissions["request_log"] = []string{"org_ng_all_access", "device_group_ng_all_access"}
	cpm.commandPermissions["request_auditlog_counts"] = []string{"org_ng_all_access", "device_group_ng_all_access"}
	cpm.commandPermissions["request_auditlog"] = []string{"org_ng_all_access", "device_group_ng_all_access"}
	cpm.commandPermissions["rd_monitor_data"] = []string{"org_ng_all_access", "device_group_ng_all_access"}
	cpm.commandPermissions["rd_network_config_unit"] = []string{"org_ng_all_access", "device_group_ng_all_access"}
	cpm.commandPermissions["rd_network_config_active"] = []string{"org_ng_all_access", "device_group_ng_all_access"}
	cpm.commandPermissions["rd_data_key"] = []string{"org_ng_all_access", "device_group_ng_all_access"}
	cpm.commandPermissions["rd_factory_settings"] = []string{"org_ng_all_access", "device_group_ng_all_access"}
	cpm.commandPermissions["rd_user_settings"] = []string{"org_ng_all_access", "device_group_ng_all_access"}
	cpm.commandPermissions["rd_port1_disables"] = []string{"org_ng_all_access", "device_group_ng_all_access"}
	cpm.commandPermissions["rd_date_time_dst"] = []string{"org_ng_all_access", "device_group_ng_all_access"}
	cpm.commandPermissions["remote_display_button_event"] = []string{"org_ng_all_access", "device_group_ng_all_access"}
	cpm.commandPermissions["test_chunk"] = []string{"org_ng_all_access", "device_group_ng_all_access"}

	// Group 1: Read Stats
	cpm.commandPermissions["rd_port1_stats"] = []string{"org_ng_read_stats", "device_group_ng_read_stats"}
	cpm.commandPermissions["rd_data_key_stats"] = []string{"org_ng_read_stats", "device_group_ng_read_stats"}
	cpm.commandPermissions["rd_main_iso_comms_stats"] = []string{"org_ng_read_stats", "device_group_ng_read_stats"}
	cpm.commandPermissions["rd_main_comms_stats"] = []string{"org_ng_read_stats", "device_group_ng_read_stats"}
	cpm.commandPermissions["rd_comms_main_stats"] = []string{"org_ng_read_stats", "device_group_ng_read_stats"}
	cpm.commandPermissions["rd_flash_stats"] = []string{"org_ng_read_stats", "device_group_ng_read_stats"}
	cpm.commandPermissions["rd_watchdog_stats"] = []string{"org_ng_read_stats", "device_group_ng_read_stats"}

	// Group 2: Set Time
	cpm.commandPermissions["wr_date_time_dst"] = []string{"org_ng_set_time", "device_group_ng_set_time"}

	// Group 3: Read Config
	cpm.commandPermissions["rd_channel_config"] = []string{"org_ng_read_config", "device_group_ng_read_config"}
	cpm.commandPermissions["rd_channel_current_sense"] = []string{"org_ng_read_config", "device_group_ng_read_config"}
	cpm.commandPermissions["rd_channel_permissives"] = []string{"org_ng_read_config", "device_group_ng_read_config"}
	cpm.commandPermissions["rd_fya_config"] = []string{"org_ng_read_config", "device_group_ng_read_config"}

	// Group 4: Clear Logs
	cpm.commandPermissions["log_clear"] = []string{"org_ng_clear_logs", "device_group_ng_clear_logs"}
	cpm.commandPermissions["auditlog_clear"] = []string{"org_ng_clear_logs", "device_group_ng_clear_logs"}
	cpm.commandPermissions["audit_log_reset"] = []string{"org_ng_clear_logs", "device_group_ng_clear_logs"}

	// Group 5: Reset Stats
	cpm.commandPermissions["clear_stats"] = []string{"org_ng_reset_stats", "device_group_ng_reset_stats"}

	// Group 6: Write DataKey/Port1
	cpm.commandPermissions["wr_data_key"] = []string{"org_ng_write_datakey", "device_group_ng_write_datakey"}
	cpm.commandPermissions["wr_port1_disables"] = []string{"org_ng_write_datakey", "device_group_ng_write_datakey"}
	cpm.commandPermissions["remote_reset"] = []string{"org_ng_write_datakey", "device_group_ng_write_datakey"}

	// Group 7: Write User Settings
	cpm.commandPermissions["wr_user_settings"] = []string{"org_ng_write_user_settings", "device_group_ng_write_user_settings"}

	// Group 8: DFU
	cpm.commandPermissions["manifest_versions"] = []string{"org_ng_dfu", "device_group_ng_dfu"}
	cpm.commandPermissions["reboot_comms_mcu"] = []string{"org_ng_dfu", "device_group_ng_dfu"}
	cpm.commandPermissions["initiate_dfu"] = []string{"org_ng_dfu", "device_group_ng_dfu"}
	cpm.commandPermissions["send_fw_manifest"] = []string{"org_ng_dfu", "device_group_ng_dfu"}
	cpm.commandPermissions["begin_firmware_download"] = []string{"org_ng_dfu", "device_group_ng_dfu"}
	cpm.commandPermissions["download_firmware_chunk"] = []string{"org_ng_dfu", "device_group_ng_dfu"}
}

// typeNameToCommandString converts Go type names to command strings
func (cpm *CommandPermissionMapper) typeNameToCommandString(typeName string) string {
	// Map from protobuf generated type names to command strings
	typeMap := map[string]string{
		"WrapperCommand_StartRealtime":            "start_realtime",
		"WrapperCommand_StopRealtime":             "stop_realtime",
		"WrapperCommand_RemoteDisplayButtonEvent": "remote_display_button_event",
		"WrapperCommand_RdPort1Stats":             "rd_port1_stats",
		"WrapperCommand_RdDataKeyStats":           "rd_data_key_stats",
		"WrapperCommand_RdMainIsoCommsStats":      "rd_main_iso_comms_stats",
		"WrapperCommand_RdMainCommsStats":         "rd_main_comms_stats",
		"WrapperCommand_RdCommsMainStats":         "rd_comms_main_stats",
		"WrapperCommand_RdFlashStats":             "rd_flash_stats",
		"WrapperCommand_RdWatchdogStats":          "rd_watchdog_stats",
		"WrapperCommand_RdDateTimeDst":            "rd_date_time_dst",
		"WrapperCommand_WrDateTimeDst":            "wr_date_time_dst",
		"WrapperCommand_RdMonitorData":            "rd_monitor_data",
		"WrapperCommand_RdNetworkConfigUnit":      "rd_network_config_unit",
		"WrapperCommand_RdNetworkConfigActive":    "rd_network_config_active",
		"WrapperCommand_RdChannelConfig":          "rd_channel_config",
		"WrapperCommand_RdChannelCurrentSense":    "rd_channel_current_sense",
		"WrapperCommand_RdChannelPermissives":     "rd_channel_permissives",
		"WrapperCommand_RdFyaConfig":              "rd_fya_config",
		"WrapperCommand_RdFactorySettings":        "rd_factory_settings",
		"WrapperCommand_RdUserSettings":           "rd_user_settings",
		"WrapperCommand_RdPort1Disables":          "rd_port1_disables",
		"WrapperCommand_LogClear":                 "log_clear",
		"WrapperCommand_AuditlogClear":            "auditlog_clear",
		"WrapperCommand_AuditLogReset":            "audit_log_reset",
		"WrapperCommand_ClearStats":               "clear_stats",
		"WrapperCommand_RdDataKey":                "rd_data_key",
		"WrapperCommand_WrDataKey":                "wr_data_key",
		"WrapperCommand_WrPort1Disables":          "wr_port1_disables",
		"WrapperCommand_WrUserSettings":           "wr_user_settings",
		"WrapperCommand_RemoteReset":              "remote_reset",
		"WrapperCommand_ManifestVersions":         "manifest_versions",
		"WrapperCommand_RebootCommsMcu":           "reboot_comms_mcu",
		"WrapperCommand_InitiateDfu":              "initiate_dfu",
		"WrapperCommand_SendFwManifest":           "send_fw_manifest",
		"WrapperCommand_BeginFirmwareDownload":    "begin_firmware_download",
		"WrapperCommand_DownloadFirmwareChunk":    "download_firmware_chunk",
		"WrapperCommand_RequestLogCounts":         "request_log_counts",
		"WrapperCommand_RequestLog":               "request_log",
		"WrapperCommand_ReqAuditlogCounts":        "req_auditlog_counts",
		"WrapperCommand_RequestAuditlog":          "request_auditlog",
		"WrapperCommand_TestChunk":                "test_chunk",
	}

	if commandString, exists := typeMap[typeName]; exists {
		return commandString
	}

	return ""
}

// ExtractCommandType extracts the command type from a WrapperCommand using reflection
func (cpm *CommandPermissionMapper) ExtractCommandType(wrapperCmd *wrappers.WrapperCommand) string {
	if wrapperCmd == nil {
		return ""
	}

	switch wrapperCmd.GetCommand().(type) {
	case *wrappers.WrapperCommand_RequestLogCounts:
		return "request_log_counts"
	case *wrappers.WrapperCommand_LogClear:
		return "log_clear"
	case *wrappers.WrapperCommand_RequestLog:
		return "request_log"
	case *wrappers.WrapperCommand_ReqAuditlogCounts:
		return "req_auditlog_counts"
	case *wrappers.WrapperCommand_AuditlogClear:
		return "auditlog_clear"
	case *wrappers.WrapperCommand_AuditLogReset:
		return "audit_log_reset"
	case *wrappers.WrapperCommand_RequestAuditlog:
		return "request_auditlog"
	case *wrappers.WrapperCommand_RdMonitorData:
		return "rd_monitor_data"
	case *wrappers.WrapperCommand_RdNetworkConfigUnit:
		return "rd_network_config_unit"
	case *wrappers.WrapperCommand_RdNetworkConfigActive:
		return "rd_network_config_active"
	case *wrappers.WrapperCommand_RdChannelConfig:
		return "rd_channel_config"
	case *wrappers.WrapperCommand_RdChannelCurrentSense:
		return "rd_channel_current_sense"
	case *wrappers.WrapperCommand_RdChannelPermissives:
		return "rd_channel_permissives"
	case *wrappers.WrapperCommand_RdFyaConfig:
		return "rd_fya_config"
	case *wrappers.WrapperCommand_RdDataKey:
		return "rd_data_key"
	case *wrappers.WrapperCommand_WrDataKey:
		return "wr_data_key"
	case *wrappers.WrapperCommand_RdFactorySettings:
		return "rd_factory_settings"
	case *wrappers.WrapperCommand_RdUserSettings:
		return "rd_user_settings"
	case *wrappers.WrapperCommand_WrUserSettings:
		return "wr_user_settings"
	case *wrappers.WrapperCommand_RdPort1Disables:
		return "rd_port1_disables"
	case *wrappers.WrapperCommand_WrPort1Disables:
		return "wr_port1_disables"
	case *wrappers.WrapperCommand_RemoteReset:
		return "remote_reset"
	case *wrappers.WrapperCommand_RdPort1Stats:
		return "rd_port1_stats"
	case *wrappers.WrapperCommand_RdDataKeyStats:
		return "rd_data_key_stats"
	case *wrappers.WrapperCommand_RdMainIsoCommsStats:
		return "rd_main_iso_comms_stats"
	case *wrappers.WrapperCommand_RdMainCommsStats:
		return "rd_main_comms_stats"
	case *wrappers.WrapperCommand_RdCommsMainStats:
		return "rd_comms_main_stats"
	case *wrappers.WrapperCommand_RdFlashStats:
		return "rd_flash_stats"
	case *wrappers.WrapperCommand_RdWatchdogStats:
		return "rd_watchdog_stats"
	case *wrappers.WrapperCommand_RdDateTimeDst:
		return "rd_date_time_dst"
	case *wrappers.WrapperCommand_ClearStats:
		return "clear_stats"
	case *wrappers.WrapperCommand_WrDateTimeDst:
		return "wr_date_time_dst"
	case *wrappers.WrapperCommand_RemoteDisplayButtonEvent:
		return "remote_display_button_event"
	case *wrappers.WrapperCommand_StartRealtime:
		return "start_realtime"
	case *wrappers.WrapperCommand_StopRealtime:
		return "stop_realtime"
	case *wrappers.WrapperCommand_ManifestVersions:
		return "manifest_versions"
	case *wrappers.WrapperCommand_RebootCommsMcu:
		return "reboot_comms_mcu"
	case *wrappers.WrapperCommand_InitiateDfu:
		return "initiate_dfu"
	case *wrappers.WrapperCommand_SendFwManifest:
		return "send_fw_manifest"
	case *wrappers.WrapperCommand_BeginFirmwareDownload:
		return "begin_firmware_download"
	case *wrappers.WrapperCommand_DownloadFirmwareChunk:
		return "download_firmware_chunk"
	case *wrappers.WrapperCommand_TestChunk:
		return "test_chunk"
	default:
		return ""
	}
}

// GetRequiredPermissions returns the required permissions for a specific command
func (cpm *CommandPermissionMapper) GetRequiredPermissions(commandType string) ([]string, bool) {
	permissions, exists := cpm.commandPermissions[commandType]
	return permissions, exists
}

// ValidateCommandPermissions checks if a user has the required permissions for a command
func (cpm *CommandPermissionMapper) ValidateCommandPermissions(
	userPermissions *authorizer.UserPermissions,
	deviceID string,
	commandType string,
	pg connect.DatabaseExecutor,
) error {
	// Check for nil user permissions first
	if userPermissions == nil {
		logger.Warnf("User permissions is nil")
		return ErrFailedToCheckPermissions
	}

	logger.Debugf("Validating command permissions: user=%s, device=%s, command=%s",
		userPermissions.UserID, deviceID, commandType)

	requiredPermissions, exists := cpm.GetRequiredPermissions(commandType)
	if !exists {
		// Unknown command type - deny by default for security
		logger.Warnf("Unknown command type: %s", commandType)
		return ErrUnknownCommandType
	}

	logger.Debugf("Required permissions for command %s: %v", commandType, requiredPermissions)

	// Check if user has any of the required permissions for this device
	canAccess, err := userPermissions.CanAccessDevice(pg, deviceID, requiredPermissions...)
	if err != nil {
		logger.Warnf("Failed to check command permissions: %v", err)
		return ErrFailedToCheckPermissions
	}

	if !canAccess {
		logger.Warnf("User %s denied access to device %s for command %s (required: %v)",
			userPermissions.UserID, deviceID, commandType, requiredPermissions)
		return ErrInsufficientPermissions
	}

	logger.Debugf("User %s granted access to device %s for command %s",
		userPermissions.UserID, deviceID, commandType)
	return nil
}
