package locations

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// mockResult implements sql.Result for testing
type mockResult struct {
	rowsAffected      int64
	rowsAffectedError error
	lastInsertId      int64
	lastInsertIdError error
}

func (m *mockResult) LastInsertId() (int64, error) {
	return m.lastInsertId, m.lastInsertIdError
}

func (m *mockResult) RowsAffected() (int64, error) {
	return m.rowsAffected, m.rowsAffectedError
}

func Test_parseCreateAndUpdateLocationRequest(t *testing.T) {
	t.<PERSON>l()

	tests := []struct {
		name    string
		body    interface{}
		wantErr bool
	}{
		{"valid", CreateAndUpdateLocationRequest{Name: "A", Description: "B", Latitude: 1, Longitude: 2}, false},
		{"empty-name", CreateAndUpdateLocationRequest{Name: "", Description: "B", Latitude: 1, Longitude: 2}, true},
		{"empty-desc", CreateAndUpdateLocationRequest{Name: "A", Description: "", Latitude: 1, Longitude: 2}, true},
		{"bad-lat", CreateAndUpdateLocationRequest{Name: "A", Description: "B", Latitude: 200, Longitude: 2}, true},
		{"bad-lon", CreateAndUpdateLocationRequest{Name: "A", Description: "B", Latitude: 20, Longitude: 181}, true},
		{"unknown-field", map[string]interface{}{"name": "A", "foo": "x"}, true},
		{"invalid-json", "not-json", true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var buf bytes.Buffer
			_ = json.NewEncoder(&buf).Encode(tt.body)
			req := httptest.NewRequest(http.MethodPost, "/", &buf)
			req.Header.Set("Content-Type", "application/json")
			_, err := parseCreateAndUpdateLocationRequest(req)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func Test_GetLocationsHandlerWithDeps(t *testing.T) {
	org := uuid.New()

	// Success case
	deps := HandlerDeps{
		GetConnections: func(ctx context.Context, ro ...bool) (*connect.Connections, error) {
			return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
		},
		GetLocationsByOrganization: func(db connect.DatabaseExecutor, id uuid.UUID) (*[]Location, error) {
			res := []Location{{Id: uuid.New(), OrganizationId: id, Name: "A", Description: "B"}}
			return &res, nil
		},
	}
	h := GetLocationsHandlerWithDeps(deps)
	r := httptest.NewRequest(http.MethodGet, "/organizations/"+org.String()+"/locations", nil)
	r = mux.SetURLVars(r, map[string]string{"organizationId": org.String()})
	w := httptest.NewRecorder()
	h.ServeHTTP(w, r)
	assert.Equal(t, http.StatusOK, w.Code)

	// Connection error
	depsConnErr := HandlerDeps{
		GetConnections: func(ctx context.Context, ro ...bool) (*connect.Connections, error) {
			return nil, errors.New("connection failed")
		},
		GetLocationsByOrganization: func(db connect.DatabaseExecutor, id uuid.UUID) (*[]Location, error) {
			return nil, nil
		},
	}
	hConnErr := GetLocationsHandlerWithDeps(depsConnErr)
	rConnErr := httptest.NewRequest(http.MethodGet, "/organizations/"+org.String()+"/locations", nil)
	rConnErr = mux.SetURLVars(rConnErr, map[string]string{"organizationId": org.String()})
	wConnErr := httptest.NewRecorder()
	hConnErr.ServeHTTP(wConnErr, rConnErr)
	assert.Equal(t, http.StatusInternalServerError, wConnErr.Code)

	// Database error
	depsDBErr := HandlerDeps{
		GetConnections: func(ctx context.Context, ro ...bool) (*connect.Connections, error) {
			return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
		},
		GetLocationsByOrganization: func(db connect.DatabaseExecutor, id uuid.UUID) (*[]Location, error) {
			return nil, errors.New("database error")
		},
	}
	hDBErr := GetLocationsHandlerWithDeps(depsDBErr)
	rDBErr := httptest.NewRequest(http.MethodGet, "/organizations/"+org.String()+"/locations", nil)
	rDBErr = mux.SetURLVars(rDBErr, map[string]string{"organizationId": org.String()})
	wDBErr := httptest.NewRecorder()
	hDBErr.ServeHTTP(wDBErr, rDBErr)
	assert.Equal(t, http.StatusInternalServerError, wDBErr.Code)

	// Invalid organization ID
	wInvalidOrg := httptest.NewRecorder()
	rInvalidOrg := httptest.NewRequest(http.MethodGet, "/organizations/invalid/locations", nil)
	rInvalidOrg = mux.SetURLVars(rInvalidOrg, map[string]string{"organizationId": "invalid"})
	h.ServeHTTP(wInvalidOrg, rInvalidOrg)
	assert.Equal(t, http.StatusBadRequest, wInvalidOrg.Code)
}

func Test_CreateLocationHandlerWithDeps(t *testing.T) {
	org := uuid.New()
	body := CreateAndUpdateLocationRequest{Name: "A", Description: "B", Latitude: 1, Longitude: 2}

	// Success case
	deps := HandlerDeps{
		GetConnections: func(ctx context.Context, ro ...bool) (*connect.Connections, error) {
			return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
		},
		CreateLocation: func(db connect.DatabaseExecutor, id uuid.UUID, req *CreateAndUpdateLocationRequest) (*Location, error) {
			return &Location{Id: uuid.New(), OrganizationId: id, Name: req.Name, Description: req.Description, Latitude: req.Latitude, Longitude: req.Longitude}, nil
		},
	}
	h := CreateLocationHandlerWithDeps(deps)
	var buf bytes.Buffer
	_ = json.NewEncoder(&buf).Encode(body)
	r := httptest.NewRequest(http.MethodPost, "/organizations/"+org.String()+"/locations", &buf)
	r.Header.Set("Content-Type", "application/json")
	r = mux.SetURLVars(r, map[string]string{"organizationId": org.String()})
	w := httptest.NewRecorder()
	h.ServeHTTP(w, r)
	assert.Equal(t, http.StatusOK, w.Code)

	// Connection error
	depsConnErr := HandlerDeps{
		GetConnections: func(ctx context.Context, ro ...bool) (*connect.Connections, error) {
			return nil, errors.New("connection failed")
		},
		CreateLocation: func(db connect.DatabaseExecutor, id uuid.UUID, req *CreateAndUpdateLocationRequest) (*Location, error) {
			return nil, nil
		},
	}
	hConnErr := CreateLocationHandlerWithDeps(depsConnErr)
	var bufConnErr bytes.Buffer
	_ = json.NewEncoder(&bufConnErr).Encode(body)
	rConnErr := httptest.NewRequest(http.MethodPost, "/organizations/"+org.String()+"/locations", &bufConnErr)
	rConnErr.Header.Set("Content-Type", "application/json")
	rConnErr = mux.SetURLVars(rConnErr, map[string]string{"organizationId": org.String()})
	wConnErr := httptest.NewRecorder()
	hConnErr.ServeHTTP(wConnErr, rConnErr)
	assert.Equal(t, http.StatusInternalServerError, wConnErr.Code)

	// Organization not found
	depsOrgNotFound := HandlerDeps{
		GetConnections: func(ctx context.Context, ro ...bool) (*connect.Connections, error) {
			return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
		},
		CreateLocation: func(db connect.DatabaseExecutor, id uuid.UUID, req *CreateAndUpdateLocationRequest) (*Location, error) {
			return nil, ErrOrganizationNotFound
		},
	}
	hOrgNotFound := CreateLocationHandlerWithDeps(depsOrgNotFound)
	var bufOrgNotFound bytes.Buffer
	_ = json.NewEncoder(&bufOrgNotFound).Encode(body)
	rOrgNotFound := httptest.NewRequest(http.MethodPost, "/organizations/"+org.String()+"/locations", &bufOrgNotFound)
	rOrgNotFound.Header.Set("Content-Type", "application/json")
	rOrgNotFound = mux.SetURLVars(rOrgNotFound, map[string]string{"organizationId": org.String()})
	wOrgNotFound := httptest.NewRecorder()
	hOrgNotFound.ServeHTTP(wOrgNotFound, rOrgNotFound)
	assert.Equal(t, http.StatusNotFound, wOrgNotFound.Code)

	// Database error
	depsDBErr := HandlerDeps{
		GetConnections: func(ctx context.Context, ro ...bool) (*connect.Connections, error) {
			return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
		},
		CreateLocation: func(db connect.DatabaseExecutor, id uuid.UUID, req *CreateAndUpdateLocationRequest) (*Location, error) {
			return nil, errors.New("database error")
		},
	}
	hDBErr := CreateLocationHandlerWithDeps(depsDBErr)
	var bufDBErr bytes.Buffer
	_ = json.NewEncoder(&bufDBErr).Encode(body)
	rDBErr := httptest.NewRequest(http.MethodPost, "/organizations/"+org.String()+"/locations", &bufDBErr)
	rDBErr.Header.Set("Content-Type", "application/json")
	rDBErr = mux.SetURLVars(rDBErr, map[string]string{"organizationId": org.String()})
	wDBErr := httptest.NewRecorder()
	hDBErr.ServeHTTP(wDBErr, rDBErr)
	assert.Equal(t, http.StatusInternalServerError, wDBErr.Code)

	// Invalid organization ID
	wInvalidOrg := httptest.NewRecorder()
	rInvalidOrg := httptest.NewRequest(http.MethodPost, "/organizations/invalid/locations", &buf)
	rInvalidOrg.Header.Set("Content-Type", "application/json")
	rInvalidOrg = mux.SetURLVars(rInvalidOrg, map[string]string{"organizationId": "invalid"})
	h.ServeHTTP(wInvalidOrg, rInvalidOrg)
	assert.Equal(t, http.StatusBadRequest, wInvalidOrg.Code)

	// Invalid request body
	wInvalidBody := httptest.NewRecorder()
	rInvalidBody := httptest.NewRequest(http.MethodPost, "/organizations/"+org.String()+"/locations", bytes.NewBufferString("not-json"))
	rInvalidBody.Header.Set("Content-Type", "application/json")
	rInvalidBody = mux.SetURLVars(rInvalidBody, map[string]string{"organizationId": org.String()})
	h.ServeHTTP(wInvalidBody, rInvalidBody)
	assert.Equal(t, http.StatusBadRequest, wInvalidBody.Code)
}

func Test_UpdateLocationHandlerWithDeps(t *testing.T) {
	org := uuid.New()
	loc := uuid.New()
	body := CreateAndUpdateLocationRequest{Name: "A", Description: "B", Latitude: 1, Longitude: 2}

	deps := HandlerDeps{
		GetConnections: func(ctx context.Context, ro ...bool) (*connect.Connections, error) {
			return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
		},
		UpdateLocation: func(db connect.DatabaseExecutor, id uuid.UUID, lid uuid.UUID, req *CreateAndUpdateLocationRequest) error {
			if lid == uuid.Nil {
				return ErrLocationNotFound
			}
			return nil
		},
	}
	h := UpdateLocationHandlerWithDeps(deps)
	var buf bytes.Buffer
	_ = json.NewEncoder(&buf).Encode(body)
	r := httptest.NewRequest(http.MethodPatch, "/organizations/"+org.String()+"/locations/"+loc.String(), &buf)
	r.Header.Set("Content-Type", "application/json")
	r = mux.SetURLVars(r, map[string]string{"organizationId": org.String(), "locationId": loc.String()})
	w := httptest.NewRecorder()
	h.ServeHTTP(w, r)
	assert.Equal(t, http.StatusOK, w.Code)

	// Connection error
	depsConnErr := HandlerDeps{
		GetConnections: func(ctx context.Context, ro ...bool) (*connect.Connections, error) {
			return nil, errors.New("connection failed")
		},
		UpdateLocation: func(db connect.DatabaseExecutor, id uuid.UUID, lid uuid.UUID, req *CreateAndUpdateLocationRequest) error {
			return nil
		},
	}
	hConnErr := UpdateLocationHandlerWithDeps(depsConnErr)
	var bufConnErr bytes.Buffer
	_ = json.NewEncoder(&bufConnErr).Encode(body)
	rConnErr := httptest.NewRequest(http.MethodPatch, "/organizations/"+org.String()+"/locations/"+loc.String(), &bufConnErr)
	rConnErr.Header.Set("Content-Type", "application/json")
	rConnErr = mux.SetURLVars(rConnErr, map[string]string{"organizationId": org.String(), "locationId": loc.String()})
	wConnErr := httptest.NewRecorder()
	hConnErr.ServeHTTP(wConnErr, rConnErr)
	assert.Equal(t, http.StatusInternalServerError, wConnErr.Code)

	// bad org id
	r2 := httptest.NewRequest(http.MethodPatch, "/organizations/invalid/locations/"+loc.String(), &buf)
	r2.Header.Set("Content-Type", "application/json")
	r2 = mux.SetURLVars(r2, map[string]string{"organizationId": "invalid", "locationId": loc.String()})
	w2 := httptest.NewRecorder()
	h.ServeHTTP(w2, r2)
	assert.Equal(t, http.StatusBadRequest, w2.Code)

	// bad location id
	r3 := httptest.NewRequest(http.MethodPatch, "/organizations/"+org.String()+"/locations/invalid", &buf)
	r3.Header.Set("Content-Type", "application/json")
	r3 = mux.SetURLVars(r3, map[string]string{"organizationId": org.String(), "locationId": "invalid"})
	w3 := httptest.NewRecorder()
	h.ServeHTTP(w3, r3)
	assert.Equal(t, http.StatusBadRequest, w3.Code)

	// invalid body
	r4 := httptest.NewRequest(http.MethodPatch, "/organizations/"+org.String()+"/locations/"+loc.String(), bytes.NewBufferString("not-json"))
	r4.Header.Set("Content-Type", "application/json")
	r4 = mux.SetURLVars(r4, map[string]string{"organizationId": org.String(), "locationId": loc.String()})
	w4 := httptest.NewRecorder()
	h.ServeHTTP(w4, r4)
	assert.Equal(t, http.StatusBadRequest, w4.Code)

	// not found - use valid JSON and valid UUIDs but return not found from business logic
	depsNF := deps
	depsNF.UpdateLocation = func(db connect.DatabaseExecutor, id uuid.UUID, lid uuid.UUID, req *CreateAndUpdateLocationRequest) error {
		return ErrLocationNotFound
	}
	hNF := UpdateLocationHandlerWithDeps(depsNF)
	var bufNF bytes.Buffer
	_ = json.NewEncoder(&bufNF).Encode(body)
	rNF := httptest.NewRequest(http.MethodPatch, "/organizations/"+org.String()+"/locations/"+loc.String(), &bufNF)
	rNF.Header.Set("Content-Type", "application/json")
	rNF = mux.SetURLVars(rNF, map[string]string{"organizationId": org.String(), "locationId": loc.String()})
	w5 := httptest.NewRecorder()
	hNF.ServeHTTP(w5, rNF)
	assert.Equal(t, http.StatusNotFound, w5.Code)

	// internal error - use valid JSON and valid UUIDs but return error from business logic
	depsErr := deps
	depsErr.UpdateLocation = func(db connect.DatabaseExecutor, id uuid.UUID, lid uuid.UUID, req *CreateAndUpdateLocationRequest) error {
		return errors.New("db error")
	}
	hErr := UpdateLocationHandlerWithDeps(depsErr)
	var bufErr bytes.Buffer
	_ = json.NewEncoder(&bufErr).Encode(body)
	rErr := httptest.NewRequest(http.MethodPatch, "/organizations/"+org.String()+"/locations/"+loc.String(), &bufErr)
	rErr.Header.Set("Content-Type", "application/json")
	rErr = mux.SetURLVars(rErr, map[string]string{"organizationId": org.String(), "locationId": loc.String()})
	w6 := httptest.NewRecorder()
	hErr.ServeHTTP(w6, rErr)
	assert.Equal(t, http.StatusInternalServerError, w6.Code)
}

func Test_DeleteLocationHandlerWithDeps(t *testing.T) {
	org := uuid.New()
	loc := uuid.New()

	deps := HandlerDeps{
		GetConnections: func(ctx context.Context, ro ...bool) (*connect.Connections, error) {
			return &connect.Connections{Postgres: &dbexecutor.FakeDBExecutor{}}, nil
		},
		DeleteLocation: func(db connect.DatabaseExecutor, id uuid.UUID, lid uuid.UUID) error {
			if lid == uuid.Nil {
				return ErrLocationNotFound
			}
			return nil
		},
	}
	h := DeleteLocationHandlerWithDeps(deps)
	r := httptest.NewRequest(http.MethodDelete, "/organizations/"+org.String()+"/locations/"+loc.String(), nil)
	r = mux.SetURLVars(r, map[string]string{"organizationId": org.String(), "locationId": loc.String()})
	w := httptest.NewRecorder()
	h.ServeHTTP(w, r)
	assert.Equal(t, http.StatusOK, w.Code)

	// Connection error
	depsConnErr := HandlerDeps{
		GetConnections: func(ctx context.Context, ro ...bool) (*connect.Connections, error) {
			return nil, errors.New("connection failed")
		},
		DeleteLocation: func(db connect.DatabaseExecutor, id uuid.UUID, lid uuid.UUID) error {
			return nil
		},
	}
	hConnErr := DeleteLocationHandlerWithDeps(depsConnErr)
	rConnErr := httptest.NewRequest(http.MethodDelete, "/organizations/"+org.String()+"/locations/"+loc.String(), nil)
	rConnErr = mux.SetURLVars(rConnErr, map[string]string{"organizationId": org.String(), "locationId": loc.String()})
	wConnErr := httptest.NewRecorder()
	hConnErr.ServeHTTP(wConnErr, rConnErr)
	assert.Equal(t, http.StatusInternalServerError, wConnErr.Code)

	// bad org id
	r2 := httptest.NewRequest(http.MethodDelete, "/organizations/invalid/locations/"+loc.String(), nil)
	r2 = mux.SetURLVars(r2, map[string]string{"organizationId": "invalid", "locationId": loc.String()})
	w2 := httptest.NewRecorder()
	h.ServeHTTP(w2, r2)
	assert.Equal(t, http.StatusBadRequest, w2.Code)

	// bad location id
	r3 := httptest.NewRequest(http.MethodDelete, "/organizations/"+org.String()+"/locations/invalid", nil)
	r3 = mux.SetURLVars(r3, map[string]string{"organizationId": org.String(), "locationId": "invalid"})
	w3 := httptest.NewRecorder()
	h.ServeHTTP(w3, r3)
	assert.Equal(t, http.StatusBadRequest, w3.Code)

	// not found - use a valid UUID but return not found from business logic
	depsNF := deps
	depsNF.DeleteLocation = func(db connect.DatabaseExecutor, id uuid.UUID, lid uuid.UUID) error { return ErrLocationNotFound }
	hNF := DeleteLocationHandlerWithDeps(depsNF)
	rNF := httptest.NewRequest(http.MethodDelete, "/organizations/"+org.String()+"/locations/"+loc.String(), nil)
	rNF = mux.SetURLVars(rNF, map[string]string{"organizationId": org.String(), "locationId": loc.String()})
	w4 := httptest.NewRecorder()
	hNF.ServeHTTP(w4, rNF)
	assert.Equal(t, http.StatusNotFound, w4.Code)

	// internal error - use a valid UUID but return error from business logic
	depsErr := deps
	depsErr.DeleteLocation = func(db connect.DatabaseExecutor, id uuid.UUID, lid uuid.UUID) error { return errors.New("db error") }
	hErr := DeleteLocationHandlerWithDeps(depsErr)
	rErr := httptest.NewRequest(http.MethodDelete, "/organizations/"+org.String()+"/locations/"+loc.String(), nil)
	rErr = mux.SetURLVars(rErr, map[string]string{"organizationId": org.String(), "locationId": loc.String()})
	w5 := httptest.NewRecorder()
	hErr.ServeHTTP(w5, rErr)
	assert.Equal(t, http.StatusInternalServerError, w5.Code)
}

// Test database interaction functions
func Test_getLocationsByOrganization(t *testing.T) {
	org := uuid.New()

	// Success case
	mockDB := &dbexecutor.FakeDBExecutor{}
	mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
		locations := dest.(*[]Location)
		*locations = []Location{
			{Id: uuid.New(), OrganizationId: org, Name: "Location 1", Description: "Desc 1", Latitude: 1.0, Longitude: 2.0},
			{Id: uuid.New(), OrganizationId: org, Name: "Location 2", Description: "Desc 2", Latitude: 3.0, Longitude: 4.0},
		}
		return nil
	}

	locations, err := getLocationsByOrganization(mockDB, org)
	assert.NoError(t, err)
	assert.Len(t, *locations, 2)
	assert.Equal(t, org, (*locations)[0].OrganizationId)

	// Database error
	mockDBErr := &dbexecutor.FakeDBExecutor{}
	mockDBErr.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
		return errors.New("database error")
	}

	_, err = getLocationsByOrganization(mockDBErr, org)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database operation failed")
}

func Test_createLocation(t *testing.T) {
	org := uuid.New()
	req := &CreateAndUpdateLocationRequest{Name: "Test Location", Description: "Test Description", Latitude: 1.0, Longitude: 2.0}

	// Success case
	mockDB := &dbexecutor.FakeDBExecutor{}
	mockDB.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
		return map[string]interface{}{"exists": true}, nil
	}
	mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
		location := dest.(*Location)
		*location = Location{
			Id:             uuid.New(),
			OrganizationId: org,
			Name:           req.Name,
			Description:    req.Description,
			Latitude:       req.Latitude,
			Longitude:      req.Longitude,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		}
		return nil
	}

	location, err := createLocation(mockDB, org, req)
	assert.NoError(t, err)
	assert.Equal(t, org, location.OrganizationId)
	assert.Equal(t, req.Name, location.Name)

	// Organization not found
	mockDBNotFound := &dbexecutor.FakeDBExecutor{}
	mockDBNotFound.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
		return map[string]interface{}{"exists": false}, nil
	}

	_, err = createLocation(mockDBNotFound, org, req)
	assert.Error(t, err)
	assert.Equal(t, ErrOrganizationNotFound, err)

	// Database error on check query
	mockDBErr := &dbexecutor.FakeDBExecutor{}
	mockDBErr.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
		return nil, errors.New("database error")
	}

	_, err = createLocation(mockDBErr, org, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database operation failed")

	// Missing exists column
	mockDBMissingCol := &dbexecutor.FakeDBExecutor{}
	mockDBMissingCol.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
		return map[string]interface{}{"other": true}, nil
	}

	_, err = createLocation(mockDBMissingCol, org, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "missing exists column")

	// Invalid type for exists column
	mockDBInvalidType := &dbexecutor.FakeDBExecutor{}
	mockDBInvalidType.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
		return map[string]interface{}{"exists": "not a bool"}, nil
	}

	_, err = createLocation(mockDBInvalidType, org, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid type for exists column")

	// Database error on insert
	mockDBInsertErr := &dbexecutor.FakeDBExecutor{}
	mockDBInsertErr.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
		return map[string]interface{}{"exists": true}, nil
	}
	mockDBInsertErr.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
		return errors.New("insert error")
	}

	_, err = createLocation(mockDBInsertErr, org, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database operation failed")
}

func Test_deleteLocation(t *testing.T) {
	org := uuid.New()
	loc := uuid.New()

	// Success case
	mockDB := &dbexecutor.FakeDBExecutor{}
	mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
		return &mockResult{rowsAffected: 1}, nil
	}

	err := deleteLocation(mockDB, org, loc)
	assert.NoError(t, err)

	// Location not found
	mockDBNotFound := &dbexecutor.FakeDBExecutor{}
	mockDBNotFound.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
		return &mockResult{rowsAffected: 0}, nil
	}

	err = deleteLocation(mockDBNotFound, org, loc)
	assert.Error(t, err)
	assert.Equal(t, ErrLocationNotFound, err)

	// Database error on exec
	mockDBExecErr := &dbexecutor.FakeDBExecutor{}
	mockDBExecErr.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
		return nil, errors.New("exec error")
	}

	err = deleteLocation(mockDBExecErr, org, loc)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database operation failed")

	// Error getting rows affected
	mockDBRowsErr := &dbexecutor.FakeDBExecutor{}
	mockDBRowsErr.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
		return &mockResult{rowsAffectedError: errors.New("rows error")}, nil
	}

	err = deleteLocation(mockDBRowsErr, org, loc)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database operation failed")
}

func Test_updateLocation(t *testing.T) {
	org := uuid.New()
	loc := uuid.New()
	req := &CreateAndUpdateLocationRequest{Name: "Updated Location", Description: "Updated Description", Latitude: 5.0, Longitude: 6.0}

	// Success case
	mockDB := &dbexecutor.FakeDBExecutor{}
	mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
		return &mockResult{rowsAffected: 1}, nil
	}

	err := updateLocation(mockDB, org, loc, req)
	assert.NoError(t, err)

	// Location not found
	mockDBNotFound := &dbexecutor.FakeDBExecutor{}
	mockDBNotFound.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
		return &mockResult{rowsAffected: 0}, nil
	}

	err = updateLocation(mockDBNotFound, org, loc, req)
	assert.Error(t, err)
	assert.Equal(t, ErrLocationNotFound, err)

	// Database error on exec
	mockDBExecErr := &dbexecutor.FakeDBExecutor{}
	mockDBExecErr.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
		return nil, errors.New("exec error")
	}

	err = updateLocation(mockDBExecErr, org, loc, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database operation failed")

	// Error getting rows affected
	mockDBRowsErr := &dbexecutor.FakeDBExecutor{}
	mockDBRowsErr.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
		return &mockResult{rowsAffectedError: errors.New("rows error")}, nil
	}

	err = updateLocation(mockDBRowsErr, org, loc, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database operation failed")
}
