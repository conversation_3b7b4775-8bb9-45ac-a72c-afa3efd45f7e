package auth

import (
	"github.com/google/uuid"
	"synapse-its.com/onramp/data"
	"synapse-its.com/onramp/domain"
	"synapse-its.com/shared/api/authtypes"
)

// =============================================================================
// TEST HELPER FUNCTIONS
// =============================================================================

// createTestUser creates a test user for testing
func createTestUser() *domain.User {
	return &domain.User{
		ID:           uuid.New(),
		FirstName:    "John",
		LastName:     "Doe",
		Mobile:       "+1234567890",
		IanaTimezone: "America/Chicago",
		Description:  "Test user",
	}
}

// createTestAuthMethod creates a test auth method for testing
func createTestAuthMethod(userID uuid.UUID) *domain.AuthMethod {
	return &domain.AuthMethod{
		ID:           uuid.New(),
		UserID:       userID,
		Type:         authtypes.AuthMethodTypeUsernamePassword,
		Sub:          "",
		Issuer:       "",
		UserName:     "testuser",
		PasswordHash: "hashed_password",
		Email:        "<EMAIL>",
		Metadata:     make(map[string]interface{}),
		IsEnabled:    true,
	}
}

// createTestUserAuthMethod creates a test UserAuthMethod for database testing
func createTestUserAuthMethod() data.UserAuthMethod {
	// Use deterministic UUIDs for testing
	userID := uuid.MustParse("12345678-1234-1234-1234-123456789abc")
	authMethodID := uuid.MustParse("*************-4321-4321-cba987654321")

	firstName := "John"
	lastName := "Doe"
	mobile := "+1234567890"
	description := "Test user"
	sub := ""
	issuer := ""
	userName := "testuser"
	passwordHash := "hashed_password"
	email := "<EMAIL>"

	return data.UserAuthMethod{
		UserID:       userID,
		FirstName:    &firstName,
		LastName:     &lastName,
		Mobile:       &mobile,
		IanaTimezone: "America/Chicago",
		Description:  &description,

		AuthMethodID:     authMethodID,
		AuthMethodUserID: userID,
		Type:             "USERNAME_PASSWORD",
		Sub:              &sub,
		Issuer:           &issuer,
		UserName:         &userName,
		PasswordHash:     &passwordHash,
		Email:            &email,
		Metadata:         []byte(`{"key":"value"}`),
		IsEnabled:        true,
	}
}
