package locationgroups

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
)

// Helper function to create a mock database executor for organization validation
func createMockDBWithOrgValidation(orgExists bool) *mocks.FakeDBExecutor {
	return &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			// Check if this is the organization validation query by name
			if strings.Contains(query, "-- name: ValidateOrganizationExistedOrDeleted") {
				// Mock organization validation result
				if orgStruct, ok := dest.(*struct {
					Exists bool `db:"exists"`
				}); ok {
					orgStruct.Exists = orgExists
				}
			}
			return nil
		},
	}
}

// Test_CreateHandlerWithDeps tests the CreateHandlerWithDeps function
func Test_CreateHandlerWithDeps(t *testing.T) {
	t.Parallel()

	// Create test data
	testOrgID := uuid.New()
	testReq := &CreateAndUpdateLocationGroupsRequest{
		Name: "Test Location Group",
	}
	testResponse := &LocationGroupResponse{
		Id:             uuid.New(),
		OrganizationId: testOrgID,
		Name:           testReq.Name,
	}

	tests := []struct {
		name           string
		organizationID string
		requestBody    interface{}
		mockDeps       func() HandlerDeps
		expectedStatus int
		expectedBody   string
	}{
		{
			name:           "successful_creation",
			organizationID: testOrgID.String(),
			requestBody:    testReq,
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: createMockDBWithOrgValidation(true),
						}, nil
					},
					CreateLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, req *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error) {
						return testResponse, nil
					},
				}
			},
			expectedStatus: 200,
			expectedBody:   "success",
		},
		{
			name:           "get_connections_error",
			organizationID: testOrgID.String(),
			requestBody:    testReq,
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return nil, assert.AnError
					},
					CreateLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, req *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error) {
						return nil, nil
					},
				}
			},
			expectedStatus: 500,
			expectedBody:   "internal server error",
		},
		{
			name:           "invalid_organization_id",
			organizationID: "invalid-uuid",
			requestBody:    testReq,
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &mocks.FakeDBExecutor{},
						}, nil
					},
					CreateLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, req *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error) {
						return nil, nil
					},
				}
			},
			expectedStatus: 400,
			expectedBody:   "bad request",
		},
		{
			name:           "organization_not_found",
			organizationID: testOrgID.String(),
			requestBody:    testReq,
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: createMockDBWithOrgValidation(false),
						}, nil
					},
					CreateLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, req *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error) {
						return nil, nil
					},
				}
			},
			expectedStatus: 400,
			expectedBody:   "bad request",
		},
		{
			name:           "invalid_request_body",
			organizationID: testOrgID.String(),
			requestBody:    map[string]interface{}{"invalid": "field"},
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: createMockDBWithOrgValidation(true),
						}, nil
					},
					CreateLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, req *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error) {
						return nil, nil
					},
				}
			},
			expectedStatus: 400,
			expectedBody:   "bad request",
		},
		{
			name:           "empty_name",
			organizationID: testOrgID.String(),
			requestBody:    &CreateAndUpdateLocationGroupsRequest{Name: ""},
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: createMockDBWithOrgValidation(true),
						}, nil
					},
					CreateLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, req *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error) {
						return nil, nil
					},
				}
			},
			expectedStatus: 400,
			expectedBody:   "bad request",
		},
		{
			name:           "database_error",
			organizationID: testOrgID.String(),
			requestBody:    testReq,
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: createMockDBWithOrgValidation(true),
						}, nil
					},
					CreateLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, req *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error) {
						return nil, ErrDatabaseOperation
					},
				}
			},
			expectedStatus: 500,
			expectedBody:   "internal server error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create request body
			bodyBytes, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest("POST", "/", bytes.NewBuffer(bodyBytes))
			req.Header.Set("Content-Type", "application/json")

			// Set URL parameters
			vars := map[string]string{
				"organizationId": tt.organizationID,
			}
			req = mux.SetURLVars(req, vars)

			// Create response recorder
			w := httptest.NewRecorder()

			// Create handler with mocked dependencies
			handler := CreateHandlerWithDeps(tt.mockDeps())

			// Execute handler
			handler(w, req)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)
			assert.Contains(t, strings.ToLower(w.Body.String()), strings.ToLower(tt.expectedBody))
		})
	}
}

// Test_GetLocationGroupsHandlerWithDeps tests the GetLocationGroupsHandlerWithDeps function
func Test_GetLocationGroupsHandlerWithDeps(t *testing.T) {
	t.Parallel()

	// Create test data
	testOrgID := uuid.New()
	testResponse := &LocationGroupsResponse{
		LocationGroups: []LocationGroupResponse{
			{
				Id:             uuid.New(),
				OrganizationId: testOrgID,
				Name:           "Test Location Group 1",
				CreatedAt:      time.Now().UTC(),
				UpdatedAt:      time.Now().UTC(),
			},
		},
	}

	tests := []struct {
		name           string
		organizationID string
		mockDeps       func() HandlerDeps
		expectedStatus int
		expectedBody   string
	}{
		{
			name:           "get_connections_error",
			organizationID: testOrgID.String(),
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return nil, assert.AnError
					},
					GetLocationGroupsByOrganizationID: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (*LocationGroupsResponse, error) {
						return nil, nil
					},
				}
			},
			expectedStatus: 500,
			expectedBody:   "internal server error",
		},
		{
			name:           "successful_retrieval",
			organizationID: testOrgID.String(),
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: createMockDBWithOrgValidation(true),
						}, nil
					},
					GetLocationGroupsByOrganizationID: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (*LocationGroupsResponse, error) {
						return testResponse, nil
					},
				}
			},
			expectedStatus: 200,
			expectedBody:   "success",
		},
		{
			name:           "invalid_organization_id",
			organizationID: "invalid-uuid",
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &mocks.FakeDBExecutor{},
						}, nil
					},
					GetLocationGroupsByOrganizationID: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (*LocationGroupsResponse, error) {
						return nil, nil
					},
				}
			},
			expectedStatus: 400,
			expectedBody:   "bad request",
		},
		{
			name:           "database_error",
			organizationID: testOrgID.String(),
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: createMockDBWithOrgValidation(true),
						}, nil
					},
					GetLocationGroupsByOrganizationID: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (*LocationGroupsResponse, error) {
						return nil, ErrDatabaseOperation
					},
				}
			},
			expectedStatus: 500,
			expectedBody:   "internal server error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create request
			req := httptest.NewRequest("GET", "/", nil)

			// Set URL parameters
			vars := map[string]string{
				"organizationId": tt.organizationID,
			}
			req = mux.SetURLVars(req, vars)

			// Create response recorder
			w := httptest.NewRecorder()

			// Create handler with mocked dependencies
			handler := GetLocationGroupsHandlerWithDeps(tt.mockDeps())

			// Execute handler
			handler(w, req)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)
			assert.Contains(t, strings.ToLower(w.Body.String()), strings.ToLower(tt.expectedBody))
		})
	}
}

// Test_UpdateHandlerWithDeps tests the UpdateHandlerWithDeps function
func Test_UpdateHandlerWithDeps(t *testing.T) {
	t.Parallel()

	// Create test data
	testOrgID := uuid.New()
	testLocationGroupID := uuid.New()
	testReq := &CreateAndUpdateLocationGroupsRequest{
		Name: "Updated Location Group",
	}
	testResponse := &LocationGroupResponse{
		Id:             testLocationGroupID,
		OrganizationId: testOrgID,
		Name:           testReq.Name,
		CreatedAt:      time.Now().UTC(),
		UpdatedAt:      time.Now().UTC(),
	}

	tests := []struct {
		name            string
		organizationID  string
		locationGroupID string
		requestBody     interface{}
		mockDeps        func() HandlerDeps
		expectedStatus  int
		expectedBody    string
	}{
		{
			name:            "get_connections_error",
			organizationID:  testOrgID.String(),
			locationGroupID: testLocationGroupID.String(),
			requestBody:     testReq,
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return nil, assert.AnError
					},
					UpdateLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, locGroupID uuid.UUID, req *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error) {
						return nil, nil
					},
				}
			},
			expectedStatus: 500,
			expectedBody:   "internal server error",
		},
		{
			name:            "successful_update",
			organizationID:  testOrgID.String(),
			locationGroupID: testLocationGroupID.String(),
			requestBody:     testReq,
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: createMockDBWithOrgValidation(true),
						}, nil
					},
					UpdateLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, locGroupID uuid.UUID, req *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error) {
						return testResponse, nil
					},
				}
			},
			expectedStatus: 200,
			expectedBody:   "success",
		},
		{
			name:            "invalid_organization_id",
			organizationID:  "invalid-uuid",
			locationGroupID: testLocationGroupID.String(),
			requestBody:     testReq,
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &mocks.FakeDBExecutor{},
						}, nil
					},
					UpdateLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, locGroupID uuid.UUID, req *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error) {
						return nil, nil
					},
				}
			},
			expectedStatus: 400,
			expectedBody:   "bad request",
		},
		{
			name:            "invalid_location_group_id",
			organizationID:  testOrgID.String(),
			locationGroupID: "invalid-uuid",
			requestBody:     testReq,
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: createMockDBWithOrgValidation(true),
						}, nil
					},
					UpdateLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, locGroupID uuid.UUID, req *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error) {
						return nil, nil
					},
				}
			},
			expectedStatus: 400,
			expectedBody:   "bad request",
		},
		{
			name:            "organization_not_found",
			organizationID:  testOrgID.String(),
			locationGroupID: testLocationGroupID.String(),
			requestBody:     testReq,
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: createMockDBWithOrgValidation(false),
						}, nil
					},
					UpdateLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, locGroupID uuid.UUID, req *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error) {
						return nil, nil
					},
				}
			},
			expectedStatus: 400,
			expectedBody:   "bad request",
		},
		{
			name:            "invalid_request_body",
			organizationID:  testOrgID.String(),
			locationGroupID: testLocationGroupID.String(),
			requestBody:     map[string]interface{}{"invalid": "field"},
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: createMockDBWithOrgValidation(true),
						}, nil
					},
					UpdateLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, locGroupID uuid.UUID, req *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error) {
						return nil, nil
					},
				}
			},
			expectedStatus: 400,
			expectedBody:   "bad request",
		},
		{
			name:            "empty_name",
			organizationID:  testOrgID.String(),
			locationGroupID: testLocationGroupID.String(),
			requestBody:     &CreateAndUpdateLocationGroupsRequest{Name: ""},
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: createMockDBWithOrgValidation(true),
						}, nil
					},
					UpdateLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, locGroupID uuid.UUID, req *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error) {
						return nil, nil
					},
				}
			},
			expectedStatus: 400,
			expectedBody:   "bad request",
		},
		{
			name:            "database_error",
			organizationID:  testOrgID.String(),
			locationGroupID: testLocationGroupID.String(),
			requestBody:     testReq,
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: createMockDBWithOrgValidation(true),
						}, nil
					},
					UpdateLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, locGroupID uuid.UUID, req *CreateAndUpdateLocationGroupsRequest) (*LocationGroupResponse, error) {
						return nil, ErrDatabaseOperation
					},
				}
			},
			expectedStatus: 500,
			expectedBody:   "internal server error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create request body
			bodyBytes, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest("PUT", "/", bytes.NewBuffer(bodyBytes))
			req.Header.Set("Content-Type", "application/json")

			// Set URL parameters
			vars := map[string]string{
				"organizationId":  tt.organizationID,
				"locationgroupId": tt.locationGroupID,
			}
			req = mux.SetURLVars(req, vars)

			// Create response recorder
			w := httptest.NewRecorder()

			// Create handler with mocked dependencies
			handler := UpdateHandlerWithDeps(tt.mockDeps())

			// Execute handler
			handler(w, req)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)
			assert.Contains(t, strings.ToLower(w.Body.String()), strings.ToLower(tt.expectedBody))
		})
	}
}

// Test_DeleteHandlerWithDeps tests the DeleteHandlerWithDeps function
func Test_DeleteHandlerWithDeps(t *testing.T) {
	t.Parallel()

	// Create test data
	testOrgID := uuid.New()
	testLocationGroupID := uuid.New()

	tests := []struct {
		name            string
		organizationID  string
		locationGroupID string
		mockDeps        func() HandlerDeps
		expectedStatus  int
		expectedBody    string
	}{
		{
			name:            "get_connections_error",
			organizationID:  testOrgID.String(),
			locationGroupID: testLocationGroupID.String(),
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return nil, assert.AnError
					},
					DeleteLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, locGroupID uuid.UUID) error {
						return nil
					},
				}
			},
			expectedStatus: 500,
			expectedBody:   "internal server error",
		},
		{
			name:            "successful_deletion",
			organizationID:  testOrgID.String(),
			locationGroupID: testLocationGroupID.String(),
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: createMockDBWithOrgValidation(true),
						}, nil
					},
					DeleteLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, locGroupID uuid.UUID) error {
						return nil
					},
				}
			},
			expectedStatus: 200,
			expectedBody:   "success",
		},
		{
			name:            "invalid_organization_id",
			organizationID:  "invalid-uuid",
			locationGroupID: testLocationGroupID.String(),
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &mocks.FakeDBExecutor{},
						}, nil
					},
					DeleteLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, locGroupID uuid.UUID) error {
						return nil
					},
				}
			},
			expectedStatus: 400,
			expectedBody:   "bad request",
		},
		{
			name:            "invalid_location_group_id",
			organizationID:  testOrgID.String(),
			locationGroupID: "invalid-uuid",
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: createMockDBWithOrgValidation(true),
						}, nil
					},
					DeleteLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, locGroupID uuid.UUID) error {
						return nil
					},
				}
			},
			expectedStatus: 400,
			expectedBody:   "bad request",
		},
		{
			name:            "organization_not_found",
			organizationID:  testOrgID.String(),
			locationGroupID: testLocationGroupID.String(),
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: createMockDBWithOrgValidation(false),
						}, nil
					},
					DeleteLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, locGroupID uuid.UUID) error {
						return nil
					},
				}
			},
			expectedStatus: 400,
			expectedBody:   "bad request",
		},
		{
			name:            "database_error",
			organizationID:  testOrgID.String(),
			locationGroupID: testLocationGroupID.String(),
			mockDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: createMockDBWithOrgValidation(true),
						}, nil
					},
					DeleteLocationGroup: func(pg connect.DatabaseExecutor, orgID uuid.UUID, locGroupID uuid.UUID) error {
						return ErrDatabaseOperation
					},
				}
			},
			expectedStatus: 500,
			expectedBody:   "internal server error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create request
			req := httptest.NewRequest("DELETE", "/", nil)

			// Set URL parameters
			vars := map[string]string{
				"organizationId":  tt.organizationID,
				"locationgroupId": tt.locationGroupID,
			}
			req = mux.SetURLVars(req, vars)

			// Create response recorder
			w := httptest.NewRecorder()

			// Create handler with mocked dependencies
			handler := DeleteHandlerWithDeps(tt.mockDeps())

			// Execute handler
			handler(w, req)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)
			assert.Contains(t, strings.ToLower(w.Body.String()), strings.ToLower(tt.expectedBody))
		})
	}
}

// Test_parseCreateAndUpdateLocationGroupsRequest tests the parseCreateAndUpdateLocationGroupsRequest function
func Test_parseCreateAndUpdateLocationGroupsRequest(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		requestBody    string
		expectedError  error
		expectedResult *CreateAndUpdateLocationGroupsRequest
	}{
		{
			name:           "valid_request",
			requestBody:    `{"name": "Test Location Group"}`,
			expectedError:  nil,
			expectedResult: &CreateAndUpdateLocationGroupsRequest{Name: "Test Location Group"},
		},
		{
			name:           "empty_name",
			requestBody:    `{"name": ""}`,
			expectedError:  ErrInvalidDescription,
			expectedResult: &CreateAndUpdateLocationGroupsRequest{Name: ""},
		},
		{
			name:           "whitespace_only_name",
			requestBody:    `{"name": "   "}`,
			expectedError:  ErrInvalidDescription,
			expectedResult: &CreateAndUpdateLocationGroupsRequest{Name: "   "},
		},

		{
			name:           "invalid_json",
			requestBody:    `{"name": "Test"`,
			expectedError:  ErrInvalidRequestBody,
			expectedResult: &CreateAndUpdateLocationGroupsRequest{},
		},
		{
			name:           "unknown_field",
			requestBody:    `{"name": "Test", "unknown": "field"}`,
			expectedError:  ErrUnexpectedFields,
			expectedResult: &CreateAndUpdateLocationGroupsRequest{Name: "Test"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create request with body
			req := httptest.NewRequest("POST", "/", strings.NewReader(tt.requestBody))
			req.Header.Set("Content-Type", "application/json")

			// Execute function
			result, err := parseCreateAndUpdateLocationGroupsRequest(req)

			// Assert results
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err)
			} else {
				assert.NoError(t, err)
			}

			if tt.expectedResult != nil {
				assert.Equal(t, tt.expectedResult.Name, result.Name)
			}
		})
	}
}

// Test_createLocationGroup tests the createLocationGroup function
func Test_createLocationGroup(t *testing.T) {
	t.Parallel()

	// Create test data
	testOrgID := uuid.New()
	testReq := &CreateAndUpdateLocationGroupsRequest{
		Name: "Test Location Group",
	}

	tests := []struct {
		name           string
		organizationID uuid.UUID
		request        *CreateAndUpdateLocationGroupsRequest
		mockDB         func() connect.DatabaseExecutor
		expectedError  error
		expectedResult *LocationGroupResponse
	}{
		{
			name:           "successful_creation",
			organizationID: testOrgID,
			request:        testReq,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Mock successful response
						response := dest.(*LocationGroupResponse)
						response.Id = uuid.New()
						response.OrganizationId = testOrgID
						response.Name = testReq.Name
						response.CreatedAt = time.Now().UTC()
						response.UpdatedAt = time.Now().UTC()
						return nil
					},
				}
			},
			expectedError:  nil,
			expectedResult: &LocationGroupResponse{},
		},
		{
			name:           "database_error",
			organizationID: testOrgID,
			request:        testReq,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return ErrDatabaseOperation
					},
				}
			},
			expectedError:  ErrDatabaseOperation,
			expectedResult: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Set up mock database
			mockDB := tt.mockDB()

			// Execute function
			result, err := createLocationGroup(mockDB, tt.organizationID, tt.request)

			// Assert results
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.organizationID, result.OrganizationId)
				assert.Equal(t, tt.request.Name, result.Name)
			}
		})
	}
}

// Test_getLocationGroupsByOrganizationID tests the getLocationGroupsByOrganizationID function
func Test_getLocationGroupsByOrganizationID(t *testing.T) {
	t.Parallel()

	// Create test data
	testOrgID := uuid.New()

	tests := []struct {
		name           string
		organizationID uuid.UUID
		mockDB         func() connect.DatabaseExecutor
		expectedError  error
		expectedResult *LocationGroupsResponse
	}{
		{
			name:           "successful_retrieval",
			organizationID: testOrgID,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Mock successful response
						locationGroups := dest.(*[]LocationGroupResponse)
						*locationGroups = []LocationGroupResponse{
							{
								Id:             uuid.New(),
								OrganizationId: testOrgID,
								Name:           "Test Location Group 1",
								CreatedAt:      time.Now().UTC(),
								UpdatedAt:      time.Now().UTC(),
							},
						}
						return nil
					},
				}
			},
			expectedError:  nil,
			expectedResult: &LocationGroupsResponse{},
		},
		{
			name:           "database_error",
			organizationID: testOrgID,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
						return ErrDatabaseOperation
					},
				}
			},
			expectedError:  ErrDatabaseOperation,
			expectedResult: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Set up mock database
			mockDB := tt.mockDB()

			// Execute function
			result, err := getLocationGroupsByOrganizationID(mockDB, tt.organizationID)

			// Assert results
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.NotNil(t, result.LocationGroups)
			}
		})
	}
}

// Test_updateLocationGroup tests the updateLocationGroup function
func Test_updateLocationGroup(t *testing.T) {
	t.Parallel()

	// Create test data
	testOrgID := uuid.New()
	testLocationGroupID := uuid.New()
	testReq := &CreateAndUpdateLocationGroupsRequest{
		Name: "Updated Location Group",
	}

	tests := []struct {
		name            string
		organizationID  uuid.UUID
		locationGroupID uuid.UUID
		request         *CreateAndUpdateLocationGroupsRequest
		mockDB          func() connect.DatabaseExecutor
		expectedError   error
		expectedResult  *LocationGroupResponse
	}{
		{
			name:            "successful_update",
			organizationID:  testOrgID,
			locationGroupID: testLocationGroupID,
			request:         testReq,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Mock successful response
						response := dest.(*LocationGroupResponse)
						response.Id = testLocationGroupID
						response.OrganizationId = testOrgID
						response.Name = testReq.Name
						response.CreatedAt = time.Now().UTC()
						response.UpdatedAt = time.Now().UTC()
						return nil
					},
				}
			},
			expectedError:  nil,
			expectedResult: &LocationGroupResponse{},
		},
		{
			name:            "location_group_not_found",
			organizationID:  testOrgID,
			locationGroupID: testLocationGroupID,
			request:         testReq,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return sql.ErrNoRows
					},
				}
			},
			expectedError:  ErrLocationGroupNotFound,
			expectedResult: nil,
		},
		{
			name:            "database_error",
			organizationID:  testOrgID,
			locationGroupID: testLocationGroupID,
			request:         testReq,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return ErrDatabaseOperation
					},
				}
			},
			expectedError:  ErrDatabaseOperation,
			expectedResult: &LocationGroupResponse{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Set up mock database
			mockDB := tt.mockDB()

			// Execute function
			result, err := updateLocationGroup(mockDB, tt.organizationID, tt.locationGroupID, tt.request)

			// Assert results
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err)
				if tt.expectedResult == nil {
					assert.Nil(t, result)
				} else {
					assert.NotNil(t, result)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.organizationID, result.OrganizationId)
				assert.Equal(t, tt.request.Name, result.Name)
			}
		})
	}
}

// Test_deleteLocationGroup tests the deleteLocationGroup function
func Test_deleteLocationGroup(t *testing.T) {
	t.Parallel()

	// Create test data
	testOrgID := uuid.New()
	testLocationGroupID := uuid.New()

	tests := []struct {
		name            string
		organizationID  uuid.UUID
		locationGroupID uuid.UUID
		mockDB          func() connect.DatabaseExecutor
		expectedError   error
	}{
		{
			name:            "successful_deletion",
			organizationID:  testOrgID,
			locationGroupID: testLocationGroupID,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return nil, nil
					},
				}
			},
			expectedError: nil,
		},
		{
			name:            "location_group_not_found",
			organizationID:  testOrgID,
			locationGroupID: testLocationGroupID,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return nil, sql.ErrNoRows
					},
				}
			},
			expectedError: ErrLocationGroupNotFound,
		},
		{
			name:            "database_error",
			organizationID:  testOrgID,
			locationGroupID: testLocationGroupID,
			mockDB: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return nil, ErrDatabaseOperation
					},
				}
			},
			expectedError: ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Set up mock database
			mockDB := tt.mockDB()

			// Execute function
			err := deleteLocationGroup(mockDB, tt.organizationID, tt.locationGroupID)

			// Assert results
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
