package locationgrouproleassignments

import (
	"time"

	"github.com/google/uuid"
)

type CreateAndUpdateLocationGroupRoleAssignmentRequest struct {
	RoleIDs []uuid.UUID `json:"roleIds" validate:"required,min=1"`
}

type LocationGroupRoleAssignmentResponse struct {
	LocationGroupRoleAssignment []LocationGroupRoleAssignment `json:"locationGroupRoleAssignment"`
}

type LocationGroupRoleAssignment struct {
	MembershipID    uuid.UUID `json:"membershipId" db:"membershipid"`
	LocationGroupID uuid.UUID `json:"locationGroupId" db:"locationgroupid"`
	RoleID          uuid.UUID `json:"roleId" db:"roleid"`
	CreatedAt       time.Time `json:"createdAt" db:"createdat"`
	UpdatedAt       time.Time `json:"updatedAt" db:"updatedat"`
}

// Enhanced user information for location group responses
type LocationGroupUser struct {
	UserID    uuid.UUID   `json:"userId" db:"userid"`
	FirstName string      `json:"firstName" db:"firstname"`
	LastName  string      `json:"lastName" db:"lastname"`
	UserName  string      `json:"userName" db:"username"`
	Email     string      `json:"email" db:"email"`
	RoleIDs   []uuid.UUID `json:"roleIds" db:"roleids"`
}

// Response for getting all users in a location group
type LocationGroupUsersResponse struct {
	Users []LocationGroupUser `json:"users"`
}

// Enhanced response for individual user assignments
type EnhancedLocationGroupRoleAssignment struct {
	MembershipID    uuid.UUID   `json:"membershipId" db:"membershipid"`
	LocationGroupID uuid.UUID   `json:"locationGroupId" db:"locationgroupid"`
	UserID          uuid.UUID   `json:"userId" db:"userid"`
	FirstName       string      `json:"firstName" db:"firstname"`
	LastName        string      `json:"lastName" db:"lastname"`
	UserName        string      `json:"userName" db:"username"`
	Email           string      `json:"email" db:"email"`
	RoleIDs         []uuid.UUID `json:"roleIds" db:"roleids"`
	CreatedAt       time.Time   `json:"createdAt" db:"createdat"`
	UpdatedAt       time.Time   `json:"updatedAt" db:"updatedat"`
}

type EnhancedLocationGroupRoleAssignmentResponse struct {
	LocationGroupRoleAssignment []EnhancedLocationGroupRoleAssignment `json:"locationGroupRoleAssignment"`
}
