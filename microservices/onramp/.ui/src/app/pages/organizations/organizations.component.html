<div class="oem-container">
  <div class="form-header">
    <div class="gr-header">
      <div class="title-oem">
        <h1 id="title-organizations">All Organizations</h1>
      </div>
      <div class="form-breadcrumb">
        <nz-breadcrumb>
          <nz-breadcrumb-item>
            Synapse
          </nz-breadcrumb-item>
          <nz-breadcrumb-item>
            All Organizations
          </nz-breadcrumb-item>
        </nz-breadcrumb>
      </div>
    </div>
    <div class="form-btn">
      <button nz-button nzType="primary" class="add-btn br-8 h-4" (click)="openAddPopup()">
        Create Organization
      </button>
    </div>
  </div>
  <div class="page-description-org">
    <span>This table shows all organizations that are in the system as well as some related statistics.</span>
  </div>

  <!-- keep for later use -->
  <!-- <div class="form-header">
    <div class="form-search">
      <nz-input-group nzSearch (nzOnSearch)="searchTable()" [nzAddOnAfter]="suffixIconButton">
        <input [(ngModel)]="searchTerm" (input)="searchTable()" (nzOnSearch)="searchTable()" type="text" nz-input
          placeholder="Filter Name" />
      </nz-input-group>
      <ng-template #suffixIconButton>
        <button nz-button nzType="primary" nzSearch><nz-icon nzType="search" /></button>
      </ng-template>
    </div>
  </div> -->
  <nz-table #basicTable nzBordered id="organization-list" [nzData]="filteredList" nzShowSizeChanger nzShowPagination
    [nzLoading]="isTableLoading">
    <thead>
      <tr>
        <th nzWidth="20%">Name</th>
        <th nzWidth="20%">Description</th>
        <th nzWidth="20%">Organization Type</th>
        <th nzWidth="20%">Created</th>
        <th nzWidth="20%">Last Updated</th>
        <th nzWidth="8%">Action</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of basicTable.data; let i = index"
        [ngClass]="data.id === highlightedRowId ? 'highlight-row' : 'default-row'">
        <td>
          <a (click)="openDetailModal(data)">{{ data.name }}</a>
        </td>
        <td>{{data.description}}</td>
        <td>{{data.orgtypeidentifier}}</td>
        <td>{{data.createdat | date: 'MM-dd-yyyy HH:mm:ss' : 'UTC'}}</td>
        <td>{{data.updatedat | date: 'MM-dd-yyyy HH:mm:ss' : 'UTC'}}</td>
        <td>
          <div class="btn-action">
            <button id="btn-permission" class="br-8" nz-button nzType="default" (click)="onClickPermissions(data.id)">
              Permissions
            </button>
            <button id="btn-users" class="br-8" nz-button nzType="default" (click)="onClickUsers(data)">Users</button>
            <button *ngIf="hasSynapseManagerAllOrganizations" class="edit-btn br-8" nz-button nzType="primary"
              (click)="openEditPopup(i)" nz-tooltip nzTooltipTitle="Edit">
              <nz-icon nzType="edit" nzTheme="outline" />
            </button>
            <button *ngIf="hasSynapseDeleteAllOrganizations" class="br-8" nz-button nzType="primary"
              (click)="handleDeleteOrg(data)" nzDanger nz-tooltip nzTooltipTitle="Delete">
              <nz-icon nzType="delete" nzTheme="outline" />
            </button>
          </div>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <app-edit-add [isVisible]="isModalVisible" [isEditMode]="isEditMode" [isDetail]="isDetailOrg" [data]="currentOEM"
    (close)="closeModal()" (confirm)="handleModalSave($event)" (delete)="handleDeleteOnModal($event)">
  </app-edit-add>
</div>