package organization

import (
	"time"

	"github.com/google/uuid"
)

type Membership struct {
	ID             uuid.UUID  `json:"membershipId"`
	UserID         uuid.UUID  `json:"userId"`
	OrganizationID uuid.UUID  `json:"organizationId"`
	FirstName      string     `json:"firstName"`
	LastName       string     `json:"lastName"`
	UserName       string     `json:"userName"`
	Email          string     `json:"email"`
	AuthMethod     string     `json:"authMethod"`
	OrgRole        string     `json:"orgRole"`
	LastLogin      *time.Time `json:"lastLogin"`
}
