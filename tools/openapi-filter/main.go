// Command openapi-filter filters a swagger.json by required tags and prunes unused components.
// Usage:
//
//	go run /tools/openapi-filter/main.go \
//	  -in ./docs/swagger.json \
//	  -out ./openapi/broker.yaml \
//	  -require broker [-require v2] ...
package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"sort"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"gopkg.in/yaml.v3"
)

func main() {
	in := flag.String("in", "", "path to input swagger.json or openapi yaml/json")
	out := flag.String("out", "", "path to output openapi yaml")
	var requires multi
	flag.Var(&requires, "require", "required tag (repeatable)")
	flag.Parse()

	if *in == "" || *out == "" || len(requires) == 0 {
		fmt.Fprintf(os.Stderr, "usage: -in <file> -out <file> -require tag [-require tag] ...\n")
		os.Exit(2)
	}

	loader := &openapi3.Loader{IsExternalRefsAllowed: true}
	doc, err := loader.LoadFromFile(*in)
	if err != nil {
		fail("load: %v", err)
	}

	// Normalize to ensure components exist.
	if doc.Components == nil {
		doc.Components = &openapi3.Components{}
	}
	if doc.Components.Schemas == nil {
		doc.Components.Schemas = openapi3.Schemas{}
	}
	if doc.Paths == nil {
		doc.Paths = openapi3.NewPaths()
	}

	req := map[string]struct{}{}
	for _, t := range requires {
		req[strings.ToLower(strings.TrimSpace(t))] = struct{}{}
	}

	// 1) Filter operations by tags.
	kept := openapi3.NewPaths()
	for pth, item := range doc.Paths.Map() {
		if item == nil {
			continue
		}
		ops := map[string]*openapi3.Operation{
			"GET":     item.Get,
			"PUT":     item.Put,
			"POST":    item.Post,
			"DELETE":  item.Delete,
			"OPTIONS": item.Options,
			"HEAD":    item.Head,
			"PATCH":   item.Patch,
			"TRACE":   item.Trace,
		}
		keepAny := false
		for method, op := range ops {
			if op == nil {
				continue
			}
			if hasAllRequiredTags(op.Tags, req) {
				keepAny = true
				continue
			}
			// drop operation
			setOperation(item, method, nil)
		}
		if keepAny {
			kept.Set(pth, item)
		}
	}
	doc.Paths = kept

	// 2) Prune unreferenced components (schemas only; extend if you need params/responses).
	usedSchemas := collectUsedSchemas(doc)
	for name := range doc.Components.Schemas {
		if !usedSchemas[name] {
			delete(doc.Components.Schemas, name)
		}
	}

	// Optional: sort paths for stable output.
	keys := make([]string, 0, doc.Paths.Len())
	for k := range doc.Paths.Map() {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	sortedPaths := openapi3.NewPaths()
	for _, k := range keys {
		sortedPaths.Set(k, doc.Paths.Map()[k])
	}
	doc.Paths = sortedPaths

	// Save YAML
	v, err := doc.MarshalYAML()
	if err != nil {
		fail("marshal: %v", err)
	}
	outBytes, err := yaml.Marshal(v)
	if err != nil {
		fail("yaml marshal: %v", err)
	}
	if err := os.MkdirAll(dirOf(*out), 0o755); err != nil {
		fail("mkdir: %v", err)
	}
	if err := os.WriteFile(*out, outBytes, 0o644); err != nil {
		fail("write: %v", err)
	}
}

type multi []string

func (m *multi) Set(v string) error { *m = append(*m, v); return nil }
func (m *multi) String() string     { b, _ := json.Marshal([]string(*m)); return string(b) }

func hasAllRequiredTags(tags []string, req map[string]struct{}) bool {
	have := map[string]struct{}{}
	for _, t := range tags {
		have[strings.ToLower(strings.TrimSpace(t))] = struct{}{}
	}
	for k := range req {
		if _, ok := have[k]; !ok {
			return false
		}
	}
	return true
}

func setOperation(item *openapi3.PathItem, method string, op *openapi3.Operation) {
	switch strings.ToUpper(method) {
	case "GET":
		item.Get = op
	case "PUT":
		item.Put = op
	case "POST":
		item.Post = op
	case "DELETE":
		item.Delete = op
	case "OPTIONS":
		item.Options = op
	case "HEAD":
		item.Head = op
	case "PATCH":
		item.Patch = op
	case "TRACE":
		item.Trace = op
	}
}

func collectUsedSchemas(doc *openapi3.T) map[string]bool {
	used := map[string]bool{}
	walkRef := func(ref *openapi3.SchemaRef) {}
	_ = walkRef

	// Helper to mark a schema ref (local component only).
	mark := func(ref *openapi3.SchemaRef) {
		if ref == nil || ref.Ref == "" {
			return
		}
		// Local component refs look like "#/components/schemas/Name"
		const prefix = "#/components/schemas/"
		if strings.HasPrefix(ref.Ref, prefix) {
			name := strings.TrimPrefix(ref.Ref, prefix)
			used[name] = true
		}
	}

	// Scan responses and request bodies.
	for _, item := range doc.Paths.Map() {
		for _, op := range []*openapi3.Operation{
			item.Get, item.Put, item.Post, item.Delete, item.Options, item.Head, item.Patch, item.Trace,
		} {
			if op == nil {
				continue
			}
			if op.RequestBody != nil && op.RequestBody.Value != nil {
				for _, mt := range op.RequestBody.Value.Content {
					if mt.Schema != nil {
						mark(mt.Schema)
					}
				}
			}
			if op.Responses != nil {
				for _, resp := range op.Responses.Map() {
					if resp.Value == nil {
						continue
					}
					for _, mt := range resp.Value.Content {
						if mt.Schema != nil {
							mark(mt.Schema)
						}
					}
				}
			}
			// Parameters
			for _, p := range op.Parameters {
				if p.Value != nil && p.Value.Schema != nil {
					mark(p.Value.Schema)
				}
			}
		}
	}
	return used
}

func dirOf(p string) string {
	i := strings.LastIndexByte(p, '/')
	if i < 0 {
		return "."
	}
	return p[:i]
}

func fail(f string, a ...any) {
	fmt.Fprintf(os.Stderr, f+"\n", a...)
	os.Exit(1)
}
