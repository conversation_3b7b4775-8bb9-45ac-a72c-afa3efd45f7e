package organization

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"synapse-its.com/shared/rest/domain/organization"
)

// MockMembershipService is a mock implementation of the membershipService interface
type MockMembershipService struct {
	mock.Mock
}

func (m *MockMembershipService) GetUsersByOrganizationID(organizationID uuid.UUID) ([]*organization.Membership, error) {
	args := m.Called(organizationID)
	return args.Get(0).([]*organization.Membership), args.Error(1)
}

func (m *MockMembershipService) UpdateUserRoleInOrganization(userID uuid.UUID, organizationID uuid.UUID, roleID uuid.UUID) error {
	args := m.Called(userID, organizationID, roleID)
	return args.Error(0)
}

func (m *MockMembershipService) DeleteUserFromOrganization(userID uuid.UUID, organizationID uuid.UUID) error {
	args := m.Called(userID, organizationID)
	return args.Error(0)
}

func TestHandler_getUsersByOrganizationID(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name            string
		organizationID  string
		mockMemberships []*organization.Membership
		mockError       error
		expectedStatus  int
		expectedBody    interface{}
	}{
		{
			name:           "success_with_users",
			organizationID: "550e8400-e29b-41d4-a716-************",
			mockMemberships: []*organization.Membership{
				{
					ID:             uuid.New(),
					UserID:         uuid.New(),
					OrganizationID: uuid.MustParse("550e8400-e29b-41d4-a716-************"),
					FirstName:      "John",
					LastName:       "Doe",
					UserName:       "johndoe",
					Email:          "<EMAIL>",
					AuthMethod:     "USERNAME_PASSWORD",
					OrgRole:        "MEMBER",
					LastLogin:      &time.Time{},
				},
				{
					ID:             uuid.New(),
					UserID:         uuid.New(),
					OrganizationID: uuid.MustParse("550e8400-e29b-41d4-a716-************"),
					FirstName:      "Jane",
					LastName:       "Smith",
					UserName:       "janesmith",
					Email:          "<EMAIL>",
					AuthMethod:     "OIDC",
					OrgRole:        "ADMIN",
					LastLogin:      nil,
				},
			},
			mockError:      nil,
			expectedStatus: http.StatusOK,
			expectedBody:   []*organization.Membership{},
		},
		{
			name:            "success_with_no_users",
			organizationID:  "550e8400-e29b-41d4-a716-************",
			mockMemberships: []*organization.Membership{},
			mockError:       nil,
			expectedStatus:  http.StatusOK,
			expectedBody:    []*organization.Membership{},
		},
		{
			name:            "invalid_organization_id",
			organizationID:  "invalid-uuid",
			mockMemberships: nil,
			mockError:       nil,
			expectedStatus:  http.StatusBadRequest,
			expectedBody:    nil,
		},
		{
			name:            "service_error",
			organizationID:  "550e8400-e29b-41d4-a716-************",
			mockMemberships: nil,
			mockError:       errors.New("database connection failed"),
			expectedStatus:  http.StatusInternalServerError,
			expectedBody:    nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService := &MockMembershipService{}
			handler := &Handler{
				membershipBiz: mockService,
			}

			// Create request
			req := httptest.NewRequest(http.MethodGet, "/organizations/"+tt.organizationID+"/users", nil)

			// Set URL variables for gorilla/mux
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.organizationID,
			})

			// Create response recorder
			w := httptest.NewRecorder()

			// Set up mock expectations if organization ID is valid
			if tt.organizationID != "invalid-uuid" {
				orgID := uuid.MustParse(tt.organizationID)
				mockService.On("GetUsersByOrganizationID", orgID).Return(tt.mockMemberships, tt.mockError)
			}

			// Execute handler
			handler.getUsersByOrganizationID(w, req)

			// Assert response status
			assert.Equal(t, tt.expectedStatus, w.Code)

			// Assert response body for success cases
			if tt.expectedStatus == http.StatusOK {
				var responseBody map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &responseBody)
				assert.NoError(t, err)

				// Check response structure
				assert.Equal(t, "success", responseBody["status"])
				assert.Equal(t, "Request Succeeded", responseBody["message"])
				assert.Equal(t, float64(http.StatusOK), responseBody["code"])

				// Check data field
				data, exists := responseBody["data"]
				assert.True(t, exists, "Response should have 'data' field")

				// For success cases, verify the data structure
				if tt.mockMemberships != nil {
					dataBytes, err := json.Marshal(data)
					assert.NoError(t, err)

					var memberships []*organization.Membership
					err = json.Unmarshal(dataBytes, &memberships)
					assert.NoError(t, err)
					assert.Len(t, memberships, len(tt.mockMemberships))
				}
			}

			// Verify mock expectations
			mockService.AssertExpectations(t)
		})
	}
}

func TestHandler_updateUserRoleInOrganization(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		organizationID string
		userID         string
		requestBody    interface{}
		mockError      error
		expectedStatus int
	}{
		{
			name:           "success",
			organizationID: "550e8400-e29b-41d4-a716-************",
			userID:         "123e4567-e89b-12d3-a456-************",
			requestBody: map[string]string{
				"roleId": "987fcdeb-51a2-43d1-b654-************",
			},
			mockError:      nil,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "invalid_organization_id",
			organizationID: "invalid-uuid",
			userID:         "123e4567-e89b-12d3-a456-************",
			requestBody: map[string]string{
				"roleId": "987fcdeb-51a2-43d1-b654-************",
			},
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid_user_id",
			organizationID: "550e8400-e29b-41d4-a716-************",
			userID:         "invalid-uuid",
			requestBody: map[string]string{
				"roleId": "987fcdeb-51a2-43d1-b654-************",
			},
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid_request_body",
			organizationID: "550e8400-e29b-41d4-a716-************",
			userID:         "123e4567-e89b-12d3-a456-************",
			requestBody:    "invalid json",
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "user_not_found",
			organizationID: "550e8400-e29b-41d4-a716-************",
			userID:         "123e4567-e89b-12d3-a456-************",
			requestBody: map[string]string{
				"roleId": "987fcdeb-51a2-43d1-b654-************",
			},
			mockError:      organization.ErrUserNotFound,
			expectedStatus: http.StatusNotFound,
		},
		{
			name:           "service_error",
			organizationID: "550e8400-e29b-41d4-a716-************",
			userID:         "123e4567-e89b-12d3-a456-************",
			requestBody: map[string]string{
				"roleId": "987fcdeb-51a2-43d1-b654-************",
			},
			mockError:      errors.New("database connection failed"),
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService := &MockMembershipService{}
			handler := &Handler{
				membershipBiz: mockService,
			}

			// Create request body
			var bodyBytes []byte
			var err error
			if str, ok := tt.requestBody.(string); ok {
				bodyBytes = []byte(str)
			} else {
				bodyBytes, err = json.Marshal(tt.requestBody)
				assert.NoError(t, err)
			}

			// Create request
			req := httptest.NewRequest(http.MethodPatch, "/organizations/"+tt.organizationID+"/users/"+tt.userID, bytes.NewBuffer(bodyBytes))
			req.Header.Set("Content-Type", "application/json")

			// Set URL variables for gorilla/mux
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.organizationID,
				"userId":         tt.userID,
			})

			// Create response recorder
			w := httptest.NewRecorder()

			// Set up mock expectations if IDs are valid
			if tt.organizationID != "invalid-uuid" && tt.userID != "invalid-uuid" && tt.expectedStatus != http.StatusBadRequest {
				orgID := uuid.MustParse(tt.organizationID)
				userID := uuid.MustParse(tt.userID)

				// Extract role ID from request body for mock expectation
				if mapBody, ok := tt.requestBody.(map[string]string); ok {
					if roleIDStr, exists := mapBody["roleId"]; exists {
						roleID := uuid.MustParse(roleIDStr)
						mockService.On("UpdateUserRoleInOrganization", userID, orgID, roleID).Return(tt.mockError)
					}
				}
			}

			// Execute handler
			handler.updateUserRoleInOrganization(w, req)

			// Assert response status
			assert.Equal(t, tt.expectedStatus, w.Code)

			// Verify mock expectations
			mockService.AssertExpectations(t)
		})
	}
}

func TestHandler_deleteUserFromOrganization(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		organizationID string
		userID         string
		mockError      error
		expectedStatus int
	}{
		{
			name:           "success",
			organizationID: "550e8400-e29b-41d4-a716-************",
			userID:         "123e4567-e89b-12d3-a456-************",
			mockError:      nil,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "invalid_organization_id",
			organizationID: "invalid-uuid",
			userID:         "123e4567-e89b-12d3-a456-************",
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid_user_id",
			organizationID: "550e8400-e29b-41d4-a716-************",
			userID:         "invalid-uuid",
			mockError:      nil,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "service_error",
			organizationID: "550e8400-e29b-41d4-a716-************",
			userID:         "123e4567-e89b-12d3-a456-************",
			mockError:      errors.New("database connection failed"),
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService := &MockMembershipService{}
			handler := &Handler{
				membershipBiz: mockService,
			}

			// Create request
			req := httptest.NewRequest(http.MethodDelete, "/organizations/"+tt.organizationID+"/users/"+tt.userID, nil)

			// Set URL variables for gorilla/mux
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.organizationID,
				"userId":         tt.userID,
			})

			// Create response recorder
			w := httptest.NewRecorder()

			// Set up mock expectations if IDs are valid
			if tt.organizationID != "invalid-uuid" && tt.userID != "invalid-uuid" {
				orgID := uuid.MustParse(tt.organizationID)
				userID := uuid.MustParse(tt.userID)
				mockService.On("DeleteUserFromOrganization", userID, orgID).Return(tt.mockError)
			}

			// Execute handler
			handler.deleteUserFromOrganization(w, req)

			// Assert response status
			assert.Equal(t, tt.expectedStatus, w.Code)

			// Verify mock expectations
			mockService.AssertExpectations(t)
		})
	}
}

func TestHandler_Integration(t *testing.T) {
	t.Parallel()

	// Test that all handlers work together correctly with the same service
	mockService := &MockMembershipService{}
	handler := &Handler{
		membershipBiz: mockService,
	}

	// Test data
	orgID := uuid.New()
	userID := uuid.New()
	roleID := uuid.New()

	// Set up mock expectations for all operations
	mockService.On("GetUsersByOrganizationID", orgID).Return([]*organization.Membership{}, nil)
	mockService.On("UpdateUserRoleInOrganization", userID, orgID, roleID).Return(nil)
	mockService.On("DeleteUserFromOrganization", userID, orgID).Return(nil)

	// Test getUsersByOrganizationID
	req1 := httptest.NewRequest(http.MethodGet, "/organizations/"+orgID.String()+"/users", nil)
	req1 = mux.SetURLVars(req1, map[string]string{"organizationId": orgID.String()})
	w1 := httptest.NewRecorder()
	handler.getUsersByOrganizationID(w1, req1)
	assert.Equal(t, http.StatusOK, w1.Code)

	// Test updateUserRoleInOrganization
	requestBody := map[string]string{"roleId": roleID.String()}
	bodyBytes, _ := json.Marshal(requestBody)
	req2 := httptest.NewRequest(http.MethodPatch, "/organizations/"+orgID.String()+"/users/"+userID.String(), bytes.NewBuffer(bodyBytes))
	req2.Header.Set("Content-Type", "application/json")
	req2 = mux.SetURLVars(req2, map[string]string{
		"organizationId": orgID.String(),
		"userId":         userID.String(),
	})
	w2 := httptest.NewRecorder()
	handler.updateUserRoleInOrganization(w2, req2)
	assert.Equal(t, http.StatusOK, w2.Code)

	// Test deleteUserFromOrganization
	req3 := httptest.NewRequest(http.MethodDelete, "/organizations/"+orgID.String()+"/users/"+userID.String(), nil)
	req3 = mux.SetURLVars(req3, map[string]string{
		"organizationId": orgID.String(),
		"userId":         userID.String(),
	})
	w3 := httptest.NewRecorder()
	handler.deleteUserFromOrganization(w3, req3)
	assert.Equal(t, http.StatusOK, w3.Code)

	// Verify all mock expectations were met
	mockService.AssertExpectations(t)
}

func TestHandler_EdgeCases(t *testing.T) {
	t.Parallel()

	mockService := &MockMembershipService{}
	handler := &Handler{
		membershipBiz: mockService,
	}

	t.Run("empty_organization_id", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/organizations//users", nil)
		req = mux.SetURLVars(req, map[string]string{"organizationId": ""})
		w := httptest.NewRecorder()

		handler.getUsersByOrganizationID(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("empty_user_id", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodDelete, "/organizations/"+uuid.New().String()+"/users/", nil)
		req = mux.SetURLVars(req, map[string]string{
			"organizationId": uuid.New().String(),
			"userId":         "",
		})
		w := httptest.NewRecorder()

		handler.deleteUserFromOrganization(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("nil_uuid", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/organizations/00000000-0000-0000-0000-000000000000/users", nil)
		req = mux.SetURLVars(req, map[string]string{"organizationId": "00000000-0000-0000-0000-000000000000"})
		w := httptest.NewRecorder()

		// Set up mock expectation for nil UUID (which is valid)
		nilUUID := uuid.Nil
		mockService.On("GetUsersByOrganizationID", nilUUID).Return([]*organization.Membership{}, nil)

		handler.getUsersByOrganizationID(w, req)
		assert.Equal(t, http.StatusOK, w.Code)

		// Verify mock expectations
		mockService.AssertExpectations(t)
	})
}
