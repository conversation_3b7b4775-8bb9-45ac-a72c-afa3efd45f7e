package invites

import (
	"context"
	"errors"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/bqbatcher"
	"synapse-its.com/shared/mocks/dbexecutor"
	"synapse-its.com/shared/mocks/pubsub"
	"synapse-its.com/shared/pubsubdata"
)

// Test_CreateInviteHandler_Integration tests the CreateInviteHandler with integration scenarios
func Test_CreateInviteHandler_Integration(t *testing.T) {
	t.Parallel()

	orgID := "123e4567-e89b-12d3-a456-************"

	tests := []struct {
		name           string
		method         string
		body           string
		orgID          string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name:           "successful_invite_creation",
			method:         "POST",
			body:           `{"email": "<EMAIL>", "inviterid": "123e4567-e89b-12d3-a456-************", "organizationrole": "123e4567-e89b-12d3-a456-************"}`,
			orgID:          orgID,
			deps:           setupTestDeps(),
			expectedStatus: 200,
		},
		{
			name:           "invalid_json_format",
			method:         "POST",
			body:           `{invalid json}`,
			orgID:          orgID,
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
		{
			name:           "missing_email_field",
			method:         "POST",
			body:           `{"inviterid": "123e4567-e89b-12d3-a456-************", "organizationrole": "123e4567-e89b-12d3-a456-************"}`,
			orgID:          orgID,
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
		{
			name:   "database_connection_failure",
			method: "POST",
			body:   `{"email": "<EMAIL>", "inviterid": "123e4567-e89b-12d3-a456-************", "organizationrole": "123e4567-e89b-12d3-a456-************"}`,
			orgID:  orgID,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return nil, errors.New("database connection failed")
				},
			},
			expectedStatus: 500,
		},
		{
			name:           "invalid_organization_id_format",
			method:         "POST",
			body:           `{"email": "<EMAIL>", "inviterid": "123e4567-e89b-12d3-a456-************", "organizationrole": "123e4567-e89b-12d3-a456-************"}`,
			orgID:          "invalid-uuid-format",
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create request with mux router to set URL parameters
			req := httptest.NewRequest(tt.method, "/api/organization/"+tt.orgID+"/invites", strings.NewReader(tt.body))
			req.Header.Set("Content-Type", "application/json")

			// Set up mux vars
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.orgID,
			})

			w := httptest.NewRecorder()

			handler := CreateInviteHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// Test_CreateInviteHandler_ErrorScenarios tests specific error scenarios for CreateInviteHandler
func Test_CreateInviteHandler_ErrorScenarios(t *testing.T) {
	t.Parallel()

	orgID := "123e4567-e89b-12d3-a456-************"

	tests := []struct {
		name           string
		body           string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name: "bq_batch_error",
			body: `{"email": "<EMAIL>", "inviterid": "123e4567-e89b-12d3-a456-************", "organizationrole": "123e4567-e89b-12d3-a456-************"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, errors.New("bq batch error")
				},
			},
			expectedStatus: 500,
		},
		{
			name: "token_generation_error",
			body: `{"email": "<EMAIL>", "inviterid": "123e4567-e89b-12d3-a456-************", "organizationrole": "123e4567-e89b-12d3-a456-************"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return bqbatcher.FakeBatch(ctx)
				},
				CreateInvite: func(pg connect.DatabaseExecutor, orgID uuid.UUID, tokenHash string, req CreateInviteRequest) (*UserInvite, error) {
					return &UserInvite{
						ID:                     uuid.New(),
						OrganizationIdentifier: orgID,
						TokenHash:              tokenHash,
						Email:                  req.Email,
						InviterID:              req.InviterID,
						CustomRoleID:           req.OrganizationRole,
						Status:                 StatusPending,
						Message:                req.Message,
						RequireSSO:             false,
						RetryCount:             0,
						Created:                time.Now().UTC(),
						Updated:                time.Now().UTC(),
					}, nil
				},
				GenerateToken: func(length uint) (string, error) {
					return "", errors.New("token generation failed")
				},
				HashString: func(input string) string {
					return "hashed-" + input
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				RenderEmailTemplate: func(data EmailTemplateData) (string, error) {
					return "<html>Test Email</html>", nil
				},
				RenderEmailSubject: func(data EmailTemplateData) (string, error) {
					return "You're Invited to " + data.AppName + "!", nil
				},
				PublishEmailNotification: func(ctx context.Context, pubsubClient connect.PsClient, topicName string, message pubsubdata.NotificationRequest) error {
					return nil
				},
				LogInviteEvent: func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
					return nil
				},
			},
			expectedStatus: 500,
		},
		{
			name: "get_organization_name_error",
			body: `{"email": "<EMAIL>", "inviterid": "123e4567-e89b-12d3-a456-************", "organizationrole": "123e4567-e89b-12d3-a456-************"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return bqbatcher.FakeBatch(ctx)
				},
				CreateInvite: func(pg connect.DatabaseExecutor, orgID uuid.UUID, tokenHash string, req CreateInviteRequest) (*UserInvite, error) {
					return &UserInvite{
						ID:                     uuid.New(),
						OrganizationIdentifier: orgID,
						TokenHash:              tokenHash,
						Email:                  req.Email,
						InviterID:              req.InviterID,
						CustomRoleID:           req.OrganizationRole,
						Status:                 StatusPending,
						Message:                req.Message,
						RequireSSO:             false,
						RetryCount:             0,
						Created:                time.Now().UTC(),
						Updated:                time.Now().UTC(),
					}, nil
				},
				GenerateToken: func(length uint) (string, error) {
					return "test-token", nil
				},
				HashString: func(input string) string {
					return "hashed-" + input
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "", errors.New("database error getting organization name")
				},
				RenderEmailTemplate: func(data EmailTemplateData) (string, error) {
					return "<html>Test Email</html>", nil
				},
				RenderEmailSubject: func(data EmailTemplateData) (string, error) {
					return "You're Invited to " + data.AppName + "!", nil
				},
				PublishEmailNotification: func(ctx context.Context, pubsubClient connect.PsClient, topicName string, message pubsubdata.NotificationRequest) error {
					return nil
				},
				LogInviteEvent: func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
					return nil
				},
			},
			expectedStatus: 500,
		},
		{
			name: "create_invite_error",
			body: `{"email": "<EMAIL>", "inviterid": "123e4567-e89b-12d3-a456-************", "organizationrole": "123e4567-e89b-12d3-a456-************"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return bqbatcher.FakeBatch(ctx)
				},
				CreateInvite: func(pg connect.DatabaseExecutor, orgID uuid.UUID, tokenHash string, req CreateInviteRequest) (*UserInvite, error) {
					return nil, errors.New("database error creating invite")
				},
				GenerateToken: func(length uint) (string, error) {
					return "test-token", nil
				},
				HashString: func(input string) string {
					return "hashed-" + input
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				RenderEmailTemplate: func(data EmailTemplateData) (string, error) {
					return "<html>Test Email</html>", nil
				},
				RenderEmailSubject: func(data EmailTemplateData) (string, error) {
					return "You're Invited to " + data.AppName + "!", nil
				},
				PublishEmailNotification: func(ctx context.Context, pubsubClient connect.PsClient, topicName string, message pubsubdata.NotificationRequest) error {
					return nil
				},
				LogInviteEvent: func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
					return nil
				},
			},
			expectedStatus: 500,
		},
		{
			name: "log_invite_event_error",
			body: `{"email": "<EMAIL>", "inviterid": "123e4567-e89b-12d3-a456-************", "organizationrole": "123e4567-e89b-12d3-a456-************"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return bqbatcher.FakeBatch(ctx)
				},
				CreateInvite: func(pg connect.DatabaseExecutor, orgID uuid.UUID, tokenHash string, req CreateInviteRequest) (*UserInvite, error) {
					return &UserInvite{
						ID:                     uuid.New(),
						OrganizationIdentifier: orgID,
						TokenHash:              tokenHash,
						Email:                  req.Email,
						InviterID:              req.InviterID,
						CustomRoleID:           req.OrganizationRole,
						Status:                 StatusPending,
						Message:                req.Message,
						RequireSSO:             false,
						RetryCount:             0,
						Created:                time.Now().UTC(),
						Updated:                time.Now().UTC(),
					}, nil
				},
				GenerateToken: func(length uint) (string, error) {
					return "test-token", nil
				},
				HashString: func(input string) string {
					return "hashed-" + input
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				RenderEmailTemplate: func(data EmailTemplateData) (string, error) {
					return "<html>Test Email</html>", nil
				},
				RenderEmailSubject: func(data EmailTemplateData) (string, error) {
					return "You're Invited to " + data.AppName + "!", nil
				},
				PublishEmailNotification: func(ctx context.Context, pubsubClient connect.PsClient, topicName string, message pubsubdata.NotificationRequest) error {
					return nil
				},
				LogInviteEvent: func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
					return errors.New("failed to log invite event")
				},
			},
			expectedStatus: 500,
		},
		{
			name: "success_with_custom_message",
			body: `{"email": "<EMAIL>", "inviterid": "123e4567-e89b-12d3-a456-************", "organizationrole": "123e4567-e89b-12d3-a456-************", "message": "Custom invite message"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return bqbatcher.FakeBatch(ctx)
				},
				CreateInvite: func(pg connect.DatabaseExecutor, orgID uuid.UUID, tokenHash string, req CreateInviteRequest) (*UserInvite, error) {
					return &UserInvite{
						ID:                     uuid.New(),
						OrganizationIdentifier: orgID,
						TokenHash:              tokenHash,
						Email:                  req.Email,
						InviterID:              req.InviterID,
						CustomRoleID:           req.OrganizationRole,
						Status:                 StatusPending,
						Message:                req.Message,
						RequireSSO:             false,
						RetryCount:             0,
						Created:                time.Now().UTC(),
						Updated:                time.Now().UTC(),
					}, nil
				},
				GenerateToken: func(length uint) (string, error) {
					return "test-token", nil
				},
				HashString: func(input string) string {
					return "hashed-" + input
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				RenderEmailTemplate: func(data EmailTemplateData) (string, error) {
					return "<html>Test Email</html>", nil
				},
				RenderEmailSubject: func(data EmailTemplateData) (string, error) {
					return "You're Invited to " + data.AppName + "!", nil
				},
				PublishEmailNotification: func(ctx context.Context, pubsubClient connect.PsClient, topicName string, message pubsubdata.NotificationRequest) error {
					return nil
				},
				LogInviteEvent: func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
					return nil
				},
			},
			expectedStatus: 200,
		},
		{
			name: "render_email_template_error",
			body: `{"email": "<EMAIL>", "inviterid": "123e4567-e89b-12d3-a456-************", "organizationrole": "123e4567-e89b-12d3-a456-************"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return bqbatcher.FakeBatch(ctx)
				},
				CreateInvite: func(pg connect.DatabaseExecutor, orgID uuid.UUID, tokenHash string, req CreateInviteRequest) (*UserInvite, error) {
					return &UserInvite{
						ID:                     uuid.New(),
						OrganizationIdentifier: orgID,
						TokenHash:              tokenHash,
						Email:                  req.Email,
						InviterID:              req.InviterID,
						CustomRoleID:           req.OrganizationRole,
						Status:                 StatusPending,
						Message:                req.Message,
						RequireSSO:             false,
						RetryCount:             0,
						Created:                time.Now().UTC(),
						Updated:                time.Now().UTC(),
					}, nil
				},
				GenerateToken: func(length uint) (string, error) {
					return "test-token", nil
				},
				HashString: func(input string) string {
					return "hashed-" + input
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				RenderEmailTemplate: func(data EmailTemplateData) (string, error) {
					return "", errors.New("failed to render email template")
				},
				RenderEmailSubject: func(data EmailTemplateData) (string, error) {
					return "You're Invited to " + data.AppName + "!", nil
				},
				PublishEmailNotification: func(ctx context.Context, pubsubClient connect.PsClient, topicName string, message pubsubdata.NotificationRequest) error {
					return nil
				},
				LogInviteEvent: func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
					return nil
				},
			},
			expectedStatus: 500,
		},
		{
			name: "render_email_subject_error",
			body: `{"email": "<EMAIL>", "inviterid": "123e4567-e89b-12d3-a456-************", "organizationrole": "123e4567-e89b-12d3-a456-************"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return bqbatcher.FakeBatch(ctx)
				},
				CreateInvite: func(pg connect.DatabaseExecutor, orgID uuid.UUID, tokenHash string, req CreateInviteRequest) (*UserInvite, error) {
					return &UserInvite{
						ID:                     uuid.New(),
						OrganizationIdentifier: orgID,
						TokenHash:              tokenHash,
						Email:                  req.Email,
						InviterID:              req.InviterID,
						CustomRoleID:           req.OrganizationRole,
						Status:                 StatusPending,
						Message:                req.Message,
						RequireSSO:             false,
						RetryCount:             0,
						Created:                time.Now().UTC(),
						Updated:                time.Now().UTC(),
					}, nil
				},
				GenerateToken: func(length uint) (string, error) {
					return "test-token", nil
				},
				HashString: func(input string) string {
					return "hashed-" + input
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				RenderEmailTemplate: func(data EmailTemplateData) (string, error) {
					return "<html>Test Email</html>", nil
				},
				RenderEmailSubject: func(data EmailTemplateData) (string, error) {
					return "", errors.New("failed to render email subject")
				},
				PublishEmailNotification: func(ctx context.Context, pubsubClient connect.PsClient, topicName string, message pubsubdata.NotificationRequest) error {
					return nil
				},
				LogInviteEvent: func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
					return nil
				},
			},
			expectedStatus: 500,
		},
		{
			name: "publish_email_notification_error",
			body: `{"email": "<EMAIL>", "inviterid": "123e4567-e89b-12d3-a456-************", "organizationrole": "123e4567-e89b-12d3-a456-************"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return bqbatcher.FakeBatch(ctx)
				},
				CreateInvite: func(pg connect.DatabaseExecutor, orgID uuid.UUID, tokenHash string, req CreateInviteRequest) (*UserInvite, error) {
					return &UserInvite{
						ID:                     uuid.New(),
						OrganizationIdentifier: orgID,
						TokenHash:              tokenHash,
						Email:                  req.Email,
						InviterID:              req.InviterID,
						CustomRoleID:           req.OrganizationRole,
						Status:                 StatusPending,
						Message:                req.Message,
						RequireSSO:             false,
						RetryCount:             0,
						Created:                time.Now().UTC(),
						Updated:                time.Now().UTC(),
					}, nil
				},
				GenerateToken: func(length uint) (string, error) {
					return "test-token", nil
				},
				HashString: func(input string) string {
					return "hashed-" + input
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				RenderEmailTemplate: func(data EmailTemplateData) (string, error) {
					return "<html>Test Email</html>", nil
				},
				RenderEmailSubject: func(data EmailTemplateData) (string, error) {
					return "You're Invited to " + data.AppName + "!", nil
				},
				PublishEmailNotification: func(ctx context.Context, pubsubClient connect.PsClient, topicName string, message pubsubdata.NotificationRequest) error {
					return errors.New("failed to publish email notification")
				},
				LogInviteEvent: func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
					return nil
				},
			},
			expectedStatus: 500,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest("POST", "/api/organization/"+orgID+"/invites", strings.NewReader(tt.body))
			req.Header.Set("Content-Type", "application/json")
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": orgID,
			})

			w := httptest.NewRecorder()

			handler := CreateInviteHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}
