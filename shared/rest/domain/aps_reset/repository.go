package apsreset

import (
	"github.com/google/uuid"
)

type Repository interface {
	CreateConfig(userID, orgID uuid.UUID) (*APSResetConfig, error)
	CreateStatistic(userID, orgID uuid.UUID) (*APSResetStatistic, error)
	FindConfigByUserIDAndOrganizationID(userID, orgID uuid.UUID) (*APSResetConfig, error)
	FindStatisticByUserIDAndOrganizationID(userID, orgID uuid.UUID) (*APSResetStatistic, error)
	UpdateStatistic(statistic *APSResetStatistic) error
}
