// Broker API
//
// @title           Broker API
// @version         1.0
// @description     Public endpoints for third-party integrations.
// @BasePath        /api
//
// @schemes         https
//
// @securityDefinitions.apikey  BearerAuth
// @in                          header
// @name                        Authorizationpackage main

package main

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"synapse-its.com/broker/api/routes"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/healthz"
	"synapse-its.com/shared/logger"
)

var (
	timeSleep        = time.Sleep
	healthzNewServer = healthz.NewServer
	loggerFatalf     = logger.Fatalf
)

func main() {
	Run(
		context.Background(),
		os.Getenv("HEALTH_PORT"),
		":8080", // main api port
		connect.NewConnections,
		bqbatch.New,
		DefaultServer,
		DefaultSignalChan,
	)
}

// Server is the subset of http.Server needed for run
type Server interface {
	ListenAndServe() error
	Shutdown(context.Context) error
}

// DefaultSignalChan returns a buffered channel that will get
// os.Interrupt and syscall.SIGTERM notifications.
func DefaultSignalChan() <-chan os.Signal {
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, os.Interrupt, syscall.SIGTERM)
	return sigCh
}

// DefaultServer returns a settable http.Server
func DefaultServer(addr string, handler http.Handler) Server {
	return &http.Server{Addr: addr, Handler: handler}
}

// Run wires everything up and blocks until ctx is done or a signal is received
func Run(
	ctx context.Context,
	healthPort string,
	addr string,
	newConns func(context.Context) *connect.Connections,
	newBatch func(connect.BigQueryExecutorInterface, connect.PsClient) bqbatch.Batcher,
	newServer func(string, http.Handler) Server,
	newSignals func() <-chan os.Signal,
) error {
	signals := newSignals()
	// start healthz service
	healthzsrv := healthzNewServer(healthPort)
	if err := healthzsrv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		loggerFatalf("healthz listen error: %v", err)
	}
	defer healthzsrv.Shutdown(ctx)

	logger.Info("Starting broker...")

	conns := newConns(ctx)
	defer conns.Close()

	batch := newBatch(conns.Bigquery, conns.Pubsub)
	defer func() {
		if err := batch.Shutdown(); err != nil {
			loggerFatalf("batch shutdown error: %v", err)
		}
	}()

	// initial boot/connections complete, now setup http server
	healthzsrv.SetBootComplete()
	srv := newServer(addr, routes.NewRouter(conns, batch))

	// create a waitgroup go goroutine
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		wg.Done() // signal that goroutine is about to call Listen and serve
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			loggerFatalf("server listenandserve error: %v", err)
		}
	}()

	// wait for goroutine to start before marking ready
	wg.Wait()

	// broker started
	logger.Infof("Broker running on %s", addr)
	healthzsrv.SetReady()

	// wait for termination signal
	select {
	case <-ctx.Done():
		// no-op for test coverage
		_ = true
	case <-signals:
		// no-op for test coverage
		_ = true
	}

	logger.Info("Shutdown signal received...")
	healthzsrv.SetNotReady()

	// allow in-flight to finish processing
	timeSleep(2 * time.Second)

	// finally shutdown http server
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	if err := srv.Shutdown(shutdownCtx); err != nil {
		logger.Errorf("server shutdown error: %v", err)
	}

	return nil
}
