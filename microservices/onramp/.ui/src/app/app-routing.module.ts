// src/app/app-routing.module.ts
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ProtectedComponent } from './protected/protected.component';
import { AuthGuard } from './core/guards/auth.guard';
import { HomeComponent } from './home/<USER>';
import { OrganizationsComponent } from './pages/organizations/organizations.component';
import { FooListComponent } from './foo-list/foo-list.component';
import { SoftwareGatewayComponent } from './pages/software-gateway/software-gateway.component';
import { SoftwareGatewayConfigurationComponent } from './pages/software-gateway-configuration/software-gateway-configuration.component';
import { DevicesComponent } from './pages/devices/devices.component';
import { LoginComponent } from './auth/login/login.component';
import { RegisterComponent } from './auth/register/register.component';
import { ForgotPasswordComponent } from './auth/forgot-password/forgot-password.component';
import { RolesPermissionsComponent } from './pages/roles-permissions/roles-permissions.component';
import { UsersComponent } from './pages/users/users.component';
import { AuthenticationMethodsComponent } from './pages/authentication-methods/authentication-methods.component';
import { InvitationsPendingComponent } from './pages/invitations-pending/invitations-pending.component';

const routes: Routes = [
  { path: '', component: HomeComponent, data: { title: 'Home' }, canActivate: [AuthGuard] },
  { path: 'login', component: LoginComponent, data: { title: 'Login' } },
  { path: 'register', component: RegisterComponent, data: { title: 'Register' } },
  { path: 'forgot-password', component: ForgotPasswordComponent, data: { title: 'Forgot Password' } },
  { path: 'protected', component: ProtectedComponent, canActivate: [AuthGuard] },
  { path: 'organizations', component: OrganizationsComponent, data: { title: 'Organizations' }, canActivate: [AuthGuard] },
  { path: 'software-gateway', component: SoftwareGatewayComponent, data: { title: 'Software Gateway' }, canActivate: [AuthGuard] },
  { path: 'software-gateway-config', component: SoftwareGatewayConfigurationComponent, data: { title: 'Software Gateway Configuration' }, canActivate: [AuthGuard] },
  { path: 'devices', component: DevicesComponent, data: { title: 'Devices' }, canActivate: [AuthGuard] },
  { path: 'organization/:orgId/users', component: UsersComponent, data: { title: 'Users' }, canActivate: [AuthGuard] },
  { path: `organization/:orgId/permissions`, component: RolesPermissionsComponent, data: { title: 'Permissions' }, canActivate: [AuthGuard] },
  { path: 'authentication', component: AuthenticationMethodsComponent, data: { title: 'Authentication Methods' }, canActivate: [AuthGuard] },
  { path: 'invitations-pending', component: InvitationsPendingComponent, data: { title: 'Invitations Pending' }, canActivate: [AuthGuard] },
  { path: 'foo', component: FooListComponent },
  { path: '**', redirectTo: '' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
