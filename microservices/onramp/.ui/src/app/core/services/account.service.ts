import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { delay } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AccountService {
  constructor(private http: HttpClient) { }
  getInvitations(userId: any): Observable<any[]> {
    return this.http.get<any[]>(`/api/user/${userId}/invites`).pipe(delay(500));
  }
  deleteInvitations(userId: any, id: string): Observable<any[]> {
    return this.http.delete<any[]>(`/api/user/${userId}/invites/${id}`);
  }
  acceptInvitations(userId: any, id: string, data: any): Observable<any[]> {
    return this.http.post<any[]>(`/api/user/${userId}/invites/${id}/redeem`, data);
  }

  getAuthMethods(userId: any): Observable<any[]> {
    return this.http.get<any[]>(`/api/user/${userId}/auth-methods`).pipe(delay(500));
  }

  changePassword(userId: any, data: any): Observable<any[]> {
    return this.http.patch<any[]>(`/api/user/${userId}/auth-methods/${data.currentPassword.id}/password`, data.formChangePassword);
  }

  deleteAuthMethod(userId: any, id: string): Observable<any[]> {
    return this.http.delete<any[]>(`/api/user/${userId}/auth-methods/${id}`);
  }
}