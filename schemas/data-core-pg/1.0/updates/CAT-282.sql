-- ================================================
-- CAT-282: Update Device table
-- Remove NOT NULL constraint from LocationId
-- Set the default value for FlushConnectionMs to 500
-- Add OrganizationId to Device table
-- ================================================

-- Remove NOT NULL constraint from LocationId
ALTER TABLE {{Device}} ALTER COLUMN LocationId DROP NOT NULL;

-- Update existing records that have NULL values to use the default
UPDATE {{Device}} SET FlushConnectionMs = 500 WHERE FlushConnectionMs IS NULL;

-- Add default value for FlushConnectionMs column
ALTER TABLE {{Device}} ALTER COLUMN FlushConnectionMs SET DEFAULT 500;

-- Add OrganizationId not null to Device table 
-- 1. Add column
ALTER TABLE {{Device}} ADD COLUMN OrganizationId UUID;

-- 2. Backfill data - Update devices with LocationId first
UPDATE {{Device}} 
SET OrganizationId = l.OrganizationId
FROM {{Location}} l
WHERE {{Device}}.LocationId = l.Id;

-- 3. Backfill data - Update devices without LocationId but with SoftwareGatewayId
UPDATE {{Device}} 
SET OrganizationId = sg.OrganizationId
FROM {{SoftwareGateway}} sg
WHERE {{Device}}.SoftwareGatewayId = sg.Id 
  AND {{Device}}.OrganizationId IS NULL;

-- 4. Set not null constraint
ALTER TABLE {{Device}} ALTER COLUMN OrganizationId SET NOT NULL;

-- 5. Add foreign key
ALTER TABLE {{Device}}
ADD CONSTRAINT {{Device_Organization_FK}}
FOREIGN KEY (OrganizationId) REFERENCES {{Organization}} (Id);

-- 6. Add index
CREATE INDEX {{Device_OrganizationId_IDX}} ON {{Device}} (OrganizationId);

-- 7. Make Description, IpAddress, and Port nullable
ALTER TABLE {{Device}} ALTER COLUMN Description DROP NOT NULL;
ALTER TABLE {{Device}} ALTER COLUMN IpAddress DROP NOT NULL;
ALTER TABLE {{Device}} ALTER COLUMN Port DROP NOT NULL;