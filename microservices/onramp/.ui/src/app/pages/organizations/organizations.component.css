.oem-container {
  width: 100%;
  margin: auto;
}

.oem-container .form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.oem-container .form-breadcrumb ::ng-deep .ant-breadcrumb {
  line-height: 26px;
  margin-bottom: 16px;
  font-size: 16px;
}

.oem-container .page-description-org {
  padding: 14px 0;
  border-top: 1px solid #000000;
  border-bottom: 1px solid #000000;
  margin-bottom: 16px;
}

/* .oem-container .form-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 10px;
} */

.oem-container .form-search input {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.oem-container .form-search button {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}

.oem-container .form-header .form-btn .add-btn {
  margin-right: 10px;
}

.oem-container .btn-action {
  display: flex;
  gap: 10px;
}

.oem-container .ant-table-thead>tr>th {
  font-weight: bold;
}

.oem-container .highlight-row {
  background-color: #e6f7ff8a;
  transition: background-color 0.5s ease;
}

.oem-container .default-row {
  transition: background-color 0.5s ease;
}

::ng-deep ant-message-notice-content {
  padding-top: 70px;
}