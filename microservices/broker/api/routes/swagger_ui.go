package routes

import (
	"net/http"

	"github.com/gorilla/mux"
	httpSwagger "github.com/swaggo/http-swagger/v2"
)

// docsSecurity is a middleware that applies basic security headers to the docs endpoint.
func docsSecurity(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Basic hardening
		w.Header().Set("X-Content-Type-Options", "nosniff")
		w.<PERSON>er().Set("X-Frame-Options", "DENY")
		w.Header().Set("Referrer-Policy", "no-referrer")
		w.<PERSON>er().Set("X-Robots-Tag", "noindex")

		// CSP for Swagger UI:
		// - allow inline scripts (required by swagger-ui's index)
		// - allow inline styles (swagger-ui uses them)
		// - allow fetching the spec from same origin
		// - block framing
		w.Header().Set("Content-Security-Policy",
			"default-src 'self'; "+
				"img-src 'self' data:; "+
				"style-src 'self' 'unsafe-inline'; "+
				"script-src 'self' 'unsafe-inline'; "+
				"connect-src 'self'; "+
				"frame-ancestors 'none';")

		next.ServeHTTP(w, r)
	})
}

func setupSwaggerUI(router *mux.Router) {
	// Redirect /docs/broker -> /docs/broker/
	router.Handle("/docs/broker", docsSecurity(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		http.Redirect(w, r, "/docs/broker/", http.StatusMovedPermanently)
	})))

	// Serve Swagger UI via library; it fetches the committed spec at /openapi/broker.yaml
	// Disable "Try it out" by setting supportedSubmitMethods to an empty array.
	uiHandler := httpSwagger.Handler(
		httpSwagger.URL("/openapi/broker.yaml"),
		httpSwagger.DomID("swagger-ui"),
		httpSwagger.UIConfig(map[string]string{
			"supportedSubmitMethods": "[]",
		}),
	)
	router.PathPrefix("/docs/broker/").Handler(docsSecurity(uiHandler))

	// Serve the committed OpenAPI spec
	router.Handle("/openapi/broker.yaml", docsSecurity(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "./openapi/broker.yaml")
	})))
}
