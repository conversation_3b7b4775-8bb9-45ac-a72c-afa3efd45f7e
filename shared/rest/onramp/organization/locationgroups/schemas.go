package locationgroups

import (
	"time"

	"github.com/google/uuid"
)

// The request for creating and updating a location group
type CreateAndUpdateLocationGroupsRequest struct {
	Name string `json:"name" validate:"required,min=1"`
}

// The response for a location group
type LocationGroupResponse struct {
	Id             uuid.UUID `json:"id" db:"id"`
	OrganizationId uuid.UUID `json:"organizationid" db:"organizationid"`
	Name           string    `json:"name" db:"name"`
	CreatedAt      time.Time `json:"createdat" db:"createdat"`
	UpdatedAt      time.Time `json:"updatedat" db:"updatedat"`
}

// The response for location groups
type LocationGroupsResponse struct {
	LocationGroups []LocationGroupResponse `json:"locationgroups"`
}
