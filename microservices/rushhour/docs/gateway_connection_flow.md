# Gateway Connection Flow (Rush Hour)

This document outlines the connection and authorization flow for **Gateway** clients connecting to the Rush Hour (RH) Socket.IO server. The architecture uses **direct socket messaging** for all device communication, with no control room joining required.

**Development and Testing Guide** - All code examples are based on tested, working implementations validated through end-to-end testing and ready for integration development.

---

## Overview

- Gateway authenticates using an existing **token-based REST API flow**, receiving a long-lived **API key**
- Gateway connects to RH via the Socket.IO namespace: `/auth/gateway`
- RH validates the API key and registers the gateway for direct socket messaging
- RH responds with `gateway_init` (confirming authentication )

---

## 1. Gateway Authentication Requirements

**Critical: Gateway authentication has been a common source of integration issues. Follow these requirements:**

### Correct Gateway Client Implementation (Go)

```go
import (
    "github.com/zishang520/engine.io-client-go/transports"
    "github.com/zishang520/engine.io/v2/types"
    "github.com/zishang520/socket.io-client-go/socket"
)

// Create connection options
opts := socket.DefaultOptions()
// WebSocket preferred, polling fallback (use polling only for tests)
opts.SetTransports(types.NewSet(transports.WebSocket, transports.Polling))

// REQUIRED: Auth data in the auth object (not query parameters)
authData := map[string]interface{}{
    "machine_key": "your_machine_key",
    "api_key":     "your_api_key",
}
opts.SetAuth(authData)

// Create manager and connect to gateway namespace
manager := socket.NewManager("https://your-rushhour-server.com", opts)
client := manager.Socket("/auth/gateway", opts)

// REQUIRED: Listen for gateway_init event (not connect)
client.On("gateway_init", func(args ...any) {
    log.Printf("Gateway authenticated successfully: %v", args)
    // Now ready for device communication
})

client.On("connect", func(args ...any) {
    log.Print("Connected to Socket.IO")
})

client.On("connect_error", func(args ...any) {
    log.Printf("Connection error: %v", args)
})

// Handle device requests from FSA
client.On("device_request", func(args ...any) {
    envelope := parseSocketEnvelope(args[0])
    log.Printf("Received device command - Device: %s, Session: %s", 
               envelope.DeviceId, envelope.SessionId)
    
    // Process device command and send response
    processDeviceCommand(client, envelope)
})

// Start connection
client.Connect()
```

### Common Gateway Authentication Mistakes

**DO NOT use query parameters:**
```go
// WRONG: Auth data in URL query parameters
client, err := socketio_client.NewClient("https://your-rushhour-server.com/auth/gateway?machine_key=xyz&api_key=abc", opts)
```

**DO NOT rely on connect event for auth:**
```go
// WRONG: connect event fires before authentication
client.On("connect", func(args ...any) {
    // Authentication not guaranteed here!
})
```

**Transport Configuration Notes:**
```go
// NORMAL USE: WebSocket with polling fallback (recommended)
opts.SetTransports(types.NewSet(transports.WebSocket, transports.Polling))

// TESTING ONLY: Polling only for deterministic test timing
opts.SetTransports(types.NewSet(transports.Polling))
```

---

## 2. RH Authentication Handler (Server Side - Go)

```go
server.OnConnect("/auth/gateway", func(conn socketio.Conn) error {
    machineKey, machineKeyOk := conn.Auth()["machine_key"].(string)
    apiKey, apiKeyOk := conn.Auth()["api_key"].(string)
    
    if !machineKeyOk || machineKey == "" || !apiKeyOk || apiKey == "" {
        return conn.Emit("error", "missing machine_key or api_key")
    }

    authInfo, err := validateGatewayAuth(machineKey, apiKey)
    if err != nil {
        return conn.Emit("error", "unauthorized")
    }

    ctx := &ConnectionContext{
        AuthInfo: &AuthInfo{
            ClientType: ClientTypeGateway,
            OrgID:      authInfo.OrgID,
            GatewayID:  authInfo.GatewayID,
        },
    }
    
    // Register socket for direct messaging
    service.RegisterSocket(conn, ctx)

    return conn.Emit("gateway_init", map[string]string{
        "status": "authenticated",
    })
})
```

---

## 3. Device Communication Examples

### Processing Device Commands and Sending Responses

```go
func processDeviceCommand(client *socket.Socket, envelope *SocketEnvelope) {
    // Process the device command based on envelope payload
    var commandPayload map[string]interface{}
    json.Unmarshal(envelope.Payload, &commandPayload)
    
    action := commandPayload["action"].(string)
    
    // Simulate device processing
    time.Sleep(100 * time.Millisecond)
    
    // Create response payload
    responsePayload, _ := json.Marshal(map[string]interface{}{
        "status":  "success",
        "message": "Device command executed successfully",
        "data":    "Mock device response",
    })
    
    // Create response envelope (preserve session ID for routing)
    response := &SocketEnvelope{
        Type:           envelope.Type,
        RequestId:      envelope.RequestId,
        SessionId:      envelope.SessionId,     // Critical: preserve for routing
        DeviceId:       envelope.DeviceId,
        OrganizationId: envelope.OrganizationId,
        Origin:         ORIGIN_GATEWAY,
        Payload:        responsePayload,
    }
    
    // Send response back to FSA via RushHour
    client.Emit("device_message", response)
}
```

### Message Envelope Structure

All device communication uses the SocketEnvelope protobuf structure:

```go
type SocketEnvelope struct {
    Type           EnvelopeType  // ENVELOPE_COMMAND_JSON
    RequestId      uint32        // For request/response correlation
    SessionId      string        // RushHour populates for routing
    UserId         string        // RushHour populates from FSA auth
    DeviceId       string        // Target device ID
    OrganizationId string        // RushHour populates from auth
    Origin         OriginType    // ORIGIN_FSA, ORIGIN_GATEWAY, etc.
    Payload        []byte        // JSON command/response data
}
```

---

## 4. Direct Messaging Communication

With direct socket messaging, gateways can:

- **Receive device commands**: RH routes device requests directly to the specific gateway socket
- **Send device responses**: Gateway sends responses that RH routes back to the originating FSA/Onramp socket  
- **Join streaming rooms**: Gateway can join device streaming rooms when viewers are present
- **Leave streaming rooms**: Gateway leaves streaming rooms when no viewers remain

Example device message handling:

```go
server.OnEvent("/auth/gateway", "device_message", func(conn socketio.Conn, envelope SocketEnvelope) {
    // Process device response from gateway
    service.ProcessDeviceMessage(conn, &envelope)
})

server.OnEvent("/auth/gateway", "stream_control", func(conn socketio.Conn, command StreamControlCommand) {
    // Handle streaming join/leave commands from RH
    service.ProcessStreamControl(conn, &command)
})
```

---

## 4. API Key Validation (Placeholder)

```go
type GatewayAuthInfo struct {
    OrgID     string
    GatewayID string
}

func validateGatewayAuth(machineKey, apiKey string) (*GatewayAuthInfo, error) {
    // Example: Call internal service to validate machine key and API key
    // and extract org_id and gateway_id (matches broker authentication pattern)
    return &GatewayAuthInfo{
        OrgID:     "org123",
        GatewayID: "gw456",
    }, nil
}
```

---

This flow securely initializes the gateway and registers it for direct socket messaging. Device communication is routed efficiently through socket IDs rather than room-based routing.

---

## Gateway Authentication Quick Reference

### Working Pattern
1. Machine key + API key credentials
2. WebSocket with polling fallback (or polling only for tests)
3. Auth data in `.auth` object with `"machine_key"` and `"api_key"` keys
4. Connect to `/auth/gateway` namespace
5. Wait for `gateway_init` event (not `connect`)
6. Listen for `device_request` from FSA
7. Use `device_message` to send responses (preserve `SessionId`)

### Common Issues
- **Query parameters**: Server expects auth object
- **Wrong namespace**: Must use `/auth/gateway`
- **Wrong event**: Wait for `gateway_init`, not `connect`
- **Missing SessionId**: Must preserve SessionId from request in response
- **Test timing**: Use polling only in test environments for deterministic results

### Test Credentials (DEV environment)
```
Machine Key: localgateway
API Key: qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd
Device ID: 87d94e14-e804-58b3-9f8c-a02e5de90aeb
```

