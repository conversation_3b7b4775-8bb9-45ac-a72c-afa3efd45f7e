package notifications

import (
	"context"

	"github.com/stretchr/testify/mock"
)

// MockNotificationService is a mock implementation of the NotificationService interface
type MockNotificationService struct {
	mock.Mock
}

// SendSMS mocks the SendSMS method
func (m *MockNotificationService) SendSMS(ctx context.Context, toPhone string, messageBody string) error {
	args := m.Called(ctx, toPhone, messageBody)
	return args.Error(0)
}

// SendEmail mocks the SendEmail method
func (m *MockNotificationService) SendEmail(ctx context.Context, to, subject, body string) error {
	args := m.Called(ctx, to, subject, body)
	return args.Error(0)
}

// MockSMSService is a mock implementation of the SMSService interface
type MockSMSService struct {
	mock.Mock
}

// SendSMS mocks the SendSMS method
func (m *MockSMSService) SendSMS(ctx context.Context, toPhone string, messageBody string) error {
	args := m.Called(ctx, toPhone, messageBody)
	return args.Error(0)
}

// MockEmailService is a mock implementation of the EmailService interface
type MockEmailService struct {
	mock.Mock
}

// SendEmail mocks the SendEmail method
func (m *MockEmailService) SendEmail(ctx context.Context, to, subject, body string) error {
	args := m.Called(ctx, to, subject, body)
	return args.Error(0)
}
