-- CAT-545: Add HDSP channel enable and power supply monitor fields to logConfiguration table
-- Adding hdspchannelenable, x12vpowersupplymonitor, x48vpowersupplymonitor fields
-- This migration recreates the table with the new schema and restores data

-- Step 1: Create backup table
CREATE TABLE {{logConfiguration_backup}} AS
SELECT * FROM {{logConfiguration}};

-- Step 2: Drop the original table
DROP TABLE {{logConfiguration}};

-- Step 3: Recreate table with new schema including the additional fields
CREATE TABLE {{logConfiguration}} (
  organizationidentifier  STRING,
  softwaregatewayid       STRING,
  tz                      STRING,
  topic                   STRING,
  pubsubtimestamp         TIMESTAMP,
  pubsubid                STRING,
  deviceid                STRING,
  header                  STRUCT<
    commversion      STRING,
    model            INT64,
    firmwareversion  STRING,
    firmwarerevision STRING,
    monitorid        INT64,
    volt220          BOOL,
    voltdc           BOOL,
    mainsdc          BOOL,
    powerdownlevel   INT64,
    blackoutlevel    INT64,
    maxchannels      INT64
  >,
  devicemodel             STRING,
  record ARRAY<STRUCT<
    datetime                         TIMESTAMP,
    ch01permissives                   ARRAY<STRING>,
    ch02permissives                   ARRAY<STRING>,
    ch03permissives                   ARRAY<STRING>,
    ch04permissives                   ARRAY<STRING>,
    ch05permissives                   ARRAY<STRING>,
    ch06permissives                   ARRAY<STRING>,
    ch07permissives                   ARRAY<STRING>,
    ch08permissives                   ARRAY<STRING>,
    ch09permissives                   ARRAY<STRING>,
    ch10permissives                  ARRAY<STRING>,
    ch11permissives                  ARRAY<STRING>,
    ch12permissives                  ARRAY<STRING>,
    ch13permissives                  ARRAY<STRING>,
    ch14permissives                  ARRAY<STRING>,
    ch15permissives                  ARRAY<STRING>,
    ch16permissives                  ARRAY<STRING>,
    ch17permissives                  ARRAY<STRING>,
    ch18permissives                  ARRAY<STRING>,
    ch19permissives                  ARRAY<STRING>,
    ch20permissives                  ARRAY<STRING>,
    ch21permissives                  ARRAY<STRING>,
    ch22permissives                  ARRAY<STRING>,
    ch23permissives                  ARRAY<STRING>,
    ch24permissives                  ARRAY<STRING>,
    ch25permissives                  ARRAY<STRING>,
    ch26permissives                  ARRAY<STRING>,
    ch27permissives                  ARRAY<STRING>,
    ch28permissives                  ARRAY<STRING>,
    ch29permissives                  ARRAY<STRING>,
    ch30permissives                  ARRAY<STRING>,
    ch31permissives                  ARRAY<STRING>,
    redfailenable                    ARRAY<BOOL>,
    greenyellowdualenable           ARRAY<BOOL>,
    yellowreddualenable              ARRAY<BOOL>,
    greenreddualenable               ARRAY<BOOL>,
    minimumyellowclearanceenable    ARRAY<BOOL>,
    minimumyellowreduclearanceenable ARRAY<BOOL>,
    fieldcheckenablegreen           ARRAY<BOOL>,
    fieldcheckenableyellow          ARRAY<BOOL>,
    fieldcheckenablered             ARRAY<BOOL>,
    yellowenable                     ARRAY<BOOL>,
    hdspchannelenable               ARRAY<BOOL>,
    walkenable_ts1                   BOOL,
    redfaulttiming                   STRING,
    recurrentpulse                   BOOL,
    watchdognngtiming                STRING,
    watchdogenableswitch             BOOL,
    programcardmemory                BOOL,
    gyenable                         BOOL,
    minimumflashtime                 STRING,
    cvmlatchenable                   BOOL,
    logcvmfaults                     BOOL,
    x24viiinputthreshold             STRING,
    x24vlatchenable                  BOOL,
    x24voltinhibit                   BOOL,
    x12vpowersupplymonitor          BOOL,
    x48vpowersupplymonitor          BOOL,
    port_1disable                    BOOL,
    typemode                         STRING,
    ledguardthresholds               BOOL,
    forcetype_16mode                 BOOL,
    type_12withsdlcmode              BOOL,
    vmcvm_24v_3xdaylatch             BOOL,
    redfailenabledbyssm              BOOL,
    dualindicationfaulttiming        STRING,
    wdterrorclearonpu                 BOOL,
    minimumflash                     BOOL,
    configchangefault                BOOL,
    redcablefault                    BOOL,
    aclinebrownout                   STRING,
    pineepolarity                    STRING,
    flashingyellowarrows             ARRAY<STRING>,
    fyaredandyellowenable            STRING,
    fyaredandgreendisable           STRING,
    fyayellowtrapdetection           BOOL,
    fyaflashratefault                BOOL,
    fyaflashratedetection            BOOL,
    pplt5suppression                 STRING,
    checkvalue                       STRING,
    changesource                     STRING,
    redvirtualchannel ARRAY<STRUCT<
      color         STRING,
      enabled       BOOL,
      sourcechannel INT64,
      sourcecolor   STRING
    >>,
    yellowvirtualchannel ARRAY<STRUCT<
      color         STRING,
      enabled       BOOL,
      sourcechannel INT64,
      sourcecolor   STRING
    >>,
    greenvirtualchannel ARRAY<STRUCT<
      color         STRING,
      enabled       BOOL,
      sourcechannel INT64,
      sourcecolor   STRING
    >>,
    currentsenseredenabled      ARRAY<BOOL>,
    currentsenseyellowenabled   ARRAY<BOOL>,
    currentsensegreenenabled    ARRAY<BOOL>,
    currentsenseredthreshold    ARRAY<INT64>,
    currentsenseyellowthreshold ARRAY<INT64>,
    currentsensegreenthreshold  ARRAY<INT64>,
    darkchannelx01              ARRAY<BOOL>,
    darkchannelx02              ARRAY<BOOL>,
    darkchannelx03              ARRAY<BOOL>,
    darkchannelx04              ARRAY<BOOL>
  >>,
  rawmessage              BYTES,
  loguuid                 STRING
)
{% PARTITION BY DATE(pubsubtimestamp)
 CLUSTER BY organizationidentifier, softwaregatewayid, deviceid %};

-- Step 4: Restore data from backup - BigQuery will handle missing fields with NULL/default values
{% 
INSERT INTO {{logConfiguration}} (
  organizationidentifier,
  softwaregatewayid,
  tz,
  topic,
  pubsubtimestamp,
  pubsubid,
  deviceid,
  header,
  devicemodel,
  record,
  rawmessage,
  loguuid
)
SELECT
  b.organizationidentifier,
  b.softwaregatewayid,
  b.tz,
  b.topic,
  b.pubsubtimestamp,
  b.pubsubid,
  b.deviceid,
  b.header,
  b.devicemodel,
  ARRAY(
    SELECT AS STRUCT
      r.datetime                         AS datetime,
      r.ch01permissives                  AS ch01permissives,
      r.ch02permissives                  AS ch02permissives,
      r.ch03permissives                  AS ch03permissives,
      r.ch04permissives                  AS ch04permissives,
      r.ch05permissives                  AS ch05permissives,
      r.ch06permissives                  AS ch06permissives,
      r.ch07permissives                  AS ch07permissives,
      r.ch08permissives                  AS ch08permissives,
      r.ch09permissives                  AS ch09permissives,
      r.ch10permissives                  AS ch10permissives,
      r.ch11permissives                  AS ch11permissives,
      r.ch12permissives                  AS ch12permissives,
      r.ch13permissives                  AS ch13permissives,
      r.ch14permissives                  AS ch14permissives,
      r.ch15permissives                  AS ch15permissives,
      r.ch16permissives                  AS ch16permissives,
      r.ch17permissives                  AS ch17permissives,
      r.ch18permissives                  AS ch18permissives,
      r.ch19permissives                  AS ch19permissives,
      r.ch20permissives                  AS ch20permissives,
      r.ch21permissives                  AS ch21permissives,
      r.ch22permissives                  AS ch22permissives,
      r.ch23permissives                  AS ch23permissives,
      r.ch24permissives                  AS ch24permissives,
      r.ch25permissives                  AS ch25permissives,
      r.ch26permissives                  AS ch26permissives,
      r.ch27permissives                  AS ch27permissives,
      r.ch28permissives                  AS ch28permissives,
      r.ch29permissives                  AS ch29permissives,
      r.ch30permissives                  AS ch30permissives,
      r.ch31permissives                  AS ch31permissives,
      r.redfailenable                    AS redfailenable,
      r.greenyellowdualenable            AS greenyellowdualenable,
      r.yellowreddualenable              AS yellowreddualenable,
      r.greenreddualenable               AS greenreddualenable,
      r.minimumyellowclearanceenable     AS minimumyellowclearanceenable,
      r.minimumyellowreduclearanceenable AS minimumyellowreduclearanceenable,
      r.fieldcheckenablegreen            AS fieldcheckenablegreen,
      r.fieldcheckenableyellow           AS fieldcheckenableyellow,
      r.fieldcheckenablered              AS fieldcheckenablered,
      r.yellowenable                     AS yellowenable,
      CAST(NULL AS ARRAY<BOOL>)          AS hdspchannelenable,       -- NEW
      r.walkenable_ts1                   AS walkenable_ts1,
      r.redfaulttiming                   AS redfaulttiming,
      r.recurrentpulse                   AS recurrentpulse,
      r.watchdognngtiming                AS watchdognngtiming,
      r.watchdogenableswitch             AS watchdogenableswitch,
      r.programcardmemory                AS programcardmemory,
      r.gyenable                         AS gyenable,
      r.minimumflashtime                 AS minimumflashtime,
      r.cvmlatchenable                   AS cvmlatchenable,
      r.logcvmfaults                     AS logcvmfaults,
      r.x24viiinputthreshold             AS x24viiinputthreshold,
      r.x24vlatchenable                  AS x24vlatchenable,
      r.x24voltinhibit                   AS x24voltinhibit,
      CAST(NULL AS BOOL)                 AS x12vpowersupplymonitor,  -- NEW
      CAST(NULL AS BOOL)                 AS x48vpowersupplymonitor,  -- NEW
      r.port_1disable                    AS port_1disable,
      r.typemode                         AS typemode,
      r.ledguardthresholds               AS ledguardthresholds,
      r.forcetype_16mode                 AS forcetype_16mode,
      r.type_12withsdlcmode              AS type_12withsdlcmode,
      r.vmcvm_24v_3xdaylatch             AS vmcvm_24v_3xdaylatch,
      r.redfailenabledbyssm              AS redfailenabledbyssm,
      r.dualindicationfaulttiming        AS dualindicationfaulttiming,
      r.wdterrorclearonpu                AS wdterrorclearonpu,
      r.minimumflash                     AS minimumflash,
      r.configchangefault                AS configchangefault,
      r.redcablefault                    AS redcablefault,
      r.aclinebrownout                   AS aclinebrownout,
      r.pineepolarity                    AS pineepolarity,
      r.flashingyellowarrows             AS flashingyellowarrows,
      r.fyaredandyellowenable            AS fyaredandyellowenable,
      r.fyaredandgreendisable            AS fyaredandgreendisable,
      r.fyayellowtrapdetection           AS fyayellowtrapdetection,
      r.fyaflashratefault                AS fyaflashratefault,
      r.fyaflashratedetection            AS fyaflashratedetection,
      r.pplt5suppression                 AS pplt5suppression,
      r.checkvalue                       AS checkvalue,
      r.changesource                     AS changesource,
      r.redvirtualchannel                AS redvirtualchannel,
      r.yellowvirtualchannel             AS yellowvirtualchannel,
      r.greenvirtualchannel              AS greenvirtualchannel,
      r.currentsenseredenabled           AS currentsenseredenabled,
      r.currentsenseyellowenabled        AS currentsenseyellowenabled,
      r.currentsensegreenenabled         AS currentsensegreenenabled,
      r.currentsenseredthreshold         AS currentsenseredthreshold,
      r.currentsenseyellowthreshold      AS currentsenseyellowthreshold,
      r.currentsensegreenthreshold       AS currentsensegreenthreshold,
      r.darkchannelx01                   AS darkchannelx01,
      r.darkchannelx02                   AS darkchannelx02,
      r.darkchannelx03                   AS darkchannelx03,
      r.darkchannelx04                   AS darkchannelx04
    FROM UNNEST(b.record) AS r
  ) AS record,
  b.rawmessage,
  b.loguuid
FROM {{logConfiguration_backup}} AS b;
%}


-- Step 5: Clean up backup table
DROP TABLE {{logConfiguration_backup}};