package password

import "errors"

var (
	ErrUsernameEmpty    = errors.New("username is empty")
	ErrUsernameTooShort = errors.New("username too short")
	ErrUsernameTooLong  = errors.New("username too long")
	ErrPasswordEmpty    = errors.New("password is empty")
	ErrPasswordTooShort = errors.New("password too short")
	ErrPasswordTooLong  = errors.New("password too long")
	ErrInvalidJSON      = errors.New("invalid JSON")
	ErrPasswordMismatch = errors.New("password mismatch")
)
