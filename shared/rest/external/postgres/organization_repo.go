package postgres

import (
	"database/sql"
	"time"

	"github.com/google/uuid"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/rest/domain/organization"
)

type organizationRepository struct {
	db connect.DatabaseExecutor
}

// membershipQueryResult represents the raw query result from the database
type membershipQueryResult struct {
	ID             uuid.UUID      `db:"id"`
	OrganizationID uuid.UUID      `db:"organization_id"`
	UserID         uuid.UUID      `db:"user_id"`
	FirstName      sql.NullString `db:"first_name"`
	LastName       sql.NullString `db:"last_name"`
	UserName       sql.NullString `db:"user_name"`
	Email          sql.NullString `db:"email"`
	AuthMethod     string         `db:"auth_method"`
	OrgRole        sql.NullString `db:"org_role"`
	LastLogin      *time.Time     `db:"last_login"`
}

func NewOrganizationRepository(db connect.DatabaseExecutor) *organizationRepository {
	return &organizationRepository{db: db}
}

func (s *organizationRepository) FindUsersByOrganizationID(organizationID uuid.UUID) ([]*organization.Membership, error) {
	query := `
		SELECT 
			m.Id as id,
			m.OrganizationId as organization_id,
			u.Id as user_id,
			u.FirstName as first_name,
			u.LastName as last_name,
			am.UserName as user_name,
			am.Email as email,
			am.Type as auth_method,
			cr.Name as org_role,
			am.LastLogin as last_login
		FROM {{Memberships}} m
		INNER JOIN {{AuthMethod}} am ON m.AuthMethodId = am.Id AND NOT am.IsDeleted
		INNER JOIN {{User}} u ON am.UserId = u.Id AND NOT u.IsDeleted
		LEFT JOIN {{OrgRoleAssignments}} ora ON m.Id = ora.MembershipId AND NOT ora.IsDeleted
		LEFT JOIN {{CustomRole}} cr ON ora.RoleId = cr.Id AND NOT cr.IsDeleted
		WHERE m.OrganizationId = $1 
		AND NOT m.IsDeleted
		ORDER BY u.LastName, u.FirstName
	`

	var queryResults []membershipQueryResult
	err := s.db.QueryGenericSlice(&queryResults, query, organizationID)
	if err != nil {
		return nil, err
	}

	// Convert query results to domain.Membership slice
	memberships := make([]*organization.Membership, len(queryResults))
	for i, result := range queryResults {
		memberships[i] = &organization.Membership{
			ID:             result.ID,
			OrganizationID: result.OrganizationID,
			UserID:         result.UserID,
			FirstName:      result.FirstName.String,
			LastName:       result.LastName.String,
			UserName:       result.UserName.String,
			Email:          result.Email.String,
			AuthMethod:     result.AuthMethod,
			OrgRole:        result.OrgRole.String,
			LastLogin:      result.LastLogin,
		}
	}

	return memberships, nil
}

func (s *organizationRepository) UpdateUserRoleInOrganization(userID uuid.UUID, organizationID uuid.UUID, roleID uuid.UUID) error {
	query := `
		WITH membership_check AS (
			SELECT m.Id as membership_id
			FROM {{Memberships}} m
			INNER JOIN {{AuthMethod}} am ON m.AuthMethodId = am.Id
			WHERE am.UserId = $1 AND m.OrganizationId = $2 AND m.IsDeleted = false
		)
		UPDATE {{OrgRoleAssignments}}
		SET RoleId = $3, UpdatedAt = NOW()
		WHERE MembershipId = (SELECT membership_id FROM membership_check)
		AND IsDeleted = false
	`

	result, err := s.db.Exec(query, userID, organizationID, roleID)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return organization.ErrUserNotFound
	}

	return nil
}

func (s *organizationRepository) DeleteUserFromOrganization(userID uuid.UUID, organizationID uuid.UUID) error {
	query := `
		UPDATE {{Memberships}}
		SET IsDeleted = true, UpdatedAt = NOW()
		WHERE AuthMethodId = (
			SELECT am.Id 
			FROM {{AuthMethod}} am
			WHERE am.UserId = $1
		) AND OrganizationId = $2
	`
	_, err := s.db.Exec(query, userID, organizationID)
	return err
}
