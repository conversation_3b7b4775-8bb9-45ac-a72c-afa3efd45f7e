package routes

import (
	"net/http"

	"github.com/gorilla/mux"

	V3DataDevice "synapse-its.com/broker/api/handlers/v3/data/device"
	V3DataFault "synapse-its.com/broker/api/handlers/v3/data/fault"
	V3GatewayAuthenticate "synapse-its.com/broker/api/handlers/v3/gateway/authenticate"
	V3GatewayIngest "synapse-its.com/broker/api/handlers/v3/gateway/ingest"
	V3GatewayUpdate "synapse-its.com/broker/api/handlers/v3/gateway/update"
	V3UserAccountClose "synapse-its.com/broker/api/handlers/v3/user/account/close"
	V3UserAccountNotifications "synapse-its.com/broker/api/handlers/v3/user/account/notifications"
	V3UserAuthenticate "synapse-its.com/broker/api/handlers/v3/user/authenticate"
	V3UserInstruction "synapse-its.com/broker/api/handlers/v3/user/instruction"
	V3UserProfile "synapse-its.com/broker/api/handlers/v3/user/profile"
	"synapse-its.com/broker/api/synapse"
	defaultapi "synapse-its.com/shared/api/handlers/defaultapi"
	"synapse-its.com/shared/api/middleware"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/httplogger"
)

// NewRouter initializes the router and registers routes.
func NewRouter(connections *connect.Connections, batch bqbatch.Batcher) *mux.Router {
	router := mux.NewRouter()

	// Setup Swagger UI
	setupSwaggerUI(router)

	// Apply middleware globally (e.g., logging, authentication).
	router.Use(middleware.ConnectionsMiddleware(connections))
	router.Use(middleware.BQBatchMiddleware(batch))
	router.Use(middleware.JWTAuthorizerMiddleware)
	router.Use(httplogger.LoggingMiddleware)

	// Define default endpoints
	router.HandleFunc("/", defaultapi.Handler).Methods(http.MethodGet)

	// Define Legacy API endpoints (will be deprecated once flutter app is updated)
	router.HandleFunc("/data/v2/device", V3DataDevice.Handler).Methods(http.MethodGet)
	router.HandleFunc("/data/v2/fault", V3DataFault.Handler).Methods(http.MethodGet)
	router.HandleFunc("/user/v2/profile", V3UserProfile.Handler).Methods(http.MethodGet)
	router.HandleFunc("/user/v2/account/close", V3UserAccountClose.Handler).Methods(http.MethodPost)
	router.HandleFunc("/user/v2/account/notifications", V3UserAccountNotifications.Handler).Methods(http.MethodPost)
	router.HandleFunc("/user/v3/authenticate", V3UserAuthenticate.Handler).Methods(http.MethodPost)
	router.HandleFunc("/user/v3/instruction", V3UserInstruction.Handler).Methods(http.MethodPost)

	// Define API endpoints (all standardized to V3).
	router.HandleFunc("/api/v3/gateway/authenticate", V3GatewayAuthenticate.Handler).Methods(http.MethodPost)
	router.HandleFunc("/api/v3/gateway/ingest", V3GatewayIngest.Handler).Methods(http.MethodPost)
	router.HandleFunc("/api/v3/gateway/update", V3GatewayUpdate.Handler).Methods(http.MethodPost)
	router.HandleFunc("/api/v3/data/device", V3DataDevice.Handler).Methods(http.MethodGet)
	router.HandleFunc("/api/v3/data/fault", V3DataFault.Handler).Methods(http.MethodGet)
	router.HandleFunc("/api/v3/user/account/close", V3UserAccountClose.Handler).Methods(http.MethodPost)
	router.HandleFunc("/api/v3/account/notifications", V3UserAccountNotifications.Handler).Methods(http.MethodPost)
	router.HandleFunc("/api/v3/user/account/notifications", V3UserAccountNotifications.Handler).Methods(http.MethodPost)
	router.HandleFunc("/api/v3/user/authenticate", V3UserAuthenticate.Handler).Methods(http.MethodPost)
	router.HandleFunc("/api/v3/user/instruction", V3UserInstruction.Handler).Methods(http.MethodPost)
	router.HandleFunc("/api/v3/user/profile", V3UserProfile.Handler).Methods(http.MethodGet)

	// Define Synapse API endpoints
	synapse.SetUpSubrouter(router)

	return router
}
