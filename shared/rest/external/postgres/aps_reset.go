package postgres

import (
	"time"

	"github.com/google/uuid"
	"github.com/lib/pq"
	"synapse-its.com/shared/connect"
	apsReset "synapse-its.com/shared/rest/domain/aps_reset"
)

// dbAPSResetConfig matches the database table structure for aps_reset_config
type dbAPSResetConfig struct {
	ID                                     uuid.UUID `db:"id"`
	UserID                                 uuid.UUID `db:"user_id"`
	OrganizationID                         uuid.UUID `db:"organization_id"`
	ConfigAPSResetSuccessLimit             int       `db:"config_aps_reset_success_limit"`
	ConfigAPSResetSuccessListLength        int       `db:"config_aps_reset_success_list_length"`
	ConfigAPSResetSuccessListWindowMinutes int       `db:"config_aps_reset_success_list_window_minutes"`
	IsDeleted                              bool      `db:"is_deleted"`
	CreatedAt                              string    `db:"created_at"`
	UpdatedAt                              string    `db:"updated_at"`
}

// dbAPSResetStatistic matches the database table structure for aps_reset_statistic
type dbAPSResetStatistic struct {
	ID                       uuid.UUID `db:"id"`
	UserID                   uuid.UUID `db:"user_id"`
	OrganizationID           uuid.UUID `db:"organization_id"`
	StatAPSResetCount        int       `db:"stat_aps_reset_count"`
	StatAPSResetSuccessCount int       `db:"stat_aps_reset_success_count"`
	StatAPSResetSuccessList  []string  `db:"stat_aps_reset_success_list"`
	IsDeleted                bool      `db:"is_deleted"`
	CreatedAt                string    `db:"created_at"`
	UpdatedAt                string    `db:"updated_at"`
}

type apsResetRepo struct {
	db connect.DatabaseExecutor
}

func NewApsResetRepo(db connect.DatabaseExecutor) *apsResetRepo {
	return &apsResetRepo{
		db: db,
	}
}

func (r *apsResetRepo) CreateConfig(userID, orgID uuid.UUID) (*apsReset.APSResetConfig, error) {
	query := `
		INSERT INTO {{APSResetConfig}} (
			UserId, 
			OrganizationId
		) VALUES (
			$1, $2
		)
		RETURNING 
			Id as id,
			UserId as user_id,
			OrganizationId as organization_id,
			ConfigAPSResetSuccessLimit as config_aps_reset_success_limit,
			ConfigAPSResetSuccessListLength as config_aps_reset_success_list_length,
			ConfigAPSResetSuccessListWindowMinutes as config_aps_reset_success_list_window_minutes
	`
	var dbConfig dbAPSResetConfig
	err := r.db.QueryRowStruct(&dbConfig, query, userID, orgID)
	if err != nil {
		return nil, err
	}
	return &apsReset.APSResetConfig{
		ID:                                     dbConfig.ID,
		UserID:                                 dbConfig.UserID,
		OrganizationID:                         dbConfig.OrganizationID,
		ConfigAPSResetSuccessLimit:             dbConfig.ConfigAPSResetSuccessLimit,
		ConfigAPSResetSuccessListLength:        dbConfig.ConfigAPSResetSuccessListLength,
		ConfigAPSResetSuccessListWindowMinutes: dbConfig.ConfigAPSResetSuccessListWindowMinutes,
	}, nil
}

func (r *apsResetRepo) CreateStatistic(userID, orgID uuid.UUID) (*apsReset.APSResetStatistic, error) {
	query := `
		INSERT INTO {{APSResetStatistic}} (
			UserId, 
			OrganizationId
		) VALUES (
			$1, $2
		)
		RETURNING 
			Id as id,
			UserId as user_id,
			OrganizationId as organization_id,
			StatAPSResetCount as stat_aps_reset_count,
			StatAPSResetSuccessCount as stat_aps_reset_success_count,
			StatAPSResetSuccessList as stat_aps_reset_success_list
	`
	var dbStat dbAPSResetStatistic
	err := r.db.QueryRowStruct(&dbStat, query, userID, orgID)
	if err != nil {
		return nil, err
	}
	return &apsReset.APSResetStatistic{
		ID:                       dbStat.ID,
		UserID:                   dbStat.UserID,
		OrganizationID:           dbStat.OrganizationID,
		StatAPSResetCount:        dbStat.StatAPSResetCount,
		StatAPSResetSuccessCount: dbStat.StatAPSResetSuccessCount,
		StatAPSResetSuccessList:  convertStringArrayToTimeSlice(dbStat.StatAPSResetSuccessList),
	}, nil
}

func (r *apsResetRepo) FindConfigByUserIDAndOrganizationID(userID, orgID uuid.UUID) (*apsReset.APSResetConfig, error) {
	query := `
		SELECT 
			Id as id,
			UserId as user_id,
			OrganizationId as organization_id,
			ConfigAPSResetSuccessLimit as config_aps_reset_success_limit,
			ConfigAPSResetSuccessListLength as config_aps_reset_success_list_length,
			ConfigAPSResetSuccessListWindowMinutes as config_aps_reset_success_list_window_minutes,
			IsDeleted as is_deleted,
			CreatedAt as created_at,
			UpdatedAt as updated_at
		FROM {{APSResetConfig}}
		WHERE UserId = $1 AND OrganizationId = $2
	`
	var dbConfig dbAPSResetConfig
	err := r.db.QueryRowStruct(&dbConfig, query, userID, orgID)
	if err != nil {
		return nil, err
	}

	// Map db struct to domain struct
	config := &apsReset.APSResetConfig{
		ID:                                     dbConfig.ID,
		UserID:                                 dbConfig.UserID,
		OrganizationID:                         dbConfig.OrganizationID,
		ConfigAPSResetSuccessLimit:             dbConfig.ConfigAPSResetSuccessLimit,
		ConfigAPSResetSuccessListLength:        dbConfig.ConfigAPSResetSuccessListLength,
		ConfigAPSResetSuccessListWindowMinutes: dbConfig.ConfigAPSResetSuccessListWindowMinutes,
	}

	return config, nil
}

func (r *apsResetRepo) FindStatisticByUserIDAndOrganizationID(userID, orgID uuid.UUID) (*apsReset.APSResetStatistic, error) {
	query := `
		SELECT 
			Id as id,
			UserId as user_id,
			OrganizationId as organization_id,
			StatAPSResetCount as stat_aps_reset_count,
			StatAPSResetSuccessCount as stat_aps_reset_success_count,
			StatAPSResetSuccessList as stat_aps_reset_success_list,
			IsDeleted as is_deleted,
			CreatedAt as created_at,
			UpdatedAt as updated_at
		FROM {{APSResetStatistic}}
		WHERE UserId = $1 AND OrganizationId = $2
	`
	var dbStat dbAPSResetStatistic
	err := r.db.QueryRowStruct(&dbStat, query, userID, orgID)
	if err != nil {
		return nil, err
	}

	// Map db struct to domain struct
	statistic := &apsReset.APSResetStatistic{
		ID:                       dbStat.ID,
		UserID:                   dbStat.UserID,
		OrganizationID:           dbStat.OrganizationID,
		StatAPSResetCount:        dbStat.StatAPSResetCount,
		StatAPSResetSuccessCount: dbStat.StatAPSResetSuccessCount,
		StatAPSResetSuccessList:  convertStringArrayToTimeSlice(dbStat.StatAPSResetSuccessList),
	}

	return statistic, nil
}

func (r *apsResetRepo) UpdateStatistic(statistic *apsReset.APSResetStatistic) error {
	query := `
		UPDATE {{APSResetStatistic}} 
		SET StatAPSResetCount = $1, 
		    StatAPSResetSuccessCount = $2, 
		    StatAPSResetSuccessList = $3,
		    UpdatedAt = NOW()
		WHERE UserId = $4 AND OrganizationId = $5
	`
	_, err := r.db.Exec(query,
		statistic.StatAPSResetCount,
		statistic.StatAPSResetSuccessCount,
		pq.Array(statistic.StatAPSResetSuccessList),
		statistic.UserID,
		statistic.OrganizationID)

	return err
}

func convertStringArrayToTimeSlice(sa []string) []time.Time {
	if sa == nil {
		return nil
	}

	times := make([]time.Time, 0, len(sa))
	for _, s := range sa {
		if t, err := time.Parse(time.RFC3339, s); err == nil {
			times = append(times, t)
		}
	}
	return times
}
