package biz

import (
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"synapse-its.com/shared/rest/domain/organization"
)

// MockRepository is a mock implementation of the organization.Repository interface
type MockRepository struct {
	mock.Mock
}

func (m *MockRepository) FindUsersByOrganizationID(organizationID uuid.UUID) ([]*organization.Membership, error) {
	args := m.Called(organizationID)
	return args.Get(0).([]*organization.Membership), args.Error(1)
}

func (m *MockRepository) UpdateUserRoleInOrganization(userID uuid.UUID, organizationID uuid.UUID, roleID uuid.UUID) error {
	args := m.Called(userID, organizationID, roleID)
	return args.Error(0)
}

func (m *MockRepository) DeleteUserFromOrganization(userID uuid.UUID, organizationID uuid.UUID) error {
	args := m.Called(userID, organizationID)
	return args.Error(0)
}

func TestNewMembershipBiz(t *testing.T) {
	t.<PERSON>()

	mockRepo := &MockRepository{}
	biz := NewMembershipBiz(mockRepo)

	assert.NotNil(t, biz)
	assert.Equal(t, mockRepo, biz.repo)
}

func TestMembershipBiz_GetUsersByOrganizationID(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name            string
		organizationID  uuid.UUID
		mockMemberships []*organization.Membership
		mockError       error
		expectedResult  []*organization.Membership
		expectedError   error
	}{
		{
			name:           "success_with_users",
			organizationID: uuid.New(),
			mockMemberships: []*organization.Membership{
				{
					ID:             uuid.New(),
					UserID:         uuid.New(),
					OrganizationID: uuid.New(),
					FirstName:      "John",
					LastName:       "Doe",
					UserName:       "johndoe",
					Email:          "<EMAIL>",
					AuthMethod:     "USERNAME_PASSWORD",
					OrgRole:        "MEMBER",
					LastLogin:      &time.Time{},
				},
				{
					ID:             uuid.New(),
					UserID:         uuid.New(),
					OrganizationID: uuid.New(),
					FirstName:      "Jane",
					LastName:       "Smith",
					UserName:       "janesmith",
					Email:          "<EMAIL>",
					AuthMethod:     "OIDC",
					OrgRole:        "ADMIN",
					LastLogin:      nil,
				},
			},
			mockError: nil,
			expectedResult: []*organization.Membership{
				{
					ID:             uuid.New(),
					UserID:         uuid.New(),
					OrganizationID: uuid.New(),
					FirstName:      "John",
					LastName:       "Doe",
					UserName:       "johndoe",
					Email:          "<EMAIL>",
					AuthMethod:     "USERNAME_PASSWORD",
					OrgRole:        "MEMBER",
					LastLogin:      &time.Time{},
				},
				{
					ID:             uuid.New(),
					UserID:         uuid.New(),
					OrganizationID: uuid.New(),
					FirstName:      "Jane",
					LastName:       "Smith",
					UserName:       "janesmith",
					Email:          "<EMAIL>",
					AuthMethod:     "OIDC",
					OrgRole:        "ADMIN",
					LastLogin:      nil,
				},
			},
			expectedError: nil,
		},
		{
			name:            "success_with_no_users",
			organizationID:  uuid.New(),
			mockMemberships: []*organization.Membership{},
			mockError:       nil,
			expectedResult:  []*organization.Membership{},
			expectedError:   nil,
		},
		{
			name:            "repository_error",
			organizationID:  uuid.New(),
			mockMemberships: nil,
			mockError:       errors.New("database connection failed"),
			expectedResult:  nil,
			expectedError:   errors.New("database connection failed"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := &MockRepository{}
			biz := NewMembershipBiz(mockRepo)

			// Set up mock expectations
			mockRepo.On("FindUsersByOrganizationID", tt.organizationID).Return(tt.mockMemberships, tt.mockError)

			// Execute the method
			result, err := biz.GetUsersByOrganizationID(tt.organizationID)

			// Assert results
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}

			if tt.expectedResult != nil {
				assert.Len(t, result, len(tt.expectedResult))
				// Note: We can't directly compare the slices due to UUID generation
				// but we can verify the structure and content
				for _, membership := range result {
					assert.NotNil(t, membership)
					assert.NotEqual(t, uuid.Nil, membership.ID)
					assert.NotEqual(t, uuid.Nil, membership.UserID)
					assert.NotEqual(t, uuid.Nil, membership.OrganizationID)
					assert.NotEmpty(t, membership.FirstName)
					assert.NotEmpty(t, membership.LastName)
					assert.NotEmpty(t, membership.UserName)
					assert.NotEmpty(t, membership.Email)
					assert.NotEmpty(t, membership.AuthMethod)
					assert.NotEmpty(t, membership.OrgRole)
				}
			} else {
				assert.Nil(t, result)
			}

			// Verify mock was called correctly
			mockRepo.AssertExpectations(t)
		})
	}
}

func TestMembershipBiz_UpdateUserRoleInOrganization(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		userID         uuid.UUID
		organizationID uuid.UUID
		roleID         uuid.UUID
		mockError      error
		expectedError  error
	}{
		{
			name:           "success",
			userID:         uuid.New(),
			organizationID: uuid.New(),
			roleID:         uuid.New(),
			mockError:      nil,
			expectedError:  nil,
		},
		{
			name:           "repository_error",
			userID:         uuid.New(),
			organizationID: uuid.New(),
			roleID:         uuid.New(),
			mockError:      errors.New("user not found"),
			expectedError:  errors.New("user not found"),
		},
		{
			name:           "permission_denied",
			userID:         uuid.New(),
			organizationID: uuid.New(),
			roleID:         uuid.New(),
			mockError:      errors.New("insufficient permissions"),
			expectedError:  errors.New("insufficient permissions"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := &MockRepository{}
			biz := NewMembershipBiz(mockRepo)

			// Set up mock expectations
			mockRepo.On("UpdateUserRoleInOrganization", tt.userID, tt.organizationID, tt.roleID).Return(tt.mockError)

			// Execute the method
			err := biz.UpdateUserRoleInOrganization(tt.userID, tt.organizationID, tt.roleID)

			// Assert results
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}

			// Verify mock was called correctly
			mockRepo.AssertExpectations(t)
		})
	}
}

func TestMembershipBiz_DeleteUserFromOrganization(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		userID         uuid.UUID
		organizationID uuid.UUID
		mockError      error
		expectedError  error
	}{
		{
			name:           "success",
			userID:         uuid.New(),
			organizationID: uuid.New(),
			mockError:      nil,
			expectedError:  nil,
		},
		{
			name:           "user_not_found",
			userID:         uuid.New(),
			organizationID: uuid.New(),
			mockError:      errors.New("user not found in organization"),
			expectedError:  errors.New("user not found in organization"),
		},
		{
			name:           "organization_not_found",
			userID:         uuid.New(),
			organizationID: uuid.New(),
			mockError:      errors.New("organization not found"),
			expectedError:  errors.New("organization not found"),
		},
		{
			name:           "database_error",
			userID:         uuid.New(),
			organizationID: uuid.New(),
			mockError:      errors.New("database connection failed"),
			expectedError:  errors.New("database connection failed"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockRepo := &MockRepository{}
			biz := NewMembershipBiz(mockRepo)

			// Set up mock expectations
			mockRepo.On("DeleteUserFromOrganization", tt.userID, tt.organizationID).Return(tt.mockError)

			// Execute the method
			err := biz.DeleteUserFromOrganization(tt.userID, tt.organizationID)

			// Assert results
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}

			// Verify mock was called correctly
			mockRepo.AssertExpectations(t)
		})
	}
}

func TestMembershipBiz_Integration(t *testing.T) {
	t.Parallel()

	// Test that all methods work together correctly
	mockRepo := &MockRepository{}
	biz := NewMembershipBiz(mockRepo)

	userID := uuid.New()
	organizationID := uuid.New()
	roleID := uuid.New()

	// Test sequence: Get users, update role, then delete user
	mockRepo.On("FindUsersByOrganizationID", organizationID).Return([]*organization.Membership{}, nil)
	mockRepo.On("UpdateUserRoleInOrganization", userID, organizationID, roleID).Return(nil)
	mockRepo.On("DeleteUserFromOrganization", userID, organizationID).Return(nil)

	// Execute all methods
	_, err1 := biz.GetUsersByOrganizationID(organizationID)
	err2 := biz.UpdateUserRoleInOrganization(userID, organizationID, roleID)
	err3 := biz.DeleteUserFromOrganization(userID, organizationID)

	// Assert all operations succeeded
	assert.NoError(t, err1)
	assert.NoError(t, err2)
	assert.NoError(t, err3)

	// Verify all mocks were called
	mockRepo.AssertExpectations(t)
}
