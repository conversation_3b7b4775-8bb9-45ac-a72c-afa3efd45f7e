-- GatewayConfigTemplate definition
CREATE TABLE {{GatewayConfigTemplate}} (
  Id              UUID      NOT NULL DEFAULT uuid_generate_v4(),
  Name            TEXT      NOT NULL,
  OrganizationId UUID NOT NULL,
  Description     TEXT      NOT NULL,
  IsDeleted      BOOLEAN   NOT NULL DEFAULT FALSE,
  CreatedAt      TIMESTAMP NOT NULL      DEFAULT NOW(),
  UpdatedAt      TIMESTAMP NOT NULL      DEFAULT NOW(),
  CONSTRAINT {{GatewayConfigTemplate_PK}} PRIMARY KEY (Id),
  CONSTRAINT {{GatewayConfigTemplate_Organization_FK}} FOREIGN KEY (OrganizationId) REFERENCES {{Organization}} (Id)
);

CREATE TABLE {{GatewayConfigTemplateBaseSettings}} (
  Setting     TEXT    NOT NULL,   -- unique identifier for the setting
  DefaultValue TEXT NOT NULL,
  Name        TEXT    NOT NULL,
  Description TEXT    NOT NULL,      -- what this setting controls
  Format      JSONB  NOT NULL,        -- JSON schema/metadata for validation or UI rendering
  IsDeleted      BOOLEAN   NOT NULL DEFAULT FALSE,
  CreatedAt      TIMESTAMP NOT NULL      DEFAULT NOW(),
  UpdatedAt      TIMESTAMP NOT NULL      DEFAULT NOW(),
  CONSTRAINT {{GatewayConfigTemplateBaseSettings_PK}} PRIMARY KEY (Setting)
);

CREATE TABLE {{GatewayConfigTemplateSettings}} (
  gatewayConfigTemplateId  UUID    NOT NULL,
  setting                  TEXT    NOT NULL,
  value                    TEXT    NOT NULL,
  CONSTRAINT {{GatewayConfigTemplateSettings_PK}} PRIMARY KEY (gatewayConfigTemplateId, setting),
  CONSTRAINT {{GatewayConfigTemplateSettings_FK_Template}} FOREIGN KEY (gatewayConfigTemplateId)  REFERENCES {{GatewayConfigTemplate}}(id),
  CONSTRAINT {{GatewayConfigTemplateSettings_FK_BaseSetting}} FOREIGN KEY (setting) REFERENCES {{GatewayConfigTemplateBaseSettings}}(setting)
);

CREATE TABLE {{GatewayConfigTemplateSettingOverrides}} (
  softwareGatewayId  UUID    NOT NULL,
  setting                  TEXT    NOT NULL,
  value                    TEXT    NOT NULL,
  CONSTRAINT {{GatewayConfigTemplateSettingOverrides_PK}} PRIMARY KEY (softwareGatewayId, setting),
  CONSTRAINT {{GatewayConfigTemplateSettingOverrides_FK_SoftwareGateway}} FOREIGN KEY (softwareGatewayId)  REFERENCES {{SoftwareGateway}}(Id),
  CONSTRAINT {{GatewayConfigTemplateSettingOverrides_FK_BaseSetting}} FOREIGN KEY (setting) REFERENCES {{GatewayConfigTemplateBaseSettings}}(setting)
);

ALTER TABLE {{SoftwareGateway}}
  ADD COLUMN TemplateId UUID;

ALTER TABLE {{SoftwareGateway}}
  ADD CONSTRAINT {{SoftwareGateway_Template_FK}} FOREIGN KEY (TemplateId) REFERENCES {{GatewayConfigTemplate}} (Id);

-- Set available gateway config template base settings
INSERT INTO {{GatewayConfigTemplateBaseSettings}} (Setting, DefaultValue, Name, Description, Format)
VALUES
  ('channel_state_send_frequency_milliseconds',
   '500',
   'Channel State Send Frequency Milliseconds',
   'Frequency to send channel state to Firebase. In the communication setup for a device, the configuration for whether or not the device is enabled to send realtime data is set there. In other words, the device must be enabled for the frequency to take hold.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('send_rmsStatus_to_cloud',
   'true',
   'Send RMS Status To Cloud',
   'Flag indicating whether or not to send rmsStatus from legacy EDI devices to the GCP.',
   '{"type":"boolean","versions":["v3"]}'
  ),
  ('rmsStatus_send_frequency_milliseconds',
   '500',
   'RMS Status Send Frequency Milliseconds',
   'Frequency to send rmsData to GCP.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('channel_state_batch_size',
   '500',
   'Channel State Batch Size',
   'The max number of devices to include in a batch sent to the cloud. The batch size is applicable to the following message-types: rmsEngine, monitorName, macAddress, rmsData.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('device_state_send_frequency_seconds',
   '60',
   'Device State Send Frequency Seconds',
   'Frequency to send rmsEngine, monitorName, macAddress information to the cloud. Note: This information is sent when the gateway-app launches or when a device goes offline and then back online. You can view this as the amount of time to wait before delivering the latest changes (if any) to the cloud.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('send_gateway_performance_stats_to_cloud',
   'true',
   'Send Gateway Performance Stats To Cloud',
   'Flag for sending gateway-app performance statistics to the cloud. Note: related to gateway_performance_stats_output_frequency_seconds.',
   '{"type":"boolean","versions":["v3"]}'
  ),
  ('gateway_performance_stats_output_frequency_seconds',
   '60',
   'Gateway Performance Stats Output Frequency Seconds',
   'The amount of time to wait before sending the gateway-app performance statistics to the cloud.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('send_gateway_logs_to_cloud',
   'true',
   'Send Gateway Logs To Cloud',
   'Flag for sending gateway-logs to the cloud. Note: related to gateway_logs_output_frequency_seconds.',
   '{"type":"boolean","versions":["v3"]}'
  ),
  ('gateway_logs_output_frequency_seconds',
   '3600',
   'Gateway Logs Output Frequency Seconds',
   'The amount of time to wait before sending the gateway-app log to the cloud.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('gateway_proxy_port_for_eccom_communication',
   '10001',
   'Gateway Proxy Port For Eccom Communication',
   'The port the ECCOM app uses for communication with the EDI device when the gateway-app is acting as a proxy.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('config_change_check_frequency_seconds',
   '30',
   'Config Change Check Frequency Seconds',
   'The amount of time the gateway-app waits before checking to see if there is a configuration update. If one is detected, the gateway-app will reset itself and download the new configuration.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('edi_device_persist_connection',
   'true',
   'EDI Device Persist Connection',
   'Keep the TCP connection alive (i.e., do not open and close the connection). Note: False no longer works and is technically not an option.',
   '{"type":"boolean","versions":["v3"]}'
  ),
  ('edi_device_processing_retries',
   '5',
   'EDI Device Processing Retries',
   'Number of consecutive attempts to communicate with a device before determining there is an error.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('log_compress_backups',
   'true',
   'Log Compress Backups',
   'Compress backups to save diskspace.',
   '{"type":"boolean","versions":["v3"]}'
  ),
  ('log_file_max_size_mb',
   '1',
   'Log File Max Size MB',
   'The maximum size of the log file.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('log_filename',
   'log/gateway-app.log',
   'Log Filename',
   'The name of the active log.',
   '{"type":"string","versions":["v3"]}'
  ),
  ('log_level',
   'error',
   'Log Level',
   'The detail of the log. "error" being the least amount of info while "debug" will fill up quite fast. Defaults to "info" if the setting is not recognized.',
   '{"type":"string","versions":["v3"]}'
  ),
  ('log_max_age_in_days',
   '30',
   'Log Max Age In Days',
   'The max age of a log before it is deleted.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('log_max_backups',
   '10',
   'Log Max Backups',
   'The maximum number of backups before they get truncated.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('debug_level_device_ids',
   '[]',
   'Debug Level Device IDs',
   'The device identifiers to emit log information for.',
   '{"type":"array","versions":["v3"]}'
  ),
  ('record_http_requests_to_folder',
   'false',
   'Record HTTP Requests To Folder',
   'Logs all communications to devices and all HTTP communications to the /logs folder.',
   '{"type":"boolean","versions":["v3"]}'
  ),
  ('threshold_device_error_seconds',
   '90',
   'Threshold Device Error Seconds',
   'The amount of time to wait before determining the device is in an error state.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('ws_active',
   'true',
   'WS Active',
   'Flag for turning on/off the web socket port for local integration.',
   '{"type":"boolean","versions":["v3"]}'
  ),
  ('ws_endpoint',
   '/gateway',
   'WS Endpoint',
   'The web socket endpoint for local integration.',
   '{"type":"string","versions":["v3"]}'
  ),
  ('ws_heartbeat_send_frequency_milliseconds',
   '60000',
   'WS Heartbeat Send Frequency Milliseconds',
   'The frequency to deliver heartbeat messages over the web socket endpoint for local integration.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('ws_max_connections',
   '2',
   'WS Max Connections',
   'The maximum number of connections to allow on the web socket port for local integration.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('ws_port',
   '8079',
   'WS Port',
   'The web socket port used for local integration.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('ws_send_frequency_milliseconds',
   '5000',
   'WS Send Frequency Milliseconds',
   'The max amount of time to wait before checking for and sending device updates on the web socket connection for local integration.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('application_version',
   'deprecated',
   'Application Version',
   'No longer used.',
   '{"type":"string","versions":["v3"]}'
  ),
  ('software_update_check_frequency_seconds',
   '43200',
   'Software Update Check Frequency Seconds',
   'No longer used.',
   '{"type":"integer","versions":["v3"]}'
  );

WITH org AS (
  SELECT
    Id   AS organizationId,
    Name AS organizationName
  FROM {{Organization}}
)
INSERT INTO {{GatewayConfigTemplate}} (
  Id,
  Name,
  OrganizationId,
  Description
)
SELECT
  -- generate a v5 UUID whose "name" embeds the org’s UUID text
  uuid_generate_v5(
    uuid_nil(),
    format(
      'SYNAPSE_softwaregatewayconfigtemplate_%s',
      organizationId::text
    )
  ) AS Id,

  format(
    'Default Software Gateway Template for %s',
    organizationName
  ) AS Name,

  organizationId,

  'Default configuration for software gateways' AS Description

FROM org;

INSERT INTO {{GatewayConfigTemplateSettings}}
  (gatewayConfigTemplateId, setting, value)
SELECT
  t.Id              AS gatewayConfigTemplateId,
  b.Setting          AS setting,
  b.DefaultValue     AS value
FROM
  {{GatewayConfigTemplate}}           AS t
CROSS JOIN
  {{GatewayConfigTemplateBaseSettings}} AS b;

UPDATE {{SoftwareGateway}} 
SET TemplateId = uuid_generate_v5(
  uuid_nil(), 
  format('SYNAPSE_softwaregatewayconfigtemplate_%s', OrganizationId::text)
);
