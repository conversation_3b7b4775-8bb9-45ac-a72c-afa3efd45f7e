package device

import "errors"

var (
	ErrDeviceNotFound           = errors.New("device not found")
	ErrDatabaseOperation        = errors.New("database operation failed")
	ErrUnexpectedFields         = errors.New("unexpected fields")
	ErrInvalidRequestBody       = errors.New("invalid request body")
	ErrInvalidDeviceName        = errors.New("invalid device name")
	ErrInvalidDeviceDescription = errors.New("invalid device description")
	ErrInvalidDeviceIpAddress   = errors.New("invalid device ip address")
	ErrInvalidDeviceType        = errors.New("invalid device type")
	ErrInvalidDeviceTypeValue   = errors.New("device type must be either 'EDI_LEGACY' or 'EDI_NEXT_GEN'")
)
