package permissions

import (
	"fmt"
	"testing"

	"bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_comms"
	"bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_config"
	"bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_dfu"
	"bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_logs"
	"bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_realtime"
	"bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_stats"
	"bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/wrappers"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
)

func TestNewCommandPermissionMapper(t *testing.T) {
	t.Parallel()

	// Test the constructor function
	mapper := NewCommandPermissionMapper()

	// Verify the mapper is created
	assert.NotNil(t, mapper)
	assert.NotNil(t, mapper.commandPermissions)

	// Verify that command permissions are initialized
	assert.Greater(t, len(mapper.commandPermissions), 0, "Command permissions should be initialized")

	// Verify some specific commands are registered
	assert.Contains(t, mapper.commandPermissions, "start_realtime")
	assert.Contains(t, mapper.commandPermissions, "rd_port1_stats")
	assert.Contains(t, mapper.commandPermissions, "wr_date_time_dst")
}

func TestCommandPermissionMapper_typeNameToCommandString(t *testing.T) {
	t.Parallel()

	mapper := NewCommandPermissionMapper()

	tests := []struct {
		name           string
		typeName       string
		expectedResult string
	}{
		{
			name:           "start_realtime command",
			typeName:       "WrapperCommand_StartRealtime",
			expectedResult: "start_realtime",
		},
		{
			name:           "stop_realtime command",
			typeName:       "WrapperCommand_StopRealtime",
			expectedResult: "stop_realtime",
		},
		{
			name:           "rd_port1_stats command",
			typeName:       "WrapperCommand_RdPort1Stats",
			expectedResult: "rd_port1_stats",
		},
		{
			name:           "wr_date_time_dst command",
			typeName:       "WrapperCommand_WrDateTimeDst",
			expectedResult: "wr_date_time_dst",
		},
		{
			name:           "unknown command type",
			typeName:       "WrapperCommand_UnknownCommand",
			expectedResult: "",
		},
		{
			name:           "empty type name",
			typeName:       "",
			expectedResult: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result := mapper.typeNameToCommandString(tt.typeName)
			assert.Equal(t, tt.expectedResult, result)
		})
	}
}

func TestCommandPermissionMapper_ExtractCommandType(t *testing.T) {
	t.Parallel()

	mapper := NewCommandPermissionMapper()

	tests := []struct {
		name           string
		wrapperCmd     *wrappers.WrapperCommand
		expectedResult string
	}{
		{
			name:           "nil wrapper command",
			wrapperCmd:     nil,
			expectedResult: "",
		},
		{
			name: "start_realtime command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_StartRealtime{
					StartRealtime: &cmd_resp_realtime.CmdStartRealtimeData{},
				},
			},
			expectedResult: "start_realtime",
		},
		{
			name: "stop_realtime command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_StopRealtime{
					StopRealtime: &cmd_resp_realtime.CmdStopRealtimeData{},
				},
			},
			expectedResult: "stop_realtime",
		},
		{
			name: "rd_port1_stats command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdPort1Stats{
					RdPort1Stats: &cmd_resp_stats.CmdReadPort1Statistics{},
				},
			},
			expectedResult: "rd_port1_stats",
		},
		{
			name: "wr_date_time_dst command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_WrDateTimeDst{
					WrDateTimeDst: &cmd_resp_stats.CmdSetTimeDatesDst{},
				},
			},
			expectedResult: "wr_date_time_dst",
		},
		{
			name: "request_log_counts command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RequestLogCounts{
					RequestLogCounts: &cmd_resp_logs.CmdRequestLogCounts{},
				},
			},
			expectedResult: "request_log_counts",
		},
		{
			name: "log_clear command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_LogClear{
					LogClear: &cmd_resp_logs.CmdRequestLogClear{},
				},
			},
			expectedResult: "log_clear",
		},
		{
			name: "rd_monitor_data command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdMonitorData{
					RdMonitorData: &cmd_resp_config.CmdReadMonitorData{},
				},
			},
			expectedResult: "rd_monitor_data",
		},
		{
			name: "manifest_versions command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_ManifestVersions{
					ManifestVersions: &cmd_resp_dfu.CmdManifestVersions{},
				},
			},
			expectedResult: "manifest_versions",
		},
		// Additional test cases to cover all command types
		{
			name: "request_log command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RequestLog{
					RequestLog: &cmd_resp_logs.CmdRequestLogEntries{},
				},
			},
			expectedResult: "request_log",
		},
		{
			name: "req_auditlog_counts command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_ReqAuditlogCounts{
					ReqAuditlogCounts: &cmd_resp_logs.CmdRequestAuditLogCounts{},
				},
			},
			expectedResult: "req_auditlog_counts",
		},
		{
			name: "auditlog_clear command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_AuditlogClear{
					AuditlogClear: &cmd_resp_logs.CmdRequestAuditLogClear{},
				},
			},
			expectedResult: "auditlog_clear",
		},
		{
			name: "audit_log_reset command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_AuditLogReset{
					AuditLogReset: &cmd_resp_logs.CmdRequestAuditLogReset{},
				},
			},
			expectedResult: "audit_log_reset",
		},
		{
			name: "request_auditlog command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RequestAuditlog{
					RequestAuditlog: &cmd_resp_logs.CmdRequestAuditLogEntries{},
				},
			},
			expectedResult: "request_auditlog",
		},
		{
			name: "rd_network_config_unit command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdNetworkConfigUnit{
					RdNetworkConfigUnit: &cmd_resp_config.CmdReadUnitNetworkConfiguration{},
				},
			},
			expectedResult: "rd_network_config_unit",
		},
		{
			name: "rd_network_config_active command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdNetworkConfigActive{
					RdNetworkConfigActive: &cmd_resp_config.CmdReadActiveNetworkConfiguration{},
				},
			},
			expectedResult: "rd_network_config_active",
		},
		{
			name: "rd_channel_config command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdChannelConfig{
					RdChannelConfig: &cmd_resp_config.CmdReadPerChannelConfiguration{},
				},
			},
			expectedResult: "rd_channel_config",
		},
		{
			name: "rd_channel_current_sense command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdChannelCurrentSense{
					RdChannelCurrentSense: &cmd_resp_config.CmdReadPerChannelCurrentSenseSettings{},
				},
			},
			expectedResult: "rd_channel_current_sense",
		},
		{
			name: "rd_channel_permissives command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdChannelPermissives{
					RdChannelPermissives: &cmd_resp_config.CmdReadPerChannelPermissiveSettings{},
				},
			},
			expectedResult: "rd_channel_permissives",
		},
		{
			name: "rd_fya_config command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdFyaConfig{
					RdFyaConfig: &cmd_resp_config.CmdReadFlashingYellowArrowConfiguration{},
				},
			},
			expectedResult: "rd_fya_config",
		},
		{
			name: "rd_data_key command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdDataKey{
					RdDataKey: &cmd_resp_config.CmdReadDataKey{},
				},
			},
			expectedResult: "rd_data_key",
		},
		{
			name: "wr_data_key command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_WrDataKey{
					WrDataKey: &cmd_resp_config.CmdWriteDataKey{},
				},
			},
			expectedResult: "wr_data_key",
		},
		{
			name: "rd_factory_settings command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdFactorySettings{
					RdFactorySettings: &cmd_resp_config.CmdReadFactorySettings{},
				},
			},
			expectedResult: "rd_factory_settings",
		},
		{
			name: "rd_user_settings command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdUserSettings{
					RdUserSettings: &cmd_resp_config.CmdReadUserSettings{},
				},
			},
			expectedResult: "rd_user_settings",
		},
		{
			name: "wr_user_settings command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_WrUserSettings{
					WrUserSettings: &cmd_resp_config.CmdWriteUserSettings{},
				},
			},
			expectedResult: "wr_user_settings",
		},
		{
			name: "rd_port1_disables command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdPort1Disables{
					RdPort1Disables: &cmd_resp_config.CmdReadPort1DisableOverrides{},
				},
			},
			expectedResult: "rd_port1_disables",
		},
		{
			name: "wr_port1_disables command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_WrPort1Disables{
					WrPort1Disables: &cmd_resp_config.CmdWritePort1DisableOverrides{},
				},
			},
			expectedResult: "wr_port1_disables",
		},
		{
			name: "remote_reset command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RemoteReset{
					RemoteReset: &cmd_resp_config.CmdRemoteReset{},
				},
			},
			expectedResult: "remote_reset",
		},
		{
			name: "rd_data_key_stats command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdDataKeyStats{
					RdDataKeyStats: &cmd_resp_stats.CmdReadDataKeyStatistics{},
				},
			},
			expectedResult: "rd_data_key_stats",
		},
		{
			name: "rd_main_iso_comms_stats command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdMainIsoCommsStats{
					RdMainIsoCommsStats: &cmd_resp_stats.CmdReadMainToIsolatedCommStatistics{},
				},
			},
			expectedResult: "rd_main_iso_comms_stats",
		},
		{
			name: "rd_main_comms_stats command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdMainCommsStats{
					RdMainCommsStats: &cmd_resp_stats.CmdReadMainToCommsCommStatistics{},
				},
			},
			expectedResult: "rd_main_comms_stats",
		},
		{
			name: "rd_comms_main_stats command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdCommsMainStats{
					RdCommsMainStats: &cmd_resp_stats.CmdReadCommsToMainCommStatistics{},
				},
			},
			expectedResult: "rd_comms_main_stats",
		},
		{
			name: "rd_flash_stats command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdFlashStats{
					RdFlashStats: &cmd_resp_stats.CmdReadFlashStatistics{},
				},
			},
			expectedResult: "rd_flash_stats",
		},
		{
			name: "rd_watchdog_stats command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdWatchdogStats{
					RdWatchdogStats: &cmd_resp_stats.CmdReadWatchdogStatistics{},
				},
			},
			expectedResult: "rd_watchdog_stats",
		},
		{
			name: "rd_date_time_dst command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RdDateTimeDst{
					RdDateTimeDst: &cmd_resp_stats.CmdGetTimeDatesDst{},
				},
			},
			expectedResult: "rd_date_time_dst",
		},
		{
			name: "clear_stats command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_ClearStats{
					ClearStats: &cmd_resp_stats.CmdClearStatistics{},
				},
			},
			expectedResult: "clear_stats",
		},
		{
			name: "remote_display_button_event command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RemoteDisplayButtonEvent{
					RemoteDisplayButtonEvent: &cmd_resp_stats.CmdRemoteDisplayButtonEvent{},
				},
			},
			expectedResult: "remote_display_button_event",
		},
		{
			name: "reboot_comms_mcu command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_RebootCommsMcu{
					RebootCommsMcu: &cmd_resp_dfu.CmdRebootCommsMcu{},
				},
			},
			expectedResult: "reboot_comms_mcu",
		},
		{
			name: "initiate_dfu command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_InitiateDfu{
					InitiateDfu: &cmd_resp_dfu.CmdInitiateFirmwareUpdate{},
				},
			},
			expectedResult: "initiate_dfu",
		},
		{
			name: "send_fw_manifest command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_SendFwManifest{
					SendFwManifest: &cmd_resp_dfu.CmdFirmwareUpdateManifest{},
				},
			},
			expectedResult: "send_fw_manifest",
		},
		{
			name: "begin_firmware_download command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_BeginFirmwareDownload{
					BeginFirmwareDownload: &cmd_resp_dfu.CmdBeginFirmwareDownload{},
				},
			},
			expectedResult: "begin_firmware_download",
		},
		{
			name: "download_firmware_chunk command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_DownloadFirmwareChunk{
					DownloadFirmwareChunk: &cmd_resp_dfu.CmdFirmwareDownloadChunk{},
				},
			},
			expectedResult: "download_firmware_chunk",
		},
		{
			name: "test_chunk command",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: &wrappers.WrapperCommand_TestChunk{
					TestChunk: &cmd_resp_comms.CmdChunkTest{},
				},
			},
			expectedResult: "test_chunk",
		},
		// Test case to cover the default block (unknown command type)
		{
			name: "unknown command type returns empty string",
			wrapperCmd: &wrappers.WrapperCommand{
				Command: nil, // This will cause the switch to hit the default case
			},
			expectedResult: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result := mapper.ExtractCommandType(tt.wrapperCmd)
			assert.Equal(t, tt.expectedResult, result)
		})
	}
}

func TestCommandPermissionMapper_GetRequiredPermissions(t *testing.T) {
	t.Parallel()

	mapper := NewCommandPermissionMapper()

	tests := []struct {
		name           string
		commandType    string
		expectedPerms  []string
		expectedExists bool
	}{
		{
			name:           "existing command with permissions",
			commandType:    "start_realtime",
			expectedPerms:  []string{"org_ng_all_access", "device_group_ng_all_access"},
			expectedExists: true,
		},
		{
			name:           "existing command with different permissions",
			commandType:    "rd_port1_stats",
			expectedPerms:  []string{"org_ng_read_stats", "device_group_ng_read_stats"},
			expectedExists: true,
		},
		{
			name:           "existing command with set time permissions",
			commandType:    "wr_date_time_dst",
			expectedPerms:  []string{"org_ng_set_time", "device_group_ng_set_time"},
			expectedExists: true,
		},
		{
			name:           "existing command with read config permissions",
			commandType:    "rd_channel_config",
			expectedPerms:  []string{"org_ng_read_config", "device_group_ng_read_config"},
			expectedExists: true,
		},
		{
			name:           "existing command with clear logs permissions",
			commandType:    "log_clear",
			expectedPerms:  []string{"org_ng_clear_logs", "device_group_ng_clear_logs"},
			expectedExists: true,
		},
		{
			name:           "existing command with reset stats permissions",
			commandType:    "clear_stats",
			expectedPerms:  []string{"org_ng_reset_stats", "device_group_ng_reset_stats"},
			expectedExists: true,
		},
		{
			name:           "existing command with write datakey permissions",
			commandType:    "wr_data_key",
			expectedPerms:  []string{"org_ng_write_datakey", "device_group_ng_write_datakey"},
			expectedExists: true,
		},
		{
			name:           "existing command with write user settings permissions",
			commandType:    "wr_user_settings",
			expectedPerms:  []string{"org_ng_write_user_settings", "device_group_ng_write_user_settings"},
			expectedExists: true,
		},
		{
			name:           "existing command with DFU permissions",
			commandType:    "manifest_versions",
			expectedPerms:  []string{"org_ng_dfu", "device_group_ng_dfu"},
			expectedExists: true,
		},
		{
			name:           "non-existing command",
			commandType:    "unknown_command",
			expectedPerms:  nil,
			expectedExists: false,
		},
		{
			name:           "empty command type",
			commandType:    "",
			expectedPerms:  nil,
			expectedExists: false,
		},
		{
			name:           "command with special characters",
			commandType:    "invalid-command@123",
			expectedPerms:  nil,
			expectedExists: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			permissions, exists := mapper.GetRequiredPermissions(tt.commandType)

			// Check if command exists
			assert.Equal(t, tt.expectedExists, exists, "Command existence should match expected")

			// Check permissions
			if tt.expectedExists {
				assert.NotNil(t, permissions, "Permissions should not be nil for existing commands")
				assert.Equal(t, tt.expectedPerms, permissions, "Permissions should match expected")
				assert.Greater(t, len(permissions), 0, "Permissions should not be empty for existing commands")
			} else {
				assert.Nil(t, permissions, "Permissions should be nil for non-existing commands")
			}
		})
	}
}

func TestCommandPermissionMapper_ValidateCommandPermissions(t *testing.T) {
	t.Parallel()

	// Create a mock database executor using the shared mocks
	mockDB := &mocks.FakeDBExecutor{}

	// Create test user permissions
	userPerms := &authorizer.UserPermissions{
		UserID: "test_user_123",
	}

	tests := []struct {
		name            string
		commandType     string
		deviceID        string
		userPermissions *authorizer.UserPermissions
		dbExecutor      connect.DatabaseExecutor
		expectedError   error
	}{
		{
			name:            "unknown command type returns error",
			commandType:     "unknown_command",
			deviceID:        "device_456",
			userPermissions: userPerms,
			dbExecutor:      mockDB,
			expectedError:   ErrUnknownCommandType,
		},
		{
			name:            "empty command type returns error",
			commandType:     "",
			deviceID:        "device_456",
			userPermissions: userPerms,
			dbExecutor:      mockDB,
			expectedError:   ErrUnknownCommandType,
		},
		{
			name:            "nil user permissions returns error",
			commandType:     "start_realtime",
			deviceID:        "device_456",
			userPermissions: nil,
			dbExecutor:      mockDB,
			expectedError:   ErrFailedToCheckPermissions,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create a new mapper for each test to avoid state pollution
			mapper := NewCommandPermissionMapper()

			// Call the method under test
			err := mapper.ValidateCommandPermissions(tt.userPermissions, tt.deviceID, tt.commandType, tt.dbExecutor)

			// Assert the result
			if tt.expectedError != nil {
				assert.Error(t, err, "Expected error but got none")
				assert.ErrorIs(t, err, tt.expectedError, "Error should match expected error type")
			} else {
				assert.NoError(t, err, "Expected no error but got one: %v", err)
			}
		})
	}
}

// TestCommandPermissionMapper_ValidateCommandPermissions_Comprehensive tests the complete flow of ValidateCommandPermissions
func TestCommandPermissionMapper_ValidateCommandPermissions_Comprehensive(t *testing.T) {
	t.Parallel()

	// Create test user permissions with different permission sets
	userPermsAllAccess := &authorizer.UserPermissions{
		UserID: "user_all_access",
		Permissions: []authorizer.Permission{
			{
				Scope:          "org",
				ScopeID:        "org_123",
				OrganizationID: "org_123",
				Permissions:    []string{"org_ng_all_access"},
			},
		},
	}

	userPermsReadStats := &authorizer.UserPermissions{
		UserID: "user_read_stats",
		Permissions: []authorizer.Permission{
			{
				Scope:          "org",
				ScopeID:        "org_123",
				OrganizationID: "org_123",
				Permissions:    []string{"org_ng_read_stats"},
			},
		},
	}

	userPermsNoAccess := &authorizer.UserPermissions{
		UserID: "user_no_access",
		Permissions: []authorizer.Permission{
			{
				Scope:          "org",
				ScopeID:        "org_123",
				OrganizationID: "org_123",
				Permissions:    []string{"org_ng_view_devices"}, // Different permission type
			},
		},
	}

	tests := []struct {
		name            string
		commandType     string
		deviceID        string
		userPermissions *authorizer.UserPermissions
		setupMock       func() connect.DatabaseExecutor
		expectedError   error
		description     string
	}{
		{
			name:            "user_with_read_stats_permission_granted",
			commandType:     "rd_port1_stats",
			deviceID:        "device_456",
			userPermissions: userPermsReadStats,
			setupMock: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Mock successful device access check by populating DeviceInfo
						if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
							deviceInfo.DeviceID = "device_456"
							deviceInfo.OrganizationID = "org_123"
							deviceInfo.DeviceGroupIDs = []string{"group_2"}
							deviceInfo.LocationGroupIDs = []string{"location_2"}
						}
						return nil
					},
				}
			},
			expectedError: nil,
			description:   "User with org_ng_read_stats permission should be granted access to rd_port1_stats command",
		},
		{
			name:            "user_without_required_permission_denied",
			commandType:     "start_realtime",
			deviceID:        "device_789",
			userPermissions: userPermsNoAccess,
			setupMock: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Mock successful device access check but user lacks required permissions
						if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
							deviceInfo.DeviceID = "device_789"
							deviceInfo.OrganizationID = "org_123"
							deviceInfo.DeviceGroupIDs = []string{"group_3"}
							deviceInfo.LocationGroupIDs = []string{"location_3"}
						}
						return nil
					},
				}
			},
			expectedError: ErrInsufficientPermissions,
			description:   "User without org_ng_all_access permission should be denied access to start_realtime command",
		},
		{
			name:            "database_error_during_permission_check",
			commandType:     "stop_realtime",
			deviceID:        "device_101",
			userPermissions: userPermsAllAccess,
			setupMock: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Mock database error during permission check
						return fmt.Errorf("database connection failed")
					},
				}
			},
			expectedError: ErrFailedToCheckPermissions,
			description:   "Database error during permission check should return ErrFailedToCheckPermissions",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create a new mapper for each test to avoid state pollution
			mapper := NewCommandPermissionMapper()

			// Setup mock database executor
			mockDB := tt.setupMock()

			// Call the method under test
			err := mapper.ValidateCommandPermissions(tt.userPermissions, tt.deviceID, tt.commandType, mockDB)

			// Assert the result
			if tt.expectedError != nil {
				assert.Error(t, err, "Expected error but got none for: %s", tt.description)
				assert.ErrorIs(t, err, tt.expectedError, "Error should match expected error type for: %s", tt.description)
			} else {
				assert.NoError(t, err, "Expected no error but got one for: %s: %v", tt.description, err)
			}
		})
	}
}
