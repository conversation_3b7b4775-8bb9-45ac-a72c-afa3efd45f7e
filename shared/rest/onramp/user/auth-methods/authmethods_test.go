package authmethods

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"synapse-its.com/shared/api/password"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// MockPasswordHasher implements the password.PasswordHasher interface for testing
type MockPasswordHasher struct {
	mock.Mock
}

func (m *MockPasswordHasher) HashPassword(password string) string {
	args := m.Called(password)
	return args.String(0)
}

func (m *MockPasswordHasher) ComparePassword(password, hash string) bool {
	args := m.Called(password, hash)
	return args.Bool(0)
}

// Test_parsePasswordUpdateRequest tests the parsePasswordUpdateRequest function
func Test_parsePasswordUpdateRequest(t *testing.T) {
	t.<PERSON>()

	tests := []struct {
		name         string
		requestBody  interface{}
		expectedErr  error
		wantErr      bool
		expectedResp *password.PasswordUpdateRequest
	}{
		{
			name: "valid_password_update_request",
			requestBody: password.PasswordUpdateRequest{
				CurrentPassword: "current123",
				NewPassword:     "passwordnew123",
				ConfirmPassword: "passwordnew123",
			},
			expectedErr: nil,
			wantErr:     false,
			expectedResp: &password.PasswordUpdateRequest{
				CurrentPassword: "current123",
				NewPassword:     "passwordnew123",
				ConfirmPassword: "passwordnew123",
			},
		},
		{
			name: "empty_current_password",
			requestBody: password.PasswordUpdateRequest{
				CurrentPassword: "",
				NewPassword:     "passwordnew123",
				ConfirmPassword: "passwordnew123",
			},
			expectedErr: password.ErrPasswordEmpty,
			wantErr:     true,
			expectedResp: &password.PasswordUpdateRequest{
				CurrentPassword: "",
				NewPassword:     "passwordnew123",
				ConfirmPassword: "passwordnew123",
			},
		},
		{
			name: "whitespace_only_current_password",
			requestBody: password.PasswordUpdateRequest{
				CurrentPassword: "   ",
				NewPassword:     "passwordnew123",
				ConfirmPassword: "passwordnew123",
			},
			expectedErr: password.ErrPasswordEmpty,
			wantErr:     true,
			expectedResp: &password.PasswordUpdateRequest{
				CurrentPassword: "   ",
				NewPassword:     "passwordnew123",
				ConfirmPassword: "passwordnew123",
			},
		},
		{
			name: "empty_new_password",
			requestBody: password.PasswordUpdateRequest{
				CurrentPassword: "current123",
				NewPassword:     "",
				ConfirmPassword: "passwordnew123",
			},
			expectedErr: password.ErrPasswordEmpty,
			wantErr:     true,
			expectedResp: &password.PasswordUpdateRequest{
				CurrentPassword: "current123",
				NewPassword:     "",
				ConfirmPassword: "passwordnew123",
			},
		},
		{
			name: "whitespace_only_new_password",
			requestBody: password.PasswordUpdateRequest{
				CurrentPassword: "current123",
				NewPassword:     "   ",
				ConfirmPassword: "passwordnew123",
			},
			expectedErr: password.ErrPasswordEmpty,
			wantErr:     true,
			expectedResp: &password.PasswordUpdateRequest{
				CurrentPassword: "current123",
				NewPassword:     "   ",
				ConfirmPassword: "passwordnew123",
			},
		},
		{
			name: "empty_confirm_password",
			requestBody: password.PasswordUpdateRequest{
				CurrentPassword: "current123",
				NewPassword:     "passwordnew123",
				ConfirmPassword: "",
			},
			expectedErr: password.ErrPasswordEmpty,
			wantErr:     true,
			expectedResp: &password.PasswordUpdateRequest{
				CurrentPassword: "current123",
				NewPassword:     "passwordnew123",
				ConfirmPassword: "",
			},
		},
		{
			name: "whitespace_only_confirm_password",
			requestBody: password.PasswordUpdateRequest{
				CurrentPassword: "current123",
				NewPassword:     "passwordnew123",
				ConfirmPassword: "   ",
			},
			expectedErr: password.ErrPasswordEmpty,
			wantErr:     true,
			expectedResp: &password.PasswordUpdateRequest{
				CurrentPassword: "current123",
				NewPassword:     "passwordnew123",
				ConfirmPassword: "   ",
			},
		},
		{
			name: "password_mismatch",
			requestBody: password.PasswordUpdateRequest{
				CurrentPassword: "current123",
				NewPassword:     "passwordnew123",
				ConfirmPassword: "different123",
			},
			expectedErr: password.ErrPasswordMismatch,
			wantErr:     true,
			expectedResp: &password.PasswordUpdateRequest{
				CurrentPassword: "current123",
				NewPassword:     "passwordnew123",
				ConfirmPassword: "different123",
			},
		},
		{
			name: "passwords_with_spaces",
			requestBody: password.PasswordUpdateRequest{
				CurrentPassword: "  current123  ",
				NewPassword:     "  passwordnew123  ",
				ConfirmPassword: "  passwordnew123  ",
			},
			expectedErr: nil,
			wantErr:     false,
			expectedResp: &password.PasswordUpdateRequest{
				CurrentPassword: "current123",
				NewPassword:     "passwordnew123",
				ConfirmPassword: "passwordnew123",
			},
		},
		{
			name:         "invalid_json",
			requestBody:  "invalid json string",
			expectedErr:  password.ErrInvalidJSON,
			wantErr:      true,
			expectedResp: &password.PasswordUpdateRequest{},
		},
		{
			name: "unknown_field",
			requestBody: map[string]interface{}{
				"current_password": "current123",
				"new_password":     "passwordnew123",
				"confirm_password": "passwordnew123",
				"unknown_field":    "value",
			},
			expectedErr: password.ErrInvalidJSON,
			wantErr:     true,
			expectedResp: &password.PasswordUpdateRequest{
				CurrentPassword: "current123",
				NewPassword:     "passwordnew123",
				ConfirmPassword: "passwordnew123",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create request body
			var body []byte
			var err error

			if str, ok := tt.requestBody.(string); ok {
				// For invalid JSON test case
				body = []byte(str)
			} else {
				body, err = json.Marshal(tt.requestBody)
				assert.NoError(t, err)
			}

			// Create HTTP request
			req := httptest.NewRequest("POST", "/test", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			// Execute the function under test
			result, err := password.ExtractAndValidatePasswordUpdate(req.Body)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				if tt.expectedResp != nil && result != nil {
					assert.Equal(t, tt.expectedResp.CurrentPassword, result.CurrentPassword)
					assert.Equal(t, tt.expectedResp.NewPassword, result.NewPassword)
					assert.Equal(t, tt.expectedResp.ConfirmPassword, result.ConfirmPassword)
				}
			} else {
				assert.NoError(t, err)
				if result != nil {
					assert.Equal(t, tt.expectedResp.CurrentPassword, result.CurrentPassword)
					assert.Equal(t, tt.expectedResp.NewPassword, result.NewPassword)
					assert.Equal(t, tt.expectedResp.ConfirmPassword, result.ConfirmPassword)
				}
			}
		})
	}
}

// Test_getAuthMethods tests the getAuthMethods function
func Test_getAuthMethods(t *testing.T) {
	t.Parallel()

	// Create a test user ID
	testUserID := uuid.MustParse("123e4567-e89b-12d3-a456-************")

	// Create test data
	testAuthMethods := []AuthMethodRecord{
		{
			ID:            "auth-method-1",
			Type:          "USERNAME_PASSWORD",
			UserName:      "testuser1",
			Email:         "<EMAIL>",
			FirstName:     "John",
			LastName:      "Doe",
			Organizations: []string{"Org1", "Org2"},
			IsEnabled:     true,
			LastLogin:     &time.Time{},
		},
		{
			ID:            "auth-method-2",
			Type:          "OIDC",
			UserName:      "testuser2",
			Email:         "<EMAIL>",
			FirstName:     "Jane",
			LastName:      "Smith",
			Organizations: []string{"Org3"},
			IsEnabled:     false,
			LastLogin:     nil,
		},
	}

	tests := []struct {
		name         string
		userID       uuid.UUID
		mockSetup    func(*dbexecutor.FakeDBExecutor)
		expectedResp []AuthMethodRecord
		expectedErr  error
		wantErr      bool
	}{
		{
			name:   "successful_auth_methods_retrieval",
			userID: testUserID,
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Verify the query contains expected elements
					assert.Contains(t, query, "SELECT")
					assert.Contains(t, query, "FROM {{AuthMethod}} am")
					assert.Contains(t, query, "INNER JOIN {{User}} u ON am.UserId = u.Id")
					assert.Contains(t, query, "WHERE u.Id = $1 AND am.IsDeleted = false")
					assert.Contains(t, query, "ORDER BY am.CreatedAt ASC")

					// Verify the user ID parameter
					assert.Len(t, args, 1)
					assert.Equal(t, testUserID, args[0])

					// Set the result
					*dest.(*[]AuthMethodRecord) = testAuthMethods
					return nil
				}
			},
			expectedResp: testAuthMethods,
			expectedErr:  nil,
			wantErr:      false,
		},
		{
			name:   "database_error",
			userID: testUserID,
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return assert.AnError
				}
			},
			expectedResp: nil,
			expectedErr:  assert.AnError,
			wantErr:      true,
		},
		{
			name:   "empty_result",
			userID: testUserID,
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					*dest.(*[]AuthMethodRecord) = []AuthMethodRecord{}
					return nil
				}
			},
			expectedResp: []AuthMethodRecord{},
			expectedErr:  nil,
			wantErr:      false,
		},
		{
			name:   "nil_user_id",
			userID: uuid.Nil,
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Verify the query is called with nil UUID
					assert.Len(t, args, 1)
					assert.Equal(t, uuid.Nil, args[0])

					*dest.(*[]AuthMethodRecord) = []AuthMethodRecord{}
					return nil
				}
			},
			expectedResp: []AuthMethodRecord{},
			expectedErr:  nil,
			wantErr:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}

			// Setup mock behavior
			if tt.mockSetup != nil {
				tt.mockSetup(mockDB)
			}

			// Execute the function under test
			result, err := getAuthMethods(mockDB, tt.userID)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResp, result)
			}

			// Verify that QueryGenericSlice was called
			assert.Equal(t, 1, mockDB.QueryGenericSliceCallCount)
		})
	}
}

// Test_validateAuthMethod tests the validateAuthMethod function
func Test_validateAuthMethod(t *testing.T) {
	t.Parallel()

	// Create test UUIDs
	testAuthMethodID := uuid.MustParse("123e4567-e89b-12d3-a456-************")
	testUserID := uuid.MustParse("987fcdeb-51a2-43d1-b654-************")

	tests := []struct {
		name           string
		authMethodID   uuid.UUID
		userID         uuid.UUID
		authMethodType string
		mockSetup      func(*dbexecutor.FakeDBExecutor)
		expectedErr    error
		wantErr        bool
	}{
		{
			name:           "valid_auth_method",
			authMethodID:   testAuthMethodID,
			userID:         testUserID,
			authMethodType: "USERNAME_PASSWORD",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Verify the query contains expected elements
					assert.Contains(t, query, "SELECT")
					assert.Contains(t, query, "FROM {{AuthMethod}}")
					assert.Contains(t, query, "WHERE Id = $1 AND UserId = $2 AND IsDeleted = false")

					// Verify the parameters
					assert.Len(t, args, 2)
					assert.Equal(t, testAuthMethodID, args[0])
					assert.Equal(t, testUserID, args[1])

					// Set the result
					*dest.(*ValidateAuthMethodRecord) = ValidateAuthMethodRecord{
						AuthMethodID:   testAuthMethodID.String(),
						AuthMethodType: "USERNAME_PASSWORD",
					}
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:           "different_auth_method_type",
			authMethodID:   testAuthMethodID,
			userID:         testUserID,
			authMethodType: "OIDC",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Set the result with different type
					*dest.(*ValidateAuthMethodRecord) = ValidateAuthMethodRecord{
						AuthMethodID:   testAuthMethodID.String(),
						AuthMethodType: "USERNAME_PASSWORD", // Different from expected
					}
					return nil
				}
			},
			expectedErr: ErrInvalidAuthMethodType,
			wantErr:     true,
		},
		{
			name:           "database_error",
			authMethodID:   testAuthMethodID,
			userID:         testUserID,
			authMethodType: "USERNAME_PASSWORD",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return assert.AnError
				}
			},
			expectedErr: ErrInvalidAuthMethodID,
			wantErr:     true,
		},
		{
			name:           "nil_auth_method_id",
			authMethodID:   uuid.Nil,
			userID:         testUserID,
			authMethodType: "USERNAME_PASSWORD",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Verify the query is called with nil UUID
					assert.Len(t, args, 2)
					assert.Equal(t, uuid.Nil, args[0])
					assert.Equal(t, testUserID, args[1])

					return assert.AnError
				}
			},
			expectedErr: ErrInvalidAuthMethodID,
			wantErr:     true,
		},
		{
			name:           "nil_user_id",
			authMethodID:   testAuthMethodID,
			userID:         uuid.Nil,
			authMethodType: "USERNAME_PASSWORD",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Verify the query is called with nil UUID
					assert.Len(t, args, 2)
					assert.Equal(t, testAuthMethodID, args[0])
					assert.Equal(t, uuid.Nil, args[1])

					return assert.AnError
				}
			},
			expectedErr: ErrInvalidAuthMethodID,
			wantErr:     true,
		},
		{
			name:           "oidc_auth_method_type",
			authMethodID:   testAuthMethodID,
			userID:         testUserID,
			authMethodType: "OIDC",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Set the result with OIDC type
					*dest.(*ValidateAuthMethodRecord) = ValidateAuthMethodRecord{
						AuthMethodID:   testAuthMethodID.String(),
						AuthMethodType: "OIDC",
					}
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}

			// Setup mock behavior
			if tt.mockSetup != nil {
				tt.mockSetup(mockDB)
			}

			// Execute the function under test
			err := validateAuthMethod(mockDB, tt.authMethodID, tt.userID, tt.authMethodType)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify that QueryRowStruct was called
			assert.Equal(t, 1, mockDB.QueryRowStructCallCount)
		})
	}
}

// Test_deleteAuthMethod tests the deleteAuthMethod function
func Test_deleteAuthMethod(t *testing.T) {
	t.Parallel()

	// Create test UUID
	testAuthMethodID := uuid.MustParse("123e4567-e89b-12d3-a456-************")

	tests := []struct {
		name         string
		authMethodID uuid.UUID
		mockSetup    func(*dbexecutor.FakeDBExecutor)
		expectedErr  error
		wantErr      bool
	}{
		{
			name:         "successful_delete",
			authMethodID: testAuthMethodID,
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					// Verify the query contains expected elements
					assert.Contains(t, query, "UPDATE {{AuthMethod}}")
					assert.Contains(t, query, "SET IsDeleted = true")
					assert.Contains(t, query, "UpdatedAt = $2")
					assert.Contains(t, query, "WHERE Id = $1")

					// Verify the parameters
					assert.Len(t, args, 2)
					assert.Equal(t, testAuthMethodID, args[0])
					assert.IsType(t, time.Time{}, args[1])

					// Return a mock result with 1 row affected
					return &mockResult{rowsAffected: 1}, nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:         "database_exec_error",
			authMethodID: testAuthMethodID,
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, assert.AnError
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:         "rows_affected_error",
			authMethodID: testAuthMethodID,
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					// Return a mock result that will cause RowsAffected to error
					return &mockResult{rowsAffectedError: assert.AnError}, nil
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:         "no_rows_affected",
			authMethodID: testAuthMethodID,
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					// Return a mock result with 0 rows affected
					return &mockResult{rowsAffected: 0}, nil
				}
			},
			expectedErr: ErrAuthMethodNotFound,
			wantErr:     true,
		},
		{
			name:         "nil_auth_method_id",
			authMethodID: uuid.Nil,
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					// Verify the query is called with nil UUID
					assert.Len(t, args, 2)
					assert.Equal(t, uuid.Nil, args[0])
					assert.IsType(t, time.Time{}, args[1])

					// Return a mock result with 0 rows affected
					return &mockResult{rowsAffected: 0}, nil
				}
			},
			expectedErr: ErrAuthMethodNotFound,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}

			// Setup mock behavior
			if tt.mockSetup != nil {
				tt.mockSetup(mockDB)
			}

			// Execute the function under test
			err := deleteAuthMethod(mockDB, tt.authMethodID)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify that Exec was called
			assert.Equal(t, 1, mockDB.ExecCallCount)
		})
	}
}

// mockResult implements sql.Result for testing
type mockResult struct {
	rowsAffected      int64
	rowsAffectedError error
	lastInsertId      int64
	lastInsertIdError error
}

func (m *mockResult) LastInsertId() (int64, error) {
	return m.lastInsertId, m.lastInsertIdError
}

func (m *mockResult) RowsAffected() (int64, error) {
	return m.rowsAffected, m.rowsAffectedError
}

// Test_validateCurrentPassword tests the validateCurrentPassword function
func Test_validateCurrentPassword(t *testing.T) {
	t.Parallel()

	// Create test UUID
	testAuthMethodID := uuid.MustParse("123e4567-e89b-12d3-a456-************")

	tests := []struct {
		name                string
		authMethodID        uuid.UUID
		currentPasswordHash string
		mockSetup           func(*dbexecutor.FakeDBExecutor)
		expectedErr         error
		wantErr             bool
	}{
		{
			name:                "valid_password_match",
			authMethodID:        testAuthMethodID,
			currentPasswordHash: "hashed_password_123",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Verify the query contains expected elements
					assert.Contains(t, query, "SELECT")
					assert.Contains(t, query, "FROM {{AuthMethod}}")
					assert.Contains(t, query, "WHERE Id = $1 AND Type =$2 AND IsDeleted = false")

					// Verify the parameters
					assert.Len(t, args, 2)
					assert.Equal(t, testAuthMethodID, args[0])
					assert.Equal(t, "USERNAME_PASSWORD", args[1])

					// Set the result with matching password hash
					*dest.(*ValidateCurrentPasswordRecord) = ValidateCurrentPasswordRecord{
						PasswordHash: "hashed_password_123",
					}
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:                "password_mismatch",
			authMethodID:        testAuthMethodID,
			currentPasswordHash: "hashed_password_123",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Set the result with different password hash
					*dest.(*ValidateCurrentPasswordRecord) = ValidateCurrentPasswordRecord{
						PasswordHash: "different_hashed_password",
					}
					return nil
				}
			},
			expectedErr: ErrInvalidCurrentPassword,
			wantErr:     true,
		},
		{
			name:                "database_error",
			authMethodID:        testAuthMethodID,
			currentPasswordHash: "hashed_password_123",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return assert.AnError
				}
			},
			expectedErr: ErrInvalidAuthMethodID,
			wantErr:     true,
		},
		{
			name:                "nil_auth_method_id",
			authMethodID:        uuid.Nil,
			currentPasswordHash: "hashed_password_123",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Verify the query is called with nil UUID
					assert.Len(t, args, 2)
					assert.Equal(t, uuid.Nil, args[0])
					assert.Equal(t, "USERNAME_PASSWORD", args[1])

					return assert.AnError
				}
			},
			expectedErr: ErrInvalidAuthMethodID,
			wantErr:     true,
		},
		{
			name:                "empty_password_hash",
			authMethodID:        testAuthMethodID,
			currentPasswordHash: "",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Set the result with empty password hash
					*dest.(*ValidateCurrentPasswordRecord) = ValidateCurrentPasswordRecord{
						PasswordHash: "",
					}
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:                "empty_stored_password_hash",
			authMethodID:        testAuthMethodID,
			currentPasswordHash: "hashed_password_123",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Set the result with empty stored password hash
					*dest.(*ValidateCurrentPasswordRecord) = ValidateCurrentPasswordRecord{
						PasswordHash: "",
					}
					return nil
				}
			},
			expectedErr: ErrInvalidCurrentPassword,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}

			// Setup mock behavior
			if tt.mockSetup != nil {
				tt.mockSetup(mockDB)
			}

			// Execute the function under test
			err := validateCurrentPassword(mockDB, tt.authMethodID, tt.currentPasswordHash)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify that QueryRowStruct was called
			assert.Equal(t, 1, mockDB.QueryRowStructCallCount)
		})
	}
}

// Test_updatePassword tests the updatePassword function
func Test_updatePassword(t *testing.T) {
	t.Parallel()

	// Create test UUID
	testAuthMethodID := uuid.MustParse("123e4567-e89b-12d3-a456-************")

	tests := []struct {
		name            string
		authMethodID    uuid.UUID
		newPasswordHash string
		mockSetup       func(*dbexecutor.FakeDBExecutor)
		expectedErr     error
		wantErr         bool
	}{
		{
			name:            "successful_password_update",
			authMethodID:    testAuthMethodID,
			newPasswordHash: "new_hashed_password_123",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					// Verify the query contains expected elements
					assert.Contains(t, query, "UPDATE {{AuthMethod}}")
					assert.Contains(t, query, "SET PasswordHash = $2")
					assert.Contains(t, query, "UpdatedAt = $3")
					assert.Contains(t, query, "WHERE Id = $1 AND Type = $4 AND IsDeleted = false")

					// Verify the parameters
					assert.Len(t, args, 4)
					assert.Equal(t, testAuthMethodID, args[0])
					assert.Equal(t, "new_hashed_password_123", args[1])
					assert.IsType(t, time.Time{}, args[2])
					assert.Equal(t, "USERNAME_PASSWORD", args[3])

					// Return a mock result with 1 row affected
					return &mockResult{rowsAffected: 1}, nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:            "database_exec_error",
			authMethodID:    testAuthMethodID,
			newPasswordHash: "new_hashed_password_123",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, assert.AnError
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:            "rows_affected_error",
			authMethodID:    testAuthMethodID,
			newPasswordHash: "new_hashed_password_123",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					// Return a mock result that will cause RowsAffected to error
					return &mockResult{rowsAffectedError: assert.AnError}, nil
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:            "no_rows_affected",
			authMethodID:    testAuthMethodID,
			newPasswordHash: "new_hashed_password_123",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					// Return a mock result with 0 rows affected
					return &mockResult{rowsAffected: 0}, nil
				}
			},
			expectedErr: ErrAuthMethodNotFound,
			wantErr:     true,
		},
		{
			name:            "nil_auth_method_id",
			authMethodID:    uuid.Nil,
			newPasswordHash: "new_hashed_password_123",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					// Verify the query is called with nil UUID
					assert.Len(t, args, 4)
					assert.Equal(t, uuid.Nil, args[0])
					assert.Equal(t, "new_hashed_password_123", args[1])
					assert.IsType(t, time.Time{}, args[2])
					assert.Equal(t, "USERNAME_PASSWORD", args[3])

					// Return a mock result with 0 rows affected
					return &mockResult{rowsAffected: 0}, nil
				}
			},
			expectedErr: ErrAuthMethodNotFound,
			wantErr:     true,
		},
		{
			name:            "empty_password_hash",
			authMethodID:    testAuthMethodID,
			newPasswordHash: "",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor) {
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					// Verify the query is called with empty password hash
					assert.Len(t, args, 4)
					assert.Equal(t, testAuthMethodID, args[0])
					assert.Equal(t, "", args[1])
					assert.IsType(t, time.Time{}, args[2])
					assert.Equal(t, "USERNAME_PASSWORD", args[3])

					// Return a mock result with 1 row affected
					return &mockResult{rowsAffected: 1}, nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}

			// Setup mock behavior
			if tt.mockSetup != nil {
				tt.mockSetup(mockDB)
			}

			// Execute the function under test
			err := updatePassword(mockDB, tt.authMethodID, tt.newPasswordHash)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify that Exec was called
			assert.Equal(t, 1, mockDB.ExecCallCount)
		})
	}
}

// Test_GetAuthMethodsHandlerWithDeps tests the GetAuthMethodsHandlerWithDeps function
func Test_GetAuthMethodsHandlerWithDeps(t *testing.T) {
	t.Parallel()

	// Create test UUID
	testUserID := uuid.MustParse("123e4567-e89b-12d3-a456-************")

	// Create test auth methods data
	testAuthMethods := []AuthMethodRecord{
		{
			ID:            "auth-method-1",
			Type:          "USERNAME_PASSWORD",
			UserName:      "testuser1",
			Email:         "<EMAIL>",
			FirstName:     "John",
			LastName:      "Doe",
			Organizations: []string{"Org1", "Org2"},
			IsEnabled:     true,
			LastLogin:     &time.Time{},
		},
		{
			ID:            "auth-method-2",
			Type:          "OIDC",
			UserName:      "testuser2",
			Email:         "<EMAIL>",
			FirstName:     "Jane",
			LastName:      "Smith",
			Organizations: []string{"Org3"},
			IsEnabled:     false,
			LastLogin:     nil,
		},
	}

	tests := []struct {
		name           string
		requestURL     string
		mockSetup      func(*dbexecutor.FakeDBExecutor, *HandlerDeps)
		expectedStatus int
		expectedBody   interface{}
		wantErr        bool
	}{
		{
			name:       "successful_auth_methods_retrieval",
			requestURL: "/auth-methods/" + testUserID.String(),
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: mockDB,
					}, nil
				}

				// Mock GetAuthMethods
				deps.GetAuthMethods = func(pg connect.DatabaseExecutor, userID uuid.UUID) ([]AuthMethodRecord, error) {
					assert.Equal(t, testUserID, userID)
					return testAuthMethods, nil
				}
			},
			expectedStatus: http.StatusOK,
			expectedBody:   testAuthMethods,
			wantErr:        false,
		},
		{
			name:       "invalid_user_id",
			requestURL: "/auth-methods/invalid-uuid",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: mockDB,
					}, nil
				}

				// Mock GetAuthMethods (should not be called)
				deps.GetAuthMethods = func(pg connect.DatabaseExecutor, userID uuid.UUID) ([]AuthMethodRecord, error) {
					t.Error("GetAuthMethods should not be called when user ID is invalid")
					return nil, nil
				}
			},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   nil,
			wantErr:        true,
		},
		{
			name:       "get_connections_error",
			requestURL: "/auth-methods/" + testUserID.String(),
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections to return error
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return nil, assert.AnError
				}

				// Mock GetAuthMethods (should not be called)
				deps.GetAuthMethods = func(pg connect.DatabaseExecutor, userID uuid.UUID) ([]AuthMethodRecord, error) {
					t.Error("GetAuthMethods should not be called when GetConnections fails")
					return nil, nil
				}
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   nil,
			wantErr:        true,
		},
		{
			name:       "get_auth_methods_error",
			requestURL: "/auth-methods/" + testUserID.String(),
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: mockDB,
					}, nil
				}

				// Mock GetAuthMethods to return error
				deps.GetAuthMethods = func(pg connect.DatabaseExecutor, userID uuid.UUID) ([]AuthMethodRecord, error) {
					assert.Equal(t, testUserID, userID)
					return nil, assert.AnError
				}
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   nil,
			wantErr:        true,
		},
		{
			name:       "empty_auth_methods",
			requestURL: "/auth-methods/" + testUserID.String(),
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: mockDB,
					}, nil
				}

				// Mock GetAuthMethods to return empty slice
				deps.GetAuthMethods = func(pg connect.DatabaseExecutor, userID uuid.UUID) ([]AuthMethodRecord, error) {
					assert.Equal(t, testUserID, userID)
					return []AuthMethodRecord{}, nil
				}
			},
			expectedStatus: http.StatusOK,
			expectedBody:   []AuthMethodRecord{},
			wantErr:        false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}

			// Create handler dependencies
			deps := &HandlerDeps{}

			// Setup mock behavior
			if tt.mockSetup != nil {
				tt.mockSetup(mockDB, deps)
			}

			// Create request
			req := httptest.NewRequest("GET", tt.requestURL, nil)

			// Set URL variables for user ID
			if strings.Contains(tt.requestURL, testUserID.String()) {
				req = mux.SetURLVars(req, map[string]string{
					"userId": testUserID.String(),
				})
			} else if strings.Contains(tt.requestURL, "invalid-uuid") {
				req = mux.SetURLVars(req, map[string]string{
					"userId": "invalid-uuid",
				})
			}

			// Create response recorder
			w := httptest.NewRecorder()

			// Create handler
			handler := GetAuthMethodsHandlerWithDeps(*deps)

			// Execute handler
			handler.ServeHTTP(w, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, w.Code)

			// Parse response body
			var responseBody interface{}
			if w.Body.Len() > 0 {
				err := json.Unmarshal(w.Body.Bytes(), &responseBody)
				assert.NoError(t, err)
			}

			// Assert response body for success cases
			if tt.expectedStatus == http.StatusOK && tt.expectedBody != nil {
				// For success cases, the response should contain the auth methods
				responseMap, ok := responseBody.(map[string]interface{})
				assert.True(t, ok)
				assert.Contains(t, responseMap, "data")
			}
		})
	}
}

// Test_DeleteAuthMethodHandlerWithDeps tests the DeleteAuthMethodHandlerWithDeps function
func Test_DeleteAuthMethodHandlerWithDeps(t *testing.T) {
	t.Parallel()

	// Create test UUIDs
	testUserID := uuid.MustParse("123e4567-e89b-12d3-a456-************")
	testAuthMethodID := uuid.MustParse("987fcdeb-51a2-43d1-b654-************")

	tests := []struct {
		name           string
		requestURL     string
		mockSetup      func(*dbexecutor.FakeDBExecutor, *HandlerDeps)
		expectedStatus int
		expectedBody   interface{}
		wantErr        bool
	}{
		{
			name:       "successful_auth_method_deletion",
			requestURL: "/auth-methods/" + testUserID.String() + "/" + testAuthMethodID.String(),
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: mockDB,
					}, nil
				}

				// Mock ValidateAuthMethod
				deps.ValidateAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
					assert.Equal(t, testAuthMethodID, authMethodID)
					assert.Equal(t, testUserID, userID)
					assert.Equal(t, "OIDC", authMethodType)
					return nil
				}

				// Mock DeleteAuthMethod
				deps.DeleteAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID) error {
					assert.Equal(t, testAuthMethodID, authMethodID)
					return nil
				}
			},
			expectedStatus: http.StatusOK,
			expectedBody:   nil,
			wantErr:        false,
		},
		{
			name:       "invalid_user_id",
			requestURL: "/auth-methods/invalid-uuid/" + testAuthMethodID.String(),
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: mockDB,
					}, nil
				}

				// Mock ValidateAuthMethod (should not be called)
				deps.ValidateAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
					t.Error("ValidateAuthMethod should not be called when user ID is invalid")
					return nil
				}

				// Mock DeleteAuthMethod (should not be called)
				deps.DeleteAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID) error {
					t.Error("DeleteAuthMethod should not be called when user ID is invalid")
					return nil
				}
			},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   nil,
			wantErr:        true,
		},
		{
			name:       "invalid_auth_method_id",
			requestURL: "/auth-methods/" + testUserID.String() + "/invalid-uuid",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: mockDB,
					}, nil
				}

				// Mock ValidateAuthMethod (should not be called)
				deps.ValidateAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
					t.Error("ValidateAuthMethod should not be called when auth method ID is invalid")
					return nil
				}

				// Mock DeleteAuthMethod (should not be called)
				deps.DeleteAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID) error {
					t.Error("DeleteAuthMethod should not be called when auth method ID is invalid")
					return nil
				}
			},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   nil,
			wantErr:        true,
		},
		{
			name:       "get_connections_error",
			requestURL: "/auth-methods/" + testUserID.String() + "/" + testAuthMethodID.String(),
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections to return error
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return nil, assert.AnError
				}

				// Mock ValidateAuthMethod (should not be called)
				deps.ValidateAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
					t.Error("ValidateAuthMethod should not be called when GetConnections fails")
					return nil
				}

				// Mock DeleteAuthMethod (should not be called)
				deps.DeleteAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID) error {
					t.Error("DeleteAuthMethod should not be called when GetConnections fails")
					return nil
				}
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   nil,
			wantErr:        true,
		},
		{
			name:       "validate_auth_method_error",
			requestURL: "/auth-methods/" + testUserID.String() + "/" + testAuthMethodID.String(),
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: mockDB,
					}, nil
				}

				// Mock ValidateAuthMethod to return error
				deps.ValidateAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
					assert.Equal(t, testAuthMethodID, authMethodID)
					assert.Equal(t, testUserID, userID)
					assert.Equal(t, "OIDC", authMethodType)
					return assert.AnError
				}

				// Mock DeleteAuthMethod (should not be called)
				deps.DeleteAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID) error {
					t.Error("DeleteAuthMethod should not be called when ValidateAuthMethod fails")
					return nil
				}
			},
			expectedStatus: http.StatusForbidden,
			expectedBody:   nil,
			wantErr:        true,
		},
		{
			name:       "delete_auth_method_error",
			requestURL: "/auth-methods/" + testUserID.String() + "/" + testAuthMethodID.String(),
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: mockDB,
					}, nil
				}

				// Mock ValidateAuthMethod
				deps.ValidateAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
					assert.Equal(t, testAuthMethodID, authMethodID)
					assert.Equal(t, testUserID, userID)
					assert.Equal(t, "OIDC", authMethodType)
					return nil
				}

				// Mock DeleteAuthMethod to return error
				deps.DeleteAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID) error {
					assert.Equal(t, testAuthMethodID, authMethodID)
					return assert.AnError
				}
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   nil,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}

			// Create handler dependencies
			deps := &HandlerDeps{}

			// Setup mock behavior
			if tt.mockSetup != nil {
				tt.mockSetup(mockDB, deps)
			}

			// Create request
			req := httptest.NewRequest("DELETE", tt.requestURL, nil)

			// Set URL variables for user ID and auth method ID
			if strings.Contains(tt.requestURL, testUserID.String()) && strings.Contains(tt.requestURL, testAuthMethodID.String()) {
				req = mux.SetURLVars(req, map[string]string{
					"userId":       testUserID.String(),
					"authMethodId": testAuthMethodID.String(),
				})
			} else if strings.Contains(tt.requestURL, "invalid-uuid") && strings.Contains(tt.requestURL, testAuthMethodID.String()) {
				req = mux.SetURLVars(req, map[string]string{
					"userId":       "invalid-uuid",
					"authMethodId": testAuthMethodID.String(),
				})
			} else if strings.Contains(tt.requestURL, testUserID.String()) && strings.Contains(tt.requestURL, "invalid-uuid") {
				req = mux.SetURLVars(req, map[string]string{
					"userId":       testUserID.String(),
					"authMethodId": "invalid-uuid",
				})
			}

			// Create response recorder
			w := httptest.NewRecorder()

			// Create handler
			handler := DeleteAuthMethodHandlerWithDeps(*deps)

			// Execute handler
			handler.ServeHTTP(w, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, w.Code)

			// Parse response body
			var responseBody interface{}
			if w.Body.Len() > 0 {
				err := json.Unmarshal(w.Body.Bytes(), &responseBody)
				assert.NoError(t, err)
			}

			// Assert response body for success cases
			if tt.expectedStatus == http.StatusOK {
				// For success cases, the response should be empty or contain success message
				if w.Body.Len() > 0 {
					_, ok := responseBody.(map[string]interface{})
					assert.True(t, ok)
					// Success response might contain a message or be empty
				}
			}
		})
	}
}

// Test_UpdatePasswordHandlerWithDeps tests the UpdatePasswordHandlerWithDeps function
func Test_UpdatePasswordHandlerWithDeps(t *testing.T) {
	t.Parallel()

	// Create test UUIDs
	testUserID := uuid.MustParse("123e4567-e89b-12d3-a456-************")
	testAuthMethodID := uuid.MustParse("987fcdeb-51a2-43d1-b654-************")

	// Create test request body
	testRequestBody := password.PasswordUpdateRequest{
		CurrentPassword: "current123",
		NewPassword:     "passwordnew123",
		ConfirmPassword: "passwordnew123",
	}

	// Create mock password hasher
	mockPasswordHasher := &MockPasswordHasher{}
	mockPasswordHasher.On("HashPassword", "current123").Return("hashed_current_password")
	mockPasswordHasher.On("HashPassword", "passwordnew123").Return("hashed_new_password")
	mockPasswordHasher.On("ComparePassword", "current123", "hashed_current_password").Return(true)
	mockPasswordHasher.On("ComparePassword", "passwordnew123", "hashed_new_password").Return(true)

	tests := []struct {
		name           string
		requestURL     string
		requestBody    interface{}
		mockSetup      func(*dbexecutor.FakeDBExecutor, *HandlerDeps)
		expectedStatus int
		expectedBody   interface{}
		wantErr        bool
	}{
		{
			name:        "successful_password_update",
			requestURL:  "/auth-methods/" + testUserID.String() + "/" + testAuthMethodID.String(),
			requestBody: testRequestBody,
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: mockDB,
					}, nil
				}

				// Mock ValidateAuthMethod
				deps.ValidateAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
					assert.Equal(t, testAuthMethodID, authMethodID)
					assert.Equal(t, "USERNAME_PASSWORD", authMethodType)
					return nil
				}

				// Set mock password hasher
				deps.PasswordHasher = mockPasswordHasher

				// Mock ValidateCurrentPassword
				deps.ValidateCurrentPassword = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, hashedPassword string) error {
					assert.Equal(t, testAuthMethodID, authMethodID)
					assert.Equal(t, "hashed_current_password", hashedPassword)
					return nil
				}

				// Mock UpdatePassword
				deps.UpdatePassword = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, hashedPassword string) error {
					assert.Equal(t, testAuthMethodID, authMethodID)
					assert.Equal(t, "hashed_new_password", hashedPassword)
					return nil
				}
			},
			expectedStatus: http.StatusOK,
			expectedBody:   nil,
			wantErr:        false,
		},
		{
			name:        "invalid_user_id",
			requestURL:  "/auth-methods/invalid-uuid/" + testAuthMethodID.String(),
			requestBody: testRequestBody,
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: mockDB,
					}, nil
				}

				// Mock ValidateAuthMethod (should not be called)
				deps.ValidateAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
					t.Error("ValidateAuthMethod should not be called when user ID is invalid")
					return nil
				}

				// Set mock password hasher (should not be called)
				mockHasher := &MockPasswordHasher{}
				deps.PasswordHasher = mockHasher

				// Mock ValidateCurrentPassword (should not be called)
				deps.ValidateCurrentPassword = func(pg connect.DatabaseExecutor, userID uuid.UUID, hashedPassword string) error {
					t.Error("ValidateCurrentPassword should not be called when user ID is invalid")
					return nil
				}

				// Mock UpdatePassword (should not be called)
				deps.UpdatePassword = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, hashedPassword string) error {
					t.Error("UpdatePassword should not be called when user ID is invalid")
					return nil
				}
			},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   nil,
			wantErr:        true,
		},
		{
			name:        "invalid_auth_method_id",
			requestURL:  "/auth-methods/" + testUserID.String() + "/invalid-uuid",
			requestBody: testRequestBody,
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: mockDB,
					}, nil
				}

				// Mock ValidateAuthMethod (should not be called)
				deps.ValidateAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
					t.Error("ValidateAuthMethod should not be called when auth method ID is invalid")
					return nil
				}

				// Set mock password hasher (should not be called)
				mockHasher := &MockPasswordHasher{}
				deps.PasswordHasher = mockHasher

				// Mock ValidateCurrentPassword (should not be called)
				deps.ValidateCurrentPassword = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, hashedPassword string) error {
					t.Error("ValidateCurrentPassword should not be called when auth method ID is invalid")
					return nil
				}

				// Mock UpdatePassword (should not be called)
				deps.UpdatePassword = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, hashedPassword string) error {
					t.Error("UpdatePassword should not be called when auth method ID is invalid")
					return nil
				}
			},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   nil,
			wantErr:        true,
		},
		{
			name:        "get_connections_error",
			requestURL:  "/auth-methods/" + testUserID.String() + "/" + testAuthMethodID.String(),
			requestBody: testRequestBody,
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections to return error
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return nil, assert.AnError
				}

				// Mock ValidateAuthMethod (should not be called)
				deps.ValidateAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
					t.Error("ValidateAuthMethod should not be called when GetConnections fails")
					return nil
				}

				// Set mock password hasher (should not be called)
				mockHasher := &MockPasswordHasher{}
				deps.PasswordHasher = mockHasher

				// Mock ValidateCurrentPassword (should not be called)
				deps.ValidateCurrentPassword = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, hashedPassword string) error {
					t.Error("ValidateCurrentPassword should not be called when GetConnections fails")
					return nil
				}

				// Mock UpdatePassword (should not be called)
				deps.UpdatePassword = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, hashedPassword string) error {
					t.Error("UpdatePassword should not be called when GetConnections fails")
					return nil
				}
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   nil,
			wantErr:        true,
		},
		{
			name:        "validate_auth_method_error",
			requestURL:  "/auth-methods/" + testUserID.String() + "/" + testAuthMethodID.String(),
			requestBody: testRequestBody,
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: mockDB,
					}, nil
				}

				// Mock ValidateAuthMethod to return error
				deps.ValidateAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
					assert.Equal(t, testAuthMethodID, authMethodID)
					assert.Equal(t, "USERNAME_PASSWORD", authMethodType)
					return assert.AnError
				}

				// Set mock password hasher (should not be called)
				mockHasher := &MockPasswordHasher{}
				deps.PasswordHasher = mockHasher

				// Mock ValidateCurrentPassword (should not be called)
				deps.ValidateCurrentPassword = func(pg connect.DatabaseExecutor, userID uuid.UUID, hashedPassword string) error {
					t.Error("ValidateCurrentPassword should not be called when ValidateAuthMethod fails")
					return nil
				}

				// Mock UpdatePassword (should not be called)
				deps.UpdatePassword = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, hashedPassword string) error {
					assert.Equal(t, testAuthMethodID, authMethodID)
					assert.Equal(t, "hashed_new_password", hashedPassword)
					return assert.AnError
				}
			},
			expectedStatus: http.StatusForbidden,
			expectedBody:   nil,
			wantErr:        true,
		},
		{
			name:        "invalid_request_body",
			requestURL:  "/auth-methods/" + testUserID.String() + "/" + testAuthMethodID.String(),
			requestBody: "invalid json",
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: mockDB,
					}, nil
				}

				// Mock ValidateAuthMethod
				deps.ValidateAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
					assert.Equal(t, testAuthMethodID, authMethodID)
					assert.Equal(t, "USERNAME_PASSWORD", authMethodType)
					return nil
				}

				// Set mock password hasher (should not be called)
				mockHasher := &MockPasswordHasher{}
				deps.PasswordHasher = mockHasher

				// Mock ValidateCurrentPassword (should not be called)
				deps.ValidateCurrentPassword = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, hashedPassword string) error {
					t.Error("ValidateCurrentPassword should not be called when request body is invalid")
					return nil
				}

				// Mock UpdatePassword (should not be called)
				deps.UpdatePassword = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, hashedPassword string) error {
					t.Error("UpdatePassword should not be called when request body is invalid")
					return nil
				}
			},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   nil,
			wantErr:        true,
		},
		{
			name:        "validate_current_password_error",
			requestURL:  "/auth-methods/" + testUserID.String() + "/" + testAuthMethodID.String(),
			requestBody: testRequestBody,
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: mockDB,
					}, nil
				}

				// Mock ValidateAuthMethod
				deps.ValidateAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
					assert.Equal(t, testAuthMethodID, authMethodID)
					assert.Equal(t, "USERNAME_PASSWORD", authMethodType)
					return nil
				}

				// Set mock password hasher
				deps.PasswordHasher = mockPasswordHasher

				// Mock ValidateCurrentPassword to return error
				deps.ValidateCurrentPassword = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, hashedPassword string) error {
					assert.Equal(t, testAuthMethodID, authMethodID)
					assert.Equal(t, "hashed_current_password", hashedPassword)
					return assert.AnError
				}

				// Mock UpdatePassword (should not be called)
				deps.UpdatePassword = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, hashedPassword string) error {
					t.Error("UpdatePassword should not be called when ValidateCurrentPassword fails")
					return nil
				}
			},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   nil,
			wantErr:        true,
		},
		{
			name:        "update_password_error",
			requestURL:  "/auth-methods/" + testUserID.String() + "/" + testAuthMethodID.String(),
			requestBody: testRequestBody,
			mockSetup: func(mockDB *dbexecutor.FakeDBExecutor, deps *HandlerDeps) {
				// Mock GetConnections
				deps.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: mockDB,
					}, nil
				}

				// Mock ValidateAuthMethod
				deps.ValidateAuthMethod = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, userID uuid.UUID, authMethodType string) error {
					assert.Equal(t, testAuthMethodID, authMethodID)
					assert.Equal(t, "USERNAME_PASSWORD", authMethodType)
					return nil
				}

				// Set mock password hasher
				deps.PasswordHasher = mockPasswordHasher

				// Mock ValidateCurrentPassword
				deps.ValidateCurrentPassword = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, hashedPassword string) error {
					assert.Equal(t, testAuthMethodID, authMethodID)
					assert.Equal(t, "hashed_current_password", hashedPassword)
					return nil
				}

				// Mock UpdatePassword to return error
				deps.UpdatePassword = func(pg connect.DatabaseExecutor, authMethodID uuid.UUID, hashedPassword string) error {
					assert.Equal(t, testAuthMethodID, authMethodID)
					assert.Equal(t, "hashed_new_password", hashedPassword)
					return assert.AnError
				}
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   nil,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}

			// Create handler dependencies
			deps := &HandlerDeps{}

			// Setup mock behavior
			if tt.mockSetup != nil {
				tt.mockSetup(mockDB, deps)
			}

			// Create request body
			var body []byte
			var err error

			if str, ok := tt.requestBody.(string); ok {
				// For invalid JSON test case
				body = []byte(str)
			} else {
				body, err = json.Marshal(tt.requestBody)
				assert.NoError(t, err)
			}

			// Create request
			req := httptest.NewRequest("PUT", tt.requestURL, bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			// Set URL variables for user ID and auth method ID
			if strings.Contains(tt.requestURL, testUserID.String()) && strings.Contains(tt.requestURL, testAuthMethodID.String()) {
				req = mux.SetURLVars(req, map[string]string{
					"userId":       testUserID.String(),
					"authMethodId": testAuthMethodID.String(),
				})
			} else if strings.Contains(tt.requestURL, "invalid-uuid") && strings.Contains(tt.requestURL, testAuthMethodID.String()) {
				req = mux.SetURLVars(req, map[string]string{
					"userId":       "invalid-uuid",
					"authMethodId": testAuthMethodID.String(),
				})
			} else if strings.Contains(tt.requestURL, testUserID.String()) && strings.Contains(tt.requestURL, "invalid-uuid") {
				req = mux.SetURLVars(req, map[string]string{
					"userId":       testUserID.String(),
					"authMethodId": "invalid-uuid",
				})
			}

			// Create response recorder
			w := httptest.NewRecorder()

			// Create handler
			handler := UpdatePasswordHandlerWithDeps(*deps)

			// Execute handler
			handler.ServeHTTP(w, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, w.Code)

			// Parse response body
			var responseBody interface{}
			if w.Body.Len() > 0 {
				err := json.Unmarshal(w.Body.Bytes(), &responseBody)
				assert.NoError(t, err)
			}

			// Assert response body for success cases
			if tt.expectedStatus == http.StatusOK {
				// For success cases, the response should be empty or contain success message
				if w.Body.Len() > 0 {
					_, ok := responseBody.(map[string]interface{})
					assert.True(t, ok)
					// Success response might contain a message or be empty
				}
			}
		})
	}
}
