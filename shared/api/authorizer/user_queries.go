package authorizer

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/lib/pq"
	connect "synapse-its.com/shared/connect"
)

// =============================================================================
// USER PERMISSIONS RETRIEVAL
// =============================================================================

// GetUserPermissions retrieves all permissions for a user across organizations, device groups, and location groups
//
// This function takes a user ID (UUID as string) and returns a comprehensive view of all permissions
// the user has across different scopes:
//
// - Organization-level permissions: Permissions that apply across the entire organization
// - Device group permissions: Permissions that apply to specific device groups within organizations
// - Location group permissions: Permissions that apply to specific location groups (not yet implemented)
//
// The function queries the database to find:
// 1. All organizations the user is a member of
// 2. All roles assigned to the user in each organization
// 3. All device groups the user has access to and their specific permissions
// 4. All location groups the user has access to (when implemented)
//
// Parameters:
//   - pg: Database executor for running queries
//   - userID: UUID string of the user to get permissions for
//
// Returns:
//   - *UserPermissions: Structured permissions data organized by scope
//   - error: Any error encountered during database operations or invalid UUID format
//
// Example usage:
//
//	permissions, err := GetUserPermissions(pg, "550e8400-e29b-41d4-a716-************")
//	if err != nil {
//	    log.Printf("Failed to get user permissions: %v", err)
//	    return
//	}
//
//	// Check organization permissions
//	for _, org := range permissions.Organizations {
//	    fmt.Printf("Organization: %s\n", org.OrganizationName)
//	    for _, perm := range org.Permissions {
//	        fmt.Printf("  %s: %t\n", perm.Name, perm.Value)
//	    }
//	}
//
//	// Check device group permissions
//	for _, dg := range permissions.DeviceGroups {
//	    fmt.Printf("Device Group: %s (Org: %s)\n", dg.DeviceGroupName, dg.OrganizationName)
//	    for _, perm := range dg.Permissions {
//	        fmt.Printf("  %s: %t\n", perm.Name, perm.Value)
//	    }
//	}
func GetUserPermissions(pg connect.DatabaseExecutor, userID string) (*UserPermissions, error) {
	// Validate userID is a valid UUID format
	if userID != "" {
		if _, err := uuid.Parse(userID); err != nil {
			return nil, fmt.Errorf("invalid user ID format: %w", err)
		}
	}

	permissions := &UserPermissions{
		UserID:      userID,
		Permissions: []Permission{},
	}

	// Return empty permissions for empty userID (no error, just no results)
	if userID == "" {
		return permissions, nil
	}

	// Get all permissions in a single query for efficiency
	allPerms, err := getAllUserPermissions(pg, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user permissions: %w", err)
	}

	// Ensure we always have a valid slice, even if empty
	if allPerms == nil {
		allPerms = []Permission{}
	}
	permissions.Permissions = allPerms

	return permissions, nil
}

// getAllUserPermissions retrieves all organization and device group permissions
// This is an internal function that performs the complex query to get all user permissions
// across different scopes and role assignments
func getAllUserPermissions(pg connect.DatabaseExecutor, userID string) ([]Permission, error) {
	query := `
		WITH scps AS (
  SELECT
    ora.membershipid    AS membership_id,
    'org'::text         AS scope,
    m.organizationid    AS scope_id,
    ora.roleid          AS role_id
  FROM {{OrgRoleAssignments}} ora
  JOIN {{Memberships}} m ON m.id = ora.membershipid
  WHERE ora.isdeleted = false
    AND m.isdeleted = false

  UNION ALL

  SELECT
    dgra.membershipid    AS membership_id,
    'device_group'::text AS scope,
    dgra.devicegroupid   AS scope_id,
    dgra.roleid          AS role_id
  FROM {{DeviceGroupRoleAssignments}} dgra
  JOIN {{DeviceGroups}} dg
    ON dg.id = dgra.devicegroupid AND dg.isdeleted = false
  WHERE dgra.isdeleted = false

  UNION ALL

  SELECT
    lgra.membershipid    AS membership_id,
    'location_group'::text AS scope,
    lgra.locationgroupid AS scope_id,
    lgra.roleid          AS role_id
  FROM {{LocationGroupRoleAssignments}} lgra
  JOIN {{LocationGroups}} lg
    ON lg.id = lgra.locationgroupid AND lg.isdeleted = false
  WHERE lgra.isdeleted = false
)
SELECT
  scps.scope 						   AS scope,
  scps.scope_id::text                  AS scope_id,
  o.id::text                           AS organization_id,
  array_agg(DISTINCT p.identifier)     AS permissions
FROM {{User}} u
JOIN {{AuthMethod}} am ON u.id = am.userid
JOIN {{Memberships}} m ON am.id = m.authmethodid
JOIN {{Organization}} o ON m.organizationid = o.id
JOIN scps ON scps.membership_id = m.id
JOIN {{CustomRole}} cr ON cr.id = scps.role_id
JOIN {{TemplateRole}} tr ON cr.templateroleidentifier = tr.identifier
JOIN {{TemplateRolePermission}} trp ON tr.identifier = trp.templateroleidentifier
JOIN {{Permission}} p ON trp.permissionidentifier = p.identifier
JOIN {{PermissionGroup}} pg ON p.permissiongroupidentifier = pg.identifier
LEFT JOIN {{CustomRolePermission}} crp
  ON cr.id = crp.customroleid AND p.identifier = crp.permissionidentifier
WHERE u.id = $1
  AND u.isdeleted = false
  AND m.isdeleted = false
  AND cr.isdeleted = false
  AND COALESCE(crp.value, trp.defaultvalue) = true
GROUP BY scps.scope, scps.scope_id, o.id
ORDER BY scps.scope, scps.scope_id, o.id;
`

	var result []Permission
	err := pg.QueryGenericSlice(&result, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to query user permissions: %w", err)
	}

	return result, nil
}

// GetEligibleUsersForDevice returns all eligible users for fault notifications for the given device
//
// This function finds all users who have the specified permissions to access the given device.
// It works by:
// 1. Finding all users who have the required permissions in scopes that grant access to the device
// 2. Filtering for users with SMS notifications enabled and valid mobile numbers
//
// The function supports organization-level, device group-level, and location group-level permissions.
// Location groups now use device_group permissions instead of location_group permissions.
// Users are eligible if they have any of the required permissions in any scope that grants access to the device.
func GetEligibleUsersForDevice(ctx context.Context, pg connect.DatabaseExecutor, deviceID string, requiredPermissions ...string) ([]User, error) {
	if len(requiredPermissions) == 0 {
		requiredPermissions = []string{"org_view_devices", "device_group_view_devices"}
	}

	// Build the query using UNION ALL to combine results from different permission scopes
	query := `
		SELECT DISTINCT u.Id, u.Mobile, u.IanaTimezone
		FROM (
			-- Organization-level permissions
			SELECT DISTINCT u.Id, u.Mobile, u.IanaTimezone
			FROM {{Device}} d
			JOIN {{SoftwareGateway}} sg ON d.SoftwareGatewayId = sg.Id
			JOIN {{Organization}} o ON sg.OrganizationId = o.Id
			JOIN {{Memberships}} m ON o.Id = m.OrganizationId AND NOT m.IsDeleted
			JOIN {{AuthMethod}} am ON m.AuthMethodId = am.Id
			JOIN {{User}} u ON am.UserId = u.Id AND NOT u.IsDeleted AND u.Mobile IS NOT NULL AND u.NotificationSmsEnabled = true
			JOIN {{OrgRoleAssignments}} ora ON m.Id = ora.MembershipId AND ora.IsDeleted = false
			JOIN {{CustomRole}} cr ON ora.RoleId = cr.Id AND NOT cr.IsDeleted
			JOIN {{TemplateRole}} tr ON cr.TemplateRoleIdentifier = tr.Identifier
			JOIN {{TemplateRolePermission}} trp ON tr.Identifier = trp.TemplateRoleIdentifier
			JOIN {{Permission}} p ON trp.PermissionIdentifier = p.Identifier
			LEFT JOIN {{CustomRolePermission}} crp ON cr.Id = crp.CustomRoleId AND p.Identifier = crp.PermissionIdentifier
			WHERE d.Id = $1
				AND NOT d.IsDeleted
				AND COALESCE(crp.Value, trp.DefaultValue) = true
				AND p.Identifier = ANY($2)

			UNION ALL

			-- Device group permissions
			SELECT DISTINCT u.Id, u.Mobile, u.IanaTimezone
			FROM {{Device}} d
			JOIN {{DeviceGroupDevices}} dgd ON d.Id = dgd.DeviceID
			JOIN {{DeviceGroups}} dg ON dgd.DeviceGroupId = dg.Id AND dg.IsDeleted = false
			JOIN {{DeviceGroupRoleAssignments}} dgra ON dg.Id = dgra.DeviceGroupId AND dgra.IsDeleted = false
			JOIN {{Memberships}} m ON dgra.MembershipId = m.Id AND NOT m.IsDeleted
			JOIN {{AuthMethod}} am ON m.AuthMethodId = am.Id
			JOIN {{User}} u ON am.UserId = u.Id AND NOT u.IsDeleted AND u.Mobile IS NOT NULL AND u.NotificationSmsEnabled = true
			JOIN {{CustomRole}} cr ON dgra.RoleId = cr.Id AND NOT cr.IsDeleted
			JOIN {{TemplateRole}} tr ON cr.TemplateRoleIdentifier = tr.Identifier
			JOIN {{TemplateRolePermission}} trp ON tr.Identifier = trp.TemplateRoleIdentifier
			JOIN {{Permission}} p ON trp.PermissionIdentifier = p.Identifier
			LEFT JOIN {{CustomRolePermission}} crp ON cr.Id = crp.CustomRoleId AND p.Identifier = crp.PermissionIdentifier
			WHERE d.Id = $1
				AND NOT d.IsDeleted
				AND COALESCE(crp.Value, trp.DefaultValue) = true
				AND p.Identifier = ANY($2)

			UNION ALL

			-- Location group permissions
			SELECT DISTINCT u.Id, u.Mobile, u.IanaTimezone
			FROM {{Device}} d
			JOIN {{Location}} l ON d.LocationId = l.Id
			JOIN {{LocationGroupLocations}} lgl ON l.Id = lgl.LocationId
			JOIN {{LocationGroups}} lg ON lgl.LocationGroupId = lg.Id AND lg.IsDeleted = false
			JOIN {{LocationGroupRoleAssignments}} lgra ON lg.Id = lgra.LocationGroupId AND lgra.IsDeleted = false
			JOIN {{Memberships}} m ON lgra.MembershipId = m.Id AND NOT m.IsDeleted
			JOIN {{AuthMethod}} am ON m.AuthMethodId = am.Id
			JOIN {{User}} u ON am.UserId = u.Id AND NOT u.IsDeleted AND u.Mobile IS NOT NULL AND u.NotificationSmsEnabled = true
			JOIN {{CustomRole}} cr ON lgra.RoleId = cr.Id AND NOT cr.IsDeleted
			JOIN {{TemplateRole}} tr ON cr.TemplateRoleIdentifier = tr.Identifier
			JOIN {{TemplateRolePermission}} trp ON tr.Identifier = trp.TemplateRoleIdentifier
			JOIN {{Permission}} p ON trp.PermissionIdentifier = p.Identifier
			LEFT JOIN {{CustomRolePermission}} crp ON cr.Id = crp.CustomRoleId AND p.Identifier = crp.PermissionIdentifier
			WHERE d.Id = $1
				AND NOT d.IsDeleted
				AND COALESCE(crp.Value, trp.DefaultValue) = true
				AND p.Identifier = ANY($2)
		) u`

	var users []User
	err := pg.QueryGenericSlice(&users, query, deviceID, pq.Array(requiredPermissions))
	if err != nil {
		return nil, fmt.Errorf("failed to query eligible users for device %s: %w", deviceID, err)
	}

	return users, nil
}
