package config

import (
	"encoding/json"

	"github.com/google/uuid"
)

// Structure of SoftwareGateway (simplified for config endpoints)
type SoftwareGateway struct {
	Id          uuid.UUID  `json:"id" db:"id"`
	Name        string     `json:"name" db:"name"`
	Description string     `json:"description" db:"description"`
	Config      string     `json:"config" db:"config"`
	TemplateId  *uuid.UUID `json:"templateId" db:"templateid"`
}

// Template-based configuration structures
type GatewayConfigTemplate struct {
	Id             uuid.UUID `json:"id" db:"id"`
	Name           string    `json:"name" db:"name"`
	OrganizationId uuid.UUID `json:"organizationId" db:"organizationid"`
	Description    string    `json:"description" db:"description"`
	IsDeleted      bool      `json:"isDeleted" db:"isdeleted"`
}

type GatewayConfigTemplateBaseSetting struct {
	Setting      string `json:"setting" db:"setting"`
	DefaultValue string `json:"defaultValue" db:"defaultvalue"`
	Name         string `json:"name" db:"name"`
	Description  string `json:"description" db:"description"`
	Format       string `json:"format" db:"format"` // JSONB stored as string
	IsDeleted    bool   `json:"isDeleted" db:"isdeleted"`
}

type GatewayConfigTemplateSetting struct {
	GatewayConfigTemplateId uuid.UUID `json:"gatewayConfigTemplateId" db:"gatewayconfigtemplateid"`
	Setting                 string    `json:"setting" db:"setting"`
	Value                   string    `json:"value" db:"value"`
}

type GatewayConfigTemplateSettingOverride struct {
	SoftwareGatewayId uuid.UUID `json:"softwareGatewayId" db:"softwaregatewayid"`
	Setting           string    `json:"setting" db:"setting"`
	Value             string    `json:"value" db:"value"`
}

// Resolved configuration setting for API responses
type ResolvedGatewayConfigSetting struct {
	Setting     string `json:"setting"`
	Value       string `json:"value"`
	Source      string `json:"source"` // "override", "template", or "default"
	Name        string `json:"name"`
	Description string `json:"description"`
	Format      string `json:"format"`
}

// Request structs for template operations
type CreateGatewayConfigTemplateRequest struct {
	Name           string    `json:"name" validate:"required,min=1"`
	OrganizationId uuid.UUID `json:"organizationId" validate:"required"`
	Description    string    `json:"description" validate:"required"`
}

type UpdateGatewayConfigTemplateRequest struct {
	Name        string `json:"name" validate:"required,min=1"`
	Description string `json:"description" validate:"required"`
}

// Request structs for template setting operations
type CreateOrUpdateGatewayConfigTemplateSettingRequest struct {
	GatewayConfigTemplateId uuid.UUID `json:"gatewayConfigTemplateId" validate:"required"`
	Setting                 string    `json:"setting" validate:"required"`
	Value                   string    `json:"value" validate:"required"`
}

// Request structs for setting override operations
type CreateOrUpdateGatewayConfigTemplateSettingOverrideRequest struct {
	SoftwareGatewayId uuid.UUID `json:"softwareGatewayId" validate:"required"`
	Setting           string    `json:"setting" validate:"required"`
	Value             string    `json:"value" validate:"required"`
}

// Request struct for bulk upsert overrides (key-value pairs)
type BulkUpsertGatewayConfigTemplateSettingOverridesRequest map[string]string

// Request structs for base setting operations
type CreateGatewayConfigTemplateBaseSettingRequest struct {
	Setting      string `json:"setting" validate:"required"`
	Name         string `json:"name" validate:"required"`
	Description  string `json:"description" validate:"required"`
	DefaultValue string `json:"defaultValue" validate:"required"`
	Format       string `json:"format" validate:"required"`
}

type UpdateGatewayConfigTemplateBaseSettingRequest struct {
	Name         string `json:"name" validate:"required"`
	Description  string `json:"description" validate:"required"`
	DefaultValue string `json:"defaultValue" validate:"required"`
	Format       string `json:"format" validate:"required"`
}

// SoftwareGatewayConfigResponse represents the API response model
type SoftwareGatewayConfigResponse struct {
	Id          uuid.UUID                      `json:"id" db:"id"`
	Name        string                         `json:"name" db:"name"`
	Description string                         `json:"description" db:"description"`
	Config      string                         `json:"config,omitempty" db:"config"` // Legacy field, optional
	TemplateId  *uuid.UUID                     `json:"templateId" db:"templateid"`
	Settings    []ResolvedGatewayConfigSetting `json:"settings,omitempty"` // New template-based settings
}

// GatewayConfigurationParameters represents the configuration parameters
type GatewayConfigurationParameters struct {
	LogLevel                                      string `json:"log_level"`
	LogFilename                                   string `json:"log_filename"`
	LogMaxBackups                                 int    `json:"log_max_backups"`
	ApplicationVersion                            string `json:"application_version"`
	LogMaxAgeInDays                               int    `json:"log_max_age_in_days"`
	LogCompressBackups                            bool   `json:"log_compress_backups"`
	LogFileMaxSizeMb                              int    `json:"log_file_max_size_mb"`
	RestApiDeviceEndpoint                         string `json:"rest_api_device_endpoint"`
	SendGatewayLogsToCloud                        bool   `json:"send_gateway_logs_to_cloud"`
	EdiDevicePersistConnection                    bool   `json:"edi_device_persist_connection"`
	EdiDeviceProcessingRetries                    int    `json:"edi_device_processing_retries"`
	RecordHttpRequestsToFolder                    bool   `json:"record_http_requests_to_folder"`
	DeviceStateSendFrequencySeconds               int    `json:"device_state_send_frequency_seconds"`
	ConfigChangeCheckFrequencySeconds             int    `json:"config_change_check_frequency_seconds"`
	SendGatewayPerformanceStatsToCloud            bool   `json:"send_gateway_performance_stats_to_cloud"`
	SoftwareUpdateCheckFrequencySeconds           int    `json:"software_update_check_frequency_seconds"`
	ChannelStateSendFrequencyMilliseconds         int    `json:"channel_state_send_frequency_milliseconds"`
	GatewayPerformanceStatsOutputFrequencySeconds int    `json:"gateway_performance_stats_output_frequency_seconds"`
	WsActive                                      bool   `json:"ws_active"`
	WsPort                                        string `json:"ws_port"`
	WsEndpoint                                    string `json:"ws_endpoint"`
	WsMaxConnections                              int    `json:"ws_max_connections"`
	WsSendFrequencyMilliseconds                   int    `json:"ws_send_frequency_milliseconds"`
	WsHeartbeatSendFrequencyMilliseconds          int    `json:"ws_heartbeat_send_frequency_milliseconds"`
	ThresholdDeviceErrorSeconds                   int    `json:"threshold_device_error_seconds"`
}

// Mapping SoftwareGateway model to SoftwareGatewayConfigResponse
func (s *SoftwareGateway) ToResponse() SoftwareGatewayConfigResponse {
	return SoftwareGatewayConfigResponse{
		Id:          s.Id,
		Name:        s.Name,
		Description: s.Description,
		Config:      s.Config,
		TemplateId:  s.TemplateId,
	}
}

// ToResponseWithSettings creates response with resolved template settings
func (s *SoftwareGateway) ToResponseWithSettings(settings []ResolvedGatewayConfigSetting) SoftwareGatewayConfigResponse {
	// Create a map for JSON config representation
	configMap := make(map[string]string)
	for _, setting := range settings {
		configMap[setting.Setting] = setting.Value
	}

	// Convert to JSON string for backward compatibility
	configJSON := ""
	if len(configMap) > 0 {
		if jsonBytes, err := json.Marshal(configMap); err == nil {
			configJSON = string(jsonBytes)
		}
	}

	return SoftwareGatewayConfigResponse{
		Id:          s.Id,
		Name:        s.Name,
		Description: s.Description,
		Config:      configJSON, // JSON representation of resolved settings
		TemplateId:  s.TemplateId,
		Settings:    settings,
	}
}
