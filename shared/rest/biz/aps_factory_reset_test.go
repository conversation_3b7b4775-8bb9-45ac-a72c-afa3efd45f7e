package biz

import (
	"database/sql"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	apsReset "synapse-its.com/shared/rest/domain/aps_reset"
)

// Mock starlark service
type mockStarlarkService struct {
	mock.Mock
}

func (m *mockStarlarkService) ExchangeCode(challengeCode string) (string, error) {
	args := m.Called(challengeCode)
	return args.String(0), args.Error(1)
}

// Mock APS reset repository
type mockAPSResetRepository struct {
	mock.Mock
}

func (m *mockAPSResetRepository) FindConfigByUserIDAndOrganizationID(userID, orgID uuid.UUID) (*apsReset.APSResetConfig, error) {
	args := m.Called(userID, orgID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*apsReset.APSResetConfig), args.Error(1)
}

func (m *mockAPSResetRepository) FindStatisticByUserIDAndOrganizationID(userID, orgID uuid.UUID) (*apsReset.APSResetStatistic, error) {
	args := m.Called(userID, orgID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*apsReset.APSResetStatistic), args.Error(1)
}

func (m *mockAPSResetRepository) CreateConfig(userID, orgID uuid.UUID) (*apsReset.APSResetConfig, error) {
	args := m.Called(userID, orgID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*apsReset.APSResetConfig), args.Error(1)
}

func (m *mockAPSResetRepository) CreateStatistic(userID, orgID uuid.UUID) (*apsReset.APSResetStatistic, error) {
	args := m.Called(userID, orgID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*apsReset.APSResetStatistic), args.Error(1)
}

func (m *mockAPSResetRepository) UpdateStatistic(statistic *apsReset.APSResetStatistic) error {
	args := m.Called(statistic)
	return args.Error(0)
}

// Mock APS reset auditor
type mockAPSResetAuditor struct {
	mock.Mock
}

func (m *mockAPSResetAuditor) Audit(userID, orgID uuid.UUID, challengeCode string, err error) error {
	args := m.Called(userID, orgID, challengeCode, err)
	return args.Error(0)
}

func TestNewApsFactoryResetBiz(t *testing.T) {
	t.Parallel()

	// Arrange
	mockStarlark := &mockStarlarkService{}
	mockRepo := &mockAPSResetRepository{}
	mockAuditor := &mockAPSResetAuditor{}

	// Act
	biz := NewApsFactoryResetBiz(mockStarlark, mockRepo, mockAuditor)

	// Assert
	assert.NotNil(t, biz)
	assert.Equal(t, mockStarlark, biz.starlarkService)
	assert.Equal(t, mockRepo, biz.apsResetRepo)
	assert.Equal(t, mockAuditor, biz.apsResetAuditor)
}

func TestApsFactoryResetBiz_GetAPSFactorResetCode(t *testing.T) {
	tests := []struct {
		name          string
		userID        uuid.UUID
		orgID         uuid.UUID
		challengeCode string
		setupMocks    func(*mockStarlarkService, *mockAPSResetRepository, *mockAPSResetAuditor)
		expectedCode  string
		expectedError error
		auditError    error
		expectedAudit bool
	}{
		{
			name:          "Success - Valid request",
			userID:        uuid.New(),
			orgID:         uuid.New(),
			challengeCode: "challenge123",
			setupMocks: func(starlark *mockStarlarkService, repo *mockAPSResetRepository, auditor *mockAPSResetAuditor) {
				config := &apsReset.APSResetConfig{
					ConfigAPSResetSuccessLimit:             5,
					ConfigAPSResetSuccessListLength:        3,
					ConfigAPSResetSuccessListWindowMinutes: 60,
				}
				statistic := &apsReset.APSResetStatistic{
					StatAPSResetCount:       2,
					StatAPSResetSuccessList: []time.Time{},
				}

				repo.On("FindConfigByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(config, nil)
				repo.On("FindStatisticByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(statistic, nil)
				starlark.On("ExchangeCode", "challenge123").Return("success_code", nil)
				repo.On("UpdateStatistic", mock.Anything).Return(nil)
				auditor.On("Audit", mock.Anything, mock.Anything, "challenge123", nil).Return(nil)
			},
			expectedCode:  "success_code",
			expectedError: nil,
			expectedAudit: true,
		},
		{
			name:          "Success - With audit error (should still return success)",
			userID:        uuid.New(),
			orgID:         uuid.New(),
			challengeCode: "challenge456",
			setupMocks: func(starlark *mockStarlarkService, repo *mockAPSResetRepository, auditor *mockAPSResetAuditor) {
				config := &apsReset.APSResetConfig{
					ConfigAPSResetSuccessLimit:             5,
					ConfigAPSResetSuccessListLength:        3,
					ConfigAPSResetSuccessListWindowMinutes: 60,
				}
				statistic := &apsReset.APSResetStatistic{
					StatAPSResetCount:       1,
					StatAPSResetSuccessList: []time.Time{},
				}

				repo.On("FindConfigByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(config, nil)
				repo.On("FindStatisticByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(statistic, nil)
				starlark.On("ExchangeCode", "challenge456").Return("success_code", nil)
				repo.On("UpdateStatistic", mock.Anything).Return(nil)
				auditor.On("Audit", mock.Anything, mock.Anything, "challenge456", nil).Return(errors.New("audit failed"))
			},
			expectedCode:  "success_code",
			expectedError: nil,
			expectedAudit: true,
		},
		{
			name:          "Error - Config not found",
			userID:        uuid.New(),
			orgID:         uuid.New(),
			challengeCode: "challenge789",
			setupMocks: func(starlark *mockStarlarkService, repo *mockAPSResetRepository, auditor *mockAPSResetAuditor) {
				repo.On("FindConfigByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(nil, errors.New("config not found"))
				auditor.On("Audit", mock.Anything, mock.Anything, "challenge789", mock.Anything).Return(nil)
			},
			expectedCode:  "",
			expectedError: errors.New("config not found"),
			expectedAudit: true,
		},
		{
			name:          "Error - Statistic not found",
			userID:        uuid.New(),
			orgID:         uuid.New(),
			challengeCode: "challenge101",
			setupMocks: func(starlark *mockStarlarkService, repo *mockAPSResetRepository, auditor *mockAPSResetAuditor) {
				config := &apsReset.APSResetConfig{
					ConfigAPSResetSuccessLimit:             5,
					ConfigAPSResetSuccessListLength:        3,
					ConfigAPSResetSuccessListWindowMinutes: 60,
				}

				repo.On("FindConfigByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(config, nil)
				repo.On("FindStatisticByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(nil, errors.New("statistic not found"))
				auditor.On("Audit", mock.Anything, mock.Anything, "challenge101", mock.Anything).Return(nil)
			},
			expectedCode:  "",
			expectedError: errors.New("statistic not found"),
			expectedAudit: true,
		},
		{
			name:          "Error - Limit reached",
			userID:        uuid.New(),
			orgID:         uuid.New(),
			challengeCode: "challenge202",
			setupMocks: func(starlark *mockStarlarkService, repo *mockAPSResetRepository, auditor *mockAPSResetAuditor) {
				config := &apsReset.APSResetConfig{
					ConfigAPSResetSuccessLimit:             5,
					ConfigAPSResetSuccessListLength:        3,
					ConfigAPSResetSuccessListWindowMinutes: 60,
				}
				statistic := &apsReset.APSResetStatistic{
					StatAPSResetCount:       5,
					StatAPSResetSuccessList: []time.Time{},
				}

				repo.On("FindConfigByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(config, nil)
				repo.On("FindStatisticByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(statistic, nil)
				auditor.On("Audit", mock.Anything, mock.Anything, "challenge202", apsReset.ErrLimitReached).Return(nil)
			},
			expectedCode:  "",
			expectedError: apsReset.ErrLimitReached,
			expectedAudit: true,
		},
		{
			name:          "Error - Success list window not expired",
			userID:        uuid.New(),
			orgID:         uuid.New(),
			challengeCode: "challenge303",
			setupMocks: func(starlark *mockStarlarkService, repo *mockAPSResetRepository, auditor *mockAPSResetAuditor) {
				config := &apsReset.APSResetConfig{
					ConfigAPSResetSuccessLimit:             5,
					ConfigAPSResetSuccessListLength:        3,
					ConfigAPSResetSuccessListWindowMinutes: 60,
				}
				statistic := &apsReset.APSResetStatistic{
					StatAPSResetCount: 2,
					StatAPSResetSuccessList: []time.Time{
						time.Now().Add(-30 * time.Minute), // 30 minutes ago - not expired
						time.Now().Add(-10 * time.Minute), // 10 minutes ago
						time.Now().Add(-5 * time.Minute),  // 5 minutes ago
					},
				}

				repo.On("FindConfigByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(config, nil)
				repo.On("FindStatisticByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(statistic, nil)
				auditor.On("Audit", mock.Anything, mock.Anything, "challenge303", apsReset.ErrSuccessListWindowNotExpired).Return(nil)
			},
			expectedCode:  "",
			expectedError: apsReset.ErrSuccessListWindowNotExpired,
			expectedAudit: true,
		},
		{
			name:          "Error - Starlark service error",
			userID:        uuid.New(),
			orgID:         uuid.New(),
			challengeCode: "challenge404",
			setupMocks: func(starlark *mockStarlarkService, repo *mockAPSResetRepository, auditor *mockAPSResetAuditor) {
				config := &apsReset.APSResetConfig{
					ConfigAPSResetSuccessLimit:             5,
					ConfigAPSResetSuccessListLength:        3,
					ConfigAPSResetSuccessListWindowMinutes: 60,
				}
				statistic := &apsReset.APSResetStatistic{
					StatAPSResetCount:       2,
					StatAPSResetSuccessList: []time.Time{},
				}

				repo.On("FindConfigByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(config, nil)
				repo.On("FindStatisticByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(statistic, nil)
				starlark.On("ExchangeCode", "challenge404").Return("", errors.New("starlark service error"))
				auditor.On("Audit", mock.Anything, mock.Anything, "challenge404", mock.Anything).Return(nil)
			},
			expectedCode:  "",
			expectedError: errors.New("starlark service error"),
			expectedAudit: true,
		},
		{
			name:          "Error - Update statistic failed",
			userID:        uuid.New(),
			orgID:         uuid.New(),
			challengeCode: "challenge505",
			setupMocks: func(starlark *mockStarlarkService, repo *mockAPSResetRepository, auditor *mockAPSResetAuditor) {
				config := &apsReset.APSResetConfig{
					ConfigAPSResetSuccessLimit:             5,
					ConfigAPSResetSuccessListLength:        3,
					ConfigAPSResetSuccessListWindowMinutes: 60,
				}
				statistic := &apsReset.APSResetStatistic{
					StatAPSResetCount:       2,
					StatAPSResetSuccessList: []time.Time{},
				}

				repo.On("FindConfigByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(config, nil)
				repo.On("FindStatisticByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(statistic, nil)
				starlark.On("ExchangeCode", "challenge505").Return("success_code", nil)
				repo.On("UpdateStatistic", mock.Anything).Return(errors.New("update failed"))
				auditor.On("Audit", mock.Anything, mock.Anything, "challenge505", mock.Anything).Return(nil)
			},
			expectedCode:  "",
			expectedError: errors.New("update failed"),
			expectedAudit: true,
		},
		{
			name:          "Success - Config not found, create new config",
			userID:        uuid.New(),
			orgID:         uuid.New(),
			challengeCode: "challenge_config_missing",
			setupMocks: func(starlark *mockStarlarkService, repo *mockAPSResetRepository, auditor *mockAPSResetAuditor) {
				// First call returns sql.ErrNoRows, second call creates config successfully
				config := &apsReset.APSResetConfig{
					ConfigAPSResetSuccessLimit:             5,
					ConfigAPSResetSuccessListLength:        3,
					ConfigAPSResetSuccessListWindowMinutes: 60,
				}
				statistic := &apsReset.APSResetStatistic{
					StatAPSResetCount:       2,
					StatAPSResetSuccessList: []time.Time{},
				}

				repo.On("FindConfigByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(nil, sql.ErrNoRows)
				repo.On("CreateConfig", mock.Anything, mock.Anything).Return(config, nil)
				repo.On("FindStatisticByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(statistic, nil)
				starlark.On("ExchangeCode", "challenge_config_missing").Return("success_code", nil)
				repo.On("UpdateStatistic", mock.Anything).Return(nil)
				auditor.On("Audit", mock.Anything, mock.Anything, "challenge_config_missing", nil).Return(nil)
			},
			expectedCode:  "success_code",
			expectedError: nil,
			expectedAudit: true,
		},
		{
			name:          "Success - Statistic not found, create new statistic",
			userID:        uuid.New(),
			orgID:         uuid.New(),
			challengeCode: "challenge_statistic_missing",
			setupMocks: func(starlark *mockStarlarkService, repo *mockAPSResetRepository, auditor *mockAPSResetAuditor) {
				config := &apsReset.APSResetConfig{
					ConfigAPSResetSuccessLimit:             5,
					ConfigAPSResetSuccessListLength:        3,
					ConfigAPSResetSuccessListWindowMinutes: 60,
				}
				// First call returns sql.ErrNoRows, second call creates statistic successfully
				statistic := &apsReset.APSResetStatistic{
					StatAPSResetCount:       0,
					StatAPSResetSuccessList: []time.Time{},
				}

				repo.On("FindConfigByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(config, nil)
				repo.On("FindStatisticByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(nil, sql.ErrNoRows)
				repo.On("CreateStatistic", mock.Anything, mock.Anything).Return(statistic, nil)
				starlark.On("ExchangeCode", "challenge_statistic_missing").Return("success_code", nil)
				repo.On("UpdateStatistic", mock.Anything).Return(nil)
				auditor.On("Audit", mock.Anything, mock.Anything, "challenge_statistic_missing", nil).Return(nil)
			},
			expectedCode:  "success_code",
			expectedError: nil,
			expectedAudit: true,
		},
		{
			name:          "Success - Both config and statistic not found, create both",
			userID:        uuid.New(),
			orgID:         uuid.New(),
			challengeCode: "challenge_both_missing",
			setupMocks: func(starlark *mockStarlarkService, repo *mockAPSResetRepository, auditor *mockAPSResetAuditor) {
				// Both calls return sql.ErrNoRows, then both create successfully
				config := &apsReset.APSResetConfig{
					ConfigAPSResetSuccessLimit:             5,
					ConfigAPSResetSuccessListLength:        3,
					ConfigAPSResetSuccessListWindowMinutes: 60,
				}
				statistic := &apsReset.APSResetStatistic{
					StatAPSResetCount:       0,
					StatAPSResetSuccessList: []time.Time{},
				}

				repo.On("FindConfigByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(nil, sql.ErrNoRows)
				repo.On("CreateConfig", mock.Anything, mock.Anything).Return(config, nil)
				repo.On("FindStatisticByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(nil, sql.ErrNoRows)
				repo.On("CreateStatistic", mock.Anything, mock.Anything).Return(statistic, nil)
				starlark.On("ExchangeCode", "challenge_both_missing").Return("success_code", nil)
				repo.On("UpdateStatistic", mock.Anything).Return(nil)
				auditor.On("Audit", mock.Anything, mock.Anything, "challenge_both_missing", nil).Return(nil)
			},
			expectedCode:  "success_code",
			expectedError: nil,
			expectedAudit: true,
		},
		{
			name:          "Error - Config not found, create config fails",
			userID:        uuid.New(),
			orgID:         uuid.New(),
			challengeCode: "challenge_config_create_fails",
			setupMocks: func(starlark *mockStarlarkService, repo *mockAPSResetRepository, auditor *mockAPSResetAuditor) {
				repo.On("FindConfigByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(nil, sql.ErrNoRows)
				repo.On("CreateConfig", mock.Anything, mock.Anything).Return(nil, errors.New("failed to create config"))
				auditor.On("Audit", mock.Anything, mock.Anything, "challenge_config_create_fails", mock.Anything).Return(nil)
			},
			expectedCode:  "",
			expectedError: errors.New("failed to create config"),
			expectedAudit: true,
		},
		{
			name:          "Error - Statistic not found, create statistic fails",
			userID:        uuid.New(),
			orgID:         uuid.New(),
			challengeCode: "challenge_statistic_create_fails",
			setupMocks: func(starlark *mockStarlarkService, repo *mockAPSResetRepository, auditor *mockAPSResetAuditor) {
				config := &apsReset.APSResetConfig{
					ConfigAPSResetSuccessLimit:             5,
					ConfigAPSResetSuccessListLength:        3,
					ConfigAPSResetSuccessListWindowMinutes: 60,
				}

				repo.On("FindConfigByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(config, nil)
				repo.On("FindStatisticByUserIDAndOrganizationID", mock.Anything, mock.Anything).Return(nil, sql.ErrNoRows)
				repo.On("CreateStatistic", mock.Anything, mock.Anything).Return(nil, errors.New("failed to create statistic"))
				auditor.On("Audit", mock.Anything, mock.Anything, "challenge_statistic_create_fails", mock.Anything).Return(nil)
			},
			expectedCode:  "",
			expectedError: errors.New("failed to create statistic"),
			expectedAudit: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			mockStarlark := &mockStarlarkService{}
			mockRepo := &mockAPSResetRepository{}
			mockAuditor := &mockAPSResetAuditor{}

			tt.setupMocks(mockStarlark, mockRepo, mockAuditor)

			biz := &apsFactoryResetBiz{
				starlarkService: mockStarlark,
				apsResetRepo:    mockRepo,
				apsResetAuditor: mockAuditor,
			}

			// Act
			code, err := biz.GetAPSFactorResetCode(tt.userID, tt.orgID, tt.challengeCode)

			// Assert
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedCode, code)
			}

			// Verify all mocks were called as expected
			mockStarlark.AssertExpectations(t)
			mockRepo.AssertExpectations(t)
			mockAuditor.AssertExpectations(t)
		})
	}
}
