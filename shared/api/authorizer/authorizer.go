package authorizer

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"synapse-its.com/shared/api/jwttokens"
	"synapse-its.com/shared/api/security"
	connect "synapse-its.com/shared/connect"
)

// =============================================================================
// CONSTANTS AND TYPES
// =============================================================================

type ctxKey string

const (
	userPermissionsKey ctxKey = "userPermissions"
	headerJWT          string = "jwt-token"
)

// =============================================================================
// MAIN AUTHORIZATION MIDDLEWARE
// =============================================================================

// Authorize is the main authorization middleware that validates JWT tokens and checks user permissions
// It extracts the JWT token from the request header, validates it, retrieves user permissions,
// and checks if the user has access to the requested endpoint.
var Authorize = func(ctx context.Context, r *http.Request) (context.Context, error) {
	// Get the jwt-token
	authToken := r.Header.Get(headerJWT)
	if authToken == "" {
		return ctx, fmt.Errorf("%w: jwt-token not found in header", ErrUnauthorized)
	}

	// Validate the jwt-token in the header
	_, _, err := jwttokens.ValidateJJwtToken(authToken)
	if err != nil {
		return ctx, fmt.Errorf("%w: %v", ErrUnauthorized, err)
	}

	// Get the postgres connection.
	connections, err := connect.GetConnections(ctx)
	if err != nil {
		return ctx, fmt.Errorf("%w: %w", ErrInternal, err)
	}
	pg := connections.Postgres

	// Validate token and get user info
	userPermissions, err := getUserFromToken(pg, authToken)
	if err != nil {
		return ctx, err
	}

	// TODO: Make this even more configurable in the future
	if !validEndPointRequestFromRole(userPermissions, r) {
		return ctx, fmt.Errorf("%w: user does not have the correct permissions to access %v", ErrForbidden, r.URL.Path)
	}

	// allow access
	return context.WithValue(ctx, userPermissionsKey, userPermissions), nil
}

// UserPermissionsFromContext extracts user permissions from the request context
// Returns the UserPermissions and a boolean indicating if the extraction was successful
func UserPermissionsFromContext(ctx context.Context) (*UserPermissions, bool) {
	up, ok := ctx.Value(userPermissionsKey).(*UserPermissions)
	return up, ok
}

// AddUserPermissionsToContext adds user permissions to context for testing or custom scenarios
// This is useful when you need to manually set permissions without going through JWT validation
func AddUserPermissionsToContext(ctx context.Context, userPermissions *UserPermissions) context.Context {
	return context.WithValue(ctx, userPermissionsKey, userPermissions)
}

// =============================================================================
// TOKEN VALIDATION AND USER RETRIEVAL
// =============================================================================

// getUserFromToken validates a JWT token against the database and retrieves user permissions
// It checks if the token exists, is not expired, and belongs to an enabled user
var getUserFromToken = func(pg connect.DatabaseExecutor, jwtToken string) (*UserPermissions, error) {
	// All DB jwts are stored as SHA256
	sha256TokenValue := security.CalculateSHA256(jwtToken)

	query := `
		SELECT 
			u.Id,
			ut.Expiration::timestamptz AS expirationutc 
		FROM {{User}} u
		JOIN {{UserToken}} ut
			ON u.Id = ut.UserId 
		WHERE NOT u.IsDeleted AND ut.JWTTokenSha256 = $1`
	userInfo := &dbUserInfo{}
	if err := pg.QueryRowStruct(userInfo, query, sha256TokenValue); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// if no rows, return error
			return nil, fmt.Errorf("%w: token not found or the user is disabled", ErrUnauthorized)
		}
		// return general error
		return nil, fmt.Errorf("%w: %v", ErrInternal, err)
	}

	// check the token expiration
	if now := time.Now().UTC(); now.After(userInfo.ExpirationUTC) {
		return nil, fmt.Errorf("%w: token expired at %s", ErrForbidden, userInfo.ExpirationUTC)
	}

	permissions, err := GetUserPermissions(pg, userInfo.UserID)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrInternal, err)
	}

	return permissions, nil
}

// =============================================================================
// ENDPOINT PERMISSION VALIDATION
// =============================================================================

// validEndPointRequestFromRole validates whether the user has access to the requested endpoint
// It maps endpoints to required permissions and checks if the user has any of those permissions
var validEndPointRequestFromRole = func(userPermissions *UserPermissions, r *http.Request) bool {
	// Define endpoint-to-permission mappings
	endpointPermissions := map[string][]string{
		// Device endpoints - require device viewing permissions
		"/data/v2/device":     {"org_view_devices", "device_group_view_devices"},
		"/api/v3/data/device": {"org_view_devices", "device_group_view_devices"},

		// Fault endpoints - require device viewing permissions
		"/data/v2/fault":     {"org_view_devices", "device_group_view_devices"},
		"/api/v3/data/fault": {"org_view_devices", "device_group_view_devices"},

		// User account notifications - require device viewing permissions
		"/user/v2/account/notifications":     {"org_view_devices", "device_group_view_devices"},
		"/api/v3/user/account/notifications": {"org_view_devices", "device_group_view_devices"},

		// User instructions - device management permissions
		"/user/v3/instruction":     {"device_group_manage_devices", "org_manage_devices"},
		"/api/v3/user/instruction": {"device_group_manage_devices", "org_manage_devices"},
	}

	// Get required permissions for this endpoint
	var requiredPermissions []string
	for endpoint, perms := range endpointPermissions {
		if strings.HasPrefix(r.URL.Path, endpoint) {
			requiredPermissions = perms
			break
		}
	}

	// If no specific permissions required, allow access (for non-protected endpoints)
	if len(requiredPermissions) == 0 {
		return true
	}

	// Check if user has any of the required permissions
	return hasAnyPermission(userPermissions, requiredPermissions)
}

// hasAnyPermission checks if the user has any of the required permissions across all scopes
// This is a utility function used by the endpoint validation logic
func hasAnyPermission(userPermissions *UserPermissions, requiredPermissions []string) bool {
	// Create a set of all user permissions for efficient lookup
	userPerms := make(map[string]bool)
	for _, permission := range userPermissions.Permissions {
		for _, perm := range permission.Permissions {
			userPerms[perm] = true
		}
	}

	// Check if user has any of the required permissions
	for _, requiredPerm := range requiredPermissions {
		if userPerms[requiredPerm] {
			return true
		}
	}

	return false
}

// =============================================================================
// REUSABLE AUTHENTICATION FUNCTIONS
// =============================================================================

// ValidateJWTAndGetPermissions validates a JWT token and retrieves user permissions
// This is a public wrapper around the internal getUserFromToken functionality
// It can be used by microservices that need JWT validation outside of HTTP middleware
func ValidateJWTAndGetPermissions(pg connect.DatabaseExecutor, jwtToken string) (*UserPermissions, error) {
	return getUserFromToken(pg, jwtToken)
}

// GatewayInfo contains gateway authentication information
type GatewayInfo struct {
	GatewayID string
	OrgID     string
}

// ValidateGatewayAuth validates a gateway using MachineKey + APIKey and returns gateway information
// This provides a shared utility for gateway authentication across microservices
// Uses the same validation pattern as the broker microservice (MachineKey + APIKey)
//
// NOTE: This function returns GatewayInfo{GatewayID, OrgID} while the broker's authenticateInfo
// function in /api/handlers/v3/gateway/ingest/handler.go only returns OrganizationIdentifier.
// Future refactoring opportunity: Broker could potentially use this function and extract
// just the OrgID from the returned GatewayInfo, eliminating duplicated validation logic.
func ValidateGatewayAuth(pg connect.DatabaseExecutor, machineKey, apiKey string) (*GatewayInfo, error) {
	if machineKey == "" {
		return nil, fmt.Errorf("%w: empty machine key", ErrUnauthorized)
	}
	if apiKey == "" {
		return nil, fmt.Errorf("%w: empty API key", ErrUnauthorized)
	}

	// Query the SoftwareGateway table using both MachineKey and APIKey (matches broker pattern)
	query := `
		SELECT Id, OrganizationId
		FROM {{SoftwareGateway}} 
		WHERE MachineKey = $1 AND ApiKey = $2 
		AND IsEnabled = true
	`

	row, err := pg.QueryRow(query, machineKey, apiKey)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("%w: invalid gateway credentials", ErrUnauthorized)
		}
		return nil, fmt.Errorf("%w: %v", ErrInternal, err)
	}

	gatewayID, ok := row["id"].(string)
	if !ok {
		return nil, fmt.Errorf("%w: invalid gateway data", ErrInternal)
	}

	orgID, ok := row["organizationid"].(string)
	if !ok {
		return nil, fmt.Errorf("%w: invalid organization data", ErrInternal)
	}

	return &GatewayInfo{
		GatewayID: gatewayID,
		OrgID:     orgID,
	}, nil
}
