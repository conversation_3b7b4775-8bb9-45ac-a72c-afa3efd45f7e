package helper

import (
	"crypto/rand"
	"database/sql"
	"encoding/hex"
	"fmt"
	"net/http"
	"strings"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

var randRead = rand.Read

const (
	OrganizationIDParam  = "organizationId"
	LocationGroupIDParam = "locationgroupId"
	UserIDParam          = "userId"
	LocationIDParam      = "locationId"
	DeviceIDParam        = "deviceId"
	InviteIDParam        = "inviteId"
	AuthMethodIDParam    = "authMethodId"
)

// Validate identifier
func ValidateIdentifier(identifier string) error {
	if strings.TrimSpace(identifier) == "" {
		return ErrInvalidIdentifier
	}
	return nil
}

// Validate OrganizationID
func ValidateOrganizationID(r *http.Request) (uuid.UUID, error) {
	vars := mux.Vars(r)
	orgIDStr := vars[OrganizationIDParam]
	if strings.TrimSpace(orgIDStr) == "" {
		return uuid.Nil, ErrInvalidOrganizationID
	}

	// Check if the organizationId is a valid UUID
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return uuid.Nil, ErrInvalidOrganizationID
	}
	return orgID, nil
}

// Validate InviteID
func ValidateInviteID(r *http.Request) (uuid.UUID, error) {
	vars := mux.Vars(r)
	inviteIDStr := vars[InviteIDParam]
	if strings.TrimSpace(inviteIDStr) == "" {
		return uuid.Nil, ErrInvalidInviteID
	}

	// Check if the inviteID is a valid UUID
	inviteID, err := uuid.Parse(inviteIDStr)
	if err != nil {
		return uuid.Nil, ErrInvalidInviteID
	}
	return inviteID, nil
}

// ValidateUserID validates the user ID from the request
func ValidateUserID(r *http.Request) (uuid.UUID, error) {
	vars := mux.Vars(r)
	userIDStr := vars[UserIDParam]
	if strings.TrimSpace(userIDStr) == "" {
		return uuid.Nil, ErrInvalidUserID
	}

	// Check if the userID is a valid UUID
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return uuid.Nil, ErrInvalidUserID
	}

	return userID, nil
}

// GenerateRandomTokenHex returns a hex-encoded random token of the specified length.
// It errors if length is negative or if the random source fails.
func GenerateRandomTokenHex(length uint) (string, error) {
	// 2 hex characters per byte
	sz := length / 2
	b := make([]byte, sz)
	if _, err := randRead(b); err != nil {
		return "", err
	}
	return hex.EncodeToString(b), nil
}

// ValidateAuthMethodID validates the auth method ID from the request
func ValidateAuthMethodID(r *http.Request) (uuid.UUID, error) {
	vars := mux.Vars(r)
	authMethodIDStr := vars[AuthMethodIDParam]
	if strings.TrimSpace(authMethodIDStr) == "" {
		return uuid.Nil, ErrInvalidAuthMethodID
	}

	// Check if the authMethodID is a valid UUID
	authMethodID, err := uuid.Parse(authMethodIDStr)
	if err != nil {
		return uuid.Nil, ErrInvalidAuthMethodID
	}
	return authMethodID, nil
}

// Validate DeviceID
func ValidateDeviceID(r *http.Request) (uuid.UUID, error) {
	vars := mux.Vars(r)
	deviceIDStr := vars[DeviceIDParam]
	if strings.TrimSpace(deviceIDStr) == "" {
		return uuid.Nil, ErrInvalidDeviceID
	}

	// Check if the deviceID is a valid UUID
	deviceID, err := uuid.Parse(deviceIDStr)
	if err != nil {
		return uuid.Nil, ErrInvalidDeviceID
	}
	return deviceID, nil
}

// Validate SoftwareGatewayId belong to the organization
func ValidateSoftwareGateWayBelongsToOrganization(pg connect.DatabaseExecutor, organizationId uuid.UUID, softwareGatewayId uuid.UUID) error {
	gatewayQuery := `
	SELECT 1 FROM {{SoftwareGateway}}
	WHERE Id = $1 AND OrganizationId = $2 AND NOT IsDeleted`

	var gatewayExists struct {
		Exists int `db:"?column?"`
	}
	err := pg.QueryRowStruct(&gatewayExists, gatewayQuery, softwareGatewayId, organizationId)
	if err != nil {
		if err == sql.ErrNoRows {
			return ErrSoftwareGatewayNotFound
		}
		logger.Errorf("Failed to verify software gateway ownership: %v", err)
		return ErrDatabaseOperation
	}
	return nil
}

// Validate LocationId belong to the organization
func ValidateLocationBelongsToOrganization(pg connect.DatabaseExecutor, organizationId uuid.UUID, locationId uuid.UUID) error {
	locationQuery := `
	SELECT 1 FROM {{Location}}
	WHERE Id = $1 AND OrganizationId = $2 AND NOT IsDeleted`

	var locationExists struct {
		Exists int `db:"?column?"`
	}
	err := pg.QueryRowStruct(&locationExists, locationQuery, locationId, organizationId)
	if err != nil {
		if err == sql.ErrNoRows {
			return ErrLocationNotFound
		}
		logger.Errorf("Failed to verify location ownership: %v", err)
		return ErrDatabaseOperation
	}
	return nil
}

// ParseUUIDFromRequest parses a UUID from the request URL variables
func ParseUUIDFromRequest(r *http.Request, key string) (uuid.UUID, error) {
	vars := mux.Vars(r)
	uuidStr := vars[key]
	if strings.TrimSpace(uuidStr) == "" {
		return uuid.Nil, ErrInvalidUUID
	}
	return uuid.Parse(uuidStr)
}

// Validate organization is existed or deleted
func ValidateOrganizationExistedOrDeleted(pg connect.DatabaseExecutor, organizationID uuid.UUID) error {
	query := `-- name: ValidateOrganizationExistedOrDeleted
		SELECT EXISTS(
			SELECT 1 FROM {{Organization}}
			WHERE Id = $1 AND NOT IsDeleted
		) as exists`

	var organizationExists struct {
		Exists bool `db:"exists"`
	}

	err := pg.QueryRowStruct(&organizationExists, query, organizationID.String())
	if err != nil {
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	if !organizationExists.Exists {
		return ErrOrganizationNotFound
	}

	return nil
}

// GetMembershipByUserID returns the membership ID for a given user ID and organization ID
func GetMembershipByUserID(pg connect.DatabaseExecutor, userID uuid.UUID, organizationID uuid.UUID) (uuid.UUID, error) {
	query := `
		SELECT m.Id as membership_id
		FROM {{Memberships}} m
		INNER JOIN {{AuthMethod}} am ON m.AuthMethodId = am.Id
		WHERE am.UserId = $1 AND m.OrganizationId = $2 AND m.IsDeleted = false
	`
	var membership struct {
		MembershipID uuid.UUID `db:"membership_id"`
	}

	err := pg.QueryRowStruct(&membership, query, userID, organizationID)
	if err != nil {
		if err == sql.ErrNoRows {
			return uuid.Nil, ErrMembershipNotFound
		}
		logger.Errorf("Failed to get membership by user ID: %v", err)
		return uuid.Nil, ErrDatabaseOperation
	}

	return membership.MembershipID, nil
}
