-- ================================================
-- CAT-491: Set permissions for next gen devices
-- ================================================
/* Summary:
'Y' indicates the role has access to the group; NULL indicates no access.
Roles:
- SU: Superuser (Factory/OEM/Dist)
- TE: Traffic Engineers
- CC: Construction Contractors
- ST: Signal Technician
- MC: Maintenance Contractors
- IT: IT
- CS: Consultants
- CL: Cloud

Access Groups:
- Group 0: All Access (Read-only: Realtime Data, Remote Display, Retrieve Logs, etc.)
- Group 1: Read Stats (Retrieve Port 1 Stats, Program Card Stats, etc.)
- Group 2: Set Time (Set Date/Time and Daylight Savings)
- Group 3: Read Config (Read Configuration Settings: Per Channel, Current Sense, etc.)
- Group 4: Clear Logs (Clear All Logs, Power Log, Reset Log, etc.)
- Group 5: Reset Stats (Reset Port 1 Stats, Program Card Stats, etc.)
- Group 6: Write DataKey/Port1 OR (Write Program Card/Data Key, Port 1 Disable Override)
- Group 7: Write User Settings (Write User Settings)
- Group 8: DFU (Firmware Update 0xF0-0xF4)
- Group 9: Set Access (Set Agency Front Panel Access Bits, Certificate Access District)
- Group 10: Provisioning (Comms Processor Certificate Re-Provisioning)
**NOTE**: Group 9 and 10 are not relevant to the cloud

Permissions Table:
------------------------------------------------------------------------------------------------
| Access Group                  | SU | TE | CC | ST | MC | IT | CS | CL |
------------------------------------------------------------------------------------------------
| Group 0: All Access           | Y  | Y  | Y  | Y  | Y  | Y  | Y  | Y  |
| Group 1: Read Stats           | Y  | Y  | Y  | Y  | Y  |    |    | Y  |
| Group 2: Set Time             | Y  | Y  | Y  | Y  | Y  | Y  |    | Y  |
| Group 3: Read Config          | Y  | Y  | Y  | Y  | Y  |    | Y  | Y  |
| Group 4: Clear Logs           | Y  | Y  | Y  |    |    |    |    | Y  |
| Group 5: Reset Stats          | Y  | Y  | Y  |    |    |    |    | Y  |
| Group 6: Write DataKey        | Y  | Y  |    | Y  |    |    |    | Y  |
| Group 7: Write User Settings  | Y  | Y  | Y  | Y  | Y  |    |    | Y  |
| Group 8: DFU                  | Y  | Y  | Y  | Y  | Y  |    |    | Y  |
| Group 9: Set Access           | Y  | Y  |    |    |    |    |    |    |
| Group 10: Provisioning        | Y  |    |    |    |    |    |    |    |
------------------------------------------------------------------------------------------------
*/

-- Create new entries for the permissions table
INSERT INTO {{Permission}} (Identifier, PermissionGroupIdentifier, OrgTypeIdentifier, Weight, Name, Description) VALUES
-- Insert new permissions for organization group
  ('org_ng_all_access',          'organization', 'municipality', 1.00, 'All Access', 'All access to the organization.'),
  ('org_ng_read_stats',          'organization', 'municipality', 2.00, 'Read Stats', 'Read stats to the organization.'),
  ('org_ng_set_time',            'organization', 'municipality', 3.00, 'Set Time', 'Set time to the organization.'),
  ('org_ng_read_config',         'organization', 'municipality', 4.00, 'Read Config', 'Read configuration to the organization.'),
  ('org_ng_clear_logs',          'organization', 'municipality', 5.00, 'Clear Logs', 'Clear logs to the organization.'),
  ('org_ng_reset_stats',         'organization', 'municipality', 6.00, 'Reset Stats', 'Reset stats to the organization.'),
  ('org_ng_write_datakey',       'organization', 'municipality', 7.00, 'Write Data Key', 'Write data key to the organization.'),
  ('org_ng_write_user_settings', 'organization', 'municipality', 8.00, 'Write User Settings', 'Write user settings to the organization.'),
  ('org_ng_dfu',                 'organization', 'municipality', 9.00, 'DFU', 'DFU to the organization.'),
-- Insert new permissions for device group
  ('device_group_ng_all_access',          'device_group', 'municipality', 1.00, 'All Access', 'All access to the device group.'),
  ('device_group_ng_read_stats',          'device_group', 'municipality', 2.00, 'Read Stats', 'Read stats to the device group.'),
  ('device_group_ng_set_time',            'device_group', 'municipality', 3.00, 'Set Time', 'Set time to the device group.'),
  ('device_group_ng_read_config',         'device_group', 'municipality', 4.00, 'Read Config', 'Read configuration to the device group.'),
  ('device_group_ng_clear_logs',          'device_group', 'municipality', 5.00, 'Clear Logs', 'Clear logs to the device group.'),
  ('device_group_ng_reset_stats',         'device_group', 'municipality', 6.00, 'Reset Stats', 'Reset stats to the device group.'),
  ('device_group_ng_write_datakey',       'device_group', 'municipality', 7.00, 'Write Data Key', 'Write data key to the device group.'),
  ('device_group_ng_write_user_settings', 'device_group', 'municipality', 8.00, 'Write User Settings', 'Write user settings to the device group.'),
  ('device_group_ng_dfu',                 'device_group', 'municipality', 9.00, 'DFU', 'DFU to the device group.'),
-- Insert new permissions for location group
  ('location_group_ng_all_access',          'location_group', 'municipality', 1.00, 'All Access', 'All access to the location group.'),
  ('location_group_ng_read_stats',          'location_group', 'municipality', 2.00, 'Read Stats', 'Read stats to the location group.'),
  ('location_group_ng_set_time',            'location_group', 'municipality', 3.00, 'Set Time', 'Set time to the location group.'),
  ('location_group_ng_read_config',         'location_group', 'municipality', 4.00, 'Read Config', 'Read configuration to the location group.'),
  ('location_group_ng_clear_logs',          'location_group', 'municipality', 5.00, 'Clear Logs', 'Clear logs to the location group.'),
  ('location_group_ng_reset_stats',         'location_group', 'municipality', 6.00, 'Reset Stats', 'Reset stats to the location group.'),
  ('location_group_ng_write_datakey',       'location_group', 'municipality', 7.00, 'Write Data Key', 'Write data key to the location group.'),
  ('location_group_ng_write_user_settings', 'location_group', 'municipality', 8.00, 'Write User Settings', 'Write user settings to the location group.'),
  ('location_group_ng_dfu',                 'location_group', 'municipality', 9.00, 'DFU', 'DFU to the location group.');

-- Update the TemplateRole table
-- 1. Rename Technician -> Signal Technician
UPDATE {{TemplateRole}}
SET Name = 'Signal Technician'
WHERE Identifier = 'mun_technician';

-- 2. Create new template role
INSERT INTO {{TemplateRole}} (Identifier, OrgTypeIdentifier, Name, Description, IsDeletable) VALUES
  ('mun_traffic_engineers',        'municipality', 'Traffic Engineers',        'Traffic engineering personnel with device access', true),
  ('mun_construction_contractors', 'municipality', 'Construction Contractors', 'Construction contractors with device access',      true),
  ('mun_maintenance_contractors',  'municipality', 'Maintenance Contractors',  'Maintenance contractors with device access',       true),
  ('mun_it',                       'municipality', 'IT',                       'IT personnel with device access',                  true),
  ('mun_consultant',               'municipality', 'Consultant',               'Consultants with device access',                   true);

-- Update the TemplateRolePermission table
INSERT INTO {{TemplateRolePermission}} (TemplateRoleIdentifier, PermissionIdentifier, DefaultValue) VALUES
-- For Admin
  ('mun_admin', 'org_ng_all_access',                     true),
  ('mun_admin', 'org_ng_read_stats',                     true),
  ('mun_admin', 'org_ng_set_time',                       true),
  ('mun_admin', 'org_ng_read_config',                    true),
  ('mun_admin', 'org_ng_clear_logs',                     true),
  ('mun_admin', 'org_ng_reset_stats',                    true),
  ('mun_admin', 'org_ng_write_datakey',                  true),
  ('mun_admin', 'org_ng_write_user_settings',            true),
  ('mun_admin', 'org_ng_dfu',                            true),
  ('mun_admin', 'device_group_ng_all_access',            true),
  ('mun_admin', 'device_group_ng_read_stats',            true),
  ('mun_admin', 'device_group_ng_set_time',              true),
  ('mun_admin', 'device_group_ng_read_config',           true),
  ('mun_admin', 'device_group_ng_clear_logs',            true),
  ('mun_admin', 'device_group_ng_reset_stats',           true),
  ('mun_admin', 'device_group_ng_write_datakey',         true),
  ('mun_admin', 'device_group_ng_write_user_settings',   true),
  ('mun_admin', 'device_group_ng_dfu',                   true),
  ('mun_admin', 'location_group_ng_all_access',          true),
  ('mun_admin', 'location_group_ng_read_stats',          true),
  ('mun_admin', 'location_group_ng_set_time',            true),
  ('mun_admin', 'location_group_ng_read_config',         true),
  ('mun_admin', 'location_group_ng_clear_logs',          true),
  ('mun_admin', 'location_group_ng_reset_stats',         true),
  ('mun_admin', 'location_group_ng_write_datakey',       true),
  ('mun_admin', 'location_group_ng_write_user_settings', true),
  ('mun_admin', 'location_group_ng_dfu',                 true),
-- For Traffic Engineers
  ('mun_traffic_engineers', 'org_ng_all_access',                     true),
  ('mun_traffic_engineers', 'org_ng_read_stats',                     true),
  ('mun_traffic_engineers', 'org_ng_set_time',                       true),
  ('mun_traffic_engineers', 'org_ng_read_config',                    true),
  ('mun_traffic_engineers', 'org_ng_clear_logs',                     true),
  ('mun_traffic_engineers', 'org_ng_reset_stats',                    true),
  ('mun_traffic_engineers', 'org_ng_write_datakey',                  true),
  ('mun_traffic_engineers', 'org_ng_write_user_settings',            true),
  ('mun_traffic_engineers', 'org_ng_dfu',                            true),
  ('mun_traffic_engineers', 'device_group_ng_all_access',            true),
  ('mun_traffic_engineers', 'device_group_ng_read_stats',            true),
  ('mun_traffic_engineers', 'device_group_ng_set_time',              true),
  ('mun_traffic_engineers', 'device_group_ng_read_config',           true),
  ('mun_traffic_engineers', 'device_group_ng_clear_logs',            true),
  ('mun_traffic_engineers', 'device_group_ng_reset_stats',           true),
  ('mun_traffic_engineers', 'device_group_ng_write_datakey',         true),
  ('mun_traffic_engineers', 'device_group_ng_write_user_settings',   true),
  ('mun_traffic_engineers', 'device_group_ng_dfu',                   true),
  ('mun_traffic_engineers', 'location_group_ng_all_access',          true),
  ('mun_traffic_engineers', 'location_group_ng_read_stats',          true),
  ('mun_traffic_engineers', 'location_group_ng_set_time',            true),
  ('mun_traffic_engineers', 'location_group_ng_read_config',         true),
  ('mun_traffic_engineers', 'location_group_ng_clear_logs',          true),
  ('mun_traffic_engineers', 'location_group_ng_reset_stats',         true),
  ('mun_traffic_engineers', 'location_group_ng_write_datakey',       true),
  ('mun_traffic_engineers', 'location_group_ng_write_user_settings', true),
  ('mun_traffic_engineers', 'location_group_ng_dfu',                 true),
-- For Construction Contractors
  ('mun_construction_contractors', 'org_ng_all_access',                     true),
  ('mun_construction_contractors', 'org_ng_read_stats',                     true),
  ('mun_construction_contractors', 'org_ng_set_time',                       true),
  ('mun_construction_contractors', 'org_ng_read_config',                    true),
  ('mun_construction_contractors', 'org_ng_clear_logs',                     true),
  ('mun_construction_contractors', 'org_ng_reset_stats',                    true),
  ('mun_construction_contractors', 'org_ng_write_datakey',                 false),
  ('mun_construction_contractors', 'org_ng_write_user_settings',            true),
  ('mun_construction_contractors', 'org_ng_dfu',                            true),
  ('mun_construction_contractors', 'device_group_ng_all_access',            true),
  ('mun_construction_contractors', 'device_group_ng_read_stats',            true),
  ('mun_construction_contractors', 'device_group_ng_set_time',              true),
  ('mun_construction_contractors', 'device_group_ng_read_config',           true),
  ('mun_construction_contractors', 'device_group_ng_clear_logs',            true),
  ('mun_construction_contractors', 'device_group_ng_reset_stats',           true),
  ('mun_construction_contractors', 'device_group_ng_write_datakey',        false),
  ('mun_construction_contractors', 'device_group_ng_write_user_settings',   true),
  ('mun_construction_contractors', 'device_group_ng_dfu',                   true),
  ('mun_construction_contractors', 'location_group_ng_all_access',          true),
  ('mun_construction_contractors', 'location_group_ng_read_stats',          true),
  ('mun_construction_contractors', 'location_group_ng_set_time',            true),
  ('mun_construction_contractors', 'location_group_ng_read_config',         true),
  ('mun_construction_contractors', 'location_group_ng_clear_logs',          true),
  ('mun_construction_contractors', 'location_group_ng_reset_stats',         true),
  ('mun_construction_contractors', 'location_group_ng_write_datakey',      false),
  ('mun_construction_contractors', 'location_group_ng_write_user_settings', true),
  ('mun_construction_contractors', 'location_group_ng_dfu',                 true),
-- For Signal Technician
  ('mun_technician', 'org_ng_all_access',                     true),
  ('mun_technician', 'org_ng_read_stats',                     true),
  ('mun_technician', 'org_ng_set_time',                       true),
  ('mun_technician', 'org_ng_read_config',                    true),
  ('mun_technician', 'org_ng_clear_logs',                    false),
  ('mun_technician', 'org_ng_reset_stats',                   false),
  ('mun_technician', 'org_ng_write_datakey',                  true),
  ('mun_technician', 'org_ng_write_user_settings',            true),
  ('mun_technician', 'org_ng_dfu',                            true),
  ('mun_technician', 'device_group_ng_all_access',            true),
  ('mun_technician', 'device_group_ng_read_stats',            true),
  ('mun_technician', 'device_group_ng_set_time',              true),
  ('mun_technician', 'device_group_ng_read_config',           true),
  ('mun_technician', 'device_group_ng_clear_logs',           false),
  ('mun_technician', 'device_group_ng_reset_stats',          false),
  ('mun_technician', 'device_group_ng_write_datakey',         true),
  ('mun_technician', 'device_group_ng_write_user_settings',   true),
  ('mun_technician', 'device_group_ng_dfu',                   true),
  ('mun_technician', 'location_group_ng_all_access',          true),
  ('mun_technician', 'location_group_ng_read_stats',          true),
  ('mun_technician', 'location_group_ng_set_time',            true),
  ('mun_technician', 'location_group_ng_read_config',         true),
  ('mun_technician', 'location_group_ng_clear_logs',         false),
  ('mun_technician', 'location_group_ng_reset_stats',        false),
  ('mun_technician', 'location_group_ng_write_datakey',       true),
  ('mun_technician', 'location_group_ng_write_user_settings', true),
  ('mun_technician', 'location_group_ng_dfu',                 true),
-- For Maintenance Contractors
  ('mun_maintenance_contractors', 'org_ng_all_access',                     true),
  ('mun_maintenance_contractors', 'org_ng_read_stats',                     true),
  ('mun_maintenance_contractors', 'org_ng_set_time',                       true),
  ('mun_maintenance_contractors', 'org_ng_read_config',                    true),
  ('mun_maintenance_contractors', 'org_ng_clear_logs',                    false),
  ('mun_maintenance_contractors', 'org_ng_reset_stats',                   false),
  ('mun_maintenance_contractors', 'org_ng_write_datakey',                 false),
  ('mun_maintenance_contractors', 'org_ng_write_user_settings',            true),
  ('mun_maintenance_contractors', 'org_ng_dfu',                            true),
  ('mun_maintenance_contractors', 'device_group_ng_all_access',            true),
  ('mun_maintenance_contractors', 'device_group_ng_read_stats',            true),
  ('mun_maintenance_contractors', 'device_group_ng_set_time',              true),
  ('mun_maintenance_contractors', 'device_group_ng_read_config',           true),
  ('mun_maintenance_contractors', 'device_group_ng_clear_logs',           false),
  ('mun_maintenance_contractors', 'device_group_ng_reset_stats',          false),
  ('mun_maintenance_contractors', 'device_group_ng_write_datakey',        false),
  ('mun_maintenance_contractors', 'device_group_ng_write_user_settings',   true),
  ('mun_maintenance_contractors', 'device_group_ng_dfu',                   true),
  ('mun_maintenance_contractors', 'location_group_ng_all_access',          true),
  ('mun_maintenance_contractors', 'location_group_ng_read_stats',          true),
  ('mun_maintenance_contractors', 'location_group_ng_set_time',            true),
  ('mun_maintenance_contractors', 'location_group_ng_read_config',         true),
  ('mun_maintenance_contractors', 'location_group_ng_clear_logs',         false),
  ('mun_maintenance_contractors', 'location_group_ng_reset_stats',        false),
  ('mun_maintenance_contractors', 'location_group_ng_write_datakey',      false),
  ('mun_maintenance_contractors', 'location_group_ng_write_user_settings', true),
  ('mun_maintenance_contractors', 'location_group_ng_dfu',                 true),
-- For IT
  ('mun_it', 'org_ng_all_access',                      true),
  ('mun_it', 'org_ng_read_stats',                     false),
  ('mun_it', 'org_ng_set_time',                        true),
  ('mun_it', 'org_ng_read_config',                    false),
  ('mun_it', 'org_ng_clear_logs',                     false),
  ('mun_it', 'org_ng_reset_stats',                    false),
  ('mun_it', 'org_ng_write_datakey',                  false),
  ('mun_it', 'org_ng_write_user_settings',            false),
  ('mun_it', 'org_ng_dfu',                            false),
  ('mun_it', 'device_group_ng_all_access',             true),
  ('mun_it', 'device_group_ng_read_stats',            false),
  ('mun_it', 'device_group_ng_set_time',               true),
  ('mun_it', 'device_group_ng_read_config',           false),
  ('mun_it', 'device_group_ng_clear_logs',            false),
  ('mun_it', 'device_group_ng_reset_stats',           false),
  ('mun_it', 'device_group_ng_write_datakey',         false),
  ('mun_it', 'device_group_ng_write_user_settings',   false),
  ('mun_it', 'device_group_ng_dfu',                   false),
  ('mun_it', 'location_group_ng_all_access',           true),
  ('mun_it', 'location_group_ng_read_stats',          false),
  ('mun_it', 'location_group_ng_set_time',             true),
  ('mun_it', 'location_group_ng_read_config',         false),
  ('mun_it', 'location_group_ng_clear_logs',          false),
  ('mun_it', 'location_group_ng_reset_stats',         false),
  ('mun_it', 'location_group_ng_write_datakey',       false),
  ('mun_it', 'location_group_ng_write_user_settings', false),
  ('mun_it', 'location_group_ng_dfu',                 false),
-- For Consultant
  ('mun_consultant', 'org_ng_all_access',                      true),
  ('mun_consultant', 'org_ng_read_stats',                     false),
  ('mun_consultant', 'org_ng_set_time',                       false),
  ('mun_consultant', 'org_ng_read_config',                     true),
  ('mun_consultant', 'org_ng_clear_logs',                     false),
  ('mun_consultant', 'org_ng_reset_stats',                    false),
  ('mun_consultant', 'org_ng_write_datakey',                  false),
  ('mun_consultant', 'org_ng_write_user_settings',            false),
  ('mun_consultant', 'org_ng_dfu',                            false),
  ('mun_consultant', 'device_group_ng_all_access',             true),
  ('mun_consultant', 'device_group_ng_read_stats',            false),
  ('mun_consultant', 'device_group_ng_set_time',              false),
  ('mun_consultant', 'device_group_ng_read_config',            true),
  ('mun_consultant', 'device_group_ng_clear_logs',            false),
  ('mun_consultant', 'device_group_ng_reset_stats',           false),
  ('mun_consultant', 'device_group_ng_write_datakey',         false),
  ('mun_consultant', 'device_group_ng_write_user_settings',   false),
  ('mun_consultant', 'device_group_ng_dfu',                   false),
  ('mun_consultant', 'location_group_ng_all_access',           true),
  ('mun_consultant', 'location_group_ng_read_stats',          false),
  ('mun_consultant', 'location_group_ng_set_time',            false),
  ('mun_consultant', 'location_group_ng_read_config',          true),
  ('mun_consultant', 'location_group_ng_clear_logs',          false),
  ('mun_consultant', 'location_group_ng_reset_stats',         false),
  ('mun_consultant', 'location_group_ng_write_datakey',       false),
  ('mun_consultant', 'location_group_ng_write_user_settings', false),
  ('mun_consultant', 'location_group_ng_dfu',                 false),
-- For new roles:
-- Grant true to org_view_devices, org_manage_devices, device_group_view_devices,
-- device_group_manage_devices, location_group_view_devices, and location_group_manage_devices
-- Traffic Engineers
  ('mun_traffic_engineers', 'org_view_devices',               true),
  ('mun_traffic_engineers', 'org_manage_devices',             true),
  ('mun_traffic_engineers', 'device_group_view_devices',      true),
  ('mun_traffic_engineers', 'device_group_manage_devices',    true),
  ('mun_traffic_engineers', 'location_group_view_devices',    true),
  ('mun_traffic_engineers', 'location_group_manage_devices',  true),
  ('mun_traffic_engineers', 'org_view_users',                false),
  ('mun_traffic_engineers', 'org_manage_users',              false),
  ('mun_traffic_engineers', 'org_delete_users',              false),
  ('mun_traffic_engineers', 'org_view_settings',             false),
  ('mun_traffic_engineers', 'org_manage_settings',           false),
  ('mun_traffic_engineers', 'org_manage_device_groups',      false),
  ('mun_traffic_engineers', 'org_manage_location_groups',    false),
  ('mun_traffic_engineers', 'org_delete_devices',            false),
  ('mun_traffic_engineers', 'org_view_reports',              false),
  ('mun_traffic_engineers', 'org_view_admin_reports',        false),
  ('mun_traffic_engineers', 'device_group_delete_devices',   false),
  ('mun_traffic_engineers', 'location_group_delete_devices', false),
-- Construction Contractors
  ('mun_construction_contractors', 'org_view_devices',               true),
  ('mun_construction_contractors', 'org_manage_devices',             true),
  ('mun_construction_contractors', 'device_group_view_devices',      true),
  ('mun_construction_contractors', 'device_group_manage_devices',    true),
  ('mun_construction_contractors', 'location_group_view_devices',    true),
  ('mun_construction_contractors', 'location_group_manage_devices',  true),
  ('mun_construction_contractors', 'org_view_users',                false),
  ('mun_construction_contractors', 'org_manage_users',              false),
  ('mun_construction_contractors', 'org_delete_users',              false),
  ('mun_construction_contractors', 'org_view_settings',             false),
  ('mun_construction_contractors', 'org_manage_settings',           false),
  ('mun_construction_contractors', 'org_manage_device_groups',      false),
  ('mun_construction_contractors', 'org_manage_location_groups',    false),
  ('mun_construction_contractors', 'org_delete_devices',            false),
  ('mun_construction_contractors', 'org_view_reports',              false),
  ('mun_construction_contractors', 'org_view_admin_reports',        false),
  ('mun_construction_contractors', 'device_group_delete_devices',   false),
  ('mun_construction_contractors', 'location_group_delete_devices', false),
-- Maintenance Contractors
  ('mun_maintenance_contractors', 'org_view_devices',               true),
  ('mun_maintenance_contractors', 'org_manage_devices',             true),
  ('mun_maintenance_contractors', 'device_group_view_devices',      true),
  ('mun_maintenance_contractors', 'device_group_manage_devices',    true),
  ('mun_maintenance_contractors', 'location_group_view_devices',    true),
  ('mun_maintenance_contractors', 'location_group_manage_devices',  true),
  ('mun_maintenance_contractors', 'org_view_users',                false),
  ('mun_maintenance_contractors', 'org_manage_users',              false),
  ('mun_maintenance_contractors', 'org_delete_users',              false),
  ('mun_maintenance_contractors', 'org_view_settings',             false),
  ('mun_maintenance_contractors', 'org_manage_settings',           false),
  ('mun_maintenance_contractors', 'org_manage_device_groups',      false),
  ('mun_maintenance_contractors', 'org_manage_location_groups',    false),
  ('mun_maintenance_contractors', 'org_delete_devices',            false),
  ('mun_maintenance_contractors', 'org_view_reports',              false),
  ('mun_maintenance_contractors', 'org_view_admin_reports',        false),
  ('mun_maintenance_contractors', 'device_group_delete_devices',   false),
  ('mun_maintenance_contractors', 'location_group_delete_devices', false),
-- IT
  ('mun_it', 'org_view_devices',               true),
  ('mun_it', 'org_manage_devices',             true),
  ('mun_it', 'device_group_view_devices',      true),
  ('mun_it', 'device_group_manage_devices',    true),
  ('mun_it', 'location_group_view_devices',    true),
  ('mun_it', 'location_group_manage_devices',  true),
  ('mun_it', 'org_view_users',                false),
  ('mun_it', 'org_manage_users',              false),
  ('mun_it', 'org_delete_users',              false),
  ('mun_it', 'org_view_settings',             false),
  ('mun_it', 'org_manage_settings',           false),
  ('mun_it', 'org_manage_device_groups',      false),
  ('mun_it', 'org_manage_location_groups',    false),
  ('mun_it', 'org_delete_devices',            false),
  ('mun_it', 'org_view_reports',              false),
  ('mun_it', 'org_view_admin_reports',        false),
  ('mun_it', 'device_group_delete_devices',   false),
  ('mun_it', 'location_group_delete_devices', false),
-- Consultant
  ('mun_consultant', 'org_view_devices',               true),
  ('mun_consultant', 'org_manage_devices',             true),
  ('mun_consultant', 'device_group_view_devices',      true),
  ('mun_consultant', 'device_group_manage_devices',    true),
  ('mun_consultant', 'location_group_view_devices',    true),
  ('mun_consultant', 'location_group_manage_devices',  true),
  ('mun_consultant', 'org_view_users',                false),
  ('mun_consultant', 'org_manage_users',              false),
  ('mun_consultant', 'org_delete_users',              false),
  ('mun_consultant', 'org_view_settings',             false),
  ('mun_consultant', 'org_manage_settings',           false),
  ('mun_consultant', 'org_manage_device_groups',      false),
  ('mun_consultant', 'org_manage_location_groups',    false),
  ('mun_consultant', 'org_delete_devices',            false),
  ('mun_consultant', 'org_view_reports',              false),
  ('mun_consultant', 'org_view_admin_reports',        false),
  ('mun_consultant', 'device_group_delete_devices',   false),
  ('mun_consultant', 'location_group_delete_devices', false),
-- For Manager role
  ('mun_manager', 'org_ng_all_access',                      true),
  ('mun_manager', 'org_ng_read_stats',                     false),
  ('mun_manager', 'org_ng_set_time',                       false),
  ('mun_manager', 'org_ng_read_config',                    false),
  ('mun_manager', 'org_ng_clear_logs',                     false),
  ('mun_manager', 'org_ng_reset_stats',                    false),
  ('mun_manager', 'org_ng_write_datakey',                  false),
  ('mun_manager', 'org_ng_write_user_settings',            false),
  ('mun_manager', 'org_ng_dfu',                            false),
  ('mun_manager', 'device_group_ng_all_access',             true),
  ('mun_manager', 'device_group_ng_read_stats',            false),
  ('mun_manager', 'device_group_ng_set_time',              false),
  ('mun_manager', 'device_group_ng_read_config',           false),
  ('mun_manager', 'device_group_ng_clear_logs',            false),
  ('mun_manager', 'device_group_ng_reset_stats',           false),
  ('mun_manager', 'device_group_ng_write_datakey',         false),
  ('mun_manager', 'device_group_ng_write_user_settings',   false),
  ('mun_manager', 'device_group_ng_dfu',                   false),
  ('mun_manager', 'location_group_ng_all_access',           true),
  ('mun_manager', 'location_group_ng_read_stats',          false),
  ('mun_manager', 'location_group_ng_set_time',            false),
  ('mun_manager', 'location_group_ng_read_config',         false),
  ('mun_manager', 'location_group_ng_clear_logs',          false),
  ('mun_manager', 'location_group_ng_reset_stats',         false),
  ('mun_manager', 'location_group_ng_write_datakey',       false),
  ('mun_manager', 'location_group_ng_write_user_settings', false),
  ('mun_manager', 'location_group_ng_dfu',                 false),
-- For Anonymous role
  ('mun_anonymous', 'org_ng_all_access',                      false),
  ('mun_anonymous', 'org_ng_read_stats',                     false),
  ('mun_anonymous', 'org_ng_set_time',                       false),
  ('mun_anonymous', 'org_ng_read_config',                    false),
  ('mun_anonymous', 'org_ng_clear_logs',                     false),
  ('mun_anonymous', 'org_ng_reset_stats',                    false),
  ('mun_anonymous', 'org_ng_write_datakey',                  false),
  ('mun_anonymous', 'org_ng_write_user_settings',            false),
  ('mun_anonymous', 'org_ng_dfu',                            false),
  ('mun_anonymous', 'device_group_ng_all_access',             false),
  ('mun_anonymous', 'device_group_ng_read_stats',            false),
  ('mun_anonymous', 'device_group_ng_set_time',              false),
  ('mun_anonymous', 'device_group_ng_read_config',           false),
  ('mun_anonymous', 'device_group_ng_clear_logs',            false),
  ('mun_anonymous', 'device_group_ng_reset_stats',           false),
  ('mun_anonymous', 'device_group_ng_write_datakey',         false),
  ('mun_anonymous', 'device_group_ng_write_user_settings',   false),
  ('mun_anonymous', 'device_group_ng_dfu',                   false),
  ('mun_anonymous', 'location_group_ng_all_access',           false),
  ('mun_anonymous', 'location_group_ng_read_stats',          false),
  ('mun_anonymous', 'location_group_ng_set_time',            false),
  ('mun_anonymous', 'location_group_ng_read_config',         false),
  ('mun_anonymous', 'location_group_ng_clear_logs',          false),
  ('mun_anonymous', 'location_group_ng_reset_stats',         false),
  ('mun_anonymous', 'location_group_ng_write_datakey',       false),
  ('mun_anonymous', 'location_group_ng_write_user_settings', false),
  ('mun_anonymous', 'location_group_ng_dfu',                 false);
