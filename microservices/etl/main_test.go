package main

import (
	"context"
	"errors"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/twilio/twilio-go"
	"synapse-its.com/etl/processors/handlers/notifications/mailgun"
	"synapse-its.com/etl/processors/subscriptions"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/healthz"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/mocks"
)

func TestRunScenarios(t *testing.T) {
	// Cannot add t.Parallel() due to overriding globals
	orig := loggerFatalf
	defer func() { loggerFatalf = orig }()
	loggerFatalf = logger.Errorf
	tests := []struct {
		name               string
		subsList           []subscriptions.Subscription
		batchOpts          []mocks.FakeBatchOption
		overrideHealth     bool
		healthzListenErr   error
		healthzShutdownErr error
		twilioClientErr    error
		mailgunClientErr   error
		useFakeSignal      bool
		expectReadinessErr bool
		contextCanceled    bool
		expectRunErr       bool
	}{
		{
			name:               "ParentContextCanceled_NoSubscriptions",
			subsList:           nil,
			batchOpts:          nil,
			overrideHealth:     false,
			twilioClientErr:    nil,
			mailgunClientErr:   nil,
			useFakeSignal:      true,
			expectReadinessErr: false, // no subs so readinessCheck() should return nil
			contextCanceled:    false, // we rely on fake signal
		},
		{
			name:               "BatcherShutdownError_NoSubscriptions",
			subsList:           nil,
			batchOpts:          []mocks.FakeBatchOption{mocks.WithBatchShutdownError(errors.New("shutdown failed"))},
			overrideHealth:     false,
			twilioClientErr:    nil,
			mailgunClientErr:   nil,
			useFakeSignal:      true,
			expectReadinessErr: false, // still no subs so readinessCheck() returns nil
			contextCanceled:    false, // we rely on fake signal
		},
		{
			name: "SubscriptionUnhealthy_HangingSubscription",
			subsList: []subscriptions.Subscription{
				{
					Name: "hangForever",
					Handler: func(ctx context.Context, s connect.PsSubscription) {
						select {} // hangs, ignoring ctx.Done()
					},
				},
			},
			batchOpts:          nil,
			overrideHealth:     false,
			twilioClientErr:    nil,
			mailgunClientErr:   nil,
			useFakeSignal:      true,
			expectReadinessErr: true,  // hanging subs so readinessCheck() should eventually return an error
			contextCanceled:    false, // we rely on fake signal
		},
		{
			name:               "HealthzListenError",
			subsList:           nil,
			batchOpts:          nil,
			overrideHealth:     true,
			healthzListenErr:   errors.New("hz-listen-fail"),
			twilioClientErr:    nil,
			mailgunClientErr:   nil,
			useFakeSignal:      true,
			expectReadinessErr: false, // no subs so readinessCheck() returns nil
			contextCanceled:    false, // we rely on fake signal
		},
		{
			name:               "HealthzShutdownError",
			subsList:           nil,
			batchOpts:          nil,
			overrideHealth:     true,
			healthzShutdownErr: errors.New("hz-teardown-fail"),
			twilioClientErr:    nil,
			mailgunClientErr:   nil,
			useFakeSignal:      true,
			expectReadinessErr: false, // no subs so readinessCheck() returns nil
			contextCanceled:    false, // we rely on fake signal
		},
		{
			name:               "ContextDone_NoSignal",
			subsList:           nil,
			batchOpts:          nil,
			overrideHealth:     false,
			twilioClientErr:    nil,
			mailgunClientErr:   nil,
			useFakeSignal:      false, // no signal
			expectReadinessErr: false, // no subs so readinessCheck() returns nil
			contextCanceled:    true,  // cancel context before running
		},
		{
			name:             "TwilioClientError",
			subsList:         nil,
			batchOpts:        nil,
			overrideHealth:   false,
			twilioClientErr:  errors.New("twilio-client-error"),
			mailgunClientErr: nil,
			useFakeSignal:    true,
			expectRunErr:     true,
		},
		{
			name:             "MailgunClientError",
			subsList:         nil,
			batchOpts:        nil,
			overrideHealth:   false,
			twilioClientErr:  nil,
			mailgunClientErr: errors.New("mailgun-client-error"),
			useFakeSignal:    true,
			expectRunErr:     true,
		},
	}

	for _, tc := range tests {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			// Possibly override healthzShutdown to return an error
			fakeHZ := &mocks.FakeHealthzServer{
				ListenErr:   tc.healthzListenErr,
				ShutdownErr: tc.healthzShutdownErr,
			}
			if tc.overrideHealth {
				healthzNewServer = func(port string) healthz.HealthzServer {
					return fakeHZ
				}
			} else {
				healthzNewServer = func(port string) healthz.HealthzServer {
					// Use real server but with no-op ListenAndServe and Shutdown
					return &mocks.FakeHealthzServer{}
				}
			}

			// Prepare newSignals depending on useFakeSignal
			var newSignals func() <-chan os.Signal
			if tc.useFakeSignal {
				fakeSig := make(chan os.Signal, 1)
				newSignals = func() <-chan os.Signal {
					return fakeSig
				}
				// Send a fake signal after 10ms
				go func() {
					time.Sleep(10 * time.Millisecond)
					fakeSig <- os.Interrupt
				}()
			} else {
				newSignals = func() <-chan os.Signal {
					ch := make(chan os.Signal, 1)
					return ch
				}
			}

			// Parent context setup
			parentCtx, cancel := context.WithCancel(context.Background())
			if tc.contextCanceled {
				// Cancel immediately to force <-ctx.Done()
				cancel()
			}
			defer cancel()

			// Fake dependencies
			newConns := func(ctx context.Context) *connect.Connections {
				return mocks.FakeConns()
			}
			newBatch := func(bq connect.BigQueryExecutorInterface, ps connect.PsClient) bqbatch.Batcher {
				return mocks.FakeBatcherWithOptions(tc.batchOpts...)
			}
			// Mock Twilio client factory
			newTwilioClient := func(ctx context.Context) (*twilio.RestClient, error) {
				if tc.twilioClientErr != nil {
					return nil, tc.twilioClientErr
				}
				return &twilio.RestClient{}, nil
			}
			newMailgunClient := func(ctx context.Context) (*mailgun.Client, error) {
				if tc.mailgunClientErr != nil {
					return nil, tc.mailgunClientErr
				}
				return &mailgun.Client{}, nil
			}

			// Call Run with the new signature
			err := Run(parentCtx, "", newConns, newBatch, tc.subsList, newSignals, newTwilioClient, newMailgunClient)
			if tc.expectRunErr {
				assert.Error(t, err, "Run should return an error for %q", tc.name)
			} else {
				assert.NoError(t, err, "Run should return no error for %q", tc.name)
			}

			// Verify fake healthz calls if overrideHealth
			if tc.overrideHealth {
				assert.True(t, fakeHZ.ListenCalled, "Healthz ListenAndServe should have been called")
				assert.True(t, fakeHZ.SetBootCalled, "Healthz SetBootComplete should have been called")
				assert.True(t, fakeHZ.SetReadyCalled, "Healthz SetReady should have been called")
				assert.True(t, fakeHZ.SetCustomCalled, "Healthz SetCustomReadinessCheck should have been called")
				assert.True(t, fakeHZ.SetNotReadyCalled, "Healthz SetNotReady should have been called")
				assert.True(t, fakeHZ.ShutdownCalled, "Healthz Shutdown should have been called")
			}

			// Directly test subscriptionReadinessCheck(mgr)
			mgr := subscriptions.NewManager(tc.subsList, subscriptions.BackoffConfig{
				Initial:    2 * time.Second,
				Max:        5 * time.Minute,
				MaxRetries: 10,
			})
			if tc.expectReadinessErr && len(tc.subsList) > 0 {
				// Manually mark the first subscription unhealthy
				mgr.MarkUnhealthy(tc.subsList[0].Name)
			}
			checkFn := subscriptionReadinessCheck(mgr)
			readyErr := checkFn()
			if tc.expectReadinessErr {
				assert.Error(t, readyErr, "expected readiness check to fail for %q", tc.name)
			} else {
				assert.NoError(t, readyErr, "expected readiness check to succeed for %q", tc.name)
			}
		})
	}
}

func TestDefaultSignalChan(t *testing.T) {
	ch := DefaultSignalChan()
	assert.NotNil(t, ch, "channel should not be nil")
	v := reflect.ValueOf(ch)
	assert.Equal(t, reflect.Chan, v.Kind(), "should be a channel")
	assert.Equal(t, 1, v.Cap(), "should have buffer size 1")
}
